/**
 * Prettier 配置文件
 * 
 * 功能说明：
 * - 统一代码格式化规则
 * - 自动格式化代码风格
 * - 与ESLint配合使用
 * 
 * 配置说明：
 * - 使用单引号
 * - 不使用尾随逗号
 * - 使用2个空格缩进
 * - 行长度限制120字符
 * - 使用分号结尾
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

module.exports = {
  // 基础格式化选项
  printWidth: 120,           // 行长度
  tabWidth: 2,              // 缩进宽度
  useTabs: false,           // 使用空格而不是tab
  semi: true,               // 使用分号
  singleQuote: true,        // 使用单引号
  quoteProps: 'as-needed',  // 对象属性引号
  trailingComma: 'none',    // 不使用尾随逗号
  bracketSpacing: true,     // 对象花括号空格
  bracketSameLine: false,   // 标签闭合换行
  arrowParens: 'avoid',     // 箭头函数参数括号
  endOfLine: 'lf',          // 换行符类型
  
  // 文件类型特定配置
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 80,
        tabWidth: 2
      }
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always'
      }
    },
    {
      files: '*.yml',
      options: {
        tabWidth: 2,
        singleQuote: false
      }
    }
  ]
};
