# 批量更新达人平台信息脚本

## 🎯 项目概述

本项目提供了一个完整的批量处理解决方案，用于检测和更新合作对接记录中的达人平台信息。通过解析达人主页链接，自动提取平台类型和用户ID，并生成可执行的批量更新脚本。

## ✨ 主要功能

### 1. 数据检测与解析
- 🔍 批量扫描合作对接记录（CooperationManagement表）
- 🔗 解析达人主页链接，支持小红书和巨量星图平台
- 📊 提取平台类型（platform）和平台用户ID（platformUserId）
- 📋 生成详细的解析结果对照表

### 2. 安全执行机制
- 🛡️ 干运行模式：仅检测分析，不修改数据
- ⚠️ 用户确认提示：防止误操作
- 📦 分批处理：避免系统过载
- 🔄 错误恢复：单个错误不影响整体流程

### 3. CRM集成测试
- 🧪 接口可用性测试
- 🔗 平台映射验证
- 📈 成功率统计
- ❌ 详细错误报告

### 4. 智能输出生成
- 📄 详细对照文件（txt格式）
- 📜 可执行批量更新脚本
- 📊 执行报告和统计信息
- 🎯 用户友好的进度显示

## 🚀 快速开始

### 1. 首次使用（推荐）
```bash
# 干运行模式 - 了解数据现状
node scripts/batch-update-influencer-platform-info.js --dry-run
```

### 2. 接口测试
```bash
# 测试CRM接口可用性
node scripts/batch-update-influencer-platform-info.js --test-only --batch-size 10
```

### 3. 正式执行
```bash
# 生成批量更新脚本
node scripts/batch-update-influencer-platform-info.js --batch-size 20
```

### 4. 查看帮助
```bash
# 显示所有可用选项
node scripts/batch-update-influencer-platform-info.js --help
```

## 📁 项目结构

```
├── scripts/
│   ├── batch-update-influencer-platform-info.js  # 主脚本文件
│   └── demo-batch-update.js                      # 功能演示脚本
├── test/
│   └── batch-update-test.js                      # 功能测试脚本
├── docs/
│   └── batch-update-influencer-platform-guide.md # 详细使用指南
├── batch-output/                                 # 默认输出目录
│   ├── influencer-platform-comparison-*.txt      # 对照文件
│   └── batch-update-script-*.js                  # 更新脚本
└── README-batch-update.md                        # 本文档
```

## 🔧 支持的平台

### 小红书（xiaohongshu）
| 链接类型 | 格式示例 | 说明 |
|---------|---------|------|
| 蒲公英链接 | `https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/xxx` | 商业合作平台 |
| 合作者链接 | `https://pgy.xiaohongshu.com/solar/cooperator/blogger/xxx` | 合作者页面 |
| 用户主页 | `https://www.xiaohongshu.com/user/profile/xxx` | 普通用户主页 |
| 直接ID | `abc123def456789012345678` | 20-30位字母数字混合 |

### 巨量星图（juxingtu）
| 链接类型 | 格式示例 | 说明 |
|---------|---------|------|
| 创作者主页 | `https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/xxx` | 完整链接 |
| 简化链接 | `https://www.xingtu.cn/ad/creator/author-homepage/xxx` | 简化格式 |
| 直接ID | `123456789012` | 8-20位纯数字 |

## 📊 输出文件示例

### 对照文件格式
```
合作记录ID | 客户名称 | 达人主页链接 | 解析状态 | 平台类型 | 平台用户ID | CRM客户ID | 原平台 | 原博主名 | 解析错误
1001 | 美妆达人小红 | https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/abc123 | 成功 | xiaohongshu | abc123 | 12345 | xiaohongshu | 小红 | 
1002 | 时尚博主Lisa | https://www.xingtu.cn/ad/creator/author-homepage/987654321 | 成功 | juxingtu | 987654321 | 12346 | juxingtu | Lisa | 
1003 | 生活达人小王 | https://invalid-platform.com/user/123 | 失败 |  |  |  |  | 小王 | 无法识别的链接格式
```

### 更新脚本特性
- ✅ 包含所有可更新记录的完整信息
- ✅ 自动调用CRM更新接口
- ✅ 包含错误处理和进度显示
- ✅ 可独立执行
- ✅ 请求间自动延迟避免过载

## 🧪 测试验证

### 运行功能测试
```bash
# 测试脚本的各项功能
node test/batch-update-test.js
```

### 运行功能演示
```bash
# 查看完整的功能演示
node scripts/demo-batch-update.js
```

### 测试覆盖范围
- ✅ 链接解析功能（10个测试用例）
- ✅ 批处理器初始化
- ✅ 文件生成逻辑
- ✅ 错误处理机制
- ✅ 无效输入处理

## ⚙️ 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--dry-run` | boolean | false | 仅检测和生成报告，不实际更新 |
| `--batch-size` | number | 50 | 批处理大小 |
| `--test-only` | boolean | false | 仅进行接口测试 |
| `--output-dir` | string | ./batch-output | 输出目录 |
| `--help` | - | - | 显示帮助信息 |

## 🔒 安全考虑

### 1. 数据安全
- 🛡️ 干运行模式验证逻辑
- ⚠️ 用户确认机制
- 📦 分批处理避免过载
- 🔄 错误隔离机制

### 2. 执行安全
- 📋 详细的执行日志
- 📊 完整的统计报告
- 🎯 明确的错误信息
- 🔧 可回滚的操作记录

### 3. 系统安全
- ⏱️ 请求间延迟控制
- 🔗 CRM接口限流
- 💾 内存使用优化
- 📈 性能监控

## 📈 使用建议

### 1. 执行前准备
1. 确保数据库连接正常
2. 验证CRM集成服务可用
3. 备份重要数据（推荐）
4. 选择业务低峰期执行

### 2. 执行流程
1. **首次使用**：执行干运行模式了解数据现状
2. **接口测试**：小批量测试CRM接口可用性
3. **正式执行**：生成批量更新脚本
4. **脚本执行**：运行生成的更新脚本
5. **结果验证**：检查更新结果和错误报告

### 3. 故障排除
1. 查看详细的执行日志
2. 分析对照文件中的错误信息
3. 验证CRM接口配置
4. 检查网络连接状态

## 🤝 技术支持

### 相关文档
- 📖 [详细使用指南](docs/batch-update-influencer-platform-guide.md)
- 🔧 [CRM集成指南](docs/crm-integration-guide.md)
- 📝 [API文档](docs/api-documentation.md)

### 联系方式
- 📧 技术支持：请联系系统管理员
- 🐛 问题反馈：请提交详细的错误日志
- 💡 功能建议：欢迎提出改进建议

## 📄 许可证

本项目为内部使用工具，请遵循公司相关规定使用。

---

**⚠️ 重要提醒**：
- 首次使用请务必先执行干运行模式
- 正式执行前请备份重要数据
- 在业务低峰期执行批量操作
- 仔细检查生成的对照文件和更新脚本
