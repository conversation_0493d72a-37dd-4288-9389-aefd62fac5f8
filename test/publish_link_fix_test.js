/**
 * 发布链接修复服务测试脚本
 * 
 * 测试PublishLinkFixService的各项功能
 */

const PublishLinkFixService = require('../src/services/PublishLinkFixService');

/**
 * 测试获取缺失publishLink统计
 */
async function testGetMissingPublishLinkStats() {
  console.log('\n🧪 测试获取缺失publishLink统计...');
  
  try {
    const service = new PublishLinkFixService();
    const stats = await service.getMissingPublishLinkStats();
    
    console.log('📊 统计结果:');
    console.log(`  总记录数: ${stats.totalCount}`);
    console.log(`  有publishLink的记录数: ${stats.withPublishLinkCount}`);
    console.log(`  缺失publishLink的记录数: ${stats.missingPublishLinkCount}`);
    console.log(`  CRM同步但缺失publishLink的记录数: ${stats.crmSyncedMissingCount}`);
    console.log(`  publishLink填充率: ${stats.publishLinkRate}%`);
    
    if (stats.totalCount > 0) {
      console.log('✅ 获取缺失publishLink统计测试通过');
      return true;
    } else {
      console.log('⚠️ 数据库中没有记录');
      return true;
    }
    
  } catch (error) {
    console.error('❌ 获取缺失publishLink统计测试失败:', error.message);
    return false;
  }
}

/**
 * 测试获取缺失publishLink记录列表
 */
async function testGetMissingPublishLinkRecords() {
  console.log('\n🧪 测试获取缺失publishLink记录列表...');
  
  try {
    const service = new PublishLinkFixService();
    
    // 测试基本查询
    const result1 = await service.getMissingPublishLinkRecords({
      page: 1,
      limit: 5
    });
    
    console.log('📋 基本查询结果:');
    console.log(`  记录数: ${result1.records.length}`);
    console.log(`  总数: ${result1.pagination.totalCount}`);
    console.log(`  总页数: ${result1.pagination.totalPages}`);
    
    if (result1.records.length > 0) {
      console.log('  样例记录:');
      result1.records.slice(0, 2).forEach((record, index) => {
        console.log(`    ${index + 1}. ID: ${record.id}, 客户: ${record.customerName}`);
        console.log(`       标题: ${record.title}`);
        console.log(`       外部协议ID: ${record.externalAgreementId}`);
      });
    }
    
    // 测试只查看CRM同步的记录
    const result2 = await service.getMissingPublishLinkRecords({
      page: 1,
      limit: 5,
      onlyCrmSynced: true
    });
    
    console.log('\n📋 CRM同步记录查询结果:');
    console.log(`  记录数: ${result2.records.length}`);
    console.log(`  总数: ${result2.pagination.totalCount}`);
    
    console.log('✅ 获取缺失publishLink记录列表测试通过');
    return true;
    
  } catch (error) {
    console.error('❌ 获取缺失publishLink记录列表测试失败:', error.message);
    return false;
  }
}

/**
 * 测试链接格式验证
 */
async function testLinkValidation() {
  console.log('\n🧪 测试链接格式验证...');
  
  try {
    const service = new PublishLinkFixService();
    
    const testCases = [
      {
        link: 'https://www.xiaohongshu.com/explore/12345678',
        expected: true,
        description: '小红书链接'
      },
      {
        link: 'https://www.iesdouyin.com/share/video/7487160221639413003/',
        expected: true,
        description: '抖音链接'
      },
      {
        link: 'https://weibo.com/1234567890/ABCDEFGHIJ',
        expected: true,
        description: '微博链接'
      },
      {
        link: 'https://www.bilibili.com/video/BV1234567890',
        expected: true,
        description: 'B站链接'
      },
      {
        link: 'https://invalid-link.com/test',
        expected: false,
        description: '无效链接'
      },
      {
        link: '',
        expected: false,
        description: '空链接'
      },
      {
        link: null,
        expected: false,
        description: 'null值'
      }
    ];
    
    let allPassed = true;
    
    for (const testCase of testCases) {
      const result = service.isValidPublishLink(testCase.link);
      if (result === testCase.expected) {
        console.log(`✅ ${testCase.description}: ${testCase.link || '(空)'} - 验证正确`);
      } else {
        console.log(`❌ ${testCase.description}: ${testCase.link || '(空)'} - 验证错误`);
        allPassed = false;
      }
    }
    
    if (allPassed) {
      console.log('✅ 链接格式验证测试通过');
    } else {
      console.log('❌ 链接格式验证测试失败');
    }
    
    return allPassed;
    
  } catch (error) {
    console.error('❌ 链接格式验证测试失败:', error.message);
    return false;
  }
}

/**
 * 测试问题分析功能
 */
async function testAnalyzePublishLinkIssues() {
  console.log('\n🧪 测试问题分析功能...');
  
  try {
    const service = new PublishLinkFixService();
    const analysis = await service.analyzePublishLinkIssues();
    
    console.log('📊 问题分析结果:');
    console.log(`  总记录数: ${analysis.totalCount}`);
    console.log(`  有publishLink的记录数: ${analysis.withPublishLinkCount}`);
    console.log(`  缺失publishLink的记录数: ${analysis.missingPublishLinkCount}`);
    console.log(`  有观看数据但没有发布链接的记录数: ${analysis.hasDataButNoLinkCount}`);
    console.log(`  CRM同步但没有发布链接的记录数: ${analysis.crmSyncedNoLinkCount}`);
    console.log(`  publishLink填充率: ${analysis.publishLinkRate}%`);
    
    console.log('\n🔍 问题诊断:');
    console.log(`  数据完整性问题: ${analysis.analysis.dataIntegrityIssue ? '是' : '否'}`);
    console.log(`  CRM映射问题: ${analysis.analysis.crmMappingIssue ? '是' : '否'}`);
    console.log(`  整体健康状况: ${analysis.analysis.overallHealthy ? '良好' : '需要关注'}`);
    
    console.log('✅ 问题分析功能测试通过');
    return true;
    
  } catch (error) {
    console.error('❌ 问题分析功能测试失败:', error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始发布链接修复服务测试...');
  
  const results = [];
  results.push(await testGetMissingPublishLinkStats());
  results.push(await testGetMissingPublishLinkRecords());
  results.push(await testLinkValidation());
  results.push(await testAnalyzePublishLinkIssues());
  
  const passedCount = results.filter(r => r).length;
  const totalCount = results.length;
  
  console.log(`\n📊 测试结果: ${passedCount}/${totalCount} 通过`);
  
  if (passedCount === totalCount) {
    console.log('✅ 所有测试通过！发布链接修复服务工作正常。');
  } else {
    console.log('❌ 部分测试失败，请检查服务实现。');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testGetMissingPublishLinkStats,
  testGetMissingPublishLinkRecords,
  testLinkValidation,
  testAnalyzePublishLinkIssues,
  runAllTests
};
