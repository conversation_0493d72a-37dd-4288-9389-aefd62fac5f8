/**
 * CRM集成功能测试脚本
 * 
 * 功能说明：
 * - 测试新增的CRM数据创建和更新接口
 * - 验证智能同步功能
 * - 测试字段映射功能
 * - 创建和验证测试数据
 * 
 * 使用方法：
 * node test/crm-integration-test.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const axios = require('axios');
const config = require('../src/config/env');

class CrmIntegrationTest {
  constructor() {
    this.baseUrl = 'http://localhost:3001/api';
    this.token = null;
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    try {
      console.log('🧪 开始CRM集成功能测试...\n');

      // 1. 登录获取token
      await this.login();

      // 2. 测试连接
      await this.testConnection();

      // 3. 测试字段映射
      await this.testFieldMapping();

      // 4. 测试创建测试数据
      await this.testCreateTestData();

      // 5. 测试智能同步
      await this.testSmartSync();

      // 6. 测试数据创建和更新
      await this.testDataOperations();

      // 输出测试结果
      this.printTestResults();

    } catch (error) {
      console.error('❌ 测试运行失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`测试运行失败: ${error.message}`);
    }
  }

  /**
   * 登录获取token
   */
  async login() {
    try {
      console.log('🔐 正在登录...');
      
      const response = await axios.post(`${this.baseUrl}/auth/login`, {
        username: 'admin',
        password: 'admin123'
      });

      if (response.data.success) {
        this.token = response.data.data.token;
        console.log('✅ 登录成功\n');
        this.testResults.passed++;
      } else {
        throw new Error('登录失败');
      }
    } catch (error) {
      console.error('❌ 登录失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`登录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 测试CRM连接
   */
  async testConnection() {
    try {
      console.log('🔍 测试CRM连接...');
      
      const response = await this.makeRequest('GET', '/crm-integration/test-connection');
      
      if (response.data.success) {
        console.log('✅ CRM连接测试成功');
        console.log(`   Token有效: ${response.data.data.tokenValid}`);
        console.log(`   客户总数: ${response.data.data.customerCount}\n`);
        this.testResults.passed++;
      } else {
        throw new Error('CRM连接测试失败');
      }
    } catch (error) {
      console.error('❌ CRM连接测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`CRM连接测试失败: ${error.message}`);
    }
  }

  /**
   * 测试字段映射
   */
  async testFieldMapping() {
    try {
      console.log('🔄 测试字段映射...');
      
      const testCooperationData = {
        customerName: '[测试]测试达人',
        customerHomepage: 'https://test.example.com/influencer',
        seedingPlatform: '小红书,抖音',
        bloggerFansCount: '腰部（粉丝1-20w）',
        title: '[测试]测试合作协议',
        cooperationForm: '图文',
        cooperationBrand: '测试品牌',
        cooperationAmount: '1000元',
        publishPlatform: '小红书',
        responsiblePerson: '测试负责人'
      };

      // 测试客户映射
      const customerMappingResponse = await this.makeRequest('POST', '/crm-integration/test-mapping', {
        cooperationData: testCooperationData,
        mappingType: 'customer'
      });

      if (customerMappingResponse.data.success) {
        console.log('✅ 客户字段映射测试成功');
        console.log('   映射结果:', JSON.stringify(customerMappingResponse.data.data.mappedData, null, 2));
        this.testResults.passed++;
      }

      // 测试协议映射
      const agreementMappingResponse = await this.makeRequest('POST', '/crm-integration/test-mapping', {
        cooperationData: testCooperationData,
        mappingType: 'agreement'
      });

      if (agreementMappingResponse.data.success) {
        console.log('✅ 协议字段映射测试成功');
        console.log('   映射结果:', JSON.stringify(agreementMappingResponse.data.data.mappedData, null, 2));
        this.testResults.passed++;
      }

      console.log('');
    } catch (error) {
      console.error('❌ 字段映射测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`字段映射测试失败: ${error.message}`);
    }
  }

  /**
   * 测试创建测试数据
   */
  async testCreateTestData() {
    try {
      console.log('🧪 测试创建测试数据...');
      
      const response = await this.makeRequest('POST', '/crm-integration/test-data');
      
      if (response.data.success) {
        console.log('✅ 测试数据创建成功');
        console.log(`   测试客户ID: ${response.data.data.customerId}`);
        console.log(`   测试协议ID: ${response.data.data.agreementId}`);
        
        if (response.data.data.errors.length > 0) {
          console.log('   错误列表:', response.data.data.errors);
        }
        
        this.testResults.passed++;
      } else {
        throw new Error('测试数据创建失败');
      }
      
      console.log('');
    } catch (error) {
      console.error('❌ 测试数据创建失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`测试数据创建失败: ${error.message}`);
    }
  }

  /**
   * 测试智能同步（需要先有合作对接数据）
   */
  async testSmartSync() {
    try {
      console.log('🔄 测试智能同步...');
      
      // 这里需要一个真实的合作对接ID，实际测试时需要替换
      const testCooperationId = 1;
      
      const response = await this.makeRequest('POST', '/crm-integration/smart-sync', {
        cooperationId: testCooperationId,
        options: {
          forceCustomerSync: true,
          forceAgreementSync: true
        }
      });
      
      if (response.data.success) {
        console.log('✅ 智能同步测试成功');
        console.log(`   客户同步: ${response.data.data.customerSynced}`);
        console.log(`   协议同步: ${response.data.data.agreementSynced}`);
        console.log(`   客户ID: ${response.data.data.customerId}`);
        console.log(`   协议ID: ${response.data.data.agreementId}`);
        
        if (response.data.data.errors.length > 0) {
          console.log('   错误列表:', response.data.data.errors);
        }
        
        this.testResults.passed++;
      } else {
        throw new Error('智能同步测试失败');
      }
      
      console.log('');
    } catch (error) {
      console.error('❌ 智能同步测试失败:', error.message);
      console.log('   注意: 此测试需要有效的合作对接记录ID\n');
      // 智能同步测试失败不计入失败数，因为可能没有测试数据
    }
  }

  /**
   * 测试数据创建和更新操作
   */
  async testDataOperations() {
    try {
      console.log('📝 测试数据操作...');
      
      // 测试创建客户数据
      const createCustomerResponse = await this.makeRequest('POST', '/crm-integration/data/create', {
        deployId: 'customer',
        data: {
          custom_name: '[测试]API测试客户',
          seas_id: 'fd18e0d6b1164e7080f0fa91dc43b0d8',
          customer_textarea_11: 'https://api-test.example.com',
          custom_remark: '[API测试] 通过API创建的测试客户'
        }
      });
      
      if (createCustomerResponse.data.success) {
        console.log('✅ 客户数据创建测试成功');
        console.log(`   创建的客户ID: ${createCustomerResponse.data.data.dataId}`);
        this.testResults.passed++;
        
        // 测试更新客户数据
        const updateCustomerResponse = await this.makeRequest('POST', '/crm-integration/data/update', {
          deployId: 'customer',
          dataId: createCustomerResponse.data.data.dataId,
          data: {
            custom_remark: '[API测试] 通过API更新的测试客户'
          }
        });
        
        if (updateCustomerResponse.data.success) {
          console.log('✅ 客户数据更新测试成功');
          this.testResults.passed++;
        }
      }
      
      console.log('');
    } catch (error) {
      console.error('❌ 数据操作测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`数据操作测试失败: ${error.message}`);
    }
  }

  /**
   * 发送HTTP请求
   */
  async makeRequest(method, path, data = null) {
    const config = {
      method,
      url: `${this.baseUrl}${path}`,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      config.data = data;
    }

    return await axios(config);
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过: ${this.testResults.passed}`);
    console.log(`❌ 失败: ${this.testResults.failed}`);
    
    if (this.testResults.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    console.log('\n🎉 CRM集成功能测试完成!');
  }
}

// 运行测试
if (require.main === module) {
  const test = new CrmIntegrationTest();
  test.runAllTests().catch(console.error);
}

module.exports = CrmIntegrationTest;
