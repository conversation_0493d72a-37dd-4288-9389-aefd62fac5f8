/**
 * 字段映射执行顺序测试脚本
 * 
 * 测试CRM数据映射的完整流程，验证执行顺序是否正确
 */

const CrmDataMappingService = require('../src/services/CrmDataMappingService');

/**
 * 测试完整的字段映射流程
 */
async function testCompleteFieldMapping() {
  console.log('\n🧪 测试完整字段映射流程...');
  
  try {
    const mappingService = new CrmDataMappingService();
    
    // 模拟完整的CRM协议数据
    const crmAgreementData = {
      id: 'test_agreement_mapping_order',
      contract_title: '小红书-测试博主-字段映射顺序测试-2024.01.01',
      custom_name: '测试博主',
      contract_url_0: 'https://www.xiaohongshu.com/explore/test123456',
      contract_amount: '¥2,000',
      contract_percent_2: '15%',
      contract_date_4: 1704067200000, // 2024-01-01的时间戳
      contract_end_date: 1704153600000, // 2024-01-02的时间戳
      contract_date_5: 1704240000000, // 2024-01-03的时间戳
      contract_integer_1: 10000, // 观看量
      contract_integer_2: 500,   // 点赞数
      contract_integer_3: 200,   // 评论数
      contract_integer_4: 100,   // 收藏数
      contract_amount_8: '¥5,000', // 店铺销售额
      created: Date.now(),
      modified: Date.now()
    };

    console.log('📋 CRM原始数据:');
    console.log('  - contract_url_0:', crmAgreementData.contract_url_0);
    console.log('  - contract_amount:', crmAgreementData.contract_amount);
    console.log('  - contract_percent_2:', crmAgreementData.contract_percent_2);
    console.log('  - contract_date_4:', crmAgreementData.contract_date_4);
    console.log('  - contract_integer_1:', crmAgreementData.contract_integer_1);

    // 执行映射
    console.log('\n🔄 开始执行映射...');
    const mappedData = mappingService.mapAgreementData(crmAgreementData, '测试博主');
    
    console.log('\n📊 映射结果验证:');
    
    // 验证关键字段
    const testFields = [
      { field: 'publishLink', expected: crmAgreementData.contract_url_0, description: '发布链接' },
      { field: 'cooperationAmount', expected: '2000', description: '合作金额' },
      { field: 'influencerCommissionRate', expected: '15', description: '佣金比例' },
      { field: 'viewCount', expected: crmAgreementData.contract_integer_1, description: '观看量' },
      { field: 'likeCount', expected: crmAgreementData.contract_integer_2, description: '点赞数' },
      { field: 'collectCount', expected: crmAgreementData.contract_integer_3, description: '收藏数' }, // contract_integer_3 -> collectCount
      { field: 'commentCount', expected: crmAgreementData.contract_integer_4, description: '评论数' }, // contract_integer_4 -> commentCount
      { field: 'storeSales', expected: '5000', description: '店铺销售额' }
    ];
    
    let allPassed = true;
    
    for (const test of testFields) {
      const actualValue = mappedData[test.field];
      const expectedValue = test.expected;
      
      if (actualValue == expectedValue) { // 使用宽松比较，因为可能有类型转换
        console.log(`✅ ${test.description} (${test.field}): ${actualValue}`);
      } else {
        console.log(`❌ ${test.description} (${test.field}): 期望 ${expectedValue}, 实际 ${actualValue}`);
        allPassed = false;
      }
    }
    
    // 验证日期字段
    const dateFields = [
      { field: 'scheduledPublishTime', source: 'contract_date_4', description: '约定发布时间' },
      { field: 'actualPublishTime', source: 'contract_end_date', description: '实际发布时间' },
      { field: 'dataRegistrationTime', source: 'contract_date_5', description: '数据登记时间' }
    ];
    
    console.log('\n📅 日期字段验证:');
    for (const dateTest of dateFields) {
      const actualValue = mappedData[dateTest.field];
      if (actualValue instanceof Date && !isNaN(actualValue.getTime())) {
        console.log(`✅ ${dateTest.description} (${dateTest.field}): ${actualValue.toISOString()}`);
      } else {
        console.log(`❌ ${dateTest.description} (${dateTest.field}): ${actualValue} (不是有效日期)`);
        allPassed = false;
      }
    }
    
    // 验证默认字段
    console.log('\n🔧 默认字段验证:');
    const defaultFields = [
      { field: 'dataSource', expected: 'dingtalk_crm', description: '数据来源' },
      { field: 'crmLinkStatus', expected: 'fully_linked', description: 'CRM关联状态' },
      { field: 'customerPublicSea', expected: 'fd18e0d6b1164e7080f0fa91dc43b0d8', description: '客户公海' },
      { field: 'platform', description: '平台' },
      { field: 'bloggerName', description: '博主名称' },
      { field: 'responsiblePerson', description: '负责人' }
    ];
    
    for (const defaultTest of defaultFields) {
      const actualValue = mappedData[defaultTest.field];
      if (defaultTest.expected) {
        if (actualValue === defaultTest.expected) {
          console.log(`✅ ${defaultTest.description} (${defaultTest.field}): ${actualValue}`);
        } else {
          console.log(`❌ ${defaultTest.description} (${defaultTest.field}): 期望 ${defaultTest.expected}, 实际 ${actualValue}`);
          allPassed = false;
        }
      } else {
        if (actualValue) {
          console.log(`✅ ${defaultTest.description} (${defaultTest.field}): ${actualValue}`);
        } else {
          console.log(`❌ ${defaultTest.description} (${defaultTest.field}): 未设置`);
          allPassed = false;
        }
      }
    }
    
    console.log('\n📋 完整映射结果:');
    console.log(JSON.stringify(mappedData, null, 2));
    
    if (allPassed) {
      console.log('\n✅ 字段映射流程测试通过');
      return true;
    } else {
      console.log('\n❌ 字段映射流程测试失败');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 字段映射流程测试异常:', error.message);
    console.error('❌ 错误堆栈:', error.stack);
    return false;
  }
}

/**
 * 测试执行顺序问题
 */
async function testExecutionOrder() {
  console.log('\n🧪 测试执行顺序...');
  
  try {
    const mappingService = new CrmDataMappingService();
    
    // 创建一个简单的测试数据，重点关注可能冲突的字段
    const crmData = {
      id: 'order_test',
      contract_title: '执行顺序测试',
      custom_name: '测试客户',
      contract_url_0: 'https://test.com/original',
      contract_amount: '1000'
    };
    
    console.log('📋 测试数据:', crmData);
    
    // 手动执行映射步骤，观察每一步的结果
    const mappedData = {};
    
    console.log('\n🔄 步骤1: 基础字段映射');
    for (const [crmField, localField] of Object.entries(mappingService.agreementFieldMapping)) {
      if (crmData[crmField] !== undefined && crmData[crmField] !== null && crmData[crmField] !== '') {
        mappedData[localField] = crmData[crmField];
        console.log(`  映射: ${crmField} -> ${localField} = ${mappedData[localField]}`);
      }
    }
    
    console.log('\n🔄 步骤2: 特殊字段处理前的状态');
    console.log('  publishLink:', mappedData.publishLink);
    console.log('  cooperationAmount:', mappedData.cooperationAmount);
    
    mappingService.processAgreementSpecialFields(crmData, mappedData);
    
    console.log('\n🔄 步骤3: 特殊字段处理后的状态');
    console.log('  publishLink:', mappedData.publishLink);
    console.log('  cooperationAmount:', mappedData.cooperationAmount);
    
    mappingService.addAgreementDefaultFields(mappedData);
    
    console.log('\n🔄 步骤4: 默认字段添加后的状态');
    console.log('  publishLink:', mappedData.publishLink);
    console.log('  cooperationAmount:', mappedData.cooperationAmount);
    console.log('  dataSource:', mappedData.dataSource);
    console.log('  customerPublicSea:', mappedData.customerPublicSea);
    
    console.log('\n✅ 执行顺序测试完成');
    return true;
    
  } catch (error) {
    console.error('❌ 执行顺序测试失败:', error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始字段映射执行顺序测试...');
  
  const results = [];
  results.push(await testExecutionOrder());
  results.push(await testCompleteFieldMapping());
  
  const passedCount = results.filter(r => r).length;
  const totalCount = results.length;
  
  console.log(`\n📊 测试结果: ${passedCount}/${totalCount} 通过`);
  
  if (passedCount === totalCount) {
    console.log('✅ 所有测试通过！字段映射执行顺序正常。');
  } else {
    console.log('❌ 部分测试失败，请检查执行顺序。');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testCompleteFieldMapping,
  testExecutionOrder,
  runAllTests
};
