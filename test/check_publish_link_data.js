/**
 * 检查数据库中publishLink字段的数据情况
 * 
 * 分析现有数据中publishLink字段的存储状态
 */

const { CooperationManagement } = require('../src/models');
const { Op } = require('sequelize');

/**
 * 检查publishLink字段的数据分布
 */
async function checkPublishLinkDistribution() {
  console.log('\n📊 检查publishLink字段数据分布...');
  
  try {
    // 统计总记录数
    const totalCount = await CooperationManagement.count();
    console.log(`📋 总记录数: ${totalCount}`);
    
    // 统计有publishLink的记录数
    const withPublishLinkCount = await CooperationManagement.count({
      where: {
        publishLink: {
          [Op.not]: null,
          [Op.ne]: ''
        }
      }
    });
    console.log(`📋 有publishLink的记录数: ${withPublishLinkCount}`);
    
    // 统计没有publishLink的记录数
    const withoutPublishLinkCount = await CooperationManagement.count({
      where: {
        [Op.or]: [
          { publishLink: null },
          { publishLink: '' }
        ]
      }
    });
    console.log(`📋 没有publishLink的记录数: ${withoutPublishLinkCount}`);
    
    // 统计有外部协议ID的记录数（表示CRM同步的协议）
    const crmSyncedCount = await CooperationManagement.count({
      where: {
        externalAgreementId: {
          [Op.not]: null,
          [Op.ne]: ''
        }
      }
    });
    console.log(`📋 有外部协议ID的记录数: ${crmSyncedCount}`);

    // 统计有外部协议ID且有publishLink的记录数
    const crmSyncedWithPublishLinkCount = await CooperationManagement.count({
      where: {
        externalAgreementId: {
          [Op.not]: null,
          [Op.ne]: ''
        },
        publishLink: {
          [Op.not]: null,
          [Op.ne]: ''
        }
      }
    });
    console.log(`📋 有外部协议ID且有publishLink的记录数: ${crmSyncedWithPublishLinkCount}`);
    
    return {
      totalCount,
      withPublishLinkCount,
      withoutPublishLinkCount,
      crmSyncedCount,
      crmSyncedWithPublishLinkCount
    };
    
  } catch (error) {
    console.error('❌ 检查publishLink数据分布失败:', error.message);
    throw error;
  }
}

/**
 * 查看具体的publishLink数据样例
 */
async function showPublishLinkSamples() {
  console.log('\n📋 查看publishLink数据样例...');
  
  try {
    // 查看有publishLink的记录样例
    const withPublishLinkSamples = await CooperationManagement.findAll({
      where: {
        publishLink: {
          [Op.not]: null,
          [Op.ne]: ''
        }
      },
      attributes: ['id', 'customerName', 'title', 'publishLink', 'externalAgreementId'],
      limit: 5,
      order: [['createdAt', 'DESC']]
    });
    
    console.log('✅ 有publishLink的记录样例:');
    withPublishLinkSamples.forEach((record, index) => {
      console.log(`  ${index + 1}. ID: ${record.id}`);
      console.log(`     客户: ${record.customerName}`);
      console.log(`     标题: ${record.title}`);
      console.log(`     发布链接: ${record.publishLink}`);
      console.log(`     外部协议ID: ${record.externalAgreementId}`);
      console.log(`     外部协议ID: ${record.externalAgreementId}`);
      console.log('');
    });
    
    // 查看有外部协议ID但没有publishLink的记录样例
    const crmWithoutPublishLinkSamples = await CooperationManagement.findAll({
      where: {
        externalAgreementId: {
          [Op.not]: null,
          [Op.ne]: ''
        },
        [Op.or]: [
          { publishLink: null },
          { publishLink: '' }
        ]
      },
      attributes: ['id', 'customerName', 'title', 'publishLink', 'externalAgreementId'],
      limit: 5,
      order: [['createdAt', 'DESC']]
    });
    
    console.log('⚠️ 有外部协议ID但没有publishLink的记录样例:');
    crmWithoutPublishLinkSamples.forEach((record, index) => {
      console.log(`  ${index + 1}. ID: ${record.id}`);
      console.log(`     客户: ${record.customerName}`);
      console.log(`     标题: ${record.title}`);
      console.log(`     发布链接: ${record.publishLink || '(空)'}`);
      console.log(`     外部协议ID: ${record.externalAgreementId}`);
      console.log(`     外部协议ID: ${record.externalAgreementId}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ 查看publishLink数据样例失败:', error.message);
    throw error;
  }
}

/**
 * 检查特定外部协议ID的数据
 */
async function checkSpecificAgreementData(externalAgreementId) {
  console.log(`\n🔍 检查特定协议数据: ${externalAgreementId}...`);
  
  try {
    const record = await CooperationManagement.findOne({
      where: {
        externalAgreementId: externalAgreementId
      },
      attributes: [
        'id', 'customerName', 'title', 'publishLink',
        'externalAgreementId', 'externalCustomerId', 'crmLinkStatus',
        'cooperationAmount', 'viewCount', 'likeCount', 'createdAt', 'updatedAt'
      ]
    });
    
    if (record) {
      console.log('✅ 找到记录:');
      console.log(`  ID: ${record.id}`);
      console.log(`  客户名称: ${record.customerName}`);
      console.log(`  标题: ${record.title}`);
      console.log(`  发布链接: ${record.publishLink || '(空)'}`);

      console.log(`  外部协议ID: ${record.externalAgreementId}`);
      console.log(`  外部客户ID: ${record.externalCustomerId}`);
      console.log(`  CRM关联状态: ${record.crmLinkStatus}`);
      console.log(`  合作金额: ${record.cooperationAmount}`);
      console.log(`  观看量: ${record.viewCount}`);
      console.log(`  点赞数: ${record.likeCount}`);
      console.log(`  创建时间: ${record.createdAt}`);
      console.log(`  更新时间: ${record.updatedAt}`);
      
      return record;
    } else {
      console.log('❌ 未找到该协议记录');
      return null;
    }
    
  } catch (error) {
    console.error('❌ 检查特定协议数据失败:', error.message);
    throw error;
  }
}

/**
 * 分析publishLink字段的问题
 */
async function analyzePublishLinkIssues() {
  console.log('\n🔍 分析publishLink字段问题...');
  
  try {
    const stats = await checkPublishLinkDistribution();
    
    console.log('\n📊 分析结果:');
    
    // 计算比例
    const publishLinkRate = stats.totalCount > 0 ? 
      (stats.withPublishLinkCount / stats.totalCount * 100).toFixed(2) : 0;
    console.log(`📈 publishLink填充率: ${publishLinkRate}%`);
    
    const crmPublishLinkRate = stats.crmSyncedCount > 0 ? 
      (stats.crmSyncedWithPublishLinkCount / stats.crmSyncedCount * 100).toFixed(2) : 0;
    console.log(`📈 CRM同步记录的publishLink填充率: ${crmPublishLinkRate}%`);
    
    // 问题分析
    if (stats.crmSyncedCount > 0 && stats.crmSyncedWithPublishLinkCount === 0) {
      console.log('⚠️ 问题: 所有CRM同步的记录都没有publishLink数据');
      console.log('   可能原因: CRM数据中contract_url_0字段为空，或映射过程有问题');
    } else if (crmPublishLinkRate < 50) {
      console.log('⚠️ 问题: CRM同步记录的publishLink填充率较低');
      console.log('   可能原因: 部分CRM数据中contract_url_0字段为空');
    } else {
      console.log('✅ CRM同步记录的publishLink填充率正常');
    }
    
    return stats;
    
  } catch (error) {
    console.error('❌ 分析publishLink字段问题失败:', error.message);
    throw error;
  }
}

/**
 * 运行完整检查
 */
async function runCompleteCheck() {
  console.log('🚀 开始检查publishLink字段数据...');
  
  try {
    await analyzePublishLinkIssues();
    await showPublishLinkSamples();
    
    console.log('\n✅ publishLink字段数据检查完成！');
    
  } catch (error) {
    console.error('❌ publishLink字段数据检查失败:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runCompleteCheck().catch(console.error);
}

module.exports = {
  checkPublishLinkDistribution,
  showPublishLinkSamples,
  checkSpecificAgreementData,
  analyzePublishLinkIssues,
  runCompleteCheck
};
