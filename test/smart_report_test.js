/**
 * 智能提报功能测试脚本
 * 用于验证智能提报的各个阶段功能
 */

const { parseInfluencerInput, validatePlatformUserId } = require('../frontend/src/utils/platformUtils');

// 测试数据
const testCases = [
  // 小红书链接测试
  {
    input: 'https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/5e7e8e8e8e8e8e8e?source=Advertiser_Kol',
    expected: { platform: 'xiaohongshu', platformUserId: '5e7e8e8e8e8e8e8e' }
  },
  {
    input: 'https://pgy.xiaohongshu.com/solar/cooperator/blogger/abc123def456',
    expected: { platform: 'xiaohongshu', platformUserId: 'abc123def456' }
  },
  
  // 巨量星图链接测试
  {
    input: 'https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/123456789',
    expected: { platform: 'juxingtu', platformUserId: '123456789' }
  },
  {
    input: 'https://www.xingtu.cn/ad/creator/author-homepage/987654321',
    expected: { platform: 'juxingtu', platformUserId: '987654321' }
  },
  
  // 直接ID测试
  {
    input: '5e7e8e8e8e8e8e8e8e8e8e8e',
    expected: { platform: 'xiaohongshu', platformUserId: '5e7e8e8e8e8e8e8e8e8e8e8e' }
  },
  {
    input: '123456789012',
    expected: { platform: 'juxingtu', platformUserId: '123456789012' }
  },
  
  // 错误输入测试
  {
    input: 'invalid-input',
    expected: { error: '无法识别的链接格式或ID，请检查输入内容' }
  },
  {
    input: '',
    expected: { error: '请输入有效的达人链接或ID' }
  }
];

/**
 * 运行链接解析测试
 */
function runParseTests() {
  console.log('🧪 开始链接解析测试...\n');
  
  let passCount = 0;
  let failCount = 0;
  
  testCases.forEach((testCase, index) => {
    console.log(`测试 ${index + 1}: ${testCase.input}`);
    
    try {
      const result = parseInfluencerInput(testCase.input);
      
      // 检查结果是否符合预期
      let isPass = true;
      
      if (testCase.expected.error) {
        isPass = result.error === testCase.expected.error;
      } else {
        isPass = result.platform === testCase.expected.platform && 
                 result.platformUserId === testCase.expected.platformUserId;
      }
      
      if (isPass) {
        console.log('✅ 通过');
        passCount++;
      } else {
        console.log('❌ 失败');
        console.log('预期:', testCase.expected);
        console.log('实际:', result);
        failCount++;
      }
    } catch (error) {
      console.log('❌ 异常:', error.message);
      failCount++;
    }
    
    console.log('');
  });
  
  console.log(`📊 测试结果: ${passCount} 通过, ${failCount} 失败\n`);
  return { passCount, failCount };
}

/**
 * 运行平台用户ID验证测试
 */
function runValidationTests() {
  console.log('🔍 开始平台用户ID验证测试...\n');
  
  const validationCases = [
    { platform: 'xiaohongshu', userId: '5e7e8e8e8e8e8e8e8e8e8e8e', expected: true },
    { platform: 'xiaohongshu', userId: 'abc123def456ghi789', expected: true },
    { platform: 'xiaohongshu', userId: '123', expected: false }, // 太短
    { platform: 'juxingtu', userId: '123456789012', expected: true },
    { platform: 'juxingtu', userId: '987654321', expected: true },
    { platform: 'juxingtu', userId: 'abc123', expected: false }, // 包含字母
    { platform: 'unknown', userId: '123456', expected: false }, // 未知平台
  ];
  
  let passCount = 0;
  let failCount = 0;
  
  validationCases.forEach((testCase, index) => {
    console.log(`验证 ${index + 1}: ${testCase.platform} - ${testCase.userId}`);
    
    try {
      const result = validatePlatformUserId(testCase.platform, testCase.userId);
      
      if (result === testCase.expected) {
        console.log('✅ 通过');
        passCount++;
      } else {
        console.log('❌ 失败');
        console.log('预期:', testCase.expected);
        console.log('实际:', result);
        failCount++;
      }
    } catch (error) {
      console.log('❌ 异常:', error.message);
      failCount++;
    }
    
    console.log('');
  });
  
  console.log(`📊 验证结果: ${passCount} 通过, ${failCount} 失败\n`);
  return { passCount, failCount };
}

/**
 * 主测试函数
 */
function runAllTests() {
  console.log('🚀 智能提报功能测试开始\n');
  console.log('=' .repeat(50));
  
  const parseResults = runParseTests();
  const validationResults = runValidationTests();
  
  const totalPass = parseResults.passCount + validationResults.passCount;
  const totalFail = parseResults.failCount + validationResults.failCount;
  
  console.log('=' .repeat(50));
  console.log(`🎯 总体测试结果: ${totalPass} 通过, ${totalFail} 失败`);
  
  if (totalFail === 0) {
    console.log('🎉 所有测试通过！智能提报功能准备就绪。');
  } else {
    console.log('⚠️  存在失败的测试，请检查相关功能。');
  }
}

// 如果直接运行此脚本，执行测试
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runParseTests,
  runValidationTests,
  runAllTests
};
