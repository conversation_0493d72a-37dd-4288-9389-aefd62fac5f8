/**
 * 简化的API测试脚本
 * 
 * 测试任务管理面板API的基本功能
 */

const axios = require('axios');

class SimpleAPITest {
  constructor() {
    this.baseURL = 'http://localhost:3001/api';
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始API基础功能测试...\n');

    try {
      await this.testAPIEndpoints();
      await this.testRouteStructure();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
    }
  }

  /**
   * 测试API端点是否存在
   */
  async testAPIEndpoints() {
    console.log('📝 测试1: API端点可达性');
    
    const endpoints = [
      '/cooperation/tasks/stats',
      '/cooperation/tasks',
      '/cooperation/cookie/status'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${this.baseURL}${endpoint}`);
        
        // 我们期望得到401认证错误，这说明端点存在但需要认证
        this.addTestResult(`endpoint_${endpoint.replace(/\//g, '_')}`, false, 
          `端点 ${endpoint} 意外返回成功（应该要求认证）`);
      } catch (error) {
        if (error.response && error.response.status === 401) {
          this.addTestResult(`endpoint_${endpoint.replace(/\//g, '_')}`, true, 
            `端点 ${endpoint} 正确要求认证`);
        } else if (error.response && error.response.status === 404) {
          this.addTestResult(`endpoint_${endpoint.replace(/\//g, '_')}`, false, 
            `端点 ${endpoint} 不存在 (404)`);
        } else {
          this.addTestResult(`endpoint_${endpoint.replace(/\//g, '_')}`, false, 
            `端点 ${endpoint} 错误: ${error.message}`);
        }
      }
    }
  }

  /**
   * 测试路由结构
   */
  async testRouteStructure() {
    console.log('📝 测试2: 路由结构验证');
    
    try {
      // 测试根路径
      const response = await axios.get(`${this.baseURL}/cooperation`);
      
      if (response.status === 401 || (response.data && !response.data.success)) {
        this.addTestResult('route_cooperation_root', true, '合作对接根路由正常');
      } else {
        this.addTestResult('route_cooperation_root', false, '合作对接根路由异常');
      }
    } catch (error) {
      if (error.response && error.response.status === 401) {
        this.addTestResult('route_cooperation_root', true, '合作对接根路由正常（要求认证）');
      } else {
        this.addTestResult('route_cooperation_root', false, `路由错误: ${error.message}`);
      }
    }

    // 测试任务路由是否不会与ID路由冲突
    try {
      const response = await axios.get(`${this.baseURL}/cooperation/tasks`);
      
      // 检查是否被错误地路由到 /:id 处理器
      if (error.response && error.response.status === 404 && 
          error.response.data && error.response.data.message && 
          error.response.data.message.includes('合作记录不存在')) {
        this.addTestResult('route_conflict_check', false, '路由冲突：/tasks被错误路由到/:id处理器');
      } else {
        this.addTestResult('route_conflict_check', true, '路由配置正确，无冲突');
      }
    } catch (error) {
      if (error.response && error.response.status === 401) {
        this.addTestResult('route_conflict_check', true, '路由配置正确（正确要求认证）');
      } else if (error.response && error.response.status === 404 && 
                 error.response.data && error.response.data.message && 
                 error.response.data.message.includes('合作记录不存在')) {
        this.addTestResult('route_conflict_check', false, '路由冲突：/tasks被错误路由到/:id处理器');
      } else {
        this.addTestResult('route_conflict_check', true, '路由配置正确');
      }
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      testName,
      success,
      message
    });
    
    const status = success ? '✅' : '❌';
    console.log(`   ${status} ${testName}: ${message}`);
  }

  /**
   * 打印测试结果汇总
   */
  printTestResults() {
    console.log('\n📊 API基础功能测试结果汇总:');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(result => result.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(result => !result.success)
        .forEach(result => {
          console.log(`   - ${result.testName}: ${result.message}`);
        });
    }
    
    console.log('\n🎉 API基础功能测试完成!');
    
    if (passedTests === totalTests) {
      console.log('\n✅ 所有测试通过！任务管理面板API已正确配置：');
      console.log('   - ✅ 所有API端点都存在且可达');
      console.log('   - ✅ 路由配置正确，无冲突');
      console.log('   - ✅ 认证机制正常工作');
      console.log('   - ✅ 错误处理正确');
      console.log('\n🚀 任务管理面板后端API已准备就绪！');
    } else {
      console.log('\n⚠️  部分测试失败，请检查上述错误信息');
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new SimpleAPITest();
  test.runAllTests().catch(console.error);
}

module.exports = SimpleAPITest;
