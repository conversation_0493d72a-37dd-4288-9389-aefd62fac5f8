/**
 * 客户公海字段测试脚本
 * 
 * 测试customerPublicSea字段在各个环节的正确实现：
 * 1. 数据库模型默认值
 * 2. 客户创建时的默认值处理
 * 3. CRM数据映射时的默认值设置
 * 4. 前端表单的默认值显示
 */

const { CooperationManagement } = require('../src/models');
const CooperationService = require('../src/services/CooperationService');
const CrmDataMappingService = require('../src/services/CrmDataMappingService');

// 默认公海ID
const DEFAULT_PUBLIC_SEA_ID = 'fd18e0d6b1164e7080f0fa91dc43b0d8';

/**
 * 测试数据库模型默认值
 */
async function testDatabaseModelDefault() {
  console.log('\n🧪 测试数据库模型默认值...');
  
  try {
    // 创建一个不包含customerPublicSea字段的记录
    const testData = {
      customerName: '测试客户_模型默认值',
      title: '测试-模型默认值-2024.01.01',
      cooperationMonth: '2024-01',
      platform: 'xiaohongshu',
      bloggerName: '测试博主',
      responsiblePerson: '测试负责人',
      createdBy: 1,
      updatedBy: 1
    };

    const cooperation = await CooperationManagement.create(testData);
    
    console.log(`✅ 记录创建成功，ID: ${cooperation.id}`);
    console.log(`✅ customerPublicSea值: ${cooperation.customerPublicSea}`);
    
    if (cooperation.customerPublicSea === DEFAULT_PUBLIC_SEA_ID) {
      console.log('✅ 数据库模型默认值测试通过');
    } else {
      console.log('❌ 数据库模型默认值测试失败');
    }

    // 清理测试数据
    await cooperation.destroy();
    
  } catch (error) {
    console.error('❌ 数据库模型默认值测试失败:', error.message);
  }
}

/**
 * 测试服务层默认值处理
 */
async function testServiceLayerDefault() {
  console.log('\n🧪 测试服务层默认值处理...');
  
  try {
    const cooperationService = CooperationService;

    // 创建一个不包含customerPublicSea字段的记录
    const testData = {
      customerName: '测试客户_服务层默认值',
      title: '测试-服务层默认值-2024.01.01',
      cooperationMonth: '2024-01',
      platform: 'xiaohongshu',
      bloggerName: '测试博主',
      responsiblePerson: '测试负责人'
    };

    const cooperation = await cooperationService.createCooperation(testData, 1);
    
    console.log(`✅ 记录创建成功，ID: ${cooperation.id}`);
    console.log(`✅ customerPublicSea值: ${cooperation.customerPublicSea}`);
    
    if (cooperation.customerPublicSea === DEFAULT_PUBLIC_SEA_ID) {
      console.log('✅ 服务层默认值测试通过');
    } else {
      console.log('❌ 服务层默认值测试失败');
    }

    // 清理测试数据
    await cooperation.destroy();
    
  } catch (error) {
    console.error('❌ 服务层默认值测试失败:', error.message);
  }
}

/**
 * 测试CRM数据映射默认值
 */
async function testCrmMappingDefault() {
  console.log('\n🧪 测试CRM数据映射默认值...');
  
  try {
    const mappingService = new CrmDataMappingService();
    
    // 模拟CRM客户数据（不包含seas_id字段）
    const crmCustomerData = {
      id: 'test_customer_123',
      custom_name: '测试客户_CRM映射',
      customer_textarea_11: 'https://test.example.com',
      created: Date.now(),
      modified: Date.now()
    };

    const mappedData = mappingService.mapCustomerData(crmCustomerData);
    
    console.log(`✅ 数据映射完成`);
    console.log(`✅ customerPublicSea值: ${mappedData.customerPublicSea}`);
    
    if (mappedData.customerPublicSea === DEFAULT_PUBLIC_SEA_ID) {
      console.log('✅ CRM数据映射默认值测试通过');
    } else {
      console.log('❌ CRM数据映射默认值测试失败');
    }
    
  } catch (error) {
    console.error('❌ CRM数据映射默认值测试失败:', error.message);
  }
}

/**
 * 测试CRM数据映射字段映射
 */
async function testCrmFieldMapping() {
  console.log('\n🧪 测试CRM字段映射...');
  
  try {
    const mappingService = new CrmDataMappingService();
    
    // 模拟CRM客户数据（包含seas_id字段）
    const crmCustomerData = {
      id: 'test_customer_456',
      custom_name: '测试客户_CRM字段映射',
      seas_id: 'custom_public_sea_id_123',
      customer_textarea_11: 'https://test.example.com',
      created: Date.now(),
      modified: Date.now()
    };

    const mappedData = mappingService.mapCustomerData(crmCustomerData);
    
    console.log(`✅ 数据映射完成`);
    console.log(`✅ customerPublicSea值: ${mappedData.customerPublicSea}`);
    
    if (mappedData.customerPublicSea === 'custom_public_sea_id_123') {
      console.log('✅ CRM字段映射测试通过');
    } else {
      console.log('❌ CRM字段映射测试失败');
    }
    
  } catch (error) {
    console.error('❌ CRM字段映射测试失败:', error.message);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始客户公海字段测试...');
  
  await testDatabaseModelDefault();
  await testServiceLayerDefault();
  await testCrmMappingDefault();
  await testCrmFieldMapping();
  
  console.log('\n✅ 所有测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testDatabaseModelDefault,
  testServiceLayerDefault,
  testCrmMappingDefault,
  testCrmFieldMapping,
  runAllTests
};
