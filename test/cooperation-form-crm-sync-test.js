/**
 * 合作对接表单CRM同步功能测试脚本
 * 
 * 功能说明：
 * - 测试合作对接表单编辑时的CRM智能同步功能
 * - 验证字段变化检测和CRM数据回写功能
 * - 测试错误处理和用户提示
 * 
 * 使用方法：
 * node test/cooperation-form-crm-sync-test.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const axios = require('axios');

class CooperationFormCrmSyncTest {
  constructor() {
    this.baseUrl = 'http://localhost:3001/api';
    this.token = null;
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    try {
      console.log('🧪 开始合作对接表单CRM同步功能测试...\n');

      // 1. 登录获取token
      await this.login();

      // 2. 测试获取合作对接记录
      const cooperationRecord = await this.getCooperationRecord();

      if (cooperationRecord) {
        // 3. 测试智能同步功能
        await this.testSmartSync(cooperationRecord.id);

        // 4. 测试字段映射
        await this.testFieldMapping(cooperationRecord);

        // 5. 测试CRM数据查询
        await this.testCrmDataQuery(cooperationRecord.customerName);
      }

      // 输出测试结果
      this.printTestResults();

    } catch (error) {
      console.error('❌ 测试运行失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`测试运行失败: ${error.message}`);
    }
  }

  /**
   * 登录获取token
   */
  async login() {
    try {
      console.log('🔐 正在登录...');
      
      const response = await axios.post(`${this.baseUrl}/auth/login`, {
        username: 'admin',
        password: 'admin123'
      });

      if (response.data.success) {
        this.token = response.data.data.token;
        console.log('✅ 登录成功\n');
        this.testResults.passed++;
      } else {
        throw new Error('登录失败');
      }
    } catch (error) {
      console.error('❌ 登录失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`登录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取合作对接记录
   */
  async getCooperationRecord() {
    try {
      console.log('📋 获取合作对接记录...');
      
      const response = await this.makeRequest('GET', '/cooperation?page=1&limit=1');
      
      if (response.data.success && response.data.data.length > 0) {
        const record = response.data.data[0];
        console.log('✅ 获取合作对接记录成功');
        console.log(`   记录ID: ${record.id}`);
        console.log(`   客户名称: ${record.customerName}`);
        console.log(`   CRM状态: ${record.crmLinkStatus}\n`);
        this.testResults.passed++;
        return record;
      } else {
        throw new Error('没有找到合作对接记录');
      }
    } catch (error) {
      console.error('❌ 获取合作对接记录失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`获取合作对接记录失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 测试智能同步功能
   */
  async testSmartSync(cooperationId) {
    try {
      console.log('🔄 测试智能同步功能...');
      
      const response = await this.makeRequest('POST', '/crm-integration/smart-sync', {
        cooperationId: cooperationId,
        options: {
          changedFields: ['customerName', 'cooperationAmount'],
          forceCustomerSync: false,
          forceAgreementSync: true
        }
      });
      
      if (response.data.success) {
        console.log('✅ 智能同步测试成功');
        console.log(`   客户同步: ${response.data.data.customerSynced}`);
        console.log(`   协议同步: ${response.data.data.agreementSynced}`);
        console.log(`   客户ID: ${response.data.data.customerId}`);
        console.log(`   协议ID: ${response.data.data.agreementId}`);
        
        if (response.data.data.errors && response.data.data.errors.length > 0) {
          console.log('   错误列表:', response.data.data.errors);
        }
        
        this.testResults.passed++;
      } else {
        throw new Error('智能同步测试失败');
      }
      
      console.log('');
    } catch (error) {
      console.error('❌ 智能同步测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`智能同步测试失败: ${error.message}`);
    }
  }

  /**
   * 测试字段映射
   */
  async testFieldMapping(cooperationData) {
    try {
      console.log('🔄 测试字段映射...');
      
      // 测试客户映射
      const customerMappingResponse = await this.makeRequest('POST', '/crm-integration/test-mapping', {
        cooperationData: cooperationData,
        mappingType: 'customer'
      });

      if (customerMappingResponse.data.success) {
        console.log('✅ 客户字段映射测试成功');
        this.testResults.passed++;
      }

      // 测试协议映射
      const agreementMappingResponse = await this.makeRequest('POST', '/crm-integration/test-mapping', {
        cooperationData: cooperationData,
        mappingType: 'agreement'
      });

      if (agreementMappingResponse.data.success) {
        console.log('✅ 协议字段映射测试成功');
        this.testResults.passed++;
      }

      console.log('');
    } catch (error) {
      console.error('❌ 字段映射测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`字段映射测试失败: ${error.message}`);
    }
  }

  /**
   * 测试CRM数据查询
   */
  async testCrmDataQuery(customerName) {
    try {
      console.log('🔍 测试CRM数据查询...');
      
      // 测试客户查询
      const customerResponse = await this.makeRequest('GET', `/crm-integration/customers?keyword=${encodeURIComponent(customerName)}&page=1&limit=5`);
      
      if (customerResponse.data.success) {
        console.log('✅ CRM客户查询测试成功');
        console.log(`   查询到客户数量: ${customerResponse.data.data.length}`);
        this.testResults.passed++;
      }

      // 测试协议查询
      const agreementResponse = await this.makeRequest('GET', `/crm-integration/customers/${encodeURIComponent(customerName)}/agreements`);
      
      if (agreementResponse.data.success) {
        console.log('✅ CRM协议查询测试成功');
        console.log(`   查询到协议数量: ${agreementResponse.data.data.length}`);
        this.testResults.passed++;
      }

      console.log('');
    } catch (error) {
      console.error('❌ CRM数据查询测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`CRM数据查询测试失败: ${error.message}`);
    }
  }

  /**
   * 发送HTTP请求
   */
  async makeRequest(method, path, data = null) {
    const config = {
      method,
      url: `${this.baseUrl}${path}`,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      config.data = data;
    }

    return await axios(config);
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过: ${this.testResults.passed}`);
    console.log(`❌ 失败: ${this.testResults.failed}`);
    
    if (this.testResults.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    console.log('\n🎉 合作对接表单CRM同步功能测试完成!');
  }
}

// 运行测试
if (require.main === module) {
  const test = new CooperationFormCrmSyncTest();
  test.runAllTests().catch(console.error);
}

module.exports = CooperationFormCrmSyncTest;
