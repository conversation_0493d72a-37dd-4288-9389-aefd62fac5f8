/**
 * 种草平台字段格式转换测试
 * 验证seedingPlatform字段从数组/对象格式正确转换为字符串格式
 */

const CrmDataMappingService = require('../src/services/CrmDataMappingService');

console.log('🧪 开始种草平台字段格式转换测试...\n');

// 创建映射服务实例
const mappingService = new CrmDataMappingService();

// 测试数据：不同格式的seedingPlatform
const testCases = [
  {
    name: '数组格式',
    data: {
      customerName: '测试客户1',
      seedingPlatform: ['小红书', '抖音', '微博'],
      customerHomepage: 'https://example.com'
    }
  },
  {
    name: '字符串格式',
    data: {
      customerName: '测试客户2',
      seedingPlatform: '小红书,抖音',
      customerHomepage: 'https://example.com'
    }
  },
  {
    name: '单个字符串',
    data: {
      customerName: '测试客户3',
      seedingPlatform: '小红书',
      customerHomepage: 'https://example.com'
    }
  },
  {
    name: '对象格式',
    data: {
      customerName: '测试客户4',
      seedingPlatform: { platforms: ['小红书', '抖音'] },
      customerHomepage: 'https://example.com'
    }
  },
  {
    name: '空数组',
    data: {
      customerName: '测试客户5',
      seedingPlatform: [],
      customerHomepage: 'https://example.com'
    }
  },
  {
    name: '空值',
    data: {
      customerName: '测试客户6',
      seedingPlatform: null,
      customerHomepage: 'https://example.com'
    }
  },
  {
    name: '未定义',
    data: {
      customerName: '测试客户7',
      customerHomepage: 'https://example.com'
      // seedingPlatform 未定义
    }
  }
];

console.log('📋 测试用例:');
testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.name}: ${JSON.stringify(testCase.data.seedingPlatform)}`);
});

console.log('\n🔄 执行mapToCrm转换测试...\n');

testCases.forEach((testCase, index) => {
  console.log(`--- 测试用例 ${index + 1}: ${testCase.name} ---`);
  
  try {
    // 执行转换
    const crmData = mappingService.mapToCrm(testCase.data, 'customer');
    
    console.log(`输入 seedingPlatform: ${JSON.stringify(testCase.data.seedingPlatform)}`);
    console.log(`输出 customer_check_box_2: "${crmData.customer_check_box_2}"`);
    console.log(`数据类型: ${typeof crmData.customer_check_box_2}`);
    
    // 验证输出是否为字符串
    const isString = typeof crmData.customer_check_box_2 === 'string';
    console.log(`字符串验证: ${isString ? '✅ 通过' : '❌ 失败'}`);
    
    if (!isString && crmData.customer_check_box_2 !== undefined) {
      console.log('⚠️ 警告: 输出不是字符串格式，可能导致CRM同步失败');
    }
    
  } catch (error) {
    console.error(`❌ 转换失败: ${error.message}`);
  }
  
  console.log('');
});

// 测试formatArrayForCrm方法
console.log('🔬 测试formatArrayForCrm方法:\n');

const arrayTestCases = [
  ['小红书', '抖音', '微博'],
  '小红书,抖音',
  '小红书',
  { platforms: ['小红书', '抖音'] },
  [],
  null,
  undefined,
  '',
  123,
  true
];

arrayTestCases.forEach((testValue, index) => {
  try {
    const result = mappingService.formatArrayForCrm(testValue);
    console.log(`${index + 1}. 输入: ${JSON.stringify(testValue)} -> 输出: "${result}"`);
  } catch (error) {
    console.log(`${index + 1}. 输入: ${JSON.stringify(testValue)} -> 错误: ${error.message}`);
  }
});

// 模拟实际的协议同步场景
console.log('\n🚀 模拟实际协议同步场景:');

const realWorldData = {
  customerName: '倩倩倩影',
  title: '小红书-倩倩倩影-致友人',
  seedingPlatform: ['小红书'], // 可能来自前端的数组格式
  customerHomepage: 'https://www.xiaohongshu.com/user/profile/123',
  influencerPlatformId: 'xiaohongshu_123',
  bloggerWechatAndNotes: '微信: qqqying123',
  externalCustomerId: 'customer_456'
};

console.log('\n原始数据:');
console.log(JSON.stringify(realWorldData, null, 2));

try {
  const crmCustomerData = mappingService.mapToCrm(realWorldData, 'customer');
  
  console.log('\n转换后的CRM客户数据:');
  console.log(JSON.stringify(crmCustomerData, null, 2));
  
  // 重点检查seedingPlatform字段
  console.log('\n🎯 关键字段验证:');
  console.log(`customer_check_box_2 (种草平台): "${crmCustomerData.customer_check_box_2}"`);
  console.log(`数据类型: ${typeof crmCustomerData.customer_check_box_2}`);
  console.log(`是否为字符串: ${typeof crmCustomerData.customer_check_box_2 === 'string' ? '✅ 是' : '❌ 否'}`);
  
  if (typeof crmCustomerData.customer_check_box_2 === 'string') {
    console.log('✅ 种草平台字段格式正确，应该能正常同步到CRM');
  } else {
    console.log('❌ 种草平台字段格式错误，可能导致CRM同步失败');
  }
  
} catch (error) {
  console.error('❌ 实际场景测试失败:', error.message);
}

console.log('\n🏁 测试完成！');

// 如果是直接运行此脚本
if (require.main === module) {
  console.log('\n💡 测试结果说明：');
  console.log('1. seedingPlatform字段支持多种输入格式（数组、字符串、对象等）');
  console.log('2. 所有格式都会转换为字符串格式，符合CRM要求');
  console.log('3. 数组格式会转换为逗号分隔的字符串');
  console.log('4. 空值和无效值会转换为空字符串');
  
  console.log('\n🔧 如果仍然出现同步错误，请检查：');
  console.log('- CrmDataMappingService.isArrayField() 方法是否包含了所有需要转换的字段');
  console.log('- CrmDataMappingService.formatArrayForCrm() 方法的转换逻辑');
  console.log('- 前端传递的数据格式是否符合预期');
  console.log('- CRM系统对customer_check_box_2字段的具体要求');
}
