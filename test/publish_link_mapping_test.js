/**
 * 发布链接字段映射测试脚本
 * 
 * 测试contract_url_0字段到publishLink字段的映射是否正确工作
 */

const CrmDataMappingService = require('../src/services/CrmDataMappingService');

/**
 * 测试CRM协议数据中contract_url_0字段的映射
 */
async function testPublishLinkMapping() {
  console.log('\n🧪 测试发布链接字段映射...');
  
  try {
    const mappingService = new CrmDataMappingService();
    
    // 模拟CRM协议数据（包含contract_url_0字段）
    const crmAgreementData = {
      id: 'test_agreement_123',
      contract_title: '测试协议-发布链接映射',
      custom_name: '测试客户',
      contract_url_0: 'https://www.xiaohongshu.com/explore/12345678',
      contract_amount: '1000',
      contract_date_4: Date.now(),
      created: Date.now(),
      modified: Date.now()
    };

    console.log('📋 CRM原始数据:');
    console.log('  - contract_url_0:', crmAgreementData.contract_url_0);

    const mappedData = mappingService.mapAgreementData(crmAgreementData, '测试客户');
    
    console.log('📋 映射后数据:');
    console.log('  - publishLink:', mappedData.publishLink);
    console.log('  - title:', mappedData.title);
    
    if (mappedData.publishLink === crmAgreementData.contract_url_0) {
      console.log('✅ 发布链接字段映射测试通过');
      return true;
    } else {
      console.log('❌ 发布链接字段映射测试失败');
      console.log('  期望值:', crmAgreementData.contract_url_0);
      console.log('  实际值:', mappedData.publishLink);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 发布链接字段映射测试失败:', error.message);
    return false;
  }
}

/**
 * 测试空值情况
 */
async function testEmptyPublishLinkMapping() {
  console.log('\n🧪 测试空值发布链接映射...');
  
  try {
    const mappingService = new CrmDataMappingService();
    
    // 模拟CRM协议数据（不包含contract_url_0字段）
    const crmAgreementData = {
      id: 'test_agreement_456',
      contract_title: '测试协议-空发布链接',
      custom_name: '测试客户',
      contract_amount: '1000',
      created: Date.now(),
      modified: Date.now()
    };

    console.log('📋 CRM原始数据:');
    console.log('  - contract_url_0:', crmAgreementData.contract_url_0);

    const mappedData = mappingService.mapAgreementData(crmAgreementData, '测试客户');
    
    console.log('📋 映射后数据:');
    console.log('  - publishLink:', mappedData.publishLink);
    
    if (mappedData.publishLink === undefined) {
      console.log('✅ 空值发布链接映射测试通过');
      return true;
    } else {
      console.log('❌ 空值发布链接映射测试失败');
      console.log('  期望值: undefined');
      console.log('  实际值:', mappedData.publishLink);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 空值发布链接映射测试失败:', error.message);
    return false;
  }
}

/**
 * 测试字段映射配置
 */
async function testFieldMappingConfiguration() {
  console.log('\n🧪 测试字段映射配置...');
  
  try {
    const mappingService = new CrmDataMappingService();
    
    console.log('📋 协议字段映射配置:');
    const agreementMapping = mappingService.agreementFieldMapping;
    
    if (agreementMapping['contract_url_0'] === 'publishLink') {
      console.log('✅ contract_url_0 -> publishLink 映射配置正确');
      return true;
    } else {
      console.log('❌ contract_url_0 映射配置错误');
      console.log('  期望值: publishLink');
      console.log('  实际值:', agreementMapping['contract_url_0']);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 字段映射配置测试失败:', error.message);
    return false;
  }
}

/**
 * 测试完整的数据映射流程
 */
async function testCompleteDataMapping() {
  console.log('\n🧪 测试完整数据映射流程...');
  
  try {
    const mappingService = new CrmDataMappingService();
    
    // 模拟完整的CRM协议数据
    const crmAgreementData = {
      id: 'test_agreement_789',
      contract_title: '小红书-测试博主-2024.01.01',
      custom_name: '测试博主',
      contract_url_0: 'https://www.xiaohongshu.com/explore/987654321',
      contract_amount: '2000',
      contract_date_4: Date.now(),
      contract_end_date: Date.now(),
      contract_integer_1: 10000,
      contract_integer_2: 500,
      contract_integer_3: 200,
      contract_integer_4: 100,
      created: Date.now(),
      modified: Date.now()
    };

    console.log('📋 完整CRM数据映射测试:');
    console.log('  - 原始发布链接:', crmAgreementData.contract_url_0);

    const mappedData = mappingService.mapAgreementData(crmAgreementData, '测试博主');
    
    console.log('📋 映射结果:');
    console.log('  - publishLink:', mappedData.publishLink);
    console.log('  - title:', mappedData.title);
    console.log('  - cooperationAmount:', mappedData.cooperationAmount);
    console.log('  - viewCount:', mappedData.viewCount);
    console.log('  - likeCount:', mappedData.likeCount);
    
    const tests = [
      { field: 'publishLink', expected: crmAgreementData.contract_url_0 },
      { field: 'title', expected: crmAgreementData.contract_title },
      { field: 'viewCount', expected: crmAgreementData.contract_integer_1 },
      { field: 'likeCount', expected: crmAgreementData.contract_integer_2 }
    ];
    
    let allPassed = true;
    for (const test of tests) {
      if (mappedData[test.field] === test.expected) {
        console.log(`✅ ${test.field} 映射正确`);
      } else {
        console.log(`❌ ${test.field} 映射错误: 期望 ${test.expected}, 实际 ${mappedData[test.field]}`);
        allPassed = false;
      }
    }
    
    return allPassed;
    
  } catch (error) {
    console.error('❌ 完整数据映射测试失败:', error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始发布链接字段映射测试...');
  
  const results = [];
  results.push(await testFieldMappingConfiguration());
  results.push(await testPublishLinkMapping());
  results.push(await testEmptyPublishLinkMapping());
  results.push(await testCompleteDataMapping());
  
  const passedCount = results.filter(r => r).length;
  const totalCount = results.length;
  
  console.log(`\n📊 测试结果: ${passedCount}/${totalCount} 通过`);
  
  if (passedCount === totalCount) {
    console.log('✅ 所有测试通过！发布链接字段映射工作正常。');
  } else {
    console.log('❌ 部分测试失败，请检查映射配置。');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testPublishLinkMapping,
  testEmptyPublishLinkMapping,
  testFieldMappingConfiguration,
  testCompleteDataMapping,
  runAllTests
};
