/**
 * CRM重复提报检查功能测试
 * 
 * 测试新增的CRM客户查询和重复提报校验功能
 */

const axios = require('axios');

class CrmDuplicateCheckTest {
  constructor() {
    this.baseURL = 'http://localhost:3001/api';
    this.token = null;
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * 执行HTTP请求
   */
  async makeRequest(method, endpoint, data = null) {
    const config = {
      method,
      url: `${this.baseURL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    if (data) {
      config.data = data;
    }

    return await axios(config);
  }

  /**
   * 用户登录获取token
   */
  async login() {
    try {
      console.log('🔐 用户登录...');
      
      const response = await this.makeRequest('POST', '/auth/login', {
        username: 'admin',
        password: 'admin123'
      });

      if (response.data.success) {
        this.token = response.data.data.token;
        console.log('✅ 登录成功');
        this.testResults.passed++;
      } else {
        throw new Error('登录失败');
      }
    } catch (error) {
      console.error('❌ 登录失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`登录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 测试CRM客户查询功能
   */
  async testCrmCustomerQuery() {
    try {
      console.log('🔍 测试CRM客户查询功能...');
      
      // 测试查询一个常见的达人名称
      const testInfluencerName = '测试达人';
      
      const response = await this.makeRequest('GET', `/crm-integration/customers?keyword=${encodeURIComponent(testInfluencerName)}&page=1&limit=10`);
      
      if (response.data.success) {
        console.log('✅ CRM客户查询测试成功');
        console.log(`   查询到客户数量: ${response.data.data.length}`);
        
        // 打印客户记录的字段结构（用于调试）
        if (response.data.data.length > 0) {
          const firstRecord = response.data.data[0];
          console.log('   客户记录字段:', Object.keys(firstRecord));
          
          // 检查是否包含负责人相关字段
          const chargerFields = ['charger_id', 'chargerId', 'owner_id', 'ownerId', 'user_id', 'userId'];
          const foundChargerFields = chargerFields.filter(field => firstRecord.hasOwnProperty(field));
          console.log('   找到的负责人字段:', foundChargerFields);
        }
        
        this.testResults.passed++;
      } else {
        throw new Error('CRM客户查询失败');
      }
    } catch (error) {
      console.error('❌ CRM客户查询测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`CRM客户查询测试失败: ${error.message}`);
    }
  }

  /**
   * 测试重复提报检查API
   */
  async testDuplicateReportCheck() {
    try {
      console.log('🔄 测试重复提报检查API...');
      
      const testData = {
        influencerName: '测试达人',
        platform: 'xiaohongshu',
        operationManager: '测试运营'
      };
      
      const response = await this.makeRequest('POST', '/influencer-reports/check-duplicate', testData);
      
      if (response.data.success) {
        console.log('✅ 重复提报检查API测试成功');
        console.log('   检查结果:', JSON.stringify(response.data.data, null, 2));
        
        // 验证返回数据结构
        const result = response.data.data;
        if (result.hasOwnProperty('canSubmit')) {
          console.log(`   是否可以提报: ${result.canSubmit}`);
          console.log(`   检查消息: ${result.message}`);
          
          // 如果包含CRM检查结果
          if (result.crmCheck) {
            console.log('   CRM检查结果:', result.crmCheck);
          }
          
          // 如果包含平台检查结果
          if (result.platformCheck) {
            console.log('   平台检查结果:', result.platformCheck);
          }
        }
        
        this.testResults.passed++;
      } else {
        throw new Error('重复提报检查API失败');
      }
    } catch (error) {
      console.error('❌ 重复提报检查API测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`重复提报检查API测试失败: ${error.message}`);
    }
  }

  /**
   * 测试CRM连接状态
   */
  async testCrmConnection() {
    try {
      console.log('🔗 测试CRM连接状态...');
      
      const response = await this.makeRequest('GET', '/crm-integration/test-connection');
      
      if (response.data.success) {
        console.log('✅ CRM连接测试成功');
        console.log('   连接状态:', response.data.message);
        this.testResults.passed++;
      } else {
        console.log('⚠️ CRM连接测试失败，但这不影响功能测试');
        console.log('   错误信息:', response.data.message);
      }
    } catch (error) {
      console.warn('⚠️ CRM连接测试异常:', error.message);
      // CRM连接失败不算作测试失败，因为可能是环境问题
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始CRM重复提报检查功能测试\n');

    try {
      // 1. 登录
      await this.login();
      console.log('');

      // 2. 测试CRM连接
      await this.testCrmConnection();
      console.log('');

      // 3. 测试CRM客户查询
      await this.testCrmCustomerQuery();
      console.log('');

      // 4. 测试重复提报检查API
      await this.testDuplicateReportCheck();
      console.log('');

    } catch (error) {
      console.error('测试执行异常:', error.message);
    }

    // 输出测试结果
    this.printTestResults();
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📊 测试结果汇总:');
    console.log(`   ✅ 通过: ${this.testResults.passed}`);
    console.log(`   ❌ 失败: ${this.testResults.failed}`);
    console.log(`   📈 成功率: ${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(1)}%`);

    if (this.testResults.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    console.log('\n🎯 测试完成！');
  }
}

// 运行测试
if (require.main === module) {
  const test = new CrmDuplicateCheckTest();
  test.runAllTests().catch(console.error);
}

module.exports = CrmDuplicateCheckTest;
