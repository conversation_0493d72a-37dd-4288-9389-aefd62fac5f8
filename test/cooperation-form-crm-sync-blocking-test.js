/**
 * CooperationForm CRM同步阻塞测试
 *
 * 测试目标：
 * 1. 验证CRM同步改为同步阻塞执行
 * 2. 验证CRM同步错误能够正确传递到前端
 * 3. 验证用户能够看到详细的错误信息
 * 4. 验证表单提交流程的完整性
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const axios = require('axios');

class CooperationFormCrmSyncBlockingTest {
  constructor() {
    this.baseURL = 'http://localhost:3001/api';
    this.authToken = null;
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * 登录获取认证令牌
   */
  async login() {
    try {
      console.log('🔐 正在登录获取认证令牌...');

      const loginData = {
        username: 'admin',
        password: 'admin123'
      };

      const response = await axios({
        method: 'POST',
        url: `${this.baseURL}/auth/login`,
        headers: {
          'Content-Type': 'application/json'
        },
        data: loginData
      });

      if (response.data.success && response.data.data.token) {
        this.authToken = response.data.data.token;
        console.log('✅ 登录成功，获取到认证令牌');
        return true;
      } else {
        console.log('❌ 登录失败:', response.data.message);
        return false;
      }
    } catch (error) {
      console.log('❌ 登录异常:', error.message);
      return false;
    }
  }

  /**
   * 发送HTTP请求
   */
  async makeRequest(method, url, data = null) {
    try {
      const config = {
        method,
        url: `${this.baseURL}${url}`,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      // 添加认证令牌
      if (this.authToken) {
        config.headers.Authorization = `Bearer ${this.authToken}`;
      }

      if (data) {
        config.data = data;
      }

      return await axios(config);
    } catch (error) {
      // 保留原始错误信息用于测试
      throw error;
    }
  }

  /**
   * 测试1: 创建合作记录并触发CRM同步
   */
  async testCreateCooperationWithCrmSync() {
    try {
      console.log('📝 测试1: 创建合作记录并触发CRM同步...');

      const cooperationData = {
        customerName: `测试客户_${Date.now()}`,
        title: `测试合作_${Date.now()}`,
        cooperationForm: 'paid_cooperation',
        publishPlatform: 'xiaohongshu',
        cooperationBrand: 'test_brand',
        scheduledPublishTime: '2024/12/31',
        syncCreateCustomer: true,
        syncCreateAgreement: false
      };

      const response = await this.makeRequest('POST', '/cooperation', cooperationData);

      if (response.data.success) {
        console.log('✅ 合作记录创建成功');
        console.log(`   记录ID: ${response.data.data.id}`);
        console.log(`   客户名称: ${response.data.data.customerName}`);

        // 检查是否有CRM同步结果
        if (response.data.data.crmSyncResult) {
          console.log('✅ CRM同步结果已返回');
          console.log(`   客户同步: ${response.data.data.crmSyncResult.customerSynced}`);
          console.log(`   协议同步: ${response.data.data.crmSyncResult.agreementSynced}`);

          if (response.data.data.crmSyncResult.errors && response.data.data.crmSyncResult.errors.length > 0) {
            console.log('⚠️ CRM同步有错误:', response.data.data.crmSyncResult.errors);
          }
        }

        this.testResults.passed++;
        return response.data.data;
      } else {
        throw new Error('合作记录创建失败');
      }
    } catch (error) {
      console.error('❌ 测试1失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`测试1失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 测试2: 更新合作记录并触发CRM同步
   */
  async testUpdateCooperationWithCrmSync(cooperationId) {
    try {
      console.log('📝 测试2: 更新合作记录并触发CRM同步...');

      if (!cooperationId) {
        throw new Error('需要有效的合作记录ID');
      }

      const updateData = {
        customerName: `更新客户_${Date.now()}`,
        cooperationAmount: '5000',
        cooperationProduct: '更新产品'
      };

      const response = await this.makeRequest('PUT', `/cooperation/${cooperationId}`, updateData);

      if (response.data.success) {
        console.log('✅ 合作记录更新成功');
        console.log(`   记录ID: ${response.data.data.id}`);
        console.log(`   更新字段: customerName, cooperationAmount, cooperationProduct`);

        this.testResults.passed++;
        return response.data.data;
      } else {
        throw new Error('合作记录更新失败');
      }
    } catch (error) {
      console.error('❌ 测试2失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`测试2失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 测试3: 直接测试CRM智能同步API
   */
  async testCrmSmartSyncAPI(cooperationId) {
    try {
      console.log('📝 测试3: 直接测试CRM智能同步API...');

      if (!cooperationId) {
        throw new Error('需要有效的合作记录ID');
      }

      const syncData = {
        cooperationId: cooperationId,
        options: {
          changedFields: ['customerName', 'cooperationAmount']
        }
      };

      const response = await this.makeRequest('POST', '/crm-integration/smart-sync', syncData);

      if (response.data.success) {
        console.log('✅ CRM智能同步API调用成功');
        console.log(`   客户同步: ${response.data.data.customerSynced}`);
        console.log(`   协议同步: ${response.data.data.agreementSynced}`);
        console.log(`   客户ID: ${response.data.data.customerId}`);
        console.log(`   协议ID: ${response.data.data.agreementId}`);

        if (response.data.data.errors && response.data.data.errors.length > 0) {
          console.log('⚠️ 同步过程中的错误:', response.data.data.errors);
        }

        this.testResults.passed++;
        return response.data.data;
      } else {
        throw new Error(`CRM同步失败: ${response.data.message}`);
      }
    } catch (error) {
      console.error('❌ 测试3失败:', error.message);

      // 检查是否是预期的错误（用于测试错误处理）
      if (error.response && error.response.data) {
        console.log('📋 错误响应详情:');
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误消息: ${error.response.data.message}`);
        console.log(`   成功标志: ${error.response.data.success}`);

        // 这种情况下，错误处理是正常的
        if (!error.response.data.success) {
          console.log('✅ 错误处理机制正常工作');
          this.testResults.passed++;
          return null;
        }
      }

      this.testResults.failed++;
      this.testResults.errors.push(`测试3失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 测试4: 测试无效数据的错误处理
   */
  async testErrorHandling() {
    try {
      console.log('📝 测试4: 测试无效数据的错误处理...');

      // 使用无效的合作记录ID
      const invalidSyncData = {
        cooperationId: 99999,
        options: {
          changedFields: ['customerName']
        }
      };

      const response = await this.makeRequest('POST', '/crm-integration/smart-sync', invalidSyncData);

      // 如果没有抛出错误，说明测试失败
      throw new Error('应该抛出错误但没有抛出');
    } catch (error) {
      console.log('✅ 错误处理测试成功');

      if (error.response && error.response.data) {
        console.log(`   错误状态码: ${error.response.status}`);
        console.log(`   错误消息: ${error.response.data.message}`);
        console.log(`   成功标志: ${error.response.data.success}`);

        if (error.response.status === 404 && !error.response.data.success) {
          console.log('✅ 404错误处理正确');
          this.testResults.passed++;
        } else {
          throw new Error('错误响应格式不正确');
        }
      } else {
        throw new Error('没有收到预期的错误响应');
      }
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始CooperationForm CRM同步阻塞测试...\n');

    try {
      // 首先登录获取认证令牌
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        console.log('❌ 无法获取认证令牌，跳过需要认证的测试');
        // 只运行不需要认证的测试
        await this.testErrorHandling();
        console.log('');
        this.printTestResults();
        return;
      }
      console.log('');

      // 测试1: 创建合作记录
      const cooperation = await this.testCreateCooperationWithCrmSync();
      console.log('');

      if (cooperation) {
        // 测试2: 更新合作记录
        await this.testUpdateCooperationWithCrmSync(cooperation.id);
        console.log('');

        // 测试3: 直接测试CRM同步API
        await this.testCrmSmartSyncAPI(cooperation.id);
        console.log('');
      }

      // 测试4: 错误处理
      await this.testErrorHandling();
      console.log('');
    } catch (error) {
      console.error('❌ 测试运行异常:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`测试运行异常: ${error.message}`);
    }

    // 输出测试结果
    this.printTestResults();
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过: ${this.testResults.passed}`);
    console.log(`❌ 失败: ${this.testResults.failed}`);
    console.log(
      `📈 成功率: ${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(
        2
      )}%`
    );

    if (this.testResults.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    console.log('\n🎯 测试重点验证:');
    console.log('   1. CRM同步是否改为同步阻塞执行');
    console.log('   2. CRM同步错误是否能正确传递到前端');
    console.log('   3. 错误信息是否详细且用户友好');
    console.log('   4. 表单提交流程是否完整');
  }
}

// 运行测试
if (require.main === module) {
  const test = new CooperationFormCrmSyncBlockingTest();
  test.runAllTests().catch(console.error);
}

module.exports = CooperationFormCrmSyncBlockingTest;
