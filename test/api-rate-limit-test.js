/**
 * API频率控制测试脚本
 * 
 * 测试批量更新脚本中的API频率控制机制
 * 验证是否符合CRM系统的60次/20秒限制
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const InfluencerPlatformInfoBatchProcessor = require('../scripts/batch-update-influencer-platform-info');

class ApiRateLimitTest {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * 测试API频率控制配置
   */
  testRateLimitConfiguration() {
    console.log('🧪 测试API频率控制配置...');

    try {
      const processor = new InfluencerPlatformInfoBatchProcessor({ dryRun: true });

      // 验证频率控制方法存在
      if (typeof processor.apiRateLimit === 'function') {
        console.log('   ✅ apiRateLimit方法存在 - 通过');
        this.testResults.passed++;
      } else {
        console.log('   ❌ apiRateLimit方法不存在 - 失败');
        this.testResults.failed++;
      }

      // 验证配置常量
      const scriptContent = require('fs').readFileSync(
        require('path').join(__dirname, '../scripts/batch-update-influencer-platform-info.js'),
        'utf8'
      );

      if (scriptContent.includes('CRM_API_LIMITS')) {
        console.log('   ✅ CRM_API_LIMITS配置存在 - 通过');
        this.testResults.passed++;
      } else {
        console.log('   ❌ CRM_API_LIMITS配置缺失 - 失败');
        this.testResults.failed++;
      }

      if (scriptContent.includes('60次/20秒')) {
        console.log('   ✅ API限制说明正确 - 通过');
        this.testResults.passed++;
      } else {
        console.log('   ❌ API限制说明缺失 - 失败');
        this.testResults.failed++;
      }

    } catch (error) {
      console.error('   ❌ API频率控制配置测试失败:', error.message);
      this.testResults.failed += 3;
      this.testResults.errors.push(`API频率控制配置测试失败: ${error.message}`);
    }
  }

  /**
   * 测试延迟时间计算
   */
  testDelayCalculation() {
    console.log('\n🧪 测试延迟时间计算...');

    try {
      // CRM API限制：60次/20秒 = 3次/秒
      // 安全间隔应该至少 333ms，推荐 500ms
      const maxRequestsPer20Seconds = 60;
      const safeInterval = 500;
      const testInterval = 800;

      // 验证安全间隔计算
      const maxSafeRequestsPerSecond = 1000 / safeInterval;
      const maxSafeRequestsPer20Seconds = maxSafeRequestsPerSecond * 20;

      if (maxSafeRequestsPer20Seconds <= maxRequestsPer20Seconds) {
        console.log(`   ✅ 安全间隔计算正确 (${safeInterval}ms -> ${maxSafeRequestsPer20Seconds.toFixed(1)}次/20秒) - 通过`);
        this.testResults.passed++;
      } else {
        console.log(`   ❌ 安全间隔过短 (${safeInterval}ms -> ${maxSafeRequestsPer20Seconds.toFixed(1)}次/20秒) - 失败`);
        this.testResults.failed++;
      }

      // 验证测试模式间隔更保守
      if (testInterval > safeInterval) {
        console.log(`   ✅ 测试模式间隔更保守 (${testInterval}ms > ${safeInterval}ms) - 通过`);
        this.testResults.passed++;
      } else {
        console.log(`   ❌ 测试模式间隔不够保守 - 失败`);
        this.testResults.failed++;
      }

    } catch (error) {
      console.error('   ❌ 延迟时间计算测试失败:', error.message);
      this.testResults.failed += 2;
      this.testResults.errors.push(`延迟时间计算测试失败: ${error.message}`);
    }
  }

  /**
   * 测试频率控制实现
   */
  async testRateLimitImplementation() {
    console.log('\n🧪 测试频率控制实现...');

    try {
      const processor = new InfluencerPlatformInfoBatchProcessor({ dryRun: true });

      // 测试不同模式的延迟
      const testModes = [
        { mode: 'normal', expectedMin: 400 },
        { mode: 'test', expectedMin: 700 },
        { mode: 'batch', expectedMin: 1800 }
      ];

      for (const testCase of testModes) {
        const startTime = Date.now();
        await processor.apiRateLimit(testCase.mode);
        const actualDelay = Date.now() - startTime;

        if (actualDelay >= testCase.expectedMin) {
          console.log(`   ✅ ${testCase.mode}模式延迟正确 (${actualDelay}ms >= ${testCase.expectedMin}ms) - 通过`);
          this.testResults.passed++;
        } else {
          console.log(`   ❌ ${testCase.mode}模式延迟不足 (${actualDelay}ms < ${testCase.expectedMin}ms) - 失败`);
          this.testResults.failed++;
        }
      }

    } catch (error) {
      console.error('   ❌ 频率控制实现测试失败:', error.message);
      this.testResults.failed += 3;
      this.testResults.errors.push(`频率控制实现测试失败: ${error.message}`);
    }
  }

  /**
   * 测试脚本中的频率控制应用
   */
  testScriptRateLimitUsage() {
    console.log('\n🧪 测试脚本中的频率控制应用...');

    try {
      const scriptContent = require('fs').readFileSync(
        require('path').join(__dirname, '../scripts/batch-update-influencer-platform-info.js'),
        'utf8'
      );

      // 验证测试模式中的频率控制
      if (scriptContent.includes('apiRateLimit(\'test\')')) {
        console.log('   ✅ 测试模式频率控制已应用 - 通过');
        this.testResults.passed++;
      } else {
        console.log('   ❌ 测试模式频率控制缺失 - 失败');
        this.testResults.failed++;
      }

      // 验证批次间延迟
      if (scriptContent.includes('apiRateLimit(\'batch\')')) {
        console.log('   ✅ 批次间频率控制已应用 - 通过');
        this.testResults.passed++;
      } else {
        console.log('   ❌ 批次间频率控制缺失 - 失败');
        this.testResults.failed++;
      }

      // 验证生成脚本中的延迟
      if (scriptContent.includes('CRM_API_LIMITS.safeRequestInterval')) {
        console.log('   ✅ 生成脚本频率控制已配置 - 通过');
        this.testResults.passed++;
      } else {
        console.log('   ❌ 生成脚本频率控制缺失 - 失败');
        this.testResults.failed++;
      }

    } catch (error) {
      console.error('   ❌ 脚本频率控制应用测试失败:', error.message);
      this.testResults.failed += 3;
      this.testResults.errors.push(`脚本频率控制应用测试失败: ${error.message}`);
    }
  }

  /**
   * 模拟频率控制效果
   */
  simulateRateLimitEffect() {
    console.log('\n🧪 模拟频率控制效果...');

    try {
      // 模拟参数
      const batchSize = 10;
      const safeInterval = 500; // ms
      const testInterval = 800; // ms

      // 计算执行时间
      const normalModeTime = (batchSize - 1) * safeInterval; // 最后一个请求不需要延迟
      const testModeTime = (batchSize - 1) * testInterval;

      console.log(`   📊 批量处理 ${batchSize} 条记录的预估时间:`);
      console.log(`      正常模式: ${(normalModeTime / 1000).toFixed(1)} 秒`);
      console.log(`      测试模式: ${(testModeTime / 1000).toFixed(1)} 秒`);

      // 验证频率是否在限制范围内
      const requestsPer20SecondsNormal = Math.floor(20000 / safeInterval);
      const requestsPer20SecondsTest = Math.floor(20000 / testInterval);

      console.log(`   📈 20秒内最大请求次数:`);
      console.log(`      正常模式: ${requestsPer20SecondsNormal} 次 (限制: 60次)`);
      console.log(`      测试模式: ${requestsPer20SecondsTest} 次 (限制: 60次)`);

      if (requestsPer20SecondsNormal <= 60 && requestsPer20SecondsTest <= 60) {
        console.log('   ✅ 频率控制效果符合API限制 - 通过');
        this.testResults.passed++;
      } else {
        console.log('   ❌ 频率控制效果超出API限制 - 失败');
        this.testResults.failed++;
      }

    } catch (error) {
      console.error('   ❌ 频率控制效果模拟失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`频率控制效果模拟失败: ${error.message}`);
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始API频率控制测试\n');

    // 运行各项测试
    this.testRateLimitConfiguration();
    this.testDelayCalculation();
    await this.testRateLimitImplementation();
    this.testScriptRateLimitUsage();
    this.simulateRateLimitEffect();

    // 输出测试结果
    this.printTestResults();
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('\n📊 测试结果汇总:');
    console.log(`   ✅ 通过: ${this.testResults.passed}`);
    console.log(`   ❌ 失败: ${this.testResults.failed}`);
    
    const total = this.testResults.passed + this.testResults.failed;
    const successRate = total > 0 ? ((this.testResults.passed / total) * 100).toFixed(1) : '0';
    console.log(`   📈 成功率: ${successRate}%`);

    if (this.testResults.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    console.log('\n🎯 测试完成！');
    
    if (this.testResults.failed === 0) {
      console.log('🎉 所有测试通过，API频率控制实现正确！');
      console.log('\n✨ 频率控制特性确认:');
      console.log('   1. ✅ 符合CRM API限制：60次/20秒');
      console.log('   2. ✅ 安全间隔设置：500ms (约2次/秒)');
      console.log('   3. ✅ 测试模式更保守：800ms');
      console.log('   4. ✅ 批次间适当延迟：2秒');
      console.log('   5. ✅ 动态频率控制方法');
    } else {
      console.log('⚠️ 部分测试失败，请检查频率控制实现！');
    }

    console.log('\n💡 使用建议:');
    console.log('   🔧 小批量测试: --batch-size 5 --test-only');
    console.log('   ⚡ 正常执行: --batch-size 10 (推荐)');
    console.log('   🚀 大批量: --batch-size 20 (谨慎使用)');
    console.log('   ⏱️  预估时间: 10条记录约5-8秒');
  }
}

// 运行测试
if (require.main === module) {
  const test = new ApiRateLimitTest();
  test.runAllTests().catch(console.error);
}

module.exports = ApiRateLimitTest;
