/**
 * 最终验证脚本
 * 
 * 验证任务管理面板的完整功能
 */

const cooperationService = require('../src/services/CooperationService');
const { CooperationManagement } = require('../src/models');
const { Op } = require('sequelize');

class FinalVerification {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有验证
   */
  async runAllVerifications() {
    console.log('🔍 开始最终功能验证...\n');

    try {
      await this.verifyDataAvailability();
      await this.verifyTaskListQuery();
      await this.verifyTaskStats();
      await this.verifyFiltering();
      await this.verifyPagination();
      
      this.printResults();
    } catch (error) {
      console.error('❌ 验证执行失败:', error.message);
    }
  }

  /**
   * 验证数据可用性
   */
  async verifyDataAvailability() {
    console.log('📝 验证1: 数据可用性');
    
    try {
      // 检查总记录数
      const totalCount = await CooperationManagement.count();
      this.addResult('total_records', totalCount > 0, `总记录数: ${totalCount}`);
      
      // 检查有noteId的记录数
      const withNoteIdCount = await CooperationManagement.count({
        where: { noteId: { [Op.ne]: null } }
      });
      this.addResult('records_with_noteid', withNoteIdCount > 0, 
        `有noteId的记录数: ${withNoteIdCount}`);
      
      // 检查数据质量
      if (withNoteIdCount > 0) {
        const sampleRecord = await CooperationManagement.findOne({
          where: { noteId: { [Op.ne]: null } },
          attributes: ['id', 'customerName', 'noteId', 'publishLink']
        });
        
        this.addResult('data_quality', 
          sampleRecord && sampleRecord.noteId && sampleRecord.noteId.length === 24,
          `样本记录noteId格式正确: ${sampleRecord?.noteId}`);
      }
      
    } catch (error) {
      this.addResult('data_availability', false, `数据检查失败: ${error.message}`);
    }
  }

  /**
   * 验证任务列表查询
   */
  async verifyTaskListQuery() {
    console.log('📝 验证2: 任务列表查询');
    
    try {
      // 基础查询
      const result = await cooperationService.getDataFetchTasks({
        page: 1,
        limit: 10
      });
      
      this.addResult('task_list_structure', 
        result && result.tasks && Array.isArray(result.tasks) && result.pagination,
        `查询结构正确，返回 ${result.tasks.length} 条记录`);
      
      // 验证记录优先级（有noteId的记录应该排在前面）
      if (result.tasks.length > 0) {
        const firstRecord = result.tasks[0];
        this.addResult('priority_sorting', 
          firstRecord.noteId !== null,
          `优先级排序正确，第一条记录有noteId: ${firstRecord.noteId}`);
        
        // 验证记录字段完整性
        const requiredFields = ['id', 'customerName', 'dataFetchStatus', 'dataRegistrationDate'];
        const hasAllFields = requiredFields.every(field => firstRecord.hasOwnProperty(field));
        this.addResult('record_fields', hasAllFields, '记录字段完整');
      }
      
    } catch (error) {
      this.addResult('task_list_query', false, `任务列表查询失败: ${error.message}`);
    }
  }

  /**
   * 验证统计信息
   */
  async verifyTaskStats() {
    console.log('📝 验证3: 统计信息');
    
    try {
      const stats = await cooperationService.getDataFetchTaskStats();
      
      // 验证统计字段
      const requiredFields = ['total', 'pending', 'fetching', 'success', 'failed', 'successRate'];
      const hasAllFields = requiredFields.every(field => stats.hasOwnProperty(field));
      this.addResult('stats_structure', hasAllFields, '统计信息结构完整');
      
      // 验证数据合理性
      const isDataValid = stats.total >= 0 && 
                         stats.pending >= 0 && 
                         stats.success >= 0 && 
                         stats.failed >= 0 &&
                         stats.successRate >= 0 && 
                         stats.successRate <= 100;
      
      this.addResult('stats_validity', isDataValid, 
        `统计数据合理: 总数${stats.total}, 待拉取${stats.pending}, 成功率${stats.successRate}%`);
      
    } catch (error) {
      this.addResult('task_stats', false, `统计信息查询失败: ${error.message}`);
    }
  }

  /**
   * 验证筛选功能
   */
  async verifyFiltering() {
    console.log('📝 验证4: 筛选功能');
    
    try {
      // 状态筛选
      const pendingResult = await cooperationService.getDataFetchTasks({
        status: 'pending',
        page: 1,
        limit: 5
      });
      
      this.addResult('status_filter', 
        pendingResult && pendingResult.tasks.every(task => task.dataFetchStatus === 'pending'),
        `状态筛选正确，返回 ${pendingResult.tasks.length} 条pending记录`);
      
      // 客户名称搜索
      if (pendingResult.tasks.length > 0) {
        const customerName = pendingResult.tasks[0].customerName;
        const searchResult = await cooperationService.getDataFetchTasks({
          customerName: customerName.substring(0, 2), // 取前两个字符搜索
          page: 1,
          limit: 5
        });
        
        this.addResult('customer_search', 
          searchResult && searchResult.tasks.length > 0,
          `客户名称搜索正常，搜索"${customerName.substring(0, 2)}"返回 ${searchResult.tasks.length} 条记录`);
      }
      
    } catch (error) {
      this.addResult('filtering', false, `筛选功能测试失败: ${error.message}`);
    }
  }

  /**
   * 验证分页功能
   */
  async verifyPagination() {
    console.log('📝 验证5: 分页功能');
    
    try {
      // 第一页
      const page1 = await cooperationService.getDataFetchTasks({
        page: 1,
        limit: 3
      });
      
      // 第二页
      const page2 = await cooperationService.getDataFetchTasks({
        page: 2,
        limit: 3
      });
      
      this.addResult('pagination_structure', 
        page1.pagination && page2.pagination &&
        page1.pagination.page === 1 && page2.pagination.page === 2,
        '分页结构正确');
      
      // 验证数据不重复
      if (page1.tasks.length > 0 && page2.tasks.length > 0) {
        const page1Ids = page1.tasks.map(task => task.id);
        const page2Ids = page2.tasks.map(task => task.id);
        const hasOverlap = page1Ids.some(id => page2Ids.includes(id));
        
        this.addResult('pagination_uniqueness', !hasOverlap, '分页数据不重复');
      }
      
    } catch (error) {
      this.addResult('pagination', false, `分页功能测试失败: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  addResult(testName, success, message) {
    this.testResults.push({ testName, success, message });
    const status = success ? '✅' : '❌';
    console.log(`   ${status} ${testName}: ${message}`);
  }

  /**
   * 打印验证结果
   */
  printResults() {
    console.log('\n📊 最终验证结果汇总:');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(result => result.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总验证项: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的验证项:');
      this.testResults
        .filter(result => !result.success)
        .forEach(result => {
          console.log(`   - ${result.testName}: ${result.message}`);
        });
    }
    
    console.log('\n🎉 任务管理面板功能验证完成!');
    
    if (passedTests === totalTests) {
      console.log('\n✅ 所有验证通过！任务管理面板已完全就绪：');
      console.log('   - ✅ 数据查询正常，有noteId的记录优先显示');
      console.log('   - ✅ 统计信息准确，数据结构完整');
      console.log('   - ✅ 筛选功能正常，支持状态和客户名称筛选');
      console.log('   - ✅ 分页功能正常，数据不重复');
      console.log('   - ✅ 排序逻辑正确，优先显示可拉取的任务');
      console.log('\n🚀 您现在可以正常使用任务管理面板了！');
      console.log('💡 访问地址: http://localhost:5173/data-fetch-tasks');
    } else {
      console.log('\n⚠️  部分验证失败，请检查上述错误信息');
    }
  }
}

// 如果直接运行此文件，执行验证
if (require.main === module) {
  const verification = new FinalVerification();
  verification.runAllVerifications().catch(console.error);
}

module.exports = FinalVerification;
