# 合作对接记录列表排序功能测试指南

## 功能概述

本文档描述了 `CooperationView.vue` 页面中新增的默认排序功能，主要针对CRM集成后的数据展示优化。

## 修改内容

### 1. 默认排序规则
- **主要排序字段**：`externalCustomerId`（CRM客户ID）
- **排序方式**：降序（DESC）
- **空值处理**：未同步到CRM的记录（`externalCustomerId` 为 null）排在最前面

### 2. 排序逻辑
```sql
ORDER BY 
  CASE WHEN external_customer_id IS NULL THEN 0 ELSE 1 END ASC,  -- 空值优先
  external_customer_id DESC,                                      -- CRM ID降序
  created_at DESC                                                 -- 创建时间降序（次要排序）
```

### 3. 用户交互功能
- 保留表格列头的手动排序功能
- 用户点击列头时覆盖默认排序
- 重置搜索时恢复默认排序

## 测试场景

### 场景1：页面初始加载
**操作步骤**：
1. 打开合作对接管理页面
2. 观察数据列表的排序

**预期结果**：
- 未同步CRM的记录（CRM客户ID为空）显示在最前面
- 已同步CRM的记录按CRM客户ID从大到小排列
- 相同CRM客户ID的记录按创建时间降序排列

### 场景2：手动列头排序
**操作步骤**：
1. 点击"客户名称"列头进行排序
2. 观察排序变化
3. 再次点击切换排序方向

**预期结果**：
- 数据按客户名称排序
- 排序方向正确切换（升序/降序）
- 控制台显示排序参数日志

### 场景3：重置搜索功能
**操作步骤**：
1. 先进行手动排序（如按客户名称排序）
2. 点击"重置"按钮
3. 观察排序是否恢复默认

**预期结果**：
- 搜索条件被清空
- 排序恢复为默认的CRM客户ID排序
- 未同步CRM的记录重新显示在最前面

### 场景4：分页排序一致性
**操作步骤**：
1. 在第一页观察排序
2. 切换到第二页
3. 观察排序是否保持一致

**预期结果**：
- 分页间排序保持一致
- 不会出现重复或遗漏的记录
- 排序逻辑在所有页面都生效

## 业务价值

### 1. 优先处理未同步记录
- 未同步到CRM的记录显示在最前面
- 便于用户优先处理需要同步的数据
- 提高工作效率

### 2. CRM数据管理
- 新创建的CRM客户（ID较大）优先显示
- 便于跟踪最新的合作记录
- 符合业务处理习惯

### 3. 用户体验优化
- 保持用户熟悉的手动排序功能
- 提供合理的默认排序
- 重置功能恢复到最佳状态

## 技术实现细节

### 1. 后端排序逻辑
**文件**：`src/services/CooperationService.js`

```javascript
// 构建排序逻辑
let orderClause;
if (sortBy === 'externalCustomerId') {
  // 按CRM客户ID排序时，空值优先显示
  orderClause = [
    [sequelize.literal('CASE WHEN external_customer_id IS NULL THEN 0 ELSE 1 END'), 'ASC'],
    ['externalCustomerId', sortOrder.toUpperCase()],
    ['createdAt', 'DESC'] // 次要排序
  ];
} else {
  // 其他字段排序
  orderClause = [
    [sortBy, sortOrder.toUpperCase()],
    ['createdAt', 'DESC'] // 次要排序
  ];
}
```

### 2. 前端排序配置
**文件**：`frontend/src/views/CooperationView.vue`

```javascript
// 排序配置
const sortConfig = reactive({
  sortBy: 'externalCustomerId', // 默认按CRM客户ID排序
  sortOrder: 'DESC' // 默认降序
});

// 排序处理
const handleSorterChange = (dataIndex, direction) => {
  if (direction) {
    sortConfig.sortBy = dataIndex;
    sortConfig.sortOrder = direction === 'ascend' ? 'ASC' : 'DESC';
  } else {
    // 重置为默认排序
    sortConfig.sortBy = 'externalCustomerId';
    sortConfig.sortOrder = 'DESC';
  }
  pagination.current = 1;
  getCooperationList();
};
```

### 3. API参数传递
```javascript
const params = {
  page: pagination.current,
  limit: pagination.pageSize,
  sortBy: sortConfig.sortBy,
  sortOrder: sortConfig.sortOrder,
  ...filteredParams
};
```

## 测试检查点

### 功能性测试
- [ ] 默认排序正确（未同步CRM记录在前）
- [ ] CRM客户ID降序排列正确
- [ ] 手动排序功能正常
- [ ] 重置搜索恢复默认排序
- [ ] 分页排序一致性

### 性能测试
- [ ] 大数据量下排序性能
- [ ] 数据库查询效率
- [ ] 前端渲染性能

### 用户体验测试
- [ ] 排序变化响应及时
- [ ] 排序状态显示正确
- [ ] 操作流程符合直觉

## 注意事项

1. **数据库索引**：确保`external_customer_id`字段有适当的索引
2. **空值处理**：正确处理NULL值的排序逻辑
3. **向后兼容**：保持现有功能不受影响
4. **性能考虑**：大数据量时的排序性能

## 相关文件

- `frontend/src/views/CooperationView.vue` - 前端页面主文件
- `src/services/CooperationService.js` - 后端服务逻辑
- `src/controllers/CooperationController.js` - 后端控制器
- `src/models/CooperationManagement.js` - 数据模型定义

## 预期效果示例

页面加载后的数据顺序：
```
1. [未同步] 客户A - CRM客户ID: null
2. [未同步] 客户B - CRM客户ID: null
3. [已同步] 客户C - CRM客户ID: 1001
4. [已同步] 客户D - CRM客户ID: 1000
5. [已同步] 客户E - CRM客户ID: 999
...
```

这样的排序确保用户优先看到需要处理的未同步记录，同时按照CRM系统的时间顺序查看已同步的记录。
