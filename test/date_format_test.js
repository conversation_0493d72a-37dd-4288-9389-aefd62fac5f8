/**
 * 日期格式化功能测试脚本
 * 验证前后端日期格式统一性
 */

const DateFormatter = require('../src/utils/dateFormatter');

console.log('🧪 开始日期格式化测试...\n');

// 测试数据
const testDates = [
  new Date('2025-07-08T14:30:25.123Z'),
  new Date('2024-12-31T23:59:59.999Z'),
  new Date('2025-01-01T00:00:00.000Z'),
  new Date(),
  null,
  undefined,
  '',
  'invalid-date'
];

console.log('📅 测试后端DateFormatter.format()方法：');
testDates.forEach((date, index) => {
  try {
    const formatted = DateFormatter.format(date);
    console.log(`${index + 1}. 输入: ${date} -> 输出: ${formatted}`);
  } catch (error) {
    console.log(`${index + 1}. 输入: ${date} -> 错误: ${error.message}`);
  }
});

console.log('\n📊 测试对象格式化：');
const testObject = {
  id: 1,
  name: '测试数据',
  createdAt: new Date('2025-07-08T14:30:25.123Z'),
  updatedAt: new Date('2024-12-31T23:59:59.999Z'),
  scheduledPublishTime: new Date('2025-08-15T10:00:00.000Z'),
  actualPublishDate: new Date('2025-08-16T15:30:00.000Z'),
  normalField: '普通字段',
  nullDate: null,
  undefinedDate: undefined
};

const formattedObject = DateFormatter.formatObject(testObject);
console.log('原始对象:', JSON.stringify(testObject, null, 2));
console.log('格式化后:', JSON.stringify(formattedObject, null, 2));

console.log('\n📋 测试数组格式化：');
const testArray = [
  {
    id: 1,
    customerName: '张三',
    createdAt: new Date('2025-07-08T14:30:25.123Z'),
    scheduledPublishTime: new Date('2025-08-15T10:00:00.000Z')
  },
  {
    id: 2,
    customerName: '李四',
    createdAt: new Date('2024-12-31T23:59:59.999Z'),
    actualPublishDate: new Date('2025-08-16T15:30:00.000Z')
  }
];

const formattedArray = DateFormatter.formatArray(testArray);
console.log('原始数组:', JSON.stringify(testArray, null, 2));
console.log('格式化后:', JSON.stringify(formattedArray, null, 2));

console.log('\n🎯 验证格式标准：');
const now = new Date();
const formatted = DateFormatter.format(now);
const expectedPattern = /^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/;

if (expectedPattern.test(formatted)) {
  console.log('✅ 格式验证通过:', formatted);
  console.log('✅ 符合 YYYY/MM/DD HH:mm:ss 标准');
} else {
  console.log('❌ 格式验证失败:', formatted);
  console.log('❌ 不符合 YYYY/MM/DD HH:mm:ss 标准');
}

console.log('\n📝 测试时间字段识别：');
console.log('已配置的时间字段:', DateFormatter.TIME_FIELDS);

// 测试新增的时间字段
const newTimeFields = [
  'scheduledPublishTime',
  'actualPublishDate', 
  'dataRegistrationDate',
  'scheduledFetchTime',
  'lastSubmittedAt',
  'reviewedAt'
];

console.log('\n🆕 新增的时间字段:');
newTimeFields.forEach(field => {
  const isIncluded = DateFormatter.TIME_FIELDS.includes(field);
  console.log(`${isIncluded ? '✅' : '❌'} ${field}: ${isIncluded ? '已包含' : '未包含'}`);
});

console.log('\n🏁 测试完成！');

// 如果是直接运行此脚本
if (require.main === module) {
  console.log('\n💡 使用说明：');
  console.log('1. 确保后端DateFormatter正确格式化所有时间字段');
  console.log('2. 前端使用 frontend/src/utils/dateFormatter.js 中的工具函数');
  console.log('3. 所有日期显示应使用 YYYY/MM/DD 格式');
  console.log('4. 所有日期时间显示应使用 YYYY/MM/DD HH:mm:ss 格式');
  
  console.log('\n🔧 如需测试前端格式化函数，请在浏览器控制台中运行：');
  console.log(`
import { formatDate, formatDateTime } from '@/utils/dateFormatter';

// 测试日期格式化
console.log(formatDate(new Date())); // 应输出: YYYY/MM/DD
console.log(formatDateTime(new Date())); // 应输出: YYYY/MM/DD HH:mm:ss
  `);
}
