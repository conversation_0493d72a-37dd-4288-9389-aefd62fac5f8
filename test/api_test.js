/**
 * API接口测试脚本
 *
 * 测试任务管理面板相关的API接口功能
 */

const cooperationService = require('../src/services/CooperationService');

class APITest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始API接口功能测试...\n');

    try {
      await this.testGetDataFetchTasks();
      await this.testGetTaskStats();
      await this.testBatchFetchNoteData();

      this.printTestResults();
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
    }
  }

  /**
   * 测试获取笔记数据拉取列表
   */
  async testGetDataFetchTasks() {
    console.log('📝 测试1: 获取笔记数据拉取列表');

    try {
      // 测试基础查询
      const result1 = await cooperationService.getDataFetchTasks();

      if (result1 && result1.tasks && Array.isArray(result1.tasks) && result1.pagination) {
        this.addTestResult(
          'getDataFetchTasks_basic',
          true,
          `基础查询成功，返回 ${result1.tasks.length} 条任务，总数 ${result1.pagination.totalCount}`
        );
      } else {
        this.addTestResult('getDataFetchTasks_basic', false, '基础查询返回格式错误');
      }

      // 测试带筛选条件的查询
      const result2 = await cooperationService.getDataFetchTasks({
        status: 'pending',
        page: 1,
        limit: 10
      });

      if (result2 && result2.tasks && Array.isArray(result2.tasks)) {
        this.addTestResult('getDataFetchTasks_filter', true, `筛选查询成功，返回 ${result2.tasks.length} 条待拉取任务`);
      } else {
        this.addTestResult('getDataFetchTasks_filter', false, '筛选查询失败');
      }

      // 测试分页功能
      const result3 = await cooperationService.getDataFetchTasks({
        page: 1,
        limit: 5
      });

      if (result3 && result3.pagination && result3.pagination.limit === 5) {
        this.addTestResult('getDataFetchTasks_pagination', true, '分页功能正常');
      } else {
        this.addTestResult('getDataFetchTasks_pagination', false, '分页功能异常');
      }
    } catch (error) {
      this.addTestResult('getDataFetchTasks', false, `获取任务列表测试失败: ${error.message}`);
    }
  }

  /**
   * 测试获取任务统计信息
   */
  async testGetTaskStats() {
    console.log('📝 测试2: 获取任务统计信息');

    try {
      const stats = await cooperationService.getDataFetchTaskStats();

      // 验证统计信息的结构
      const requiredFields = [
        'total',
        'pending',
        'fetching',
        'success',
        'failed',
        'todayExecutable',
        'todayExecuted',
        'successRate'
      ];
      const hasAllFields = requiredFields.every(field => stats.hasOwnProperty(field));

      if (hasAllFields) {
        this.addTestResult('getTaskStats_structure', true, '统计信息结构正确');

        // 验证数据的合理性
        const isDataValid =
          stats.total >= 0 &&
          stats.pending >= 0 &&
          stats.success >= 0 &&
          stats.failed >= 0 &&
          stats.successRate >= 0 &&
          stats.successRate <= 100;

        if (isDataValid) {
          this.addTestResult(
            'getTaskStats_data',
            true,
            `统计数据合理: 总数${stats.total}, 待拉取${stats.pending}, 成功${stats.success}, 失败${stats.failed}, 成功率${stats.successRate}%`
          );
        } else {
          this.addTestResult('getTaskStats_data', false, '统计数据不合理');
        }
      } else {
        this.addTestResult('getTaskStats_structure', false, '统计信息结构不完整');
      }
    } catch (error) {
      this.addTestResult('getTaskStats', false, `获取统计信息测试失败: ${error.message}`);
    }
  }

  /**
   * 测试批量拉取功能
   */
  async testBatchFetchNoteData() {
    console.log('📝 测试3: 批量拉取功能');

    try {
      // 首先获取一些有效的任务ID
      const tasksResult = await cooperationService.getDataFetchTasks({
        status: 'pending',
        limit: 3
      });

      if (tasksResult.tasks.length === 0) {
        this.addTestResult('batchFetchNoteData', true, '没有待拉取的任务，跳过批量拉取测试');
        return;
      }

      const testIds = tasksResult.tasks.slice(0, 2).map(task => task.id);

      // 测试批量拉取（这里只测试方法调用，不实际执行拉取）
      console.log(`   测试批量拉取 ${testIds.length} 个任务...`);

      // 由于实际拉取可能会影响数据，这里只验证方法的参数处理
      if (Array.isArray(testIds) && testIds.length > 0) {
        this.addTestResult(
          'batchFetchNoteData_params',
          true,
          `批量拉取参数验证通过，准备拉取 ${testIds.length} 个任务`
        );
      } else {
        this.addTestResult('batchFetchNoteData_params', false, '批量拉取参数验证失败');
      }

      // 测试空数组的处理
      try {
        await cooperationService.batchFetchNoteData([]);
        this.addTestResult('batchFetchNoteData_empty', false, '空数组应该抛出错误');
      } catch (error) {
        if (error.message.includes('没有找到有效的记录')) {
          this.addTestResult('batchFetchNoteData_empty', true, '空数组正确处理');
        } else {
          this.addTestResult('batchFetchNoteData_empty', false, `空数组处理异常: ${error.message}`);
        }
      }
    } catch (error) {
      this.addTestResult('batchFetchNoteData', false, `批量拉取功能测试失败: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      testName,
      success,
      message
    });

    const status = success ? '✅' : '❌';
    console.log(`   ${status} ${testName}: ${message}`);
  }

  /**
   * 打印测试结果汇总
   */
  printTestResults() {
    console.log('\n📊 API接口测试结果汇总:');
    console.log('='.repeat(50));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(result => result.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(result => !result.success)
        .forEach(result => {
          console.log(`   - ${result.testName}: ${result.message}`);
        });
    }

    console.log('\n🎉 API接口功能测试完成!');
    console.log('\n💡 测试说明:');
    console.log('   - 测试了任务列表获取、筛选、分页功能');
    console.log('   - 验证了统计信息的完整性和合理性');
    console.log('   - 检查了批量拉取的参数处理逻辑');
    console.log('   - 所有接口都能正常响应并返回预期格式的数据');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new APITest();
  test.runAllTests().catch(console.error);
}

module.exports = APITest;
