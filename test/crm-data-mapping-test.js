/**
 * CRM数据映射服务测试脚本
 * 
 * 功能说明：
 * - 测试重构后的CRM数据映射功能
 * - 验证统一字段映射配置的正确性
 * - 检查是否有遗漏的字段映射
 * 
 * 使用方法：
 * node test/crm-data-mapping-test.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const CrmDataMappingService = require('../src/services/CrmDataMappingService');

class CrmDataMappingTest {
  constructor() {
    this.mappingService = new CrmDataMappingService();
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    try {
      console.log('🧪 开始CRM数据映射服务测试...\n');

      // 1. 测试基础字段映射
      await this.testBasicFieldMapping();

      // 2. 测试完整字段映射
      await this.testCompleteFieldMapping();

      // 3. 测试特殊值映射
      await this.testSpecialValueMapping();

      // 4. 测试未映射字段检查
      await this.testUnmappedFieldsCheck();

      // 5. 测试映射配置获取
      await this.testMappingConfiguration();

      // 输出测试结果
      this.printTestResults();

    } catch (error) {
      console.error('❌ 测试运行失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`测试运行失败: ${error.message}`);
    }
  }

  /**
   * 测试基础字段映射
   */
  async testBasicFieldMapping() {
    try {
      console.log('🔄 测试基础字段映射...');

      const testData = {
        title: '测试协议标题',
        customerName: '测试客户',
        cooperationForm: '图文',
        cooperationBrand: '测试品牌',
        cooperationProduct: '测试产品',
        cooperationAmount: '5000',
        publishPlatform: '小红书',
        publishLink: 'https://test.com/link',
        responsiblePerson: '测试负责人',
        cooperationNotes: '测试备注',
        externalCustomerId: 'test_customer_123'
      };

      const result = this.mappingService.mapCooperationToAgreement(testData);

      // 验证基础字段映射
      const expectedMappings = {
        'contract_title': '测试协议标题',
        'contract_textarea_1': '测试客户',
        'contract_select_2': 'form_image_text',
        'contract_textarea_3': '测试品牌',
        'contract_textarea_4': '测试产品',
        'contract_textarea_5': '5000',
        'contract_select_3': 'platform_xiaohongshu',
        'contract_textarea_6': 'https://test.com/link',
        'contract_textarea_7': '测试负责人',
        'contract_textarea_8': '测试备注',
        'customer_id': 'test_customer_123'
      };

      let allPassed = true;
      for (const [crmField, expectedValue] of Object.entries(expectedMappings)) {
        if (result[crmField] !== expectedValue) {
          console.error(`❌ 字段映射错误: ${crmField} 期望 "${expectedValue}", 实际 "${result[crmField]}"`);
          allPassed = false;
        }
      }

      if (allPassed) {
        console.log('✅ 基础字段映射测试通过');
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
        this.testResults.errors.push('基础字段映射测试失败');
      }

      console.log('');
    } catch (error) {
      console.error('❌ 基础字段映射测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`基础字段映射测试失败: ${error.message}`);
    }
  }

  /**
   * 测试完整字段映射
   */
  async testCompleteFieldMapping() {
    try {
      console.log('🔄 测试完整字段映射...');

      const completeTestData = {
        title: '完整测试协议',
        customerName: '完整测试客户',
        cooperationForm: '视频',
        cooperationBrand: '完整测试品牌',
        cooperationProduct: '完整测试产品',
        cooperationAmount: '10000',
        scheduledPublishTime: '2024-12-01 10:00:00',
        actualPublishDate: '2024-12-02 15:30:00',
        publishPlatform: '抖音',
        publishLink: 'https://test.com/complete',
        responsiblePerson: '完整测试负责人',
        cooperationNotes: '完整测试备注',
        influencerCommissionRate: '15%',
        payeeName: '测试收款人',
        bankAccount: '**********',
        bankName: '测试银行',
        rebateCompleted: '已完成',
        dataRegistrationDate: '2024-12-03',
        viewCount: 10000,
        likeCount: 500,
        collectCount: 200,
        commentCount: 100,
        contentImplantCoefficient: '高',
        commentMaintenanceCoefficient: '中',
        brandTopicIncluded: '是',
        selfEvaluation: '优秀',
        externalCustomerId: 'complete_test_123'
      };

      const result = this.mappingService.mapCooperationToAgreement(completeTestData);

      // 验证所有字段都被映射
      const mappingConfig = this.mappingService.cooperationToAgreementMapping;
      let allFieldsMapped = true;

      for (const [localField, crmField] of Object.entries(mappingConfig)) {
        if (completeTestData[localField] !== undefined && !result.hasOwnProperty(crmField)) {
          console.error(`❌ 字段未映射: ${localField} -> ${crmField}`);
          allFieldsMapped = false;
        }
      }

      if (allFieldsMapped) {
        console.log('✅ 完整字段映射测试通过');
        console.log(`   映射字段数量: ${Object.keys(result).length}`);
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
        this.testResults.errors.push('完整字段映射测试失败');
      }

      console.log('');
    } catch (error) {
      console.error('❌ 完整字段映射测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`完整字段映射测试失败: ${error.message}`);
    }
  }

  /**
   * 测试特殊值映射
   */
  async testSpecialValueMapping() {
    try {
      console.log('🔄 测试特殊值映射...');

      const specialTestData = {
        title: '', // 空标题
        customerName: '特殊测试客户',
        cooperationForm: '未知形式', // 未映射的合作形式
        publishPlatform: '未知平台', // 未映射的发布平台
        scheduledPublishTime: 'invalid-date', // 无效日期
        cooperationNotes: null, // null值
        externalCustomerId: undefined // undefined值
      };

      const result = this.mappingService.mapCooperationToAgreement(specialTestData);

      // 验证特殊值处理
      const checks = [
        { field: 'contract_title', expected: '特殊测试客户合作协议', description: '空标题应生成默认标题' },
        { field: 'contract_select_2', expected: '', description: '未映射的合作形式应返回空字符串' },
        { field: 'contract_select_3', expected: '', description: '未映射的发布平台应返回空字符串' },
        { field: 'contract_date_1', expected: '', description: '无效日期应返回空字符串' },
        { field: 'contract_textarea_8', expected: '', description: 'null值应返回空字符串' },
        { field: 'customer_id', expected: '', description: 'undefined值应返回空字符串' }
      ];

      let allChecksPass = true;
      for (const check of checks) {
        if (result[check.field] !== check.expected) {
          console.error(`❌ ${check.description}: 期望 "${check.expected}", 实际 "${result[check.field]}"`);
          allChecksPass = false;
        }
      }

      if (allChecksPass) {
        console.log('✅ 特殊值映射测试通过');
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
        this.testResults.errors.push('特殊值映射测试失败');
      }

      console.log('');
    } catch (error) {
      console.error('❌ 特殊值映射测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`特殊值映射测试失败: ${error.message}`);
    }
  }

  /**
   * 测试未映射字段检查
   */
  async testUnmappedFieldsCheck() {
    try {
      console.log('🔄 测试未映射字段检查...');

      const testDataWithUnmappedFields = {
        title: '测试标题',
        customerName: '测试客户',
        unmappedField1: '未映射字段1',
        unmappedField2: '未映射字段2',
        cooperationForm: '图文'
      };

      const unmappedFields = this.mappingService.checkUnmappedFields(testDataWithUnmappedFields);

      if (Array.isArray(unmappedFields)) {
        console.log('✅ 未映射字段检查测试通过');
        console.log(`   检查结果: ${unmappedFields.length > 0 ? unmappedFields.join(', ') : '无未映射字段'}`);
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
        this.testResults.errors.push('未映射字段检查测试失败');
      }

      console.log('');
    } catch (error) {
      console.error('❌ 未映射字段检查测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`未映射字段检查测试失败: ${error.message}`);
    }
  }

  /**
   * 测试映射配置获取
   */
  async testMappingConfiguration() {
    try {
      console.log('🔄 测试映射配置获取...');

      const config = this.mappingService.getMappingConfiguration();

      const requiredConfigs = ['cooperationToAgreement', 'cooperationForm', 'publishPlatform', 'seedingPlatform', 'platform'];
      let allConfigsPresent = true;

      for (const configName of requiredConfigs) {
        if (!config[configName]) {
          console.error(`❌ 缺少配置: ${configName}`);
          allConfigsPresent = false;
        }
      }

      if (allConfigsPresent) {
        console.log('✅ 映射配置获取测试通过');
        console.log(`   配置项数量: ${Object.keys(config).length}`);
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
        this.testResults.errors.push('映射配置获取测试失败');
      }

      console.log('');
    } catch (error) {
      console.error('❌ 映射配置获取测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`映射配置获取测试失败: ${error.message}`);
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过: ${this.testResults.passed}`);
    console.log(`❌ 失败: ${this.testResults.failed}`);
    
    if (this.testResults.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    console.log('\n🎉 CRM数据映射服务测试完成!');
  }
}

// 运行测试
if (require.main === module) {
  const test = new CrmDataMappingTest();
  test.runAllTests().catch(console.error);
}

module.exports = CrmDataMappingTest;
