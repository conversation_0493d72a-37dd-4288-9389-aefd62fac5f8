/**
 * CRM日期格式转换测试
 * 验证前端传递的ISO日期格式能正确转换为CRM接受的格式
 */

const CrmDataMappingService = require('../src/services/CrmDataMappingService');

console.log('🧪 开始CRM日期格式转换测试...\n');

// 创建映射服务实例
const mappingService = new CrmDataMappingService();

// 测试数据：模拟前端传递的数据
const testLocalData = {
  title: '测试协议',
  customerName: '测试客户',
  externalCustomerId: 'customer_123',
  actualPublishDate: '2025-07-21T00:00:00.000Z', // 前端传递的ISO格式
  scheduledPublishTime: '2025-07-20T10:30:00.000Z', // 前端传递的ISO格式
  dataRegistrationTime: '2025-07-22T16:45:00.000Z', // 前端传递的ISO格式
  cooperationAmount: '5000',
  publishLink: 'https://example.com/test',
  cooperationNotes: '测试备注'
};

console.log('📋 测试数据（前端传递的格式）:');
console.log(JSON.stringify(testLocalData, null, 2));

console.log('\n🔄 执行mapToCrm转换...');

try {
  // 执行转换
  const crmData = mappingService.mapToCrm(testLocalData, 'agreement');
  
  console.log('\n✅ 转换结果（CRM格式）:');
  console.log(JSON.stringify(crmData, null, 2));
  
  // 验证日期格式
  console.log('\n🎯 日期格式验证:');
  
  const dateFields = [
    { local: 'actualPublishDate', crm: 'contract_date_4' },
    { local: 'scheduledPublishTime', crm: 'contract_end_date' },
    { local: 'dataRegistrationTime', crm: 'contract_date_5' }
  ];
  
  dateFields.forEach(({ local, crm }) => {
    const originalValue = testLocalData[local];
    const convertedValue = crmData[crm];
    
    console.log(`${local}:`);
    console.log(`  原始值: ${originalValue}`);
    console.log(`  转换值: ${convertedValue}`);
    
    // 验证格式是否正确
    const datePattern = /^\d{4}\/\d{2}\/\d{2}$/;
    const isValidFormat = datePattern.test(convertedValue);
    console.log(`  格式验证: ${isValidFormat ? '✅ 通过' : '❌ 失败'}`);
    
    if (isValidFormat) {
      // 验证日期是否正确
      const originalDate = new Date(originalValue);
      const convertedDate = new Date(convertedValue);
      const isSameDate = originalDate.toDateString() === convertedDate.toDateString();
      console.log(`  日期验证: ${isSameDate ? '✅ 正确' : '❌ 错误'}`);
    }
    console.log('');
  });
  
} catch (error) {
  console.error('❌ 转换失败:', error.message);
  console.error(error.stack);
}

// 测试其他日期格式
console.log('\n🔬 测试其他日期格式:');

const otherDateFormats = [
  '2025/07/21',           // 前端可能的格式
  '2025-07-21',           // 另一种格式
  new Date('2025-07-21'), // Date对象
  '2025-07-21 14:30:00',  // 带时间的格式
  null,                   // 空值
  undefined,              // 未定义
  '',                     // 空字符串
  'invalid-date'          // 无效日期
];

otherDateFormats.forEach((dateValue, index) => {
  try {
    const formatted = mappingService.formatDateForCrm(dateValue);
    console.log(`${index + 1}. 输入: ${dateValue} -> 输出: "${formatted}"`);
  } catch (error) {
    console.log(`${index + 1}. 输入: ${dateValue} -> 错误: ${error.message}`);
  }
});

// 测试完整的协议数据同步流程
console.log('\n🚀 模拟完整的协议数据同步流程:');

const fullCooperationData = {
  id: 123,
  title: '小红书-测试博主-日期格式测试-2025.07.21',
  customerName: '测试博主',
  externalCustomerId: 'customer_456',
  cooperationForm: '图文',
  publishPlatform: '小红书',
  cooperationBrand: '测试品牌',
  cooperationProduct: '测试产品',
  cooperationAmount: '8000',
  actualPublishDate: '2025-07-21T00:00:00.000Z',
  scheduledPublishTime: '2025-07-20T10:00:00.000Z',
  dataRegistrationTime: '2025-07-22T18:00:00.000Z',
  publishLink: 'https://www.xiaohongshu.com/explore/test123',
  cooperationNotes: '这是一个日期格式测试协议',
  influencerCommissionRate: '15',
  payeeName: '测试收款人',
  bankAccount: '****************',
  bankName: '测试银行',
  rebateCompleted: '未完成',
  viewCount: 10000,
  likeCount: 500,
  collectCount: 200,
  commentCount: 100
};

try {
  const fullCrmData = mappingService.mapToCrm(fullCooperationData, 'agreement');
  
  console.log('完整协议数据转换成功！');
  console.log('关键字段验证:');
  console.log(`- 协议标题: ${fullCrmData.contract_title}`);
  console.log(`- 客户名称: ${fullCrmData.custom_name}`);
  console.log(`- 客户ID: ${fullCrmData.customer_id}`);
  console.log(`- 实际发布日期: ${fullCrmData.contract_date_4}`);
  console.log(`- 约定发布时间: ${fullCrmData.contract_end_date}`);
  console.log(`- 数据登记时间: ${fullCrmData.contract_date_5}`);
  console.log(`- 合作金额: ${fullCrmData.contract_amount}`);
  console.log(`- 发布链接: ${fullCrmData.contract_url_0}`);
  
} catch (error) {
  console.error('❌ 完整数据转换失败:', error.message);
}

console.log('\n🏁 测试完成！');

// 如果是直接运行此脚本
if (require.main === module) {
  console.log('\n💡 测试结果说明：');
  console.log('1. 前端传递的ISO格式日期（如：2025-07-21T00:00:00.000Z）');
  console.log('2. 应该正确转换为CRM格式（如：2025/07/21）');
  console.log('3. 转换后的日期应该保持相同的日期值');
  console.log('4. 空值和无效日期应该返回空字符串');
  
  console.log('\n🔧 如果测试失败，请检查：');
  console.log('- CrmDataMappingService.formatDateForCrm() 方法');
  console.log('- CrmDataMappingService.isDateField() 方法');
  console.log('- CrmDataMappingService.mapToCrm() 方法中的日期处理逻辑');
}
