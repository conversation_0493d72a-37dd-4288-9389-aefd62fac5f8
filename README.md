# 达人信息管理系统

基于 **Koa2 + MySQL + Sequelize** 构建的现代化达人信息管理系统后端服务，支持多平台达人数据采集、管理和分析。

## ✨ 功能特性

### 🎯 核心功能
- **达人管理**: 完整的达人信息CRUD操作，支持批量导入导出，多维度筛选查询
- **爬虫管理**: 智能爬虫任务调度，实时进度监控，结果分析，支持重试和批量操作
- **Cookie管理**: 多账户Cookie轮换，自动失效检测，使用统计，安全加密存储
- **用户认证**: JWT认证，角色权限控制，会话管理，密码安全策略
- **API文档**: Swagger自动生成，在线测试，中文界面，完整的接口说明

### 🚀 技术亮点
- **高性能后端**: Koa2 + Sequelize ORM + MySQL，支持连接池和事务
- **优雅架构**: MVC模式，模块化设计，清晰的代码结构
- **统一规范**: ESLint + Prettier代码规范，完整的错误处理机制
- **安全可靠**: JWT认证，参数验证，SQL注入防护，敏感信息加密
- **易于维护**: 详细的中文注释，完善的文档，模块化的组织结构
- **开发友好**: 热重载，日志记录，环境配置，调试支持

## 🏗️ 技术栈

### 后端技术栈
- **框架**: Koa2 - 轻量级Node.js Web框架
- **数据库**: MySQL 8.0 - 关系型数据库
- **ORM**: Sequelize - 强大的ORM框架，支持事务和连接池
- **认证**: JWT + bcryptjs - 安全的用户认证和密码加密
- **文档**: Swagger + OpenAPI 3.0 - 自动生成API文档，支持在线测试
- **爬虫**: Axios + 自定义爬虫引擎 - 多平台数据采集
- **加密**: crypto - 敏感数据加密存储
- **验证**: 自定义验证工具 - 数据格式和业务规则验证
- **文件处理**: xlsx - Excel文件导入导出

### 开发工具
- **代码规范**: ESLint + Prettier - 统一代码风格和质量检查
- **版本控制**: Git - 代码版本管理
- **包管理**: npm - 依赖包管理
- **开发调试**: nodemon - 热重载开发服务器
- **环境配置**: dotenv - 环境变量管理
- **API测试**: Swagger UI - 在线API测试界面

### 支持的平台
- **小红书**: 达人数据采集和分析
- **巨量星图**: 达人信息爬取和管理
- **扩展性**: 支持新平台的快速接入

## 📁 项目结构

```
daren-server/
├── src/                    # 源代码目录
│   ├── config/            # 配置文件
│   │   ├── constants.js   # 系统常量定义
│   │   ├── database.js    # 数据库配置
│   │   ├── env.js         # 环境变量管理
│   │   └── swagger.js     # API文档配置
│   ├── controllers/       # 控制器层
│   │   ├── authController.js      # 用户认证控制器
│   │   ├── CookieController.js    # Cookie管理控制器
│   │   ├── CrawlerController.js   # 爬虫控制器
│   │   └── influencerController.js # 达人管理控制器
│   ├── middleware/        # 中间件
│   │   ├── auth.js        # 认证中间件
│   │   ├── errorHandler.js # 错误处理中间件
│   │   ├── logger.js      # 日志中间件
│   │   └── swagger.js     # API文档中间件
│   ├── models/            # 数据模型
│   │   ├── User.js        # 用户模型
│   │   ├── Influencer.js  # 达人模型
│   │   ├── CrawlerCookie.js # Cookie模型
│   │   ├── CrawlTask.js   # 爬虫任务模型
│   │   ├── CrawlResult.js # 爬虫结果模型
│   │   ├── CrawlLog.js    # 爬虫日志模型
│   │   └── index.js       # 模型入口文件
│   ├── modules/           # 功能模块（优化后新增）
│   │   ├── auth/          # 认证模块
│   │   ├── crawler/       # 爬虫模块
│   │   ├── cookie/        # Cookie管理模块
│   │   ├── influencer/    # 达人管理模块
│   │   └── index.js       # 模块入口文件
│   ├── routes/            # 路由定义
│   │   ├── auth.js        # 认证路由
│   │   ├── cookies.js     # Cookie管理路由
│   │   ├── crawler.js     # 爬虫路由
│   │   ├── influencers.js # 达人管理路由
│   │   └── index.js       # 路由入口文件
│   ├── services/          # 业务服务层
│   │   ├── CookieManager.js # Cookie管理服务
│   │   └── crawler/       # 爬虫服务
│   │       ├── index.js           # 爬虫服务入口
│   │       ├── CrawlerManager.js  # 爬虫管理器
│   │       ├── TaskQueue.js       # 任务队列管理
│   │       └── crawlers/          # 具体爬虫实现
│   └── utils/             # 工具函数
│       ├── errors.js      # 自定义错误类（新增）
│       ├── excel.js       # Excel处理工具
│       ├── jwt.js         # JWT工具
│       ├── response.js    # 响应格式工具
│       └── validation.js  # 数据验证工具
├── public/                # 静态文件（简单前端页面）
│   ├── index.html         # 主页
│   ├── login.html         # 登录页
│   ├── cookies.html       # Cookie管理页
│   ├── crawler.html       # 爬虫管理页
│   ├── crawler-results.html # 爬虫结果页
│   ├── css/               # 样式文件
│   └── js/                # 前端脚本
├── docs/                  # 文档目录
│   ├── API.md             # API文档
│   ├── DATABASE.md        # 数据库文档
│   ├── FRONTEND_GUIDE.md  # 前端指南
│   └── CODING_STANDARDS.md # 代码规范（新增）
├── logs/                  # 日志文件
├── sql/                   # SQL脚本
│   └── init.sql           # 数据库初始化脚本
├── .eslintrc.js           # ESLint配置（新增）
├── .prettierrc.js         # Prettier配置（新增）
├── app.js                 # 应用入口文件
├── package.json           # 项目依赖
└── README.md              # 项目说明
```

## 🚀 快速开始

### 环境要求
- **Node.js** >= 16.0.0
- **MySQL** >= 5.7 或 8.0
- **npm** >= 7.0.0 或 **yarn** >= 1.22.0
- **Git** (用于代码管理)

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd daren-server
```

#### 2. 安装依赖
```bash
npm install
# 或者使用 yarn
yarn install
```

#### 3. 环境配置
复制环境变量模板并配置：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键参数：
```env
# 服务器配置
NODE_ENV=development
PORT=3001
HOST=localhost

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=daren_db
DB_USER=root
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Cookie管理配置
COOKIE_ENCRYPTION_KEY=your-secret-key-32-chars-long!!
COOKIE_TEST_MODE=true

# 日志配置
DB_LOGGING=false
LOG_LEVEL=info
```

#### 4. 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE daren_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行初始化脚本（可选）
mysql -u root -p daren_db < sql/init.sql
```

#### 5. 启动服务
```bash
# 开发模式（热重载）
npm run dev

# 生产模式
npm start
```

### 访问应用

启动成功后，您可以访问：

- **API服务**: http://localhost:3001
- **API文档**: http://localhost:3001/api-docs
- **简单前端**: http://localhost:3001/index.html
- **登录页面**: http://localhost:3001/login.html
- **Cookie管理**: http://localhost:3001/cookies.html
- **爬虫管理**: http://localhost:3001/crawler.html

#### 1. 克隆项目
```bash
git clone <repository-url>
cd daren-server
```

#### 2. 后端设置
```bash
# 安装后端依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息

# 创建数据库
mysql -u root -p
CREATE DATABASE daren_system;

# 导入初始化脚本
mysql -u root -p daren_system < sql/init.sql

# 启动后端服务
npm run dev
```

#### 3. 前端设置
```bash
# 进入前端目录
cd frontend

# 安装前端依赖
npm install

# 配置环境变量
cp .env.example .env.development

# 启动前端开发服务器
npm run dev
```

#### 4. 访问应用
- 前端应用: http://localhost:3000
- 后端API: http://localhost:3001
- 默认登录账号: admin / admin123

### 方式二：Docker部署 (推荐)

#### 1. 克隆项目
```bash
git clone <repository-url>
cd daren-server
```

#### 2. 一键启动
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 3. 访问应用
- 前端应用: http://localhost
- 后端API: http://localhost:3001
- MySQL: localhost:3306
- Redis: localhost:6379

## 📖 使用指南

### 功能模块

#### 1. 仪表板
- 数据概览统计
- 实时任务监控
- 快速操作入口
- 最近活动记录

#### 2. 达人管理
- 达人信息维护
- 批量导入导出
- 高级搜索筛选
- 标签分类管理

#### 3. 爬虫管理
- 任务创建配置
- 执行进度监控
- 结果数据查看
- 日志错误追踪

#### 4. Cookie管理
- 账户Cookie维护
- 状态实时监控
- 轮换策略配置
- 使用统计分析

### API接口

详细的API文档请查看 [docs/API.md](docs/API.md)

#### 认证接口
```
POST /api/auth/login      # 用户登录
POST /api/auth/logout     # 用户登出
GET  /api/auth/me         # 获取用户信息
```

#### 达人管理接口
```
GET    /api/influencers           # 获取达人列表
POST   /api/influencers           # 创建达人
PUT    /api/influencers/:id       # 更新达人
DELETE /api/influencers/:id       # 删除达人
GET    /api/influencers/export    # 导出数据
POST   /api/influencers/import    # 导入数据
```

#### 爬虫管理接口
```
GET    /api/crawler/tasks         # 获取任务列表
POST   /api/crawler/tasks         # 创建任务
PUT    /api/crawler/tasks/:id     # 更新任务
DELETE /api/crawler/tasks/:id     # 删除任务
POST   /api/crawler/tasks/:id/start   # 启动任务
POST   /api/crawler/tasks/:id/stop    # 停止任务
```

#### Cookie管理接口
```
GET    /api/cookies               # 获取Cookie列表
POST   /api/cookies               # 创建Cookie
PUT    /api/cookies/:id           # 更新Cookie
DELETE /api/cookies/:id           # 删除Cookie
POST   /api/cookies/:id/test      # 测试Cookie
```

## 🎨 界面预览

### 仪表板
- 📊 数据统计卡片
- 📈 趋势图表展示
- 🔄 实时任务状态
- 📋 最近活动列表

### 达人管理
- 📋 达人信息表格
- 🔍 高级搜索筛选
- ➕ 添加/编辑表单
- 📤 批量导入导出

### 爬虫管理
- 🤖 任务列表管理
- ⚙️ 任务配置界面
- 📊 进度监控面板
- 📝 日志查看器

### Cookie管理
- 🍪 Cookie账户列表
- 🔧 配置管理界面
- 📈 使用统计图表
- 🔄 轮换策略设置

## 🔧 开发指南

### 前端开发
```bash
cd frontend
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run lint         # 代码检查
npm run type-check   # 类型检查
```

### 后端开发
```bash
npm run dev          # 启动开发服务器
npm start            # 启动生产服务器
npm test             # 运行测试
```

### 代码规范
- 使用 ESLint + Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 组件和函数必须添加类型注解
- API 接口遵循 RESTful 设计规范

## 🚀 部署指南

### 生产环境部署
1. 构建前端应用
2. 配置环境变量
3. 启动后端服务
4. 配置反向代理 (Nginx)
5. 设置 SSL 证书

### Docker 部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 停止服务
docker-compose down
```



## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 Wiki: [项目文档](https://github.com/your-repo/wiki)

---

⭐ 如果这个项目对您有帮助，请给我们一个 Star！



# 更新合作记录的时候 用的是updateCooperation方法