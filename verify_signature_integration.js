/**
 * 小红书签名机制集成验证脚本
 * 
 * 使用方法：
 * node verify_signature_integration.js
 */

const fs = require('fs');
const path = require('path');

/**
 * 验证文件是否存在
 */
function verifyFileExists(filePath, description) {
  const fullPath = path.join(__dirname, filePath);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${description}: ${filePath}`);
    return true;
  } else {
    console.error(`❌ ${description}文件不存在: ${filePath}`);
    return false;
  }
}

/**
 * 验证文件内容包含指定字符串
 */
function verifyFileContent(filePath, searchString, description) {
  try {
    const fullPath = path.join(__dirname, filePath);
    const content = fs.readFileSync(fullPath, 'utf8');
    
    if (content.includes(searchString)) {
      console.log(`✅ ${description}: 已找到 "${searchString}"`);
      return true;
    } else {
      console.error(`❌ ${description}: 未找到 "${searchString}"`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 读取文件失败 (${filePath}): ${error.message}`);
    return false;
  }
}

/**
 * 验证签名器可以正常导入和使用
 */
function verifySignerImport() {
  try {
    console.log('\n🧪 验证签名器导入和基础功能...');
    
    const XiaohongshuSigner = require('./src/services/crawler/crawlers/xiaohongshu_signer');
    const signer = new XiaohongshuSigner();
    
    // 测试签名生成
    const signature = signer.generateSignature('/api/test', { test: 'value' });
    
    if (signature && signature['X-s'] && signature['X-t']) {
      console.log('✅ 签名器导入和基础功能正常');
      console.log(`   生成的签名: X-s=${signature['X-s'].substring(0, 20)}..., X-t=${signature['X-t']}`);
      return true;
    } else {
      console.error('❌ 签名器功能异常');
      return false;
    }
  } catch (error) {
    console.error(`❌ 签名器导入失败: ${error.message}`);
    return false;
  }
}

/**
 * 验证爬虫集成
 */
function verifyCrawlerIntegration() {
  try {
    console.log('\n🧪 验证爬虫集成...');
    
    const XiaohongshuCrawler = require('./src/services/crawler/crawlers/XiaohongshuCrawler');
    const crawler = new XiaohongshuCrawler();
    
    // 检查签名器是否已初始化
    if (crawler.signer) {
      console.log('✅ 爬虫中签名器已正确初始化');
      
      // 测试签名机制
      const testResult = crawler.testSignature('/api/test', { test: 'value' });
      
      if (testResult.success) {
        console.log('✅ 爬虫签名机制测试通过');
        return true;
      } else {
        console.error('❌ 爬虫签名机制测试失败:', testResult.error);
        return false;
      }
    } else {
      console.error('❌ 爬虫中签名器未正确初始化');
      return false;
    }
  } catch (error) {
    console.error(`❌ 爬虫集成验证失败: ${error.message}`);
    return false;
  }
}

/**
 * 主验证函数
 */
async function runVerification() {
  console.log('🔍 小红书签名机制集成验证');
  console.log('=' * 50);
  
  let allPassed = true;
  
  // 1. 验证核心文件存在
  console.log('\n📁 验证核心文件存在性...');
  allPassed &= verifyFileExists('src/services/crawler/crawlers/xiaohongshu_signer.js', '签名生成器');
  allPassed &= verifyFileExists('src/services/crawler/crawlers/XiaohongshuCrawler.js', '小红书爬虫');
  allPassed &= verifyFileExists('src/controllers/authorVideoController.js', '作品控制器');
  allPassed &= verifyFileExists('src/controllers/influencerReportController.js', '提报控制器');
  
  // 2. 验证代码集成
  console.log('\n📝 验证代码集成...');
  allPassed &= verifyFileContent(
    'src/services/crawler/crawlers/XiaohongshuCrawler.js',
    'const XiaohongshuSigner = require',
    '爬虫中签名器引用'
  );
  
  allPassed &= verifyFileContent(
    'src/services/crawler/crawlers/XiaohongshuCrawler.js',
    'this.signer = new XiaohongshuSigner()',
    '爬虫中签名器初始化'
  );
  
  allPassed &= verifyFileContent(
    'src/services/crawler/crawlers/XiaohongshuCrawler.js',
    'this.signer.generateSignature',
    '爬虫中签名生成调用'
  );
  
  allPassed &= verifyFileContent(
    'src/controllers/authorVideoController.js',
    'const XiaohongshuCrawler = require',
    '控制器中爬虫引用'
  );
  
  // 3. 验证功能性
  console.log('\n⚙️ 验证功能性...');
  allPassed &= verifySignerImport();
  allPassed &= verifyCrawlerIntegration();
  
  // 4. 验证测试文件
  console.log('\n🧪 验证测试文件...');
  allPassed &= verifyFileExists('test_xiaohongshu_signature.js', '签名机制测试脚本');
  allPassed &= verifyFileExists('docs/XIAOHONGSHU_SIGNATURE_INTEGRATION.md', '集成文档');
  
  // 5. 输出验证结果
  console.log('\n' + '=' * 50);
  if (allPassed) {
    console.log('🎉 所有验证通过！');
    console.log('✅ 小红书签名机制已成功集成到爬虫系统中');
    console.log('');
    console.log('📋 集成状态总结:');
    console.log('   ✅ 签名生成器 (xiaohongshu_signer.js) - 正常');
    console.log('   ✅ 爬虫集成 (XiaohongshuCrawler.js) - 正常');
    console.log('   ✅ 控制器集成 (authorVideoController.js) - 正常');
    console.log('   ✅ 提报控制器 (influencerReportController.js) - 正常');
    console.log('   ✅ 测试脚本 (test_xiaohongshu_signature.js) - 可用');
    console.log('   ✅ 文档 (XIAOHONGSHU_SIGNATURE_INTEGRATION.md) - 完整');
    console.log('');
    console.log('🚀 系统已准备就绪，可以开始使用签名机制进行API调用');
    console.log('');
    console.log('📖 下一步操作:');
    console.log('   1. 运行测试: node test_xiaohongshu_signature.js');
    console.log('   2. 检查Cookie配置: 确保有可用的小红书Cookie');
    console.log('   3. 监控API调用: 观察签名机制的实际效果');
    
  } else {
    console.error('❌ 部分验证失败，请检查上述错误信息');
    console.error('');
    console.error('🔧 可能的解决方案:');
    console.error('   1. 检查文件路径是否正确');
    console.error('   2. 确认所有代码修改已保存');
    console.error('   3. 验证Node.js模块依赖');
    console.error('   4. 检查文件权限');
  }
  
  console.log('=' * 50);
}

// 运行验证
if (require.main === module) {
  runVerification().catch(console.error);
}

module.exports = {
  verifyFileExists,
  verifyFileContent,
  verifySignerImport,
  verifyCrawlerIntegration,
  runVerification
};
