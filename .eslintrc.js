/**
 * ESLint 配置文件
 * 
 * 功能说明：
 * - 统一JavaScript代码风格和质量标准
 * - 检测潜在的代码问题和错误
 * - 强制执行编码规范和最佳实践
 * 
 * 规则说明：
 * - 使用Node.js环境配置
 * - 采用ES2021语法标准
 * - 强制使用分号和单引号
 * - 限制行长度和缩进
 * - 禁止未使用的变量
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

module.exports = {
  env: {
    browser: false,
    commonjs: true,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module'
  },
  rules: {
    // 代码风格规则
    'indent': ['error', 2], // 使用2个空格缩进
    'linebreak-style': ['error', 'unix'], // 使用Unix换行符
    'quotes': ['error', 'single'], // 使用单引号
    'semi': ['error', 'always'], // 强制使用分号
    'max-len': ['warn', { code: 120 }], // 行长度限制120字符
    'no-trailing-spaces': 'error', // 禁止行尾空格
    'eol-last': 'error', // 文件末尾需要换行
    
    // 变量规则
    'no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }], // 禁止未使用的变量
    'no-undef': 'error', // 禁止使用未定义的变量
    'prefer-const': 'error', // 优先使用const
    'no-var': 'error', // 禁止使用var
    
    // 函数规则
    'func-style': ['error', 'declaration', { allowArrowFunctions: true }], // 函数声明风格
    'arrow-spacing': 'error', // 箭头函数空格
    'no-arrow-condition': 'off', // 允许箭头函数条件
    
    // 对象和数组规则
    'object-curly-spacing': ['error', 'always'], // 对象花括号空格
    'array-bracket-spacing': ['error', 'never'], // 数组方括号空格
    'comma-dangle': ['error', 'never'], // 禁止尾随逗号
    'comma-spacing': ['error', { before: false, after: true }], // 逗号空格
    
    // 控制流规则
    'brace-style': ['error', '1tbs'], // 大括号风格
    'keyword-spacing': 'error', // 关键字空格
    'space-before-blocks': 'error', // 块前空格
    'space-before-function-paren': ['error', 'never'], // 函数括号前空格
    
    // 比较和逻辑规则
    'eqeqeq': ['error', 'always'], // 强制使用===
    'no-console': 'off', // 允许console（开发阶段）
    'no-debugger': 'warn', // 警告debugger
    'no-alert': 'warn', // 警告alert
    
    // 异步规则
    'no-async-promise-executor': 'error', // 禁止async Promise执行器
    'prefer-promise-reject-errors': 'error', // Promise reject错误
    
    // Node.js特定规则
    'no-process-exit': 'error', // 禁止process.exit
    'handle-callback-err': 'error', // 处理回调错误
    'no-new-require': 'error', // 禁止new require
    'no-path-concat': 'error', // 禁止路径拼接
    
    // 安全规则
    'no-eval': 'error', // 禁止eval
    'no-implied-eval': 'error', // 禁止隐式eval
    'no-new-func': 'error', // 禁止new Function
    'no-script-url': 'error' // 禁止script URL
  },
  
  // 忽略特定文件的规则
  overrides: [
    {
      files: ['*.test.js', '*.spec.js'],
      rules: {
        'no-unused-expressions': 'off' // 测试文件允许未使用的表达式
      }
    }
  ],
  
  // 全局变量
  globals: {
    process: 'readonly',
    Buffer: 'readonly',
    __dirname: 'readonly',
    __filename: 'readonly',
    module: 'readonly',
    require: 'readonly',
    exports: 'readonly',
    global: 'readonly'
  }
};
