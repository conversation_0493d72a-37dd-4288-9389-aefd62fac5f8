-- CRM字典数据表
-- 用于存储从CRM系统同步的字典数据

-- 删除已存在的表（开发环境）
-- DROP TABLE IF EXISTS `crm_dictionaries`;

-- 创建CRM字典数据表
CREATE TABLE IF NOT EXISTS `crm_dictionaries` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- CRM源数据字段
  `deploy_id` varchar(50) NOT NULL COMMENT 'CRM部署ID（customer/contract）',
  `field_name` varchar(200) NOT NULL COMMENT 'CRM字段名称',
  `field_code` varchar(100) NOT NULL COMMENT 'CRM字段代码',
  `field_dom_type` varchar(50) NOT NULL COMMENT 'CRM字段类型（SELECT等）',
  
  -- 字典项数据字段
  `data_id` varchar(100) NOT NULL COMMENT 'CRM字典项ID',
  `data_name` varchar(200) NOT NULL COMMENT 'CRM字典项显示名称',
  `data_value` varchar(200) DEFAULT NULL COMMENT 'CRM字典项值',
  `data_order` int DEFAULT 0 COMMENT 'CRM字典项排序',
  
  -- 本地映射字段
  `local_category` varchar(100) DEFAULT NULL COMMENT '映射到本地字典的分类',
  `local_key` varchar(100) DEFAULT NULL COMMENT '映射到本地字典的键值',
  `local_label` varchar(200) DEFAULT NULL COMMENT '映射到本地字典的标签',
  
  -- 同步管理字段
  `sync_status` enum('pending','synced','failed','disabled') DEFAULT 'pending' COMMENT '同步状态（待同步/已同步/失败/禁用）',
  `sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `sync_version` varchar(50) DEFAULT NULL COMMENT '同步版本号',
  `sync_error` text COMMENT '同步错误信息',
  
  -- 原始数据字段
  `raw_data` json DEFAULT NULL COMMENT 'CRM原始数据（JSON格式）',
  
  -- 状态管理字段
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否已删除（软删除）',
  
  -- 审计字段
  `created_by` int DEFAULT NULL COMMENT '创建人ID（系统同步时可为空）',
  `updated_by` int DEFAULT NULL COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  
  -- 索引定义
  KEY `idx_deploy_id` (`deploy_id`),
  KEY `idx_field_code` (`field_code`),
  KEY `idx_data_id` (`data_id`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_local_mapping` (`local_category`, `local_key`),
  KEY `idx_query_optimization` (`deploy_id`, `field_code`, `is_active`, `is_deleted`),
  
  -- 唯一索引：确保同一字段下的数据ID唯一
  UNIQUE KEY `idx_unique_field_data` (`deploy_id`, `field_code`, `data_id`)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CRM字典数据表';

-- 插入示例数据（可选）
-- INSERT INTO `crm_dictionaries` (
--   `deploy_id`, `field_name`, `field_code`, `field_dom_type`,
--   `data_id`, `data_name`, `data_value`, `data_order`,
--   `local_category`, `local_key`, `local_label`,
--   `sync_status`, `is_active`
-- ) VALUES 
-- ('customer', '合作形式', 'customer_select_1', 'SELECT', 
--  'form_1', '图文', 'image_text', 1,
--  'cooperation_form', 'image_text', '图文',
--  'synced', 1),
-- ('customer', '合作形式', 'customer_select_1', 'SELECT',
--  'form_2', '视频', 'video', 2,
--  'cooperation_form', 'video', '视频',
--  'synced', 1);

-- 创建视图：活跃的CRM字典数据
CREATE OR REPLACE VIEW `v_active_crm_dictionaries` AS
SELECT 
  `id`,
  `deploy_id`,
  `field_name`,
  `field_code`,
  `field_dom_type`,
  `data_id`,
  `data_name`,
  `data_value`,
  `data_order`,
  `local_category`,
  `local_key`,
  `local_label`,
  `sync_status`,
  `sync_time`,
  `created_at`,
  `updated_at`
FROM `crm_dictionaries`
WHERE `is_active` = 1 AND `is_deleted` = 0
ORDER BY `deploy_id`, `field_code`, `data_order`, `data_name`;

-- 创建视图：按部署类型分组的字典统计
CREATE OR REPLACE VIEW `v_crm_dictionary_stats` AS
SELECT 
  `deploy_id`,
  `field_code`,
  `field_name`,
  COUNT(*) as `total_items`,
  COUNT(CASE WHEN `sync_status` = 'synced' THEN 1 END) as `synced_items`,
  COUNT(CASE WHEN `sync_status` = 'failed' THEN 1 END) as `failed_items`,
  COUNT(CASE WHEN `is_active` = 1 THEN 1 END) as `active_items`,
  MAX(`sync_time`) as `last_sync_time`,
  MAX(`updated_at`) as `last_updated`
FROM `crm_dictionaries`
WHERE `is_deleted` = 0
GROUP BY `deploy_id`, `field_code`, `field_name`
ORDER BY `deploy_id`, `field_code`;
