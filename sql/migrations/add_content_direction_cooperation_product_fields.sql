-- 为达人提报表添加内容方向和合作产品字段
-- 执行时间: 2025-08-01
-- 描述: 为达人提报系统添加内容方向和合作产品字段支持

USE daren_db;

-- 检查当前数据库连接
SELECT 'Connected to database' AS status, DATABASE() AS current_db;

-- 备份提醒
SELECT '⚠️  建议在执行前备份数据库' AS warning;

-- 检查字段是否已存在（避免重复添加）
SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'daren_db' 
  AND TABLE_NAME = 'influencer_reports'
  AND COLUMN_NAME IN ('content_direction', 'cooperation_product');

-- 为达人提报表添加 content_direction 字段（如果不存在）
SET @sql = (
  SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'daren_db' 
       AND TABLE_NAME = 'influencer_reports' 
       AND COLUMN_NAME = 'content_direction') = 0,
    'ALTER TABLE influencer_reports ADD COLUMN content_direction VARCHAR(200) COMMENT ''内容方向'' AFTER selection_reason;',
    'SELECT ''influencer_reports.content_direction field already exists'' AS message;'
  )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为达人提报表添加 cooperation_product 字段（如果不存在）
SET @sql = (
  SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'daren_db' 
       AND TABLE_NAME = 'influencer_reports' 
       AND COLUMN_NAME = 'cooperation_product') = 0,
    'ALTER TABLE influencer_reports ADD COLUMN cooperation_product VARCHAR(200) COMMENT ''合作产品'' AFTER content_direction;',
    'SELECT ''influencer_reports.cooperation_product field already exists'' AS message;'
  )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段添加结果
SELECT 
  '✅ 字段添加完成，验证结果：' AS status;

SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'daren_db' 
  AND TABLE_NAME = 'influencer_reports'
  AND COLUMN_NAME IN ('content_direction', 'cooperation_product')
ORDER BY ORDINAL_POSITION;

-- 显示表结构（可选）
-- DESCRIBE influencer_reports;

SELECT '🎉 达人提报表字段添加完成！' AS completion_message;
