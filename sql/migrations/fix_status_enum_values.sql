-- 修复 public_influencers 表的 status 字段 ENUM 值
-- 添加 'collected' 状态到 ENUM 中
-- 执行时间: 2025-07-22
-- 描述: 修复达人收藏功能中的 status 字段 ENUM 值不匹配问题

USE daren_db;

-- 修改 public_influencers 表的 status 字段，添加 'collected' 状态
ALTER TABLE public_influencers 
MODIFY COLUMN status ENUM('pending', 'processed', 'collected', 'failed', 'imported') 
DEFAULT 'pending' 
COMMENT '处理状态：pending-待处理，processed-已处理，collected-已收藏，failed-失败，imported-已导入';

-- 验证字段修改是否成功
SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  COLUMN_TYPE,
  COLUMN_DEFAULT,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'daren_db' 
  AND TABLE_NAME = 'public_influencers'
  AND COLUMN_NAME = 'status';

-- 显示修改后的表结构
DESCRIBE public_influencers;

SELECT 'status 字段 ENUM 值修复完成' AS status;
