-- 生产环境：添加播放量中位数字段到达人相关表
-- 执行时间: 2025-07-21
-- 描述: 为生产环境的达人管理系统添加播放量中位数字段支持
-- 注意: 此脚本专门用于生产环境数据库迁移

USE daren_db;

-- 检查当前数据库连接
SELECT 'Connected to production database' AS status, DATABASE() AS current_db;

-- 备份提醒
SELECT '⚠️  建议在执行前备份数据库' AS warning;

-- 检查表是否存在
SELECT 
  TABLE_NAME,
  TABLE_ROWS,
  CREATE_TIME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'daren_db' 
  AND TABLE_NAME IN ('my_influencers', 'public_influencers');

-- 检查字段是否已存在（避免重复添加）
SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'daren_db' 
  AND TABLE_NAME IN ('my_influencers', 'public_influencers')
  AND COLUMN_NAME = 'play_mid';

-- 为我的达人表添加 play_mid 字段（如果不存在）
SET @sql = (
  SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'daren_db' 
       AND TABLE_NAME = 'my_influencers' 
       AND COLUMN_NAME = 'play_mid') = 0,
    'ALTER TABLE my_influencers ADD COLUMN play_mid VARCHAR(50) COMMENT ''播放量中位数'' AFTER influencer_tags;',
    'SELECT ''my_influencers.play_mid field already exists'' AS message;'
  )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为达人公海表添加 play_mid 字段（如果不存在）
SET @sql = (
  SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'daren_db' 
       AND TABLE_NAME = 'public_influencers' 
       AND COLUMN_NAME = 'play_mid') = 0,
    'ALTER TABLE public_influencers ADD COLUMN play_mid VARCHAR(50) COMMENT ''播放量中位数'' AFTER influencer_tags;',
    'SELECT ''public_influencers.play_mid field already exists'' AS message;'
  )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段添加结果
SELECT 
  '✅ 字段添加完成，验证结果：' AS status;

SELECT 
  TABLE_NAME,
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'daren_db' 
  AND TABLE_NAME IN ('my_influencers', 'public_influencers')
  AND COLUMN_NAME = 'play_mid';

-- 检查表结构
SELECT '📋 my_influencers 表结构：' AS info;
DESCRIBE my_influencers;

SELECT '📋 public_influencers 表结构：' AS info;
DESCRIBE public_influencers;

-- 统计现有数据
SELECT 
  'my_influencers' AS table_name,
  COUNT(*) AS total_records,
  COUNT(play_mid) AS records_with_play_mid,
  (COUNT(*) - COUNT(play_mid)) AS records_without_play_mid
FROM my_influencers

UNION ALL

SELECT 
  'public_influencers' AS table_name,
  COUNT(*) AS total_records,
  COUNT(play_mid) AS records_with_play_mid,
  (COUNT(*) - COUNT(play_mid)) AS records_without_play_mid
FROM public_influencers;

SELECT '🎉 生产环境播放量中位数字段迁移完成！' AS completion_message;
