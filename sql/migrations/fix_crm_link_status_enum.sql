-- 修复 cooperation_management 表的 crm_link_status 字段 ENUM 值问题
-- 执行时间: 2025-07-25
-- 描述: 修复CRM数据同步中的 crm_link_status 字段值不匹配问题

USE daren_db;

-- 显示当前问题记录
SELECT 'Current invalid crm_link_status values:' AS status;
SELECT 
  id,
  customer_name,
  title,
  crm_link_status,
  external_customer_id,
  external_agreement_id
FROM cooperation_management 
WHERE crm_link_status NOT IN ('unlinked', 'customer_linked', 'fully_linked')
LIMIT 10;

-- 备份当前数据（可选）
-- CREATE TABLE cooperation_management_backup_20250725 AS 
-- SELECT * FROM cooperation_management WHERE crm_link_status NOT IN ('unlinked', 'customer_linked', 'fully_linked');

-- 修复无效的 crm_link_status 值
-- 1. 将 'agreement_linked' 修改为 'fully_linked'（因为有协议数据表示完全关联）
UPDATE cooperation_management 
SET crm_link_status = 'fully_linked' 
WHERE crm_link_status = 'agreement_linked';

-- 2. 将其他无效值根据实际情况修复
-- 如果有外部客户ID但没有外部协议ID，设置为 'customer_linked'
UPDATE cooperation_management 
SET crm_link_status = 'customer_linked' 
WHERE crm_link_status NOT IN ('unlinked', 'customer_linked', 'fully_linked')
  AND external_customer_id IS NOT NULL 
  AND external_customer_id != ''
  AND (external_agreement_id IS NULL OR external_agreement_id = '');

-- 如果既有外部客户ID又有外部协议ID，设置为 'fully_linked'
UPDATE cooperation_management 
SET crm_link_status = 'fully_linked' 
WHERE crm_link_status NOT IN ('unlinked', 'customer_linked', 'fully_linked')
  AND external_customer_id IS NOT NULL 
  AND external_customer_id != ''
  AND external_agreement_id IS NOT NULL 
  AND external_agreement_id != '';

-- 如果没有任何外部ID，设置为 'unlinked'
UPDATE cooperation_management 
SET crm_link_status = 'unlinked' 
WHERE crm_link_status NOT IN ('unlinked', 'customer_linked', 'fully_linked')
  AND (external_customer_id IS NULL OR external_customer_id = '')
  AND (external_agreement_id IS NULL OR external_agreement_id = '');

-- 验证修复结果
SELECT 'After fix - checking for any remaining invalid values:' AS status;
SELECT 
  COUNT(*) as invalid_count,
  GROUP_CONCAT(DISTINCT crm_link_status) as invalid_values
FROM cooperation_management 
WHERE crm_link_status NOT IN ('unlinked', 'customer_linked', 'fully_linked');

-- 显示修复后的状态分布
SELECT 'Status distribution after fix:' AS status;
SELECT 
  crm_link_status,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM cooperation_management), 2) as percentage
FROM cooperation_management 
GROUP BY crm_link_status
ORDER BY count DESC;

-- 显示有CRM关联的记录统计
SELECT 'CRM linked records summary:' AS status;
SELECT 
  'Total records' as type,
  COUNT(*) as count
FROM cooperation_management
UNION ALL
SELECT 
  'Has external_customer_id' as type,
  COUNT(*) as count
FROM cooperation_management 
WHERE external_customer_id IS NOT NULL AND external_customer_id != ''
UNION ALL
SELECT 
  'Has external_agreement_id' as type,
  COUNT(*) as count
FROM cooperation_management 
WHERE external_agreement_id IS NOT NULL AND external_agreement_id != ''
UNION ALL
SELECT 
  'Customer linked' as type,
  COUNT(*) as count
FROM cooperation_management 
WHERE crm_link_status = 'customer_linked'
UNION ALL
SELECT 
  'Fully linked' as type,
  COUNT(*) as count
FROM cooperation_management 
WHERE crm_link_status = 'fully_linked';

SELECT '✅ crm_link_status 字段值修复完成' AS result;
