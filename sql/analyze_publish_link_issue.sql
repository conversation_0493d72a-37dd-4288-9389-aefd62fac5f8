-- 分析publishLink字段问题的SQL脚本
-- 用于诊断CRM数据同步中contract_url_0字段映射问题

-- 1. 统计publishLink字段的数据分布
SELECT 
  '总记录数' as 统计项,
  COUNT(*) as 数量
FROM cooperation_management
UNION ALL
SELECT 
  '有publishLink的记录数' as 统计项,
  COUNT(*) as 数量
FROM cooperation_management 
WHERE publish_link IS NOT NULL AND publish_link != ''
UNION ALL
SELECT 
  '没有publishLink的记录数' as 统计项,
  COUNT(*) as 数量
FROM cooperation_management 
WHERE publish_link IS NULL OR publish_link = ''
UNION ALL
SELECT 
  '有外部协议ID的记录数' as 统计项,
  COUNT(*) as 数量
FROM cooperation_management 
WHERE external_agreement_id IS NOT NULL AND external_agreement_id != ''
UNION ALL
SELECT 
  '有外部协议ID且有publishLink的记录数' as 统计项,
  COUNT(*) as 数量
FROM cooperation_management 
WHERE external_agreement_id IS NOT NULL 
  AND external_agreement_id != ''
  AND publish_link IS NOT NULL 
  AND publish_link != '';

-- 2. 查看有publishLink的记录样例
SELECT 
  '有publishLink的记录样例' as 类型,
  id,
  customer_name,
  title,
  LEFT(publish_link, 50) as publish_link_preview,
  external_agreement_id,
  created_at
FROM cooperation_management 
WHERE publish_link IS NOT NULL AND publish_link != ''
ORDER BY created_at DESC 
LIMIT 5;

-- 3. 查看有外部协议ID但没有publishLink的记录样例
SELECT 
  '有外部协议ID但没有publishLink的记录样例' as 类型,
  id,
  customer_name,
  title,
  publish_link,
  external_agreement_id,
  view_count,
  like_count,
  created_at
FROM cooperation_management 
WHERE external_agreement_id IS NOT NULL 
  AND external_agreement_id != ''
  AND (publish_link IS NULL OR publish_link = '')
ORDER BY created_at DESC 
LIMIT 10;

-- 4. 分析CRM关联状态与publishLink的关系
SELECT 
  crm_link_status,
  COUNT(*) as 记录数,
  COUNT(CASE WHEN publish_link IS NOT NULL AND publish_link != '' THEN 1 END) as 有publishLink数量,
  ROUND(
    COUNT(CASE WHEN publish_link IS NOT NULL AND publish_link != '' THEN 1 END) * 100.0 / COUNT(*), 
    2
  ) as publishLink填充率
FROM cooperation_management 
GROUP BY crm_link_status
ORDER BY 记录数 DESC;

-- 5. 按月份统计publishLink填充情况
SELECT 
  DATE_FORMAT(created_at, '%Y-%m') as 月份,
  COUNT(*) as 总记录数,
  COUNT(CASE WHEN publish_link IS NOT NULL AND publish_link != '' THEN 1 END) as 有publishLink数量,
  COUNT(CASE WHEN external_agreement_id IS NOT NULL AND external_agreement_id != '' THEN 1 END) as CRM同步数量,
  ROUND(
    COUNT(CASE WHEN publish_link IS NOT NULL AND publish_link != '' THEN 1 END) * 100.0 / COUNT(*), 
    2
  ) as publishLink填充率
FROM cooperation_management 
WHERE created_at >= '2024-01-01'
GROUP BY DATE_FORMAT(created_at, '%Y-%m')
ORDER BY 月份 DESC;

-- 6. 查找可能的数据质量问题
-- 检查publishLink格式
SELECT 
  '发布链接格式分析' as 分析项,
  CASE 
    WHEN publish_link LIKE 'https://www.xiaohongshu.com%' THEN '小红书链接'
    WHEN publish_link LIKE 'https://www.iesdouyin.com%' THEN '抖音链接'
    WHEN publish_link LIKE 'https://weibo.com%' THEN '微博链接'
    WHEN publish_link LIKE 'https://www.bilibili.com%' THEN 'B站链接'
    ELSE '其他格式'
  END as 链接类型,
  COUNT(*) as 数量
FROM cooperation_management 
WHERE publish_link IS NOT NULL AND publish_link != ''
GROUP BY 
  CASE 
    WHEN publish_link LIKE 'https://www.xiaohongshu.com%' THEN '小红书链接'
    WHEN publish_link LIKE 'https://www.iesdouyin.com%' THEN '抖音链接'
    WHEN publish_link LIKE 'https://weibo.com%' THEN '微博链接'
    WHEN publish_link LIKE 'https://www.bilibili.com%' THEN 'B站链接'
    ELSE '其他格式'
  END
ORDER BY 数量 DESC;

-- 7. 检查是否有重复的publishLink
SELECT 
  publish_link,
  COUNT(*) as 重复次数,
  GROUP_CONCAT(id) as 记录ID列表
FROM cooperation_management 
WHERE publish_link IS NOT NULL AND publish_link != ''
GROUP BY publish_link
HAVING COUNT(*) > 1
ORDER BY 重复次数 DESC;

-- 8. 建议的修复策略
SELECT 
  '修复建议' as 类型,
  '1. 检查CRM系统中contract_url_0字段是否有数据' as 建议1,
  '2. 确认CRM数据同步时是否正确传递了contract_url_0字段' as 建议2,
  '3. 考虑为没有publishLink的记录提供手动录入功能' as 建议3,
  '4. 在CRM数据映射时添加更详细的日志记录' as 建议4;
