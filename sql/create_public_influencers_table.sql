-- 单独创建 public_influencers 表的脚本
-- 如果表已存在则跳过创建

USE daren_db;

-- 达人公海表（原爬虫结果表）
CREATE TABLE IF NOT EXISTS public_influencers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  task_id INT NOT NULL COMMENT '关联的爬虫任务ID',
  platform ENUM('xiaohongshu', 'juxingtu') NOT NULL COMMENT '平台类型',
  platform_user_id VARCHAR(100) NOT NULL COMMENT '平台用户ID',
  nickname VARCHAR(100) NOT NULL COMMENT '达人昵称',
  avatar_url VARCHAR(500) COMMENT '头像链接',
  followers_count INT DEFAULT 0 COMMENT '粉丝数量',
  city VARCHAR(50) COMMENT '所在城市',
  unique_id VARCHAR(100) COMMENT '平台唯一标识（如抖音号）',
  contact_info JSON COMMENT '联系方式信息（微信、手机等）',
  video_stats JSON COMMENT '视频统计数据（播放量、点赞数等）',
  video_details JSON COMMENT '视频详情数据',
  raw_data JSON COMMENT '原始爬取数据',
  author_ext_info JSON COMMENT '达人扩展信息（来自巨量星图列表接口的动态附加信息）',
  play_mid VARCHAR(50) COMMENT '播放量中位数',
  status ENUM('pending', 'processed', 'imported', 'failed') DEFAULT 'pending' COMMENT '处理状态',
  error_message TEXT COMMENT '错误信息',
  imported_influencer_id INT COMMENT '导入到我的达人表的ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_task_id (task_id),
  INDEX idx_platform (platform),
  INDEX idx_platform_user_id (platform_user_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (task_id) REFERENCES crawl_tasks(id) ON DELETE CASCADE,
  FOREIGN KEY (imported_influencer_id) REFERENCES my_influencers(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='达人公海表';

-- 验证表创建
SELECT 'public_influencers 表创建完成' AS status;

-- 显示表结构
DESCRIBE public_influencers;

-- 显示表的索引
SHOW INDEX FROM public_influencers;
