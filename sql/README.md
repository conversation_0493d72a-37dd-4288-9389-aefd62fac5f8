# 数据库初始化说明

## 📋 概述

本目录包含达人信息管理系统的数据库初始化脚本。系统使用新的表结构设计，直接创建优化后的表名和结构。

## 🗄️ 表结构说明

### 核心表结构

| 表名 | 说明 | 备注 |
|------|------|------|
| `users` | 用户表 | 系统用户管理 |
| `my_influencers` | 我的达人表 | 用户管理的达人信息 |
| `public_influencers` | 达人公海表 | 爬虫获取的达人数据 |
| `crawl_tasks` | 爬虫任务表 | 爬虫任务管理 |
| `crawl_logs` | 爬虫日志表 | 爬虫执行日志 |
| `crawler_cookies` | Cookie管理表 | 爬虫Cookie管理 |
| `author_videos` | 达人视频表 | 达人视频作品数据 |

### 表关系说明

```
users (用户)
├── my_influencers (我的达人) - 通过 created_by 关联
├── crawl_tasks (爬虫任务) - 通过 created_by 关联
└── crawler_cookies (Cookie) - 通过 created_by 关联

crawl_tasks (爬虫任务)
├── public_influencers (达人公海) - 通过 task_id 关联
├── crawl_logs (爬虫日志) - 通过 task_id 关联
└── author_videos (达人视频) - 通过 crawl_task_id 关联

my_influencers (我的达人)
├── author_videos (达人视频) - 通过 author_id 关联
└── public_influencers (达人公海) - 通过 imported_influencer_id 关联
```

## 🚀 初始化步骤

### 1. 创建数据库

```bash
# 方式一：直接执行初始化脚本
mysql -u root -p < sql/init.sql

# 方式二：分步执行
mysql -u root -p
source sql/init.sql;
```

### 2. 验证安装

```sql
-- 检查数据库是否创建成功
SHOW DATABASES LIKE 'daren_db';

-- 检查表是否创建成功
USE daren_db;
SHOW TABLES;

-- 检查默认管理员用户
SELECT username, email, role FROM users WHERE role = 'admin';
```

## 🔧 配置说明

### 默认管理员账户

- **用户名**: `admin`
- **邮箱**: `<EMAIL>`
- **密码**: `password` (请在生产环境中修改)
- **角色**: `admin`

### 数据库配置

- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **存储引擎**: `InnoDB`

## 📊 新表结构特性

### 我的达人表 (my_influencers)

- 包含 `author_ext_info` 字段用于存储扩展信息
- 支持达人报价、标签、内容主题等新字段
- 与视频作品表建立完整关联

### 达人公海表 (public_influencers)

- 存储爬虫获取的达人数据
- 支持导入到我的达人功能
- 包含完整的爬虫任务关联

### 达人视频表 (author_videos)

- 独立存储达人视频作品数据
- 支持多平台视频数据
- 与达人和爬虫任务双重关联

## 🔄 从旧版本升级

如果您从旧版本升级，系统会自动处理表结构变更。新部署的系统直接使用新的表结构，无需额外迁移操作。

## ⚠️ 注意事项

1. **生产环境部署前请修改默认管理员密码**
2. **确保数据库用户具有足够的权限**
3. **建议在生产环境中配置数据库备份策略**
4. **JSON字段需要MySQL 5.7+版本支持**

## 🛠️ 故障排除

### 常见问题

1. **字符集问题**: 确保MySQL配置支持utf8mb4
2. **权限问题**: 确保数据库用户有CREATE、ALTER权限
3. **版本兼容**: 确保MySQL版本5.7+

### 重新初始化

如需重新初始化数据库：

```sql
-- 警告：此操作会删除所有数据
DROP DATABASE IF EXISTS daren_db;
source sql/init.sql;
```

## 📝 更新日志

- **v2.0**: 重构表结构，采用新的命名规范
- **v1.5**: 添加达人视频作品表
- **v1.0**: 初始版本
