-- 字典管理表结构和初始数据
-- 用于合作对接管理系统的下拉选项管理

-- 创建字典表
CREATE TABLE IF NOT EXISTS `dictionaries` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category` varchar(50) NOT NULL COMMENT '字典分类',
  `dict_key` varchar(100) NOT NULL COMMENT '字典键值',
  `dict_label` varchar(200) NOT NULL COMMENT '字典标签',
  `dict_value` varchar(200) DEFAULT NULL COMMENT '字典值',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
  `description` text COMMENT '描述信息',
  `created_by` int NOT NULL COMMENT '创建人ID',
  `updated_by` int NOT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_dict_key` (`dict_key`),
  KEY `idx_status` (`status`),
  KEY `idx_category_status_sort` (`category`,`status`,`sort_order`),
  UNIQUE KEY `idx_category_key_unique` (`category`,`dict_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典管理表';

-- 插入合作形式字典数据
INSERT INTO `dictionaries` (`category`, `dict_key`, `dict_label`, `sort_order`, `created_by`, `updated_by`) VALUES
('cooperation_form', 'paid_promotion', '付费推广', 1, 1, 1),
('cooperation_form', 'product_exchange', '产品置换', 2, 1, 1),
('cooperation_form', 'commission_based', '佣金合作', 3, 1, 1),
('cooperation_form', 'brand_ambassador', '品牌大使', 4, 1, 1),
('cooperation_form', 'event_cooperation', '活动合作', 5, 1, 1);

-- 插入合作品牌字典数据
INSERT INTO `dictionaries` (`category`, `dict_key`, `dict_label`, `sort_order`, `created_by`, `updated_by`) VALUES
('cooperation_brand', 'beauty_skincare', '美妆护肤', 1, 1, 1),
('cooperation_brand', 'fashion_clothing', '时尚服饰', 2, 1, 1),
('cooperation_brand', 'food_beverage', '食品饮料', 3, 1, 1),
('cooperation_brand', 'digital_electronics', '数码电子', 4, 1, 1),
('cooperation_brand', 'home_living', '家居生活', 5, 1, 1),
('cooperation_brand', 'health_fitness', '健康健身', 6, 1, 1),
('cooperation_brand', 'travel_tourism', '旅游出行', 7, 1, 1),
('cooperation_brand', 'education_training', '教育培训', 8, 1, 1),
('cooperation_brand', 'finance_investment', '金融投资', 9, 1, 1),
('cooperation_brand', 'other', '其他', 10, 1, 1);

-- 插入返点完成状态字典数据
INSERT INTO `dictionaries` (`category`, `dict_key`, `dict_label`, `sort_order`, `created_by`, `updated_by`) VALUES
('rebate_status', 'completed', '已完成', 1, 1, 1),
('rebate_status', 'pending', '进行中', 2, 1, 1),
('rebate_status', 'not_applicable', '不适用', 3, 1, 1);

-- 插入内容植入系数字典数据
INSERT INTO `dictionaries` (`category`, `dict_key`, `dict_label`, `sort_order`, `created_by`, `updated_by`) VALUES
('content_implant_coefficient', 'high', '高', 1, 1, 1),
('content_implant_coefficient', 'medium', '中', 2, 1, 1),
('content_implant_coefficient', 'low', '低', 3, 1, 1),
('content_implant_coefficient', 'none', '无', 4, 1, 1);

-- 插入评论区维护系数字典数据
INSERT INTO `dictionaries` (`category`, `dict_key`, `dict_label`, `sort_order`, `created_by`, `updated_by`) VALUES
('comment_maintenance_coefficient', 'excellent', '优秀', 1, 1, 1),
('comment_maintenance_coefficient', 'good', '良好', 2, 1, 1),
('comment_maintenance_coefficient', 'average', '一般', 3, 1, 1),
('comment_maintenance_coefficient', 'poor', '较差', 4, 1, 1);

-- 插入品牌话题包含字典数据
INSERT INTO `dictionaries` (`category`, `dict_key`, `dict_label`, `sort_order`, `created_by`, `updated_by`) VALUES
('brand_topic_included', 'yes', '是', 1, 1, 1),
('brand_topic_included', 'no', '否', 2, 1, 1),
('brand_topic_included', 'partial', '部分', 3, 1, 1);

-- 插入自评字典数据
INSERT INTO `dictionaries` (`category`, `dict_key`, `dict_label`, `sort_order`, `created_by`, `updated_by`) VALUES
('self_evaluation', 'excellent_paid', '优秀付费笔记', 1, 1, 1),
('self_evaluation', 'good_paid', '良好付费笔记', 2, 1, 1),
('self_evaluation', 'average_paid', '一般付费笔记', 3, 1, 1),
('self_evaluation', 'excellent_exchange', '优秀置换爆文', 4, 1, 1),
('self_evaluation', 'good_exchange', '良好置换爆文', 5, 1, 1),
('self_evaluation', 'average_exchange', '一般置换爆文', 6, 1, 1);
