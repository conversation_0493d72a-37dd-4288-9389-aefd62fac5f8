-- 更新客户公海字段默认值
-- 为现有的合作记录设置默认的所属公海ID

-- 更新表结构，添加默认值
ALTER TABLE cooperation_management 
MODIFY COLUMN customer_public_sea VARCHAR(100) 
DEFAULT 'fd18e0d6b1164e7080f0fa91dc43b0d8' 
COMMENT '所属公海';

-- 更新现有记录中为空的customerPublicSea字段
UPDATE cooperation_management 
SET customer_public_sea = 'fd18e0d6b1164e7080f0fa91dc43b0d8' 
WHERE customer_public_sea IS NULL OR customer_public_sea = '';

-- 验证更新结果
SELECT 
  COUNT(*) as total_records,
  COUNT(CASE WHEN customer_public_sea IS NOT NULL AND customer_public_sea != '' THEN 1 END) as records_with_public_sea,
  COUNT(CASE WHEN customer_public_sea = 'fd18e0d6b1164e7080f0fa91dc43b0d8' THEN 1 END) as records_with_default_public_sea
FROM cooperation_management;

-- 显示更新后的示例数据
SELECT 
  id,
  customer_name,
  customer_public_sea,
  created_at
FROM cooperation_management 
ORDER BY created_at DESC 
LIMIT 10;
