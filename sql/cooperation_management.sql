-- 合作对接管理表创建脚本
-- 用于创建合作对接管理相关的数据库表结构

-- 创建合作对接管理表
CREATE TABLE IF NOT EXISTS `cooperation_management` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cooperation_month` varchar(20) DEFAULT NULL COMMENT '合作月份，格式：YYYY-MM',
  `platform` enum('xiaohongshu','douyin','weibo','bilibili') NOT NULL COMMENT '平台类型',
  `blogger_name` varchar(100) NOT NULL COMMENT '博主名称',
  `responsible_person` varchar(50) NOT NULL COMMENT '负责人花名',
  `influencer_homepage` text COMMENT '达人平台主页链接',
  `cooperation_note_link` text COMMENT '合作笔记链接',
  `cooperation_price` varchar(200) DEFAULT NULL COMMENT '合作价格（支持文本描述）',
  `content_direction` varchar(200) DEFAULT NULL COMMENT '内容方向',
  `cooperation_product` varchar(200) DEFAULT NULL COMMENT '合作品',
  `scheduled_publish_time` varchar(100) DEFAULT NULL COMMENT '约定发布时间（字符串格式）',
  `work_progress` varchar(200) DEFAULT NULL COMMENT '工作进度（自由输入）',
  `note_id` varchar(100) DEFAULT NULL COMMENT '笔记ID（从链接自动解析）',
  `note_link_update_time` varchar(50) DEFAULT NULL COMMENT '笔记链接更新时间',
  `scheduled_fetch_time` varchar(50) DEFAULT NULL COMMENT '定时拉取笔记数据时间（更新时间+10天上午9点）',
  `note_data` json DEFAULT NULL COMMENT '完整的笔记数据（API返回的原始数据）',
  `imp_num` int DEFAULT NULL COMMENT '曝光数',
  `like_num` int DEFAULT NULL COMMENT '点赞数',
  `fav_num` int DEFAULT NULL COMMENT '收藏数',
  `cmt_num` int DEFAULT NULL COMMENT '评论数',
  `read_num` int DEFAULT NULL COMMENT '阅读数',
  `share_num` int DEFAULT NULL COMMENT '分享数',
  `follow_cnt` int DEFAULT NULL COMMENT '关注数',
  `data_fetch_status` enum('pending','fetching','success','failed') DEFAULT 'pending' COMMENT '数据拉取状态',
  `data_fetch_time` varchar(50) DEFAULT NULL COMMENT '数据拉取时间',
  `data_fetch_error` text COMMENT '数据拉取错误信息',
  `created_by` int DEFAULT NULL COMMENT '创建人ID',
  `updated_by` int DEFAULT NULL COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_platform` (`platform`),
  KEY `idx_blogger_name` (`blogger_name`),
  KEY `idx_responsible_person` (`responsible_person`),
  KEY `idx_work_progress` (`work_progress`),
  KEY `idx_note_id` (`note_id`),
  KEY `idx_scheduled_fetch_time` (`scheduled_fetch_time`),
  KEY `idx_data_fetch_status` (`data_fetch_status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合作对接管理表';

-- 插入示例数据（可选）
INSERT INTO `cooperation_management` (
  `cooperation_month`,
  `platform`,
  `blogger_name`,
  `responsible_person`,
  `influencer_homepage`,
  `cooperation_note_link`,
  `cooperation_price`,
  `content_direction`,
  `cooperation_product`,
  `scheduled_publish_time`,
  `work_progress`,
  `created_by`,
  `updated_by`
) VALUES
(
  '2024-01',
  'xiaohongshu',
  '测试博主',
  '张三',
  'https://www.xiaohongshu.com/user/profile/123456',
  'https://www.xiaohongshu.com/explore/686e38b60000000011001e15?xsec_token=test',
  '5000元+提成',
  '美妆护肤',
  '面膜产品',
  '2024-01-15 上午10:00',
  '待开始，等待博主回复',
  1,
  1
);

-- 创建索引优化查询性能
CREATE INDEX `idx_cooperation_blogger` ON `cooperation_management` (`cooperation_month`, `blogger_name`);
CREATE INDEX `idx_cooperation_status` ON `cooperation_management` (`work_progress`, `data_fetch_status`);
CREATE INDEX `idx_cooperation_fetch` ON `cooperation_management` (`scheduled_fetch_time`, `data_fetch_status`);

-- 显示表结构
DESCRIBE `cooperation_management`;
