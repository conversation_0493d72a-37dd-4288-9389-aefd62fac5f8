-- 创建数据库
CREATE DATABASE IF NOT EXISTS daren_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE daren_db;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
  email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  role ENUM('admin', 'user') DEFAULT 'user' COMMENT '用户角色',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '用户状态',
  last_login_at DATETIME COMMENT '最后登录时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 我的达人表（原达人信息表）
CREATE TABLE IF NOT EXISTS my_influencers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  nickname VARCHAR(100) NOT NULL COMMENT '达人昵称',
  platform ENUM('xiaohongshu', 'juxingtu') NOT NULL COMMENT '平台类型',
  platform_id VARCHAR(100) COMMENT '平台ID',
  avatar_url VARCHAR(500) COMMENT '头像链接',
  followers_count INT DEFAULT 0 COMMENT '粉丝数量',
  category VARCHAR(50) COMMENT '分类',
  tags JSON COMMENT '标签',
  contact_info JSON COMMENT '联系方式',
  price_info JSON COMMENT '报价信息',
  cooperation_history JSON COMMENT '合作历史',
  video_stats JSON COMMENT '视频统计数据（播放量、点赞数等）',
  author_ext_info JSON COMMENT '达人扩展信息（来自巨量星图列表接口的动态附加信息）',
  content_theme JSON COMMENT '内容主题（从authorExtInfo.content_theme_labels_180d提取）',
  influencer_tags JSON COMMENT '达人标签（从authorExtInfo.tags_relation提取）',
  play_mid VARCHAR(50) COMMENT '播放量中位数',
  notes TEXT COMMENT '备注',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  created_by INT COMMENT '创建者ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_platform (platform),
  INDEX idx_nickname (nickname),
  INDEX idx_followers (followers_count),
  INDEX idx_category (category),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='我的达人表';

-- 爬虫任务表
CREATE TABLE IF NOT EXISTS crawl_tasks (
  id INT PRIMARY KEY AUTO_INCREMENT,
  task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
  platform ENUM('xiaohongshu', 'juxingtu') NOT NULL COMMENT '目标平台',
  keywords VARCHAR(500) NOT NULL COMMENT '搜索关键词',
  config JSON COMMENT '爬虫配置参数（页数、延迟、过滤条件等）',
  status ENUM('pending', 'running', 'paused', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
  priority INT DEFAULT 0 COMMENT '任务优先级（数字越大优先级越高）',
  progress INT DEFAULT 0 COMMENT '进度百分比',
  total_count INT DEFAULT 0 COMMENT '总数量',
  success_count INT DEFAULT 0 COMMENT '成功数量',
  failed_count INT DEFAULT 0 COMMENT '失败数量',
  current_page INT DEFAULT 1 COMMENT '当前爬取页数',
  max_pages INT DEFAULT 5 COMMENT '最大爬取页数',
  error_message TEXT COMMENT '错误信息',
  result_summary JSON COMMENT '结果摘要统计',
  started_at DATETIME COMMENT '开始时间',
  completed_at DATETIME COMMENT '完成时间',
  paused_at DATETIME COMMENT '暂停时间',
  resumed_at DATETIME COMMENT '恢复时间',
  created_by INT COMMENT '创建者ID',
  retry_count INT DEFAULT 0 COMMENT '重试次数',
  max_retries INT DEFAULT 3 COMMENT '最大重试次数',
  last_retry_at DATETIME COMMENT '最后重试时间',
  retry_history JSON COMMENT '重试历史记录',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_status (status),
  INDEX idx_platform (platform),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬虫任务表';

-- 达人公海表（原爬虫结果表）
CREATE TABLE IF NOT EXISTS public_influencers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  task_id INT NOT NULL COMMENT '关联的爬虫任务ID',
  platform ENUM('xiaohongshu', 'juxingtu') NOT NULL COMMENT '平台类型',
  platform_user_id VARCHAR(100) NOT NULL COMMENT '平台用户ID',
  nickname VARCHAR(100) NOT NULL COMMENT '达人昵称',
  avatar_url VARCHAR(500) COMMENT '头像链接',
  followers_count INT DEFAULT 0 COMMENT '粉丝数量',
  city VARCHAR(50) COMMENT '所在城市',
  unique_id VARCHAR(100) COMMENT '平台唯一标识（如抖音号）',
  contact_info JSON COMMENT '联系方式信息（微信、手机等）',
  video_stats JSON COMMENT '视频统计数据（播放量、点赞数等）',
  video_details JSON COMMENT '视频详情数据',
  raw_data JSON COMMENT '原始爬取数据',
  author_ext_info JSON COMMENT '达人扩展信息（来自巨量星图列表接口的动态附加信息）',
  content_theme JSON COMMENT '内容主题（从authorExtInfo.content_theme_labels_180d提取）',
  influencer_tags JSON COMMENT '达人标签（从authorExtInfo.tags_relation提取）',
  play_mid VARCHAR(50) COMMENT '播放量中位数',
  status ENUM('pending', 'processed', 'collected', 'failed', 'imported') DEFAULT 'pending' COMMENT '处理状态',
  error_message TEXT COMMENT '错误信息',
  imported_influencer_id INT COMMENT '收藏到我的达人表的ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_task_id (task_id),
  INDEX idx_platform (platform),
  INDEX idx_platform_user_id (platform_user_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (task_id) REFERENCES crawl_tasks(id) ON DELETE CASCADE,
  FOREIGN KEY (imported_influencer_id) REFERENCES my_influencers(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='达人公海表';

-- 爬虫日志表
CREATE TABLE IF NOT EXISTS crawl_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  task_id INT NOT NULL COMMENT '关联的爬虫任务ID',
  level ENUM('info', 'warn', 'error', 'debug') NOT NULL DEFAULT 'info' COMMENT '日志级别',
  message TEXT NOT NULL COMMENT '日志消息',
  details JSON COMMENT '详细信息（JSON格式）',
  step VARCHAR(100) COMMENT '执行步骤标识',
  url VARCHAR(500) COMMENT '相关URL',
  duration INT COMMENT '执行耗时（毫秒）',
  retry_count INT DEFAULT 0 COMMENT '重试次数',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX idx_task_id (task_id),
  INDEX idx_level (level),
  INDEX idx_step (step),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (task_id) REFERENCES crawl_tasks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬虫日志表';

-- Cookie管理表
CREATE TABLE IF NOT EXISTS crawler_cookies (
  id INT PRIMARY KEY AUTO_INCREMENT,
  account_name VARCHAR(100) NOT NULL COMMENT '账户名称（用于标识）',
  platform ENUM('xiaohongshu', 'juxingtu') NOT NULL COMMENT '平台类型',
  cookie_data TEXT NOT NULL COMMENT '加密后的Cookie数据',
  user_agent VARCHAR(500) COMMENT '对应的User-Agent',
  status ENUM('active', 'inactive', 'expired', 'banned') DEFAULT 'active' COMMENT 'Cookie状态',
  priority INT DEFAULT 1 COMMENT '优先级（数字越大优先级越高）',
  usage_count INT DEFAULT 0 COMMENT '使用次数',
  last_used_at DATETIME COMMENT '最后使用时间',
  expires_at DATETIME COMMENT 'Cookie过期时间',
  cooldown_until DATETIME COMMENT '冷却结束时间',
  notes TEXT COMMENT '备注信息',
  created_by INT COMMENT '创建者ID',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_platform (platform),
  INDEX idx_status (status),
  INDEX idx_priority (priority),
  INDEX idx_last_used_at (last_used_at),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Cookie管理表';

-- 达人视频作品表
CREATE TABLE IF NOT EXISTS author_videos (
  id INT PRIMARY KEY AUTO_INCREMENT,
  author_id INT NOT NULL COMMENT '关联的达人ID（my_influencers表）',
  platform ENUM('xiaohongshu', 'juxingtu', 'douyin') NOT NULL COMMENT '平台类型',
  platform_user_id VARCHAR(100) NOT NULL COMMENT '平台用户ID（星图ID等）',
  video_id VARCHAR(100) NOT NULL COMMENT '视频ID',
  title VARCHAR(500) NOT NULL COMMENT '视频标题',
  video_url VARCHAR(1000) COMMENT '视频链接',
  video_cover VARCHAR(1000) COMMENT '视频封面图片链接',
  duration INT COMMENT '视频时长（秒）',
  publish_time DATETIME COMMENT '发布时间',
  like_count INT DEFAULT 0 COMMENT '点赞数',
  play_count INT DEFAULT 0 COMMENT '播放数',
  share_count INT DEFAULT 0 COMMENT '分享数',
  comment_count INT DEFAULT 0 COMMENT '评论数',
  collect_count INT DEFAULT 0 COMMENT '收藏数',
  tags JSON COMMENT '视频标签',
  description TEXT COMMENT '视频描述',
  location VARCHAR(100) COMMENT '拍摄地点',
  music_info JSON COMMENT '背景音乐信息',
  video_stats JSON COMMENT '视频统计数据（扩展字段）',
  raw_data JSON COMMENT '原始爬取数据',
  status ENUM('active', 'deleted', 'private') DEFAULT 'active' COMMENT '视频状态',
  crawl_task_id INT COMMENT '关联的爬虫任务ID',
  last_updated DATETIME COMMENT '数据最后更新时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_author_id (author_id),
  INDEX idx_platform (platform),
  INDEX idx_platform_user_id (platform_user_id),
  INDEX idx_video_id (video_id),
  INDEX idx_publish_time (publish_time),
  INDEX idx_play_count (play_count),
  INDEX idx_like_count (like_count),
  INDEX idx_crawl_task_id (crawl_task_id),
  UNIQUE INDEX unique_platform_user_video (platform, platform_user_id, video_id),
  INDEX idx_author_publish_time (author_id, publish_time),
  FOREIGN KEY (author_id) REFERENCES my_influencers(id) ON DELETE CASCADE,
  FOREIGN KEY (crawl_task_id) REFERENCES crawl_tasks(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='达人视频作品表';

-- 插入默认管理员用户
INSERT INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin')
ON DUPLICATE KEY UPDATE username = username;
