# 小红书爬虫实现文档

## 📋 实现概述

本文档详细说明了小红书爬虫功能的完整实现，包括真实API调用、数据处理、错误处理等核心功能。

## 🚀 主要功能

### 1. 真实API调用
- **达人列表接口**: `https://pgy.xiaohongshu.com/api/solar/cooperator/blogger/v2`
- **达人详情接口**: `https://pgy.xiaohongshu.com/api/solar/kol/data_v3/notes_rate`
- 支持POST和GET请求方式
- 完整的请求头和参数配置

### 2. Cookie管理
- 集成CookieManager进行Cookie管理
- 支持Cookie轮换和失效检测
- 自动刷新和重试机制
- 与巨量星图爬虫相同的Cookie管理策略

### 3. 数据处理
- 达人基本信息解析和映射
- 笔记数据统计和转换
- 粉丝数量格式化（支持万、k等单位）
- 扩展信息提取和结构化

### 4. 批量处理
- 分页爬取支持
- 批量处理达人信息（每批3个）
- 进度回调和状态更新
- 完整的错误处理机制

## 🔧 技术实现

### 核心方法

#### `crawl(config, callbacks)`
主要爬取方法，支持：
- 分页爬取控制
- 进度回调
- 结果回调
- 错误处理

#### `fetchAuthorList(keyword, page, pageSize)`
获取达人列表：
- 调用小红书达人搜索API：`/api/solar/cooperator/blogger/v2`
- 真实数据结构解析：`response.data.kols[]`
- 完整的字段映射和数据转换
- 错误处理和重试机制

#### `transformKolData(kol)`
转换小红书API数据：
- 映射API字段到标准结构
- 处理价格信息（picturePrice、videoPrice、lowerPrice）
- 提取统计数据（businessNoteCount、clickMidNum、interMidNum）
- 解析标签信息（personalTags、contentTags、featureTags）

#### `getAuthorDetail(authorId, author, options)`
获取达人详细信息：
- 调用达人笔记API：`/api/solar/kol/data_v3/notes_rate`
- 使用API返回的丰富数据构建完整对象
- 集成小红书特有的统计指标
- 支持视频数据保存

#### `processAuthorsInBatches(authors, results, callbacks, options)`
批量处理达人：
- 并发控制（每批3个）
- 批次间延迟
- 详细的成功信息显示（粉丝数、报价等）
- 错误隔离处理

### 反爬虫措施

```javascript
// 防爬虫配置
this.antiCrawlerConfig = {
  minDelay: 2000,      // 最小延迟2秒
  maxDelay: 5000,      // 最大延迟5秒
  maxRetries: 3,       // 最大重试3次
  retryDelay: 3000,    // 重试延迟3秒
  requestTimeout: 15000 // 请求超时15秒
};
```

- 随机延迟机制
- User-Agent轮换
- 请求重试策略
- Cookie自动切换

## 📊 数据库适配

### 表结构支持
- `public_influencers`表已支持小红书平台
- 所有必要字段已包含
- JSON字段存储扩展信息
- 完整的索引和约束

### 数据映射

#### API字段映射
```javascript
// 小红书API字段 -> 标准字段
userId -> platformUserId
name -> nickname
headPhoto -> avatarUrl
location -> city
fansNum -> followersCount (经过parseFollowerCount处理)
```

#### 完整数据结构
```javascript
const authorDetail = {
  platform: 'xiaohongshu',
  platformUserId: authorId,
  nickname: author.nickname,
  avatarUrl: author.avatar,
  followersCount: this.parseFollowerCount(author.fansCount),
  city: author.city,
  uniqueId: authorId,
  contactInfo: { wechat: null, phone: null },
  videoStats: {
    videoCount, averagePlay, totalPlay, totalLike, totalComment, totalShare,
    // 小红书特有统计
    businessNoteCount, clickMidNum, interMidNum
  },
  rawData: {
    authorInfo: author.rawData,
    notesInfo,
    transformedData: { priceInfo, statsInfo, tagsInfo }
  },
  authorExtInfo: {
    tags_relation: [...personalTags, ...featureTags],
    content_theme_labels_180d: contentTags,
    price_info: { picture_price, video_price, lower_price },
    stats_info: { business_note_count, click_mid_num, inter_mid_num },
    personal_tags, content_tags, feature_tags,
    price_20_60: videoPrice || lowerPrice
  }
};
```

## 🔄 集成状态

### CrawlerManager更新
- 已注册小红书爬虫实例
- 添加Cookie可用性检查
- 支持小红书任务执行
- 完整的错误处理流程

### 视频数据保存
- 集成AuthorVideoService
- 支持笔记数据保存到author_videos表
- 自动关联爬虫任务ID
- 数据去重和更新机制

## 🧪 测试验证

### 测试脚本
提供了`test_xiaohongshu_crawler.js`测试脚本：
- 基础功能测试
- 数据解析验证
- 方法调用测试
- 错误处理验证

### 运行测试
```bash
node test_xiaohongshu_crawler.js
```

## 📝 使用说明

### 1. 添加Cookie
在Cookie管理界面添加小红书Cookie：
- 平台选择：xiaohongshu
- 填入有效的Cookie字符串
- 设置User-Agent
- 配置优先级和使用限制

### 2. 创建爬虫任务
- 平台选择：小红书
- 设置搜索关键词
- 配置爬取页数
- 启动任务执行

### 3. 查看结果
- 在达人公海查看爬取结果
- 支持数据导出和批量导入
- 查看详细的笔记统计信息

## ⚠️ 注意事项

### 1. API接口
- 小红书API可能需要特殊认证
- 接口参数可能需要根据实际情况调整
- 建议先测试接口可用性

### 2. Cookie要求
- 需要有效的小红书登录Cookie
- Cookie应包含必要的认证信息
- 定期更新Cookie以保持有效性

### 3. 数据质量
- 部分达人可能没有详细信息
- 笔记数据可能不完整
- 建议设置合理的重试和容错机制

## 🆕 最新更新 (基于真实API数据结构)

### 数据处理优化

#### 1. 真实API字段映射
根据小红书API的实际响应结构，完善了字段映射：

```javascript
// 小红书API真实字段
{
  userId: "5f8a9b2c3d4e5f6789012345",
  name: "美妆达人小红",
  headPhoto: "https://sns-avatar-qc.xhscdn.com/avatar/...",
  location: "上海",
  fansNum: 156789,
  picturePrice: "500-800",
  videoPrice: "1000-1500",
  lowerPrice: "500",
  businessNoteCount: 45,
  clickMidNum: 12500,
  interMidNum: 8900,
  totalNoteCount: 234,
  personalTags: ["美妆博主", "护肤达人"],
  contentTags: ["美妆教程", "护肤心得"],
  featureTags: ["高颜值", "专业"]
}
```

#### 2. 增强的数据转换
- **transformKolData()**: 新增专门的数据转换方法
- **parseTagsArray()**: 智能标签解析，支持数组和JSON字符串
- **extractAuthorExtInfoFromKol()**: 基于真实数据的扩展信息提取
- **formatNumber()**: 数字格式化显示（万、k等单位）

#### 3. 完善的错误处理
- 数据转换失败时的容错机制
- 字段缺失时的默认值处理
- 详细的日志输出和错误追踪

#### 4. 丰富的统计信息
新增小红书特有的统计指标：
- 商业笔记数量 (businessNoteCount)
- 平均点击数 (clickMidNum)
- 平均互动数 (interMidNum)
- 总笔记数量 (totalNoteCount)

## 🔮 后续优化

### 1. API接口完善
- ✅ 已根据实际API响应调整数据解析
- ✅ 优化了字段映射和数据转换
- 继续增加更多的错误处理场景

### 2. 数据增强
- ✅ 添加了丰富的达人属性字段
- ✅ 支持详细的价格和统计信息
- ✅ 增加了数据质量验证

### 3. 性能优化
- 优化并发处理策略
- 改进缓存机制
- 减少不必要的API调用

## 📞 技术支持

如有问题或需要进一步优化，请联系开发团队。
