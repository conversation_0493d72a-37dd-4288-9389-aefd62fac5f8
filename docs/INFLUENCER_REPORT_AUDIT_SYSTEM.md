# 达人提报审核流程管理系统

## 📋 概述

本文档介绍达人提报管理系统的全面改造，实现了完整的审核流程管理，包括权限分级、审核意见管理、重新提报功能等。

## 🎯 改造目标

### 1. 操作按钮权限分级
- **所有用户默认操作**：查看详情、编辑、删除
- **管理员专属操作**：审核通过、审核拒绝、需二次确认
- **普通用户特殊操作**：重新提报（仅限被拒绝的提报）

### 2. 审核意见管理
- 审核操作必须填写审核意见
- 审核意见与操作时间、操作人员关联存储
- 在提报列表和详情中显示审核意见

### 3. 重新提报功能
- 被拒绝的提报可以重新提报
- 重新提报需要填写说明/理由
- 重新提报后状态自动变更为"审核中"

### 4. 提报状态系统
- **审核中** (pending)：新提报或重新提报的状态
- **审核通过** (approved)：管理员审核通过
- **审核拒绝** (rejected)：管理员审核拒绝，可重新提报
- **需二次确认** (need_confirmation)：需要进一步确认的状态

### 5. 平台用户ID展示
- 在提报表单和列表中显示平台用户ID
- 用于统一标识达人身份

## 🔧 技术实现

### 数据库改造

#### 新增字段
```sql
-- 审核意见
review_comment TEXT COMMENT '审核意见'

-- 审核人员ID
reviewed_by INT COMMENT '审核人员ID'

-- 审核时间
reviewed_at DATETIME COMMENT '审核时间'

-- 重新提报说明
resubmit_reason TEXT COMMENT '重新提报说明/理由'

-- 重新提报次数
resubmit_count INT DEFAULT 0 COMMENT '重新提报次数'
```

#### 状态枚举更新
```sql
status ENUM('pending', 'approved', 'rejected', 'need_confirmation') 
DEFAULT 'pending' 
COMMENT '审核状态：审核中/审核通过/审核拒绝/需二次确认'
```

#### 新增索引
```sql
-- 审核人员索引
CREATE INDEX idx_reviewed_by ON influencer_reports (reviewed_by);

-- 审核时间索引
CREATE INDEX idx_reviewed_at ON influencer_reports (reviewed_at);

-- 复合查询索引
CREATE INDEX idx_review_query ON influencer_reports (status, reviewed_by, reviewed_at);
```

### 后端API扩展

#### 新增审核接口
- `PUT /api/influencer-reports/:id/approve` - 审核通过
- `PUT /api/influencer-reports/:id/reject` - 审核拒绝
- `PUT /api/influencer-reports/:id/need-confirmation` - 需二次确认
- `PUT /api/influencer-reports/:id/resubmit` - 重新提报

#### 权限控制
- 审核操作需要管理员权限 (`requireAdmin` 中间件)
- 重新提报只能由提报人操作
- 编辑和删除权限：提报人或管理员

#### 数据关联
- 提报记录关联提报人 (`submitter`)
- 提报记录关联审核人 (`reviewer`)
- 查询时自动包含用户信息

### 前端功能实现

#### 权限控制
```javascript
// 管理员可见审核按钮
<template v-if="userStore.isAdmin">
  <a-link @click="showReviewModal(record, 'approve')">审核通过</a-link>
  <a-link @click="showReviewModal(record, 'reject')">审核拒绝</a-link>
  <a-link @click="showReviewModal(record, 'need_confirmation')">需二次确认</a-link>
</template>

// 普通用户可见重新提报按钮
<template v-if="!userStore.isAdmin">
  <a-link 
    v-if="record.status === 'rejected' && record.submittedBy === userStore.user?.id"
    @click="showResubmitModal(record)"
  >
    重新提报
  </a-link>
</template>
```

#### 审核意见弹窗
- 表单验证确保审核意见必填
- 不同操作显示不同的确认提示
- 支持最大500字符限制和字数统计

#### 重新提报弹窗
- 重新提报说明必填
- 操作确认提示
- 自动更新状态和刷新列表

#### 表格优化
- 新增"审核意见"列
- 新增"平台用户ID"列
- 状态筛选支持新的四种状态
- 操作列宽度调整为280px

## 🚀 使用指南

### 管理员操作流程

1. **查看待审核提报**
   - 登录系统，进入达人提报管理页面
   - 筛选状态为"审核中"的提报

2. **执行审核操作**
   - 点击"审核通过"、"审核拒绝"或"需二次确认"按钮
   - 在弹窗中填写审核意见（必填）
   - 确认提交，系统自动更新状态和记录审核信息

3. **查看审核历史**
   - 在提报详情中查看审核意见、审核人、审核时间
   - 在列表中直接查看审核意见列

### 普通用户操作流程

1. **提交提报**
   - 填写提报信息，状态自动设为"审核中"
   - 等待管理员审核

2. **处理被拒绝的提报**
   - 查看审核意见了解拒绝原因
   - 点击"重新提报"按钮
   - 填写重新提报说明
   - 提交后状态重新变为"审核中"

3. **查看提报状态**
   - 实时查看提报的审核状态
   - 查看审核意见和重新提报历史

## 📊 状态流转图

```
新提报 → 审核中 (pending)
                ↓
        ┌─────────────────┐
        ↓                 ↓
    审核通过          审核拒绝
   (approved)        (rejected)
                         ↓
                    重新提报
                         ↓
                    审核中 (pending)
                         ↓
                 需二次确认 (need_confirmation)
```

## 🔒 安全考虑

### 权限控制
- 审核操作严格限制管理员权限
- 重新提报只能由原提报人操作
- 所有操作都有用户身份验证

### 数据完整性
- 审核意见必填验证
- 状态变更日志记录
- 操作时间和操作人记录

### 业务逻辑
- 只有"审核中"状态的提报可以被审核
- 只有"审核拒绝"状态的提报可以重新提报
- 重新提报会清空之前的审核信息

## 🧪 测试验证

### 功能测试
1. **权限测试**
   - 管理员登录：可见审核按钮
   - 普通用户登录：可见重新提报按钮（仅限被拒绝的自己的提报）

2. **审核流程测试**
   - 审核通过：状态变更、记录审核信息
   - 审核拒绝：状态变更、支持重新提报
   - 需二次确认：状态变更、等待进一步处理

3. **重新提报测试**
   - 被拒绝提报可以重新提报
   - 重新提报后状态变为"审核中"
   - 重新提报次数正确累计

### 数据验证
- 审核意见正确存储和显示
- 用户关联信息正确
- 时间戳准确记录
- 状态变更历史完整

## 📝 注意事项

1. **数据迁移**
   - 运行迁移脚本前请备份数据库
   - 现有数据状态会自动映射到新状态

2. **向后兼容**
   - 保留了旧状态枚举值以确保兼容性
   - 逐步迁移现有数据到新状态

3. **性能优化**
   - 添加了必要的数据库索引
   - 查询时使用关联查询减少数据库访问

4. **用户体验**
   - 操作确认提示清晰
   - 错误信息友好
   - 实时状态更新

## 🔄 后续优化建议

1. **通知系统**
   - 审核结果邮件通知
   - 站内消息提醒

2. **批量操作**
   - 批量审核功能
   - 批量状态变更

3. **审核流程**
   - 多级审核流程
   - 审核委派功能

4. **数据分析**
   - 审核效率统计
   - 提报质量分析
