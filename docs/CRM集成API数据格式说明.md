# CRM集成API数据格式说明

## 问题描述

前端在调用CRM集成相关API时，发现后端返回的数据格式存在问题，特别是 `tokenValid` 字段返回 `null` 值，导致前端状态显示异常。

## 当前问题

### 1. 系统状态API (`/api/crm-integration/status`)

**当前返回的数据格式：**
```json
{
  "success": true,
  "data": {
    "tokenValid": null,           // ❌ 问题：应该是布尔值，不应该是 null
    "customerCount": 0,
    "lastTokenRefresh": "2025-07-25T05:08:21.181Z",
    "nextTokenRefresh": "2025-07-25T07:08:21.181Z",
    "queueLength": 0,
    "isProcessingQueue": false
  }
}
```

**期望的数据格式：**
```json
{
  "success": true,
  "data": {
    "tokenValid": true,           // ✅ 应该是明确的布尔值 true/false
    "customerCount": 150,         // 实际的客户数量
    "lastTokenRefresh": "2025-07-25T05:08:21.181Z",
    "nextTokenRefresh": "2025-07-25T07:08:21.181Z",
    "queueLength": 3,             // 实际的队列长度
    "isProcessingQueue": false
  }
}
```

## 字段说明

### tokenValid 字段
- **类型**: `boolean`
- **必需**: 是
- **说明**: 表示当前CRM Token是否有效
- **可能值**:
  - `true`: Token有效，可以正常调用CRM API
  - `false`: Token无效，需要刷新或重新获取
- **❌ 不应该的值**: `null`、`undefined`、字符串等

### customerCount 字段
- **类型**: `number`
- **必需**: 是
- **说明**: 当前CRM系统中的客户总数
- **默认值**: 如果无法获取，应返回 `0`

### lastTokenRefresh 字段
- **类型**: `string` (ISO 8601 日期格式) 或 `null`
- **必需**: 否
- **说明**: 上次Token刷新的时间
- **格式**: `"2025-07-25T05:08:21.181Z"`

### nextTokenRefresh 字段
- **类型**: `string` (ISO 8601 日期格式) 或 `null`
- **必需**: 否
- **说明**: 下次Token刷新的预计时间
- **格式**: `"2025-07-25T07:08:21.181Z"`

### queueLength 字段
- **类型**: `number`
- **必需**: 是
- **说明**: 当前请求队列中的任务数量
- **默认值**: `0`

### isProcessingQueue 字段
- **类型**: `boolean`
- **必需**: 是
- **说明**: 是否正在处理队列中的任务
- **可能值**: `true` 或 `false`

## 其他API的数据格式要求

### 2. 测试连接API (`/api/crm-integration/test-connection`)

**期望返回格式：**
```json
{
  "success": true,
  "message": "CRM连接测试成功",
  "data": {
    "customerCount": 150,         // 可选：连接成功时返回的客户数量
    "responseTime": 234           // 可选：响应时间（毫秒）
  }
}
```

**失败时返回格式：**
```json
{
  "success": false,
  "message": "连接失败：Token已过期",
  "error": "TOKEN_EXPIRED"        // 可选：错误代码
}
```

### 3. 刷新Token API (`/api/crm-integration/refresh-token`)

**期望返回格式：**
```json
{
  "success": true,
  "message": "Token刷新成功",
  "data": {
    "tokenValid": true,
    "expiresAt": "2025-07-25T07:08:21.181Z"
  }
}
```

## 前端处理逻辑

### 当前前端的修复

为了兼容后端返回 `tokenValid: null` 的情况，前端已经做了以下修复：

1. **状态计算逻辑**：
   ```javascript
   const connectionStatus = computed(() => {
     const isConnected = systemStatus.tokenValid === true;
     const hasTokenInfo = systemStatus.tokenValid !== null;
     
     return {
       connected: isConnected,
       text: isConnected ? '已连接' : (hasTokenInfo ? '未连接' : '检测中'),
       class: isConnected ? 'success' : (hasTokenInfo ? 'error' : 'warning')
     };
   });
   ```

2. **数据处理逻辑**：
   ```javascript
   const loadSystemStatus = async () => {
     const response = await crmIntegrationAPI.getSystemStatus();
     if (response.success) {
       const statusData = { ...response.data };
       
       // 如果 tokenValid 是 null，根据其他信息判断
       if (statusData.tokenValid === null) {
         statusData.tokenValid = !!statusData.lastTokenRefresh;
       }
       
       Object.assign(systemStatus, statusData);
     }
   };
   ```

### 建议的后端修复

建议后端在以下情况下返回明确的 `tokenValid` 值：

1. **Token有效时**: `tokenValid: true`
2. **Token无效时**: `tokenValid: false`
3. **未知状态时**: 应该进行Token验证，然后返回明确的布尔值

## 测试建议

### 1. 正常流程测试
- 确保新用户首次访问时，`tokenValid` 返回 `false`
- 确保测试连接成功后，`tokenValid` 返回 `true`
- 确保Token过期后，`tokenValid` 返回 `false`

### 2. 边界情况测试
- CRM服务不可用时的返回值
- Token刷新失败时的返回值
- 网络异常时的错误处理

### 3. 数据一致性测试
- `tokenValid` 与 `lastTokenRefresh` 的逻辑一致性
- `customerCount` 的实时性和准确性
- `queueLength` 的实时更新

## 总结

主要问题是 `tokenValid` 字段返回 `null` 而不是明确的布尔值。建议后端：

1. **立即修复**: 确保 `tokenValid` 始终返回 `true` 或 `false`
2. **数据验证**: 在返回数据前验证Token的实际状态
3. **错误处理**: 提供清晰的错误信息和状态码
4. **文档更新**: 更新API文档，明确各字段的类型和含义

前端已经做了兼容性处理，但建议后端尽快修复以确保数据的准确性和一致性。
