# 达人管理系统 - 部署指南

## 📋 概述

本文档详细说明了达人管理系统的部署流程，包括开发环境、测试环境和生产环境的部署方案。

## 🎯 部署环境

### 环境分类
- **开发环境** (Development): 本地开发使用
- **测试环境** (Staging): 功能测试和集成测试
- **生产环境** (Production): 正式运行环境

### 服务器要求

#### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 50GB SSD
- **网络**: 10Mbps带宽

#### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **网络**: 100Mbps带宽

## 🛠️ 环境准备

### 1. 系统环境
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y curl wget git

# CentOS/RHEL
sudo yum update
sudo yum install -y curl wget git
```

### 2. Node.js安装
```bash
# 使用NodeSource仓库安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 3. MySQL安装
```bash
# Ubuntu/Debian
sudo apt install -y mysql-server mysql-client

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

### 4. PM2安装（生产环境）
```bash
# 全局安装PM2
sudo npm install -g pm2

# 设置开机自启
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

## 🚀 部署流程

### 方式一：手动部署

#### 1. 代码部署
```bash
# 克隆代码
git clone <repository-url> /opt/daren-server
cd /opt/daren-server

# 安装依赖
npm ci --production

# 设置文件权限
sudo chown -R $USER:$USER /opt/daren-server
chmod +x /opt/daren-server/app.js
```

#### 2. 环境配置
```bash
# 创建生产环境配置
cp .env.example .env.production

# 编辑生产环境配置
nano .env.production
```

生产环境配置示例：
```env
# 生产环境配置
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=daren_db_prod
DB_USER=daren_user
DB_PASSWORD=secure_password_here

# JWT配置（必须修改）
JWT_SECRET=your-super-secure-jwt-secret-key-for-production
JWT_EXPIRES_IN=7d

# Cookie管理配置
COOKIE_ENCRYPTION_KEY=your-32-character-encryption-key-here
COOKIE_TEST_MODE=false

# 日志配置
DB_LOGGING=false
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_MAX=1000
```

#### 3. 数据库初始化
```bash
# 创建数据库用户
mysql -u root -p << EOF
CREATE DATABASE daren_db_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'daren_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON daren_db_prod.* TO 'daren_user'@'localhost';
FLUSH PRIVILEGES;
EOF

# 运行数据库迁移
NODE_ENV=production npm run db:migrate
```

#### 4. 启动服务
```bash
# 使用PM2启动
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save
```

### 方式二：Docker部署

#### 1. 创建Dockerfile
```dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 设置文件权限
RUN chown -R nodejs:nodejs /app
USER nodejs

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/health || exit 1

# 启动命令
CMD ["node", "app.js"]
```

#### 2. 创建docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    depends_on:
      - mysql
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    networks:
      - daren-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: daren_db_prod
      MYSQL_USER: daren_user
      MYSQL_PASSWORD: secure_password_here
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - daren-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - daren-network

volumes:
  mysql_data:

networks:
  daren-network:
    driver: bridge
```

#### 3. 部署命令
```bash
# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

## 🔧 Nginx配置

### 1. 基础配置
```nginx
upstream daren_backend {
    server 127.0.0.1:3001;
}

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态文件
    location /static/ {
        alias /opt/daren-server/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://daren_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 前端页面
    location / {
        proxy_pass http://daren_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 监控和日志

### 1. PM2监控
```bash
# 查看进程状态
pm2 status

# 查看日志
pm2 logs

# 监控面板
pm2 monit

# 重启服务
pm2 restart daren-server
```

### 2. 日志管理
```bash
# 创建日志目录
mkdir -p /opt/daren-server/logs

# 设置日志轮转
sudo nano /etc/logrotate.d/daren-server
```

日志轮转配置：
```
/opt/daren-server/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nodejs nodejs
    postrotate
        pm2 reload daren-server
    endscript
}
```

### 3. 系统监控
```bash
# 安装htop
sudo apt install htop

# 监控系统资源
htop

# 监控磁盘使用
df -h

# 监控内存使用
free -h
```

## 🔐 安全配置

### 1. 防火墙设置
```bash
# 启用UFW防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 查看防火墙状态
sudo ufw status
```

### 2. SSL证书
```bash
# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 数据库安全
```bash
# 修改MySQL默认端口
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
# 修改 port = 3307

# 禁用远程root登录
mysql -u root -p
mysql> DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
mysql> FLUSH PRIVILEGES;
```

## 🔄 更新和维护

### 1. 应用更新
```bash
# 拉取最新代码
cd /opt/daren-server
git pull origin main

# 安装新依赖
npm ci --production

# 运行数据库迁移
NODE_ENV=production npm run db:migrate

# 重启服务
pm2 restart daren-server
```

### 2. 数据备份
```bash
# 创建备份脚本
nano /opt/scripts/backup.sh
```

备份脚本内容：
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups"
DB_NAME="daren_db_prod"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -u daren_user -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/db_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_DIR/db_backup_$DATE.sql.gz"
```

### 3. 定期维护
```bash
# 设置定期备份
sudo crontab -e
# 添加以下行：
# 0 2 * * * /opt/scripts/backup.sh

# 设置日志清理
# 0 3 * * 0 find /opt/daren-server/logs -name "*.log" -mtime +30 -delete
```

## 🚨 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口占用
sudo netstat -tlnp | grep :3001

# 检查日志
pm2 logs daren-server

# 检查配置文件
node -c app.js
```

#### 2. 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -u daren_user -p -h localhost daren_db_prod

# 检查防火墙
sudo ufw status
```

#### 3. 内存不足
```bash
# 检查内存使用
free -h

# 重启服务释放内存
pm2 restart daren-server

# 增加swap空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

---

**注意**: 部署前请仔细检查所有配置，确保安全设置正确。建议先在测试环境验证部署流程。
