# CRM集成服务扩展功能使用指南

## 概述

本文档介绍了CRM集成服务的扩展功能，包括数据新增、更新、智能同步和字段映射等功能。这些功能实现了与外部CRM系统的双向数据同步，支持自动化的数据管理。

## 新增功能

### 1. 数据新增接口 (createCrmData)

**接口路径**: `POST /api/crm-integration/data/create`

**功能**: 在CRM系统中创建新的客户或协议数据

**请求参数**:
```json
{
  "deployId": "customer", // 或 "contract"
  "data": {
    // 要创建的数据字段
  }
}
```

**使用示例**:
```javascript
// 创建客户数据
const customerData = {
  deployId: 'customer',
  data: {
    custom_name: '测试客户',
    seas_id: 'fd18e0d6b1164e7080f0fa91dc43b0d8',
    customer_textarea_11: 'https://example.com',
    custom_remark: '测试备注'
  }
};

const result = await crmIntegrationAPI.createCrmData(customerData);
```

### 2. 数据更新接口 (updateCrmData)

**接口路径**: `POST /api/crm-integration/data/update`

**功能**: 更新CRM系统中的客户或协议数据

**请求参数**:
```json
{
  "deployId": "customer", // 或 "contract"
  "dataId": 80082,
  "data": {
    // 要更新的数据字段
  }
}
```

### 3. 智能同步功能 (smartSyncCooperationData)

**接口路径**: `POST /api/crm-integration/smart-sync`

**功能**: 智能检测并同步合作对接数据到CRM系统

**特性**:
- 自动检测需要同步的数据类型
- 支持字段变化检测
- 智能判断客户/协议数据同步需求
- 自动更新本地CRM关联状态

**请求参数**:
```json
{
  "cooperationId": 123,
  "options": {
    "forceCustomerSync": false,
    "forceAgreementSync": false,
    "changedFields": ["customerName", "cooperationAmount"]
  }
}
```

## 字段映射服务

### 1. 客户数据映射

**映射规则**:
```javascript
// 合作对接数据 -> CRM客户数据
{
  customerName: 'custom_name',
  customerHomepage: 'customer_textarea_11',
  influencerPlatformId: 'customer_textarea_13',
  bloggerWechatAndNotes: 'custom_remark',
  seedingPlatform: 'customer_check_box_2', // 需要转换为ID数组
  bloggerFansCount: 'customer_select_28',   // 需要转换为ID
  starMapId: 'customer_textarea_14'
}
```

### 2. 协议数据映射

**映射规则**:
```javascript
// 合作对接数据 -> CRM协议数据
{
  title: 'contract_title',
  customerName: 'contract_textarea_1',
  cooperationForm: 'contract_select_2',     // 需要转换为ID
  cooperationBrand: 'contract_textarea_3',
  cooperationProduct: 'contract_textarea_4',
  cooperationAmount: 'contract_textarea_5',
  scheduledPublishTime: 'contract_date_1',  // 需要格式化
  actualPublishDate: 'contract_date_2',
  publishPlatform: 'contract_select_3',     // 需要转换为ID
  publishLink: 'contract_textarea_6',
  responsiblePerson: 'contract_textarea_7',
  cooperationNotes: 'contract_textarea_8'
}
```

## 测试功能

### 1. 创建测试数据

**接口路径**: `POST /api/crm-integration/test-data`

**功能**: 创建标准的测试客户和协议数据

**特性**:
- 自动创建带有"[测试]"标识的数据
- 返回创建的客户ID和协议ID
- 便于功能验证和调试

### 2. 字段映射测试

**接口路径**: `POST /api/crm-integration/test-mapping`

**功能**: 测试合作对接数据到CRM数据的字段映射

**请求参数**:
```json
{
  "cooperationData": {
    // 合作对接数据
  },
  "mappingType": "customer" // 或 "agreement"
}
```

## 使用场景

### 1. 创建合作记录时自动同步

```javascript
// 在CooperationService中
const cooperation = await CooperationManagement.create(data);

// 自动同步到CRM
const syncResult = await crmIntegrationService.smartSyncCooperationData(
  cooperation,
  { forceCustomerSync: true, forceAgreementSync: true }
);
```

### 2. 更新合作记录时智能同步

```javascript
// 检测字段变化
const changedFields = ['customerName', 'cooperationAmount'];

// 智能同步
const syncResult = await crmIntegrationService.smartSyncCooperationData(
  cooperation,
  { changedFields }
);
```

### 3. 手动重新创建CRM数据

```javascript
// 重新创建客户
const customerResult = await cooperationService.recreateCrmCustomer(cooperationId);

// 重新创建协议
const agreementResult = await cooperationService.recreateCrmAgreement(cooperationId);
```

## 配置说明

### 环境变量

确保以下环境变量已正确配置：

```env
# CRM集成配置
CRM_PROXY_URL=http://crmapi.superboss.cc
CRM_CORP_ID=ding96479bbd378e45bf35c2f4657eb6378f
CRM_APP_ID=3a3acf7ff6c9486d8d39342f24b0b887
CRM_APP_SECRET=13DBA19386A53548B7573B754E639958
CRM_API_TIMEOUT=30000
```

### 字段映射配置

在`CrmDataMappingService.js`中可以自定义字段映射规则：

```javascript
// 平台映射
this.platformMapping = {
  'c02d8d5e77684038811e57595513c154': '抖音',
  '1ebf642a94dc44f28e5aa7eb6959ed21': '小红书'
};

// 粉丝量级映射
this.fansLevelMapping = {
  'c3dcec2008734b77ae29db7679ac18bc': '腰部（粉丝1-20w）',
  '188c960126d4402c8b0a19744834b95e': '头部（粉丝20w+）'
};
```

## 错误处理

### 常见错误及解决方案

1. **Token过期**
   - 错误: `CRM Token已过期`
   - 解决: 调用`/api/crm-integration/refresh-token`刷新Token

2. **字段映射错误**
   - 错误: `字段映射失败`
   - 解决: 检查字段映射配置，确保CRM字段ID正确

3. **数据验证失败**
   - 错误: `数据验证失败`
   - 解决: 检查必填字段是否完整，数据格式是否正确

## 测试指南

### 运行测试脚本

```bash
# 运行CRM集成测试
node test/crm-integration-test.js
```

### 测试步骤

1. **连接测试**: 验证CRM系统连接状态
2. **字段映射测试**: 验证数据映射功能
3. **创建测试数据**: 创建标准测试数据
4. **智能同步测试**: 验证智能同步功能
5. **数据操作测试**: 验证创建和更新操作

## 最佳实践

1. **数据同步策略**
   - 创建时使用强制同步
   - 更新时使用智能检测
   - 定期验证数据一致性

2. **错误处理**
   - 记录详细的同步日志
   - 实现重试机制
   - 提供手动修复功能

3. **性能优化**
   - 批量操作时控制并发数
   - 使用缓存减少重复请求
   - 监控API调用频率

## 技术支持

如有问题，请查看：
1. 服务器日志：`/logs/app.log`
2. CRM集成日志：控制台输出
3. API文档：`http://localhost:3001/api-docs`

---

**版本**: 1.0.0  
**更新时间**: 2024年  
**维护者**: 达人管理系统团队
