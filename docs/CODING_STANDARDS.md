# 达人管理系统 - 代码规范文档

## 📋 概述

本文档定义了达人管理系统项目的代码规范和最佳实践，旨在确保代码的一致性、可读性和可维护性。

## 🎯 目标

- **一致性**: 统一的代码风格和命名规范
- **可读性**: 清晰易懂的代码结构和注释
- **可维护性**: 便于修改和扩展的代码组织
- **质量**: 减少bug和提高代码质量

## 📁 文件和目录命名

### 目录命名
- 使用小写字母和连字符：`user-management`
- 功能模块目录：`src/modules/auth/`
- 配置文件目录：`src/config/`
- 工具类目录：`src/utils/`

### 文件命名
- JavaScript文件：使用驼峰命名 `userController.js`
- 配置文件：使用小写和点分隔 `database.config.js`
- 常量文件：使用小写和连字符 `error-codes.js`
- 测试文件：添加`.test.js`或`.spec.js`后缀

## 🔤 命名规范

### 变量和函数
```javascript
// ✅ 好的命名
const userName = 'john_doe';
const isUserActive = true;
const getUserById = (id) => { /* ... */ };

// ❌ 避免的命名
const u = 'john_doe';
const flag = true;
const get = (id) => { /* ... */ };
```

### 常量
```javascript
// ✅ 使用大写字母和下划线
const MAX_RETRY_COUNT = 3;
const API_BASE_URL = 'https://api.example.com';
const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user'
};
```

### 类和构造函数
```javascript
// ✅ 使用帕斯卡命名法
class UserManager {
  constructor() {
    this.users = [];
  }
}

class ApiError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.statusCode = statusCode;
  }
}
```

## 📝 注释规范

### 文件头注释
```javascript
/**
 * 用户管理控制器
 * 
 * 功能说明：
 * - 处理用户相关的API请求
 * - 提供用户CRUD操作
 * - 支持用户认证和授权
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
```

### 函数注释
```javascript
/**
 * 根据ID获取用户信息
 * 
 * @param {number} userId - 用户ID
 * @param {Object} options - 查询选项
 * @param {boolean} options.includeProfile - 是否包含用户资料
 * @returns {Promise<Object>} 用户信息对象
 * @throws {NotFoundError} 当用户不存在时抛出
 * 
 * @example
 * const user = await getUserById(123, { includeProfile: true });
 */
async function getUserById(userId, options = {}) {
  // 实现代码...
}
```

### 行内注释
```javascript
// 验证用户输入参数
const isValid = validateUserInput(userData);

// TODO: 添加缓存机制提高性能
const users = await User.findAll();

// FIXME: 修复并发访问时的数据竞争问题
let counter = 0;
```

## 🏗️ 代码结构

### 模块导入顺序
```javascript
// 1. Node.js内置模块
const fs = require('fs');
const path = require('path');

// 2. 第三方模块
const express = require('express');
const bcrypt = require('bcryptjs');

// 3. 项目内部模块
const config = require('../config');
const { User } = require('../models');
const { validateEmail } = require('../utils/validation');
```

### 函数组织
```javascript
class UserController {
  // 1. 静态方法
  static validateUserData(data) {
    // ...
  }

  // 2. 构造函数
  constructor() {
    // ...
  }

  // 3. 公共方法（按字母顺序）
  async createUser(userData) {
    // ...
  }

  async deleteUser(userId) {
    // ...
  }

  async getUserById(userId) {
    // ...
  }

  // 4. 私有方法（以_开头）
  _validateUserPermissions(user) {
    // ...
  }
}
```

## 🔧 错误处理

### 统一错误处理
```javascript
// ✅ 使用自定义错误类
const { NotFoundError, ValidationError } = require('../utils/errors');

async function getUserById(userId) {
  if (!userId) {
    throw new ValidationError('用户ID不能为空');
  }

  const user = await User.findByPk(userId);
  if (!user) {
    throw new NotFoundError('用户不存在');
  }

  return user;
}
```

### 异步错误处理
```javascript
// ✅ 使用try-catch处理异步错误
async function createUser(userData) {
  try {
    const user = await User.create(userData);
    return user;
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      throw new ConflictError('用户已存在');
    }
    throw error;
  }
}
```

## 📊 数据验证

### 输入验证
```javascript
// ✅ 使用专门的验证函数
const { validateRequired, isValidEmail } = require('../utils/validation');

function validateUserData(userData) {
  const errors = validateRequired(userData, ['username', 'email', 'password']);
  
  if (userData.email && !isValidEmail(userData.email)) {
    errors.email = '邮箱格式无效';
  }
  
  return Object.keys(errors).length > 0 ? errors : null;
}
```

## 🔐 安全规范

### 敏感信息处理
```javascript
// ✅ 不在日志中输出敏感信息
console.log('用户登录:', { 
  userId: user.id, 
  username: user.username 
  // 不包含密码等敏感信息
});

// ✅ 使用环境变量存储密钥
const jwtSecret = process.env.JWT_SECRET;
```

### SQL注入防护
```javascript
// ✅ 使用参数化查询
const users = await User.findAll({
  where: {
    username: {
      [Op.like]: `%${searchTerm}%`
    }
  }
});
```

## 📈 性能优化

### 数据库查询优化
```javascript
// ✅ 只查询需要的字段
const users = await User.findAll({
  attributes: ['id', 'username', 'email'],
  limit: 10
});

// ✅ 使用索引字段进行查询
const user = await User.findOne({
  where: { email: userEmail } // email字段有索引
});
```

### 内存使用优化
```javascript
// ✅ 及时释放大对象
let largeData = await processLargeDataset();
// 使用完后设置为null
largeData = null;
```

## 🧪 测试规范

### 测试文件命名
- 单元测试：`userController.test.js`
- 集成测试：`userApi.integration.test.js`
- 端到端测试：`userFlow.e2e.test.js`

### 测试结构
```javascript
describe('UserController', () => {
  describe('getUserById', () => {
    it('should return user when valid ID is provided', async () => {
      // Arrange
      const userId = 1;
      
      // Act
      const result = await getUserById(userId);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe(userId);
    });
  });
});
```

## 📚 文档规范

### README文档
- 项目简介和功能特性
- 安装和运行说明
- API文档链接
- 贡献指南

### API文档
- 使用Swagger/OpenAPI规范
- 包含请求/响应示例
- 错误码说明
- 认证方式说明

## 🔍 代码审查

### 审查要点
1. **功能正确性**: 代码是否实现了预期功能
2. **代码质量**: 是否遵循编码规范
3. **性能考虑**: 是否存在性能问题
4. **安全性**: 是否存在安全漏洞
5. **测试覆盖**: 是否有足够的测试

### 审查流程
1. 创建Pull Request
2. 自动化检查（ESLint、测试）
3. 同行代码审查
4. 修改和完善
5. 合并到主分支

## 🛠️ 工具配置

### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git钩子管理
- **Jest**: 单元测试框架

### 编辑器配置
- 启用ESLint和Prettier插件
- 配置自动格式化
- 设置制表符为2个空格
- 启用行尾空格显示

---

**注意**: 本规范是活文档，会根据项目发展和团队反馈持续更新和完善。
