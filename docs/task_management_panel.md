# 数据拉取任务管理面板

## 概述

数据拉取任务管理面板是一个专门用于管理和监控合作对接记录中笔记数据拉取任务的功能模块。该面板提供了完整的任务管理功能，包括任务列表查看、状态监控、批量操作、实时更新等。

## 功能特性

### 📊 统计信息展示
- **任务总数统计**：显示所有需要拉取数据的任务总数
- **状态分布**：按状态分类显示任务数量（待拉取、拉取中、已完成、失败）
- **今日任务**：显示今日可执行和已执行的任务数量
- **成功率统计**：计算并显示数据拉取的成功率

### 📋 任务列表管理
- **分页显示**：支持分页浏览任务列表，可自定义每页显示数量
- **多条件筛选**：
  - 按拉取状态筛选（待拉取、拉取中、已完成、失败）
  - 按客户名称搜索
  - 按数据登记日期范围筛选
- **排序功能**：按数据登记日期和创建时间排序

### 🔄 批量操作
- **批量选择**：支持单选、多选和全选操作
- **批量拉取**：一次性触发多个任务的数据拉取
- **确认对话框**：批量操作前显示确认对话框，防止误操作
- **操作反馈**：显示批量操作的详细结果（成功/失败数量）

### ⚡ 实时更新
- **自动刷新**：每15秒自动刷新统计信息和任务状态
- **智能刷新**：当有正在进行的任务时，自动刷新任务列表
- **手动控制**：可以手动开启/关闭自动刷新功能
- **状态指示**：显示自动刷新的开启/关闭状态

### 🛠️ 单个任务操作
- **手动拉取**：对单个任务执行数据拉取操作
- **重试功能**：对失败的任务提供重试选项
- **任务详情**：查看任务的详细信息和数据指标
- **状态监控**：实时显示任务的执行状态

### 📈 数据展示
- **数据指标**：显示曝光数、点赞数、收藏数、评论数等关键指标
- **链接预览**：显示发布链接的截断预览，支持点击跳转
- **时间格式化**：友好的时间显示格式
- **数字格式化**：大数字的千分位分隔显示

## 技术架构

### 后端架构
```
src/
├── controllers/
│   └── CooperationController.js     # 合作对接控制器（新增任务管理接口）
├── services/
│   └── CooperationService.js        # 合作对接服务（新增任务管理方法）
└── routes/
    └── cooperation.js               # 路由配置（新增任务管理路由）
```

### 前端架构
```
frontend/src/
├── components/
│   └── DataFetchTaskPanel.vue      # 任务管理面板主组件
├── services/
│   └── api.js                       # API接口配置（新增任务管理接口）
└── router/
    └── index.js                     # 路由配置（新增任务管理路由）
```

## API接口

### 1. 获取任务统计信息
```http
GET /api/cooperation/tasks/stats
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "total": 150,
    "pending": 45,
    "fetching": 3,
    "success": 95,
    "failed": 7,
    "todayExecutable": 20,
    "todayExecuted": 15,
    "successRate": "63.3"
  }
}
```

### 2. 获取任务列表
```http
GET /api/cooperation/tasks?page=1&limit=20&status=pending&customerName=测试客户
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "id": 1,
        "customerName": "测试客户",
        "publishLink": "https://www.xiaohongshu.com/explore/...",
        "noteId": "64a1b2c3d4e5f6",
        "platform": "xiaohongshu",
        "dataRegistrationDate": "2024-01-15",
        "dataFetchStatus": "pending",
        "dataFetchTime": null,
        "dataFetchError": null,
        "impNum": null,
        "likeNum": null,
        "favNum": null,
        "cmtNum": null,
        "readNum": null,
        "shareNum": null
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "totalCount": 150,
      "totalPages": 8
    }
  }
}
```

### 3. 批量拉取任务
```http
POST /api/cooperation/tasks/batch-fetch
Content-Type: application/json

{
  "ids": [1, 2, 3, 4, 5]
}
```

### 4. 检查Cookie状态
```http
GET /api/cooperation/cookie/status
```

## 使用说明

### 访问路径
- 前端访问地址：`http://localhost:5173/data-fetch-tasks`
- 导航菜单：主菜单 → 业务管理 → 数据拉取任务

### 操作流程

1. **查看任务概览**
   - 进入页面后，首先查看统计卡片了解任务整体情况
   - 关注待拉取任务数量和成功率

2. **筛选和搜索**
   - 使用状态筛选器查看特定状态的任务
   - 使用客户名称搜索定位特定客户的任务
   - 使用日期范围筛选查看特定时间段的任务

3. **执行拉取操作**
   - 单个拉取：点击任务行的"拉取"按钮
   - 批量拉取：选择多个任务后点击"批量拉取"按钮
   - 手动触发：点击页面右上角的"手动触发拉取"按钮

4. **监控执行状态**
   - 观察任务状态的实时变化
   - 查看拉取完成后的数据指标
   - 对失败的任务进行重试

### 注意事项

- **认证要求**：所有操作都需要用户登录认证
- **权限控制**：确保用户有合作对接管理权限
- **网络依赖**：数据拉取功能依赖外部平台API，需要稳定的网络连接
- **Cookie管理**：确保相关平台的Cookie配置正确且有效

## 测试验证

项目包含完整的测试脚本：

```bash
# 运行API基础功能测试
node test/simple_api_test.js

# 运行完整功能测试（需要有效认证）
node test/task_panel_test.js
```

测试覆盖：
- ✅ API端点可达性
- ✅ 路由配置正确性
- ✅ 认证机制验证
- ✅ 数据结构验证
- ✅ 错误处理机制

## 部署说明

1. **后端部署**
   - 确保数据库连接正常
   - 验证Cookie管理服务可用
   - 检查定时任务服务运行状态

2. **前端部署**
   - 更新导航菜单配置
   - 确保路由正确注册
   - 验证API接口地址配置

3. **功能验证**
   - 登录系统验证认证功能
   - 访问任务管理面板验证界面
   - 执行测试操作验证功能完整性

## 维护和扩展

### 性能优化建议
- 对大量任务的分页查询进行数据库索引优化
- 实现任务状态的缓存机制减少数据库查询
- 考虑使用WebSocket替代轮询实现真正的实时更新

### 功能扩展方向
- 添加任务执行历史记录
- 实现任务优先级管理
- 增加数据拉取结果的详细分析
- 支持自定义拉取策略配置

---

**开发完成时间**：2024年1月
**版本**：v1.0.0
**状态**：✅ 已完成并测试通过
