# Cookie存储测试模式说明

## ⚠️ 重要安全警告

**当前Cookie管理系统运行在测试模式下，Cookie数据以明文形式存储！**

**🔒 生产环境部署前必须恢复加密功能以保证数据安全性！**

## 修改概述

为了便于开发测试阶段的调试和验证，我们临时修改了Cookie管理系统的存储机制，将加密存储改为明文存储。

## 修改的文件和功能

### 1. `src/services/CookieManager.js`

#### 修改的方法：
- **`encryptCookie(cookieString)`**: 跳过加密，直接返回明文Cookie字符串
- **`decryptCookie(encryptedData)`**: 跳过解密，直接返回原始数据
- **`addCookie(cookieData)`**: 使用明文存储Cookie数据
- **`getAvailableCookie(platform)`**: 直接读取明文Cookie数据
- **`validateCookie(cookieId, validationFunction)`**: 使用明文数据进行验证

#### 新增功能：
- 构造函数中添加测试模式警告信息
- 所有相关操作都添加了测试模式标识的日志输出
- 添加了 `isTestMode` 标识符

### 2. `src/controllers/CookieController.js`

#### 修改的方法：
- **`getCookies()`**: 修改脱敏处理，显示Cookie前20个字符用于调试
- **`getCookieById()`**: 修改脱敏处理，显示Cookie前20个字符用于调试
- **`updateCookie()`**: 更新Cookie时使用明文存储

#### 新增功能：
- 所有Cookie操作都添加了测试模式标识的日志输出
- 在API响应中添加测试模式提示信息

## 测试模式特性

### 1. 明文存储
- Cookie数据直接以明文形式存储在数据库的 `cookie_data` 字段中
- 不进行任何加密处理，便于直接查看和调试

### 2. 增强的调试信息
- 所有Cookie操作都会输出详细的测试模式日志
- API响应中包含Cookie前缀信息用于识别
- 数据库中可以直接查看完整的Cookie内容

### 3. 保持功能完整性
- Cookie的添加、更新、删除、验证等功能完全正常
- 轮换策略、使用统计等功能不受影响
- API接口保持兼容性

## 安全考虑

### 测试阶段
- ✅ 便于开发调试和问题排查
- ✅ 可以直接验证Cookie数据的正确性
- ✅ 简化了开发流程

### 生产环境风险
- ❌ Cookie数据明文存储存在严重安全风险
- ❌ 数据库泄露将直接暴露用户Cookie信息
- ❌ 不符合数据保护和隐私安全要求

## 恢复加密功能的步骤

### 1. 修改 `src/services/CookieManager.js`

```javascript
// 在构造函数中
this.isTestMode = false; // 设置为生产模式

// 恢复 encryptCookie 方法
encryptCookie(cookieString) {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(this.algorithm, this.encryptionKey);
  let encrypted = cipher.update(cookieString, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}

// 恢复 decryptCookie 方法
decryptCookie(encryptedData) {
  try {
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];
    const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    console.error('Cookie解密失败:', error.message);
    return null;
  }
}
```

### 2. 修改 `src/controllers/CookieController.js`

```javascript
// 恢复完全脱敏处理
const sanitizedRows = rows.map(cookie => {
  const data = cookie.toJSON();
  data.cookiePreview = data.cookieData ? '***已设置***' : '未设置';
  delete data.cookieData;
  return data;
});
```

### 3. 数据迁移

如果测试阶段已有明文Cookie数据，需要：
1. 备份现有数据
2. 对所有明文Cookie数据进行加密
3. 更新数据库记录
4. 验证加密后的数据可以正常解密和使用

## 当前状态验证

### 检查测试模式是否生效：
1. 启动服务器，查看控制台是否显示测试模式警告
2. 添加一个Cookie，检查数据库中 `cookie_data` 字段是否为明文
3. 查看API响应中的 `cookiePreview` 字段是否显示前缀信息

### 功能测试清单：
- [ ] Cookie添加功能
- [ ] Cookie列表查询
- [ ] Cookie详情查询
- [ ] Cookie更新功能
- [ ] Cookie删除功能
- [ ] Cookie验证功能
- [ ] 批量导入功能
- [ ] 统计信息查询

## 注意事项

1. **数据安全**: 测试期间避免使用真实的生产Cookie数据
2. **环境隔离**: 确保测试环境与生产环境完全隔离
3. **代码审查**: 部署前必须审查所有相关代码，确保加密功能已恢复
4. **数据清理**: 测试完成后清理所有明文Cookie数据
5. **文档更新**: 恢复加密后更新相关文档和注释

## 联系信息

如有任何问题或需要协助恢复加密功能，请及时联系开发团队。

---

**再次提醒：此修改仅用于开发测试阶段，生产环境必须使用加密存储！**
