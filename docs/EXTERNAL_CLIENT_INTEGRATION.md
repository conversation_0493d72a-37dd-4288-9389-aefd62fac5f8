# 外部客户端接入完整方案

本文档提供了外部客户端（如其他应用程序、Agent系统或第三方工具）接入MCP服务的完整解决方案。

## 🎯 方案概述

### 核心特性
- **即插即用**: 提供多种编程语言的客户端代码模板
- **实时通信**: 基于SSE的事件流，支持任务状态实时推送
- **自动化工具**: 配置向导和测试脚本，简化接入过程
- **生产就绪**: 包含错误处理、重连机制、监控等生产级特性

### 支持的接入方式
1. **HTTP API调用** - 直接调用REST API接口
2. **SSE事件流** - 实时接收任务状态更新
3. **Agent框架集成** - 与主流Agent框架深度集成
4. **命令行工具** - 通过脚本快速测试和使用

## 🚀 快速接入（5分钟）

### 步骤1: 验证服务可用性
```bash
# 检查MCP服务是否运行
curl http://localhost:3000/health

# 或使用测试脚本
node scripts/test-mcp-connection.js
```

### 步骤2: 生成客户端代码
```bash
# 运行配置向导
node scripts/mcp-client-setup.js

# 选择语言: javascript/python/go/curl
# 配置Agent信息和参数
# 自动生成客户端代码
```

### 步骤3: 运行客户端
```bash
# 进入生成的目录
cd ./mcp-client

# 运行客户端（根据选择的语言）
node mcp-client.js        # JavaScript
python mcp-client.py      # Python
go run mcp-client.go      # Go
./mcp-client.sh          # Shell脚本
```

## 📋 完整接入流程

### 1. 服务器信息确认
- **默认地址**: `http://localhost:3000`
- **健康检查**: `GET /health`
- **API文档**: `http://localhost:3000/api-docs`

### 2. Agent注册连接
```http
POST /api/mcp/agent/connect
Content-Type: application/json

{
  "agentId": "your_agent_id",
  "agentName": "您的Agent名称",
  "version": "1.0.0",
  "capabilities": ["crawler", "data_analysis"],
  "metadata": {
    "description": "Agent描述",
    "environment": "production"
  }
}
```

**响应**: 获取 `sessionId` 和 `sseEndpoint`

### 3. 建立SSE连接
```javascript
const sseUrl = `http://localhost:3000/api/mcp/agent/events/${sessionId}`;
const eventSource = new EventSource(sseUrl);

// 监听事件
eventSource.addEventListener('task_status_update', handleStatusUpdate);
eventSource.addEventListener('task_completed', handleTaskCompleted);
```

### 4. 执行工具操作
```http
POST /api/mcp/agent/execute
Content-Type: application/json

{
  "sessionId": "your_session_id",
  "tool": "create_crawler_task",
  "arguments": {
    "keywords": "美妆博主",
    "platform": "xiaohongshu",
    "taskName": "测试任务"
  }
}
```

### 5. 处理实时事件
```javascript
// 任务状态更新
function handleStatusUpdate(event) {
  const data = JSON.parse(event.data);
  console.log(`任务 ${data.taskId} 状态: ${data.status}`);
}

// 任务完成
function handleTaskCompleted(event) {
  const data = JSON.parse(event.data);
  console.log(`任务 ${data.taskId} 完成，结果: ${data.results}`);
}
```

## 🔧 具体配置方案

### JavaScript/Node.js客户端
```javascript
const MCPClient = require('./mcp-client');

const client = new MCPClient({
  serverUrl: 'http://localhost:3000',
  agentId: 'js_client_001',
  agentName: 'JavaScript客户端',
  autoReconnect: true,
  heartbeatInterval: 30000
});

// 连接并使用
await client.connect();
const result = await client.searchInfluencers('美妆博主', 'xiaohongshu');
```

### Python客户端
```python
from mcp_client import MCPClient

client = MCPClient(
    server_url='http://localhost:3000',
    agent_id='py_client_001',
    agent_name='Python客户端'
)

# 连接并使用
await client.connect()
result = await client.search_influencers('美妆博主', 'xiaohongshu')
```

### cURL脚本
```bash
#!/bin/bash
# 一键执行脚本

SERVER_URL="http://localhost:3000"
KEYWORDS="美妆博主"

# 连接获取sessionId
SESSION_ID=$(curl -s -X POST "$SERVER_URL/api/mcp/agent/connect" \
  -H "Content-Type: application/json" \
  -d '{"agentId":"curl_client","agentName":"cURL客户端"}' | \
  jq -r '.data.sessionId')

# 创建任务
curl -X POST "$SERVER_URL/api/mcp/agent/execute" \
  -H "Content-Type: application/json" \
  -d "{\"sessionId\":\"$SESSION_ID\",\"tool\":\"create_crawler_task\",\"arguments\":{\"keywords\":\"$KEYWORDS\",\"platform\":\"xiaohongshu\"}}"
```

## 🤖 Agent系统集成

### OpenAI Assistant集成
```python
# 将MCP工具注册为Assistant函数
assistant = openai.beta.assistants.create(
    name="达人搜索助手",
    instructions="使用MCP工具搜索达人信息",
    tools=[{
        "type": "function",
        "function": {
            "name": "search_influencers",
            "description": "搜索达人信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "keywords": {"type": "string"},
                    "platform": {"type": "string"}
                }
            }
        }
    }]
)
```

### LangChain Agent集成
```python
from langchain.tools import BaseTool

class InfluencerSearchTool(BaseTool):
    name = "influencer_search"
    description = "搜索达人信息"
    
    def _run(self, keywords: str, platform: str) -> str:
        # 调用MCP服务
        return mcp_client.search_influencers(keywords, platform)
```

## 📊 SSE事件流配置

### 支持的事件类型
- `connected`: 连接建立确认
- `task_status_update`: 任务状态更新
- `task_completed`: 任务完成通知
- `tool_execution_result`: 工具执行结果
- `heartbeat`: 心跳检测

### 事件处理示例
```javascript
const eventHandlers = {
  'connected': (data) => console.log('连接建立:', data),
  'task_status_update': (data) => updateTaskStatus(data),
  'task_completed': (data) => handleTaskCompletion(data),
  'heartbeat': (data) => updateLastHeartbeat()
};

// 注册事件处理器
Object.keys(eventHandlers).forEach(eventType => {
  eventSource.addEventListener(eventType, (event) => {
    const data = JSON.parse(event.data);
    eventHandlers[eventType](data);
  });
});
```

## 🛠️ 错误处理和重连

### 自动重连机制
```javascript
class AutoReconnectManager {
  constructor(client, options = {}) {
    this.client = client;
    this.maxRetries = options.maxRetries || 5;
    this.retryDelay = options.retryDelay || 1000;
    this.retryCount = 0;
  }

  async reconnect() {
    if (this.retryCount >= this.maxRetries) {
      throw new Error('达到最大重连次数');
    }

    const delay = this.retryDelay * Math.pow(2, this.retryCount);
    await new Promise(resolve => setTimeout(resolve, delay));
    
    try {
      await this.client.connect();
      this.retryCount = 0; // 重置计数
    } catch (error) {
      this.retryCount++;
      throw error;
    }
  }
}
```

### 错误分类处理
```javascript
const errorHandlers = {
  'NETWORK_ERROR': () => reconnectManager.reconnect(),
  'SESSION_EXPIRED': () => client.refreshSession(),
  'RATE_LIMITED': () => delay(5000),
  'SERVER_ERROR': () => logError('服务器错误')
};
```

## 📈 性能优化建议

### 连接管理
- 使用连接池避免频繁创建连接
- 实现连接复用机制
- 设置合理的超时时间

### 事件处理
- 使用事件缓冲区处理大量事件
- 实现事件去重机制
- 异步处理事件避免阻塞

### 资源清理
- 及时关闭不用的连接
- 定期清理过期会话
- 监控内存使用情况

## 🔐 安全配置

### 生产环境建议
```javascript
const productionConfig = {
  serverUrl: 'https://your-secure-server.com',
  timeout: 60000,
  retryCount: 5,
  enableSSL: true,
  validateCertificate: true,
  apiKey: process.env.MCP_API_KEY
};
```

### 访问控制
- 使用HTTPS加密传输
- 实现API密钥认证
- 设置IP白名单限制
- 定期轮换会话密钥

## 📚 相关资源

### 文档链接
- [详细API文档](http://localhost:3000/api-docs)
- [客户端示例集合](./MCP_CLIENT_EXAMPLES.md)
- [Agent集成指南](./AGENT_INTEGRATION_GUIDE.md)

### 工具脚本
- `scripts/mcp-client-setup.js` - 客户端代码生成器
- `scripts/test-mcp-connection.js` - 连接测试工具

### 配置模板
- `config/mcp-client-template.json` - JSON配置模板
- `config/.env.template` - 环境变量模板

## 🆘 技术支持

### 常见问题
1. **连接失败**: 检查服务器地址和端口
2. **SSE断开**: 实现自动重连机制
3. **工具执行失败**: 验证参数格式
4. **性能问题**: 优化连接和事件处理

### 获取帮助
- 查看服务器端日志获取详细错误信息
- 使用测试脚本诊断连接问题
- 参考示例代码了解最佳实践
- 检查API文档确认接口规范

---

**🎉 恭喜！您现在已经掌握了MCP服务外部客户端接入的完整方案。开始构建您的集成应用吧！**
