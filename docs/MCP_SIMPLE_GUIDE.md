# MCP 简化使用指南

## 什么是 MCP？

MCP (Model Context Protocol) 是一个让AI助手能够调用外部工具的协议。在我们的达人爬虫系统中，MCP让AI助手可以直接创建爬虫任务、查询状态、获取结果等。

## 核心功能

我们的简化版MCP提供4个核心工具：

### 1. 创建爬虫任务 (create_crawler_task)
- **功能**：创建新的达人爬虫任务
- **参数**：
  - `keywords`: 搜索关键词（如"美妆博主"）
  - `platform`: 平台类型（xiaohongshu 或 juxingtu）
  - `taskName`: 任务名称
  - `maxPages`: 最大爬取页数（1-20）
  - `config`: 可选配置（页面大小、延迟等）
  - `priority`: 任务优先级（0-10）

### 2. 获取任务状态 (get_task_status)
- **功能**：查询任务执行状态和进度
- **参数**：
  - `taskId`: 任务ID

### 3. 获取任务结果 (get_task_results)
- **功能**：获取爬虫结果数据
- **参数**：
  - `taskId`: 任务ID
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认20）
  - `status`: 结果状态筛选（可选）
  - `keyword`: 关键词搜索（可选）

### 4. 导出Excel (export_results_to_excel)
- **功能**：将结果导出为Excel文件
- **参数**：
  - `taskId`: 任务ID
  - `filename`: 文件名（可选）
  - `status`: 导出指定状态的结果（可选）

## 如何使用

### 方式1：通过API接口

#### 1. 获取工具列表
```bash
curl -X GET "http://localhost:3001/api/simple-mcp/tools"
```

#### 2. 执行工具
```bash
curl -X POST "http://localhost:3001/api/simple-mcp/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "create_crawler_task",
    "arguments": {
      "keywords": "美妆博主",
      "platform": "xiaohongshu",
      "taskName": "美妆达人搜索",
      "maxPages": 3
    }
  }'
```

#### 3. 查看服务状态
```bash
curl -X GET "http://localhost:3001/api/simple-mcp/status"
```

### 方式2：在代码中使用

```javascript
const simpleMCPService = require('./src/services/SimpleMCPService');

// 初始化服务
await simpleMCPService.initialize();

// 获取服务器实例
const server = simpleMCPService.getServer();

// 服务已经注册了所有工具，可以直接使用
```

## 使用示例

### 完整工作流程

```bash
# 1. 创建任务
curl -X POST "http://localhost:3001/api/simple-mcp/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "create_crawler_task",
    "arguments": {
      "keywords": "美食博主",
      "platform": "xiaohongshu",
      "taskName": "美食达人采集",
      "maxPages": 5
    }
  }'

# 返回: {"success": true, "data": {"taskId": 123, ...}}

# 2. 查询状态
curl -X POST "http://localhost:3001/api/simple-mcp/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "get_task_status",
    "arguments": {
      "taskId": 123
    }
  }'

# 3. 获取结果
curl -X POST "http://localhost:3001/api/simple-mcp/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "get_task_results",
    "arguments": {
      "taskId": 123,
      "page": 1,
      "limit": 20
    }
  }'

# 4. 导出Excel
curl -X POST "http://localhost:3001/api/simple-mcp/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "export_results_to_excel",
    "arguments": {
      "taskId": 123,
      "filename": "美食达人数据.xlsx"
    }
  }'
```

## 错误处理

所有工具调用都会返回统一格式：

**成功响应**：
```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... }
}
```

**错误响应**：
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 常见问题

### Q: 任务创建后多久开始执行？
A: 任务会立即添加到队列，通常在几秒内开始执行。

### Q: 如何知道任务是否完成？
A: 使用 `get_task_status` 工具查询，状态为 `completed` 表示完成。

### Q: 支持哪些平台？
A: 目前支持小红书(xiaohongshu)和巨量星图(juxingtu)。

### Q: 最大可以爬取多少页？
A: 为了避免过度请求，限制最大20页。

### Q: Excel文件保存在哪里？
A: 文件保存在 `public/exports/` 目录，可通过返回的下载链接访问。

## 技术架构

- **基础框架**: 基于官方 @modelcontextprotocol/sdk
- **服务层**: SimpleMCPService 提供核心功能
- **控制层**: SimpleMCPController 处理HTTP请求
- **路由层**: simple-mcp.js 定义API接口
- **集成**: 与现有Koa2系统无缝集成

## 开发说明

这是一个简化版本，专注于核心功能：
- ✅ 基本的工具注册和调用
- ✅ 爬虫任务管理
- ✅ 结果查询和导出
- ❌ 复杂的SSE事件流
- ❌ 多客户端会话管理
- ❌ 高级认证和权限控制

适合快速开发和维护，满足MVP需求。
