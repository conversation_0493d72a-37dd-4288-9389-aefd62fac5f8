# 视频数据迁移指南

## 📋 概述

本文档介绍如何使用视频数据迁移脚本，将达人表(influencers)中的videoDetails字段数据迁移到独立的author_videos表中。

## 🎯 迁移目的

随着系统架构的优化，我们实现了独立的达人视频表(author_videos)来存储视频数据。为了清理达人表中冗余的videoDetails字段，需要将现有数据迁移到新的表结构中。

## 🔧 迁移脚本功能

### 核心功能
- ✅ **数据检测**: 自动扫描和统计需要迁移的数据
- ✅ **批量迁移**: 分批处理大量数据，避免长时间锁表
- ✅ **数据验证**: 迁移完成后自动验证数据完整性
- ✅ **安全清理**: 验证无误后清空冗余字段
- ✅ **事务安全**: 使用数据库事务确保操作原子性
- ✅ **详细报告**: 提供完整的迁移过程报告

### 安全措施
- ✅ **重复检测**: 自动跳过已存在的视频数据
- ✅ **回滚机制**: 提供紧急回滚功能
- ✅ **错误隔离**: 单个达人迁移失败不影响整体流程
- ✅ **详细日志**: 记录每个步骤的详细信息

## 🚀 使用方法

### 1. 执行迁移

```bash
# 进入项目目录
cd /path/to/daren-server

# 执行迁移脚本（只清空字段内容，不删除字段）
node scripts/migrate_video_details_to_author_videos.js

# 执行迁移并自动删除videoDetails字段
node scripts/migrate_video_details_to_author_videos.js --drop-column

# 或使用别名
node scripts/migrate_video_details_to_author_videos.js --auto-drop
```

### 2. 查看迁移过程

迁移过程中会显示详细的进度信息：

```
🚀 开始视频数据迁移流程...

📋 步骤1: 初始化数据库...
✅ 数据库初始化完成

📋 步骤2: 检测和分析数据...
📊 总达人数: 129
📊 有视频数据的达人数: 121
📊 需要迁移的视频总数: 1168

📋 步骤3: 迁移确认...
⚠️ 即将执行以下操作:
   - 迁移 121 个达人的视频数据
   - 总共 1168 个视频将被迁移到 author_videos 表
   - 迁移完成后将清空 influencers 表的 videoDetails 字段

📋 步骤4: 执行数据迁移...
🔄 处理第 1 批达人 (1-10/121)
📹 迁移达人 花花鲤娱酱 (ID: 13) 的视频数据...
✅ 达人 花花鲤娱酱 迁移完成: 成功 10, 失败 0
...

📋 步骤5: 验证迁移结果...
✅ 迁移结果验证通过

📋 步骤6: 清理冗余数据...
✅ 冗余数据清理完成

📋 步骤7: 删除videoDetails字段...
🔒 为了安全起见，字段删除需要手动执行
💡 如需删除字段，请使用以下命令:
   node scripts/migrate_video_details_to_author_videos.js --drop-column

📋 步骤8: 生成迁移报告...
📊 ===== 视频数据迁移报告 =====
⏱️ 迁移耗时: 2.5 分钟
📈 总达人数: 129
📹 有视频数据的达人: 121
🎬 需要迁移的视频总数: 1168
✅ 成功迁移的达人: 121
❌ 失败的达人: 0
🎯 实际迁移的视频数: 968
📊 迁移成功率: 100%
🗑️ videoDetails字段删除: ❌ 未删除
```

### 3. 字段删除选项

迁移脚本提供两种字段处理方式：

**默认模式（推荐）**：
- 只清空videoDetails字段的内容
- 保留字段结构，便于回滚
- 适用于谨慎的生产环境

**自动删除模式**：
- 完全删除videoDetails字段
- 释放数据库存储空间
- 操作不可逆，需谨慎使用

### 4. 紧急回滚（如需要）

如果迁移过程中出现问题，可以执行回滚操作：

```bash
# 回滚迁移（删除所有迁移的视频数据）
node scripts/migrate_video_details_to_author_videos.js --rollback

# 恢复videoDetails字段（如果已删除）
node scripts/test_column_drop.js --restore-column
```

## 📊 迁移流程详解

### 步骤1: 数据检测和统计
- 扫描influencers表中videoDetails字段不为空的记录
- 统计需要迁移的达人数量和视频总数
- 显示详细的数据分析报告

### 步骤2: 迁移确认
- 显示即将执行的操作详情
- 提醒用户备份重要数据
- 确认后继续执行迁移

### 步骤3: 批量数据迁移
- 分批处理达人数据（默认每批10个）
- 对每个达人的视频数据进行格式转换
- 使用事务确保每个达人的数据迁移原子性
- 自动跳过已存在的重复视频

### 步骤4: 数据验证
- 验证迁移前后的数据数量一致性
- 检查视频数据字段的完整性
- 确认达人与视频的关联关系正确

### 步骤5: 清理冗余数据
- 清空已成功迁移达人的videoDetails字段
- 保留迁移失败达人的原始数据
- 使用事务确保清理操作的安全性

### 步骤6: 删除videoDetails字段（可选）
- 检查是否还有未清理的数据
- 根据配置决定是否删除字段
- 提供手动删除的SQL命令

### 步骤7: 生成迁移报告
- 统计迁移耗时和成功率
- 列出所有错误和警告信息
- 显示字段删除状态
- 提供详细的数据统计报告

## 🔍 数据映射关系

### 原始数据结构 (influencers.videoDetails)
```json
{
  "videoId": "7527579343573224750",
  "title": "精彩视频标题",
  "playCount": 10000,
  "likeCount": 500,
  "commentCount": 50,
  "shareCount": 20,
  "publishTime": "2024-01-01T00:00:00Z",
  "duration": 120,
  "videoUrl": "https://example.com/video.mp4",
  "videoCover": "https://example.com/cover.jpg"
}
```

### 迁移后数据结构 (author_videos)
```sql
CREATE TABLE author_videos (
  id INT PRIMARY KEY AUTO_INCREMENT,
  author_id INT NOT NULL,
  platform VARCHAR(50) NOT NULL,
  platform_user_id VARCHAR(100) NOT NULL,
  video_id VARCHAR(100) NOT NULL,
  title TEXT,
  video_url TEXT,
  video_cover TEXT,
  duration INT DEFAULT 0,
  publish_time DATETIME,
  play_count BIGINT DEFAULT 0,
  like_count BIGINT DEFAULT 0,
  comment_count BIGINT DEFAULT 0,
  share_count BIGINT DEFAULT 0,
  collect_count BIGINT DEFAULT 0,
  tags JSON,
  description TEXT,
  location VARCHAR(255),
  music_info JSON,
  video_stats JSON,
  raw_data JSON,
  status VARCHAR(20) DEFAULT 'active',
  crawl_task_id INT,
  last_updated DATETIME,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_video (platform, platform_user_id, video_id),
  KEY idx_author_id (author_id),
  KEY idx_publish_time (publish_time),
  KEY idx_play_count (play_count)
);
```

## ⚠️ 注意事项

### 执行前准备
1. **数据备份**: 强烈建议在执行迁移前备份数据库
2. **系统维护**: 建议在系统维护时间窗口执行迁移
3. **资源监控**: 监控数据库CPU和内存使用情况
4. **网络稳定**: 确保网络连接稳定，避免迁移中断

### 迁移过程中
1. **避免中断**: 不要强制终止迁移进程
2. **监控日志**: 密切关注迁移过程中的日志输出
3. **性能影响**: 迁移过程中数据库性能可能受到影响
4. **并发限制**: 避免同时执行其他大量数据库操作

### 迁移完成后
1. **数据验证**: 验证关键达人的视频数据是否正确迁移
2. **功能测试**: 测试视频相关功能是否正常工作
3. **性能检查**: 检查新表结构的查询性能
4. **清理确认**: 确认videoDetails字段已被正确清空

## 🧪 测试验证

### 运行测试脚本
```bash
# 运行完整的迁移功能测试
node scripts/test_video_migration.js
```

### 测试覆盖范围
- ✅ 数据迁移流程测试
- ✅ 重复数据处理测试
- ✅ 数据完整性验证测试
- ✅ 错误处理机制测试
- ✅ 事务回滚测试

## 🔧 配置选项

### 批量处理大小
```javascript
// 在迁移脚本中调整批量大小
migrator.batchSize = 10; // 默认为10，可根据服务器性能调整
```

### 迁移策略
- **保守策略**: 较小的批量大小，更多的验证步骤
- **快速策略**: 较大的批量大小，适用于性能较好的服务器
- **安全策略**: 每个达人单独事务，最大化数据安全

## 📞 故障排除

### 常见问题

**Q: 迁移过程中出现"连接超时"错误**
A: 检查数据库连接配置，增加连接超时时间，或减小批量处理大小

**Q: 某些达人的视频数据迁移失败**
A: 查看错误日志，通常是数据格式问题，可以手动修复后重新运行

**Q: 迁移完成后发现数据不一致**
A: 运行验证脚本检查具体问题，必要时执行回滚操作

**Q: 迁移过程中系统性能下降**
A: 调整批量处理大小，增加批次间的延迟时间

### 紧急处理
如果迁移过程中出现严重问题：

1. **立即停止**: 如果可能，优雅停止迁移进程
2. **数据检查**: 检查已迁移的数据是否完整
3. **执行回滚**: 如有必要，执行回滚操作
4. **恢复备份**: 最后手段，从备份恢复数据

## 🎉 总结

视频数据迁移脚本提供了安全、可靠的数据迁移解决方案，具备完整的错误处理和回滚机制。通过分批处理和事务保护，确保了大量数据迁移的安全性和可靠性。

迁移完成后，系统将拥有更清晰的数据结构和更好的查询性能，为后续的功能开发和数据分析提供坚实的基础。
