# Excel文件生成服务

> 📋 **相关文档**: [MCP服务文档](./MCP_SERVICE.md) - 了解如何通过MCP接口使用Excel导出功能

## 概述

Excel文件生成服务是一个独立的工具类服务，提供将JSON数据转换为Excel文件的功能。支持单工作表和多工作表模式，具有自动文件清理、错误处理和参数验证等特性。

## 功能特性

- ✅ **单工作表Excel生成**：将JSON数组转换为Excel文件
- ✅ **多工作表Excel生成**：支持在一个Excel文件中创建多个工作表
- ✅ **自定义文件名**：支持自定义文件名，自动生成唯一标识
- ✅ **自动列宽调整**：根据内容自动计算合适的列宽
- ✅ **中文支持**：完全支持中文文件名和内容
- ✅ **文件管理**：自动清理过期文件，避免磁盘空间占用
- ✅ **错误处理**：完善的参数验证和错误处理机制
- ✅ **RESTful API**：提供标准的REST API接口
- ✅ **Swagger文档**：完整的API文档和在线测试

## API接口

### 1. 生成Excel文件

**接口**: `POST /api/excel/generate`

**请求体**:
```json
{
  "data": [
    {"姓名": "张三", "年龄": 25, "城市": "北京"},
    {"姓名": "李四", "年龄": 30, "城市": "上海"}
  ],
  "fileName": "用户数据",
  "formatting": {
    "headerStyle": true
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "Excel文件生成成功",
  "data": {
    "downloadUrl": "http://localhost:3001/exports/用户数据_20241215_12345678.xlsx",
    "fileName": "用户数据_20241215_12345678.xlsx",
    "fileSize": 16384,
    "fileSizeFormatted": "16.00 KB"
  }
}
```

### 2. 生成多工作表Excel文件

**接口**: `POST /api/excel/generate-multi`

**请求体**:
```json
{
  "sheets": [
    {
      "name": "用户信息",
      "data": [
        {"ID": 1, "姓名": "张三", "部门": "技术部"},
        {"ID": 2, "姓名": "李四", "部门": "产品部"}
      ]
    },
    {
      "name": "销售数据",
      "data": [
        {"月份": "2024-01", "销售额": 100000},
        {"月份": "2024-02", "销售额": 120000}
      ]
    }
  ],
  "fileName": "综合报表"
}
```

### 3. 生成示例文件

**接口**: `POST /api/excel/generate-sample`

生成包含示例数据的Excel文件，用于快速测试功能。

### 4. 检查文件是否存在

**接口**: `GET /api/excel/check/{fileName}`

检查指定的Excel文件是否存在。

### 5. 获取服务状态

**接口**: `GET /api/excel/status`

获取Excel服务的运行状态和统计信息。

### 6. 清理过期文件

**接口**: `POST /api/excel/cleanup`

手动触发过期文件清理操作。

## 使用示例

### JavaScript/前端调用

```javascript
// 生成简单Excel文件
async function generateExcel() {
  const data = [
    {"产品名称": "iPhone 15", "价格": 5999, "库存": 100},
    {"产品名称": "MacBook Pro", "价格": 12999, "库存": 50}
  ];
  
  const response = await fetch('/api/excel/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      data: data,
      fileName: '产品列表',
      formatting: { headerStyle: true }
    })
  });
  
  const result = await response.json();
  
  if (result.success) {
    // 下载文件
    window.open(result.data.downloadUrl, '_blank');
  }
}

// 生成多工作表Excel文件
async function generateMultiSheetExcel() {
  const sheets = [
    {
      name: "用户信息",
      data: [
        {"ID": 1, "姓名": "张三", "部门": "技术部"},
        {"ID": 2, "姓名": "李四", "部门": "产品部"}
      ]
    },
    {
      name: "销售数据", 
      data: [
        {"月份": "2024-01", "销售额": 100000},
        {"月份": "2024-02", "销售额": 120000}
      ]
    }
  ];
  
  const response = await fetch('/api/excel/generate-multi', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      sheets: sheets,
      fileName: '综合报表'
    })
  });
  
  const result = await response.json();
  
  if (result.success) {
    window.open(result.data.downloadUrl, '_blank');
  }
}
```

### Node.js/后端调用

```javascript
const ExcelService = require('./src/services/ExcelService');

async function example() {
  const excelService = new ExcelService();
  await excelService.initialize();
  
  // 生成Excel文件
  const result = await excelService.generateExcel({
    data: [
      {"姓名": "张三", "年龄": 25},
      {"姓名": "李四", "年龄": 30}
    ],
    fileName: '用户数据',
    formatting: { headerStyle: true }
  });
  
  console.log('文件生成成功:', result.downloadUrl);
}
```

## 配置说明

### 环境变量

```bash
# .env 文件
BASE_URL=http://localhost:3001  # 服务基础URL，用于生成下载链接
```

### 文件存储

- **存储目录**: `public/exports/`
- **文件格式**: `.xlsx`
- **文件命名**: `{fileName}_{timestamp}_{uuid}.xlsx`
- **自动清理**: 24小时后自动删除过期文件

### 文件大小限制

- **请求体大小**: 最大10MB（在app.js中配置）
- **生成的Excel文件**: 无特定限制，取决于数据量

## 测试

### 运行自动化测试

```bash
node scripts/test_excel_service.js
```

### 访问测试页面

打开浏览器访问: `http://localhost:3001/excel-test.html`

### API文档

访问Swagger文档: `http://localhost:3001/api-docs`

## 技术实现

### 核心依赖

- **xlsx**: Excel文件读写库
- **uuid**: 生成唯一文件标识
- **koa**: Web框架
- **fs/promises**: 文件系统操作

### 架构设计

```
src/
├── services/
│   └── ExcelService.js      # Excel服务核心逻辑
├── controllers/
│   └── ExcelController.js   # API控制器
├── routes/
│   └── excel.js            # 路由配置
└── utils/
    └── excel.js            # Excel工具类（原有）
```

### 文件清理机制

- **定时清理**: 每小时执行一次自动清理
- **清理条件**: 文件创建时间超过24小时
- **手动清理**: 提供API接口手动触发清理

## 错误处理

### 常见错误

1. **数据源为空**: 返回400错误
2. **JSON格式错误**: 返回400错误
3. **文件生成失败**: 返回500错误
4. **文件不存在**: 返回404错误

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 安全考虑

1. **文件名验证**: 防止路径遍历攻击
2. **数据大小限制**: 防止内存溢出
3. **自动清理**: 防止磁盘空间耗尽
4. **错误信息**: 不暴露敏感系统信息

## 扩展功能

### 可能的扩展

1. **样式支持**: 使用exceljs库支持更丰富的样式
2. **模板支持**: 支持Excel模板填充
3. **数据验证**: 支持Excel数据验证规则
4. **图表支持**: 支持在Excel中插入图表
5. **密码保护**: 支持生成密码保护的Excel文件

### 集成建议

1. **权限控制**: 可以集成现有的JWT认证
2. **日志记录**: 记录文件生成和下载日志
3. **监控告警**: 监控文件生成频率和存储空间
4. **CDN集成**: 将生成的文件上传到CDN

## 故障排除

### 常见问题

1. **文件无法下载**: 检查BASE_URL配置和静态文件服务
2. **中文乱码**: 确保使用UTF-8编码
3. **内存不足**: 减少单次处理的数据量
4. **磁盘空间不足**: 检查自动清理是否正常工作

### 调试方法

1. 查看服务器日志
2. 检查exports目录权限
3. 验证API响应格式
4. 使用测试页面进行功能验证
