# API频率控制指南

## 🎯 概述

本文档详细说明了批量更新脚本中的API频率控制机制，确保符合CRM系统的**60次/20秒**访问限制，避免因请求过频而导致的API调用失败。

## ⚠️ CRM API限制

### 官方限制
- **频率限制**: 60次/20秒
- **等效限制**: 3次/秒
- **超限后果**: API调用被拒绝，可能导致批量处理失败

### 安全策略
- **安全间隔**: 500ms (约2次/秒)
- **安全余量**: 33% (60次 → 40次/20秒)
- **测试模式**: 800ms (更保守，约1.25次/秒)

## 🔧 频率控制实现

### 1. 配置常量

```javascript
// CRM API频率控制配置
const CRM_API_LIMITS = {
  maxRequestsPer20Seconds: 60, // CRM API限制：60次/20秒
  safeRequestInterval: 500, // 安全请求间隔：500ms (约2次/秒，留有余量)
  batchDelay: 2000, // 批次间延迟：2秒
  testModeInterval: 800 // 测试模式间隔：800ms (更保守)
};
```

### 2. 频率控制方法

```javascript
/**
 * API频率控制延迟
 * @param {string} mode 模式：'normal' | 'test' | 'batch'
 */
async apiRateLimit(mode = 'normal') {
  let delay;
  switch (mode) {
    case 'test':
      delay = CRM_API_LIMITS.testModeInterval; // 800ms
      break;
    case 'batch':
      delay = CRM_API_LIMITS.batchDelay; // 2000ms
      break;
    default:
      delay = CRM_API_LIMITS.safeRequestInterval; // 500ms
  }
  
  if (delay > 0) {
    await new Promise(resolve => setTimeout(resolve, delay));
  }
}
```

### 3. 应用场景

#### 3.1 测试模式频率控制
```javascript
// 在testCrmUpdate调用后
if (record.externalCustomerId) {
  const testResult = await this.testCrmUpdate(/* ... */);
  // 测试模式下的API频率控制 (更保守的间隔)
  await this.apiRateLimit('test');
}
```

#### 3.2 正式执行频率控制
```javascript
// 在生成的批量更新脚本中
for (const task of updateTasks) {
  // 执行CRM更新
  const result = await crmService.updateCrmData(/* ... */);
  
  // 添加延迟避免请求过快 (CRM API限制: 60次/20秒，安全间隔500ms)
  await new Promise(resolve => setTimeout(resolve, 500));
}
```

#### 3.3 批次间延迟
```javascript
// 批次处理间的延迟
for (let i = 0; i < batches; i++) {
  // 处理当前批次
  await this.processBatch(batch);
  
  // 批次间延迟
  if (i < batches - 1) {
    await this.apiRateLimit('batch');
  }
}
```

## 📊 性能分析

### 时间成本计算

| 批量大小 | 正常模式 | 测试模式 | 说明 |
|----------|----------|----------|------|
| 5条记录 | 2.0秒 | 3.2秒 | 小批量测试 |
| 10条记录 | 4.5秒 | 7.2秒 | 推荐批量 |
| 20条记录 | 9.5秒 | 15.2秒 | 大批量处理 |
| 50条记录 | 24.5秒 | 39.2秒 | 最大批量 |

### 频率控制效果

| 模式 | 间隔时间 | 20秒内请求数 | 安全余量 |
|------|----------|--------------|----------|
| 正常模式 | 500ms | 40次 | 33% |
| 测试模式 | 800ms | 25次 | 58% |
| 批次间 | 2000ms | 10次 | 83% |

## 🚀 使用建议

### 1. 批量大小选择

```bash
# 小批量测试（推荐首次使用）
node scripts/batch-update-influencer-platform-info.js --test-only --batch-size 5

# 正常批量处理（推荐日常使用）
node scripts/batch-update-influencer-platform-info.js --batch-size 10

# 大批量处理（谨慎使用）
node scripts/batch-update-influencer-platform-info.js --batch-size 20
```

### 2. 执行时机建议

- **业务低峰期**: 避免与其他系统竞争API资源
- **分时段执行**: 大量数据分多个时段处理
- **监控执行**: 观察API响应时间和成功率

### 3. 错误处理策略

```javascript
// 建议的重试逻辑
async function safeApiCall(apiFunction, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiFunction();
    } catch (error) {
      if (error.message.includes('频率限制') && i < maxRetries - 1) {
        // 频率限制错误，增加延迟后重试
        await new Promise(resolve => setTimeout(resolve, 2000));
        continue;
      }
      throw error;
    }
  }
}
```

## 🔍 监控和调试

### 1. 执行日志示例

```
🚀 批量处理达人平台信息脚本启动
📋 执行模式: 正式执行
📦 批处理大小: 10
🧪 测试模式: 否
⏱️  API频率控制: 60次/20秒 (间隔500ms)

🔄 更新客户 美妆达人小红 (ID: 2700447) - 平台用户ID: abc123, 操作用户: 216865175626190590
✅ 更新成功: 美妆达人小红
⏳ API频率控制延迟: 500ms

🔄 更新客户 时尚博主Lisa (ID: 2700448) - 平台用户ID: def456, 操作用户: 216865175626190590
✅ 更新成功: 时尚博主Lisa
⏳ API频率控制延迟: 500ms
```

### 2. 性能监控指标

- **平均响应时间**: 监控CRM API响应时间
- **成功率**: 统计API调用成功率
- **频率合规性**: 确保不超过60次/20秒限制
- **错误率**: 监控频率限制相关错误

### 3. 调试模式

```bash
# 启用详细日志
DEBUG=crm:api node scripts/batch-update-influencer-platform-info.js --test-only --batch-size 3
```

## ⚡ 优化建议

### 1. 动态频率调整

```javascript
// 根据API响应时间动态调整间隔
class AdaptiveRateLimit {
  constructor() {
    this.baseInterval = 500;
    this.currentInterval = 500;
  }
  
  adjustInterval(responseTime) {
    if (responseTime > 2000) {
      // API响应慢，增加间隔
      this.currentInterval = Math.min(this.currentInterval * 1.2, 1000);
    } else if (responseTime < 500) {
      // API响应快，可以适当减少间隔
      this.currentInterval = Math.max(this.currentInterval * 0.9, this.baseInterval);
    }
  }
}
```

### 2. 批量优化策略

- **智能分批**: 根据数据复杂度动态调整批量大小
- **并发控制**: 在频率限制内使用适度并发
- **缓存机制**: 缓存重复的API调用结果

### 3. 错误恢复

- **断点续传**: 记录处理进度，支持中断后继续
- **失败重试**: 对临时性错误进行智能重试
- **降级策略**: API不可用时的备用方案

## 📋 最佳实践

### 1. 执行前检查

```bash
# 1. 验证API连接
curl -X GET "CRM_API_ENDPOINT/health" -H "Authorization: Bearer TOKEN"

# 2. 小批量测试
node scripts/batch-update-influencer-platform-info.js --dry-run --batch-size 3

# 3. 接口测试
node scripts/batch-update-influencer-platform-info.js --test-only --batch-size 5
```

### 2. 执行中监控

- 观察控制台输出的频率控制日志
- 监控CRM系统的API使用统计
- 注意任何频率限制警告或错误

### 3. 执行后验证

- 检查更新成功率统计
- 验证CRM系统中的数据更新
- 分析执行日志中的性能指标

## 🛡️ 安全保障

### 1. 多重保护

- **配置级保护**: 在配置中设置安全间隔
- **代码级保护**: 在每个API调用后强制延迟
- **监控级保护**: 实时监控API调用频率

### 2. 异常处理

- **频率超限**: 自动增加延迟时间
- **API错误**: 区分频率限制和其他错误
- **网络异常**: 提供重试和降级机制

### 3. 合规性检查

- **执行前验证**: 检查配置是否符合API限制
- **执行中监控**: 实时监控频率合规性
- **执行后审计**: 生成频率控制报告

---

## 📈 总结

### 关键特性
1. ✅ **严格遵守API限制**: 60次/20秒
2. ✅ **多模式频率控制**: 正常/测试/批次
3. ✅ **安全余量设计**: 33%安全余量
4. ✅ **动态延迟控制**: 根据模式调整间隔
5. ✅ **完整监控日志**: 详细的执行信息

### 业务价值
1. **稳定性保障**: 避免API调用被拒绝
2. **合规性确保**: 严格遵守CRM系统限制
3. **可预测性**: 明确的执行时间预估
4. **可维护性**: 清晰的配置和监控机制

**⚠️ 重要提醒**: 
- 首次使用请务必进行小批量测试
- 在业务低峰期执行大批量处理
- 密切监控API调用频率和成功率
- 遇到频率限制错误时适当增加延迟
