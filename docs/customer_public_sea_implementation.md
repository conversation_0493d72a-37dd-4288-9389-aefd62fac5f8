# 客户公海字段(customerPublicSea)实现文档

## 概述

本文档详细说明了客户管理系统中"所属公海"字段(customerPublicSea)的完整实现，包括数据库模型、服务层处理、CRM集成和前端表单的各个环节。

## 字段信息

- **字段名称**: customerPublicSea
- **数据库字段**: customer_public_sea
- **数据类型**: VARCHAR(100)
- **默认值**: `fd18e0d6b1164e7080f0fa91dc43b0d8`
- **CRM映射字段**: seas_id
- **是否必填**: 否（但有默认值）

## 实现层级

### 1. 数据库模型层 (src/models/CooperationManagement.js)

```javascript
customerPublicSea: {
  type: DataTypes.STRING(100),
  field: 'customer_public_sea',
  allowNull: true,
  defaultValue: 'fd18e0d6b1164e7080f0fa91dc43b0d8',
  comment: '所属公海'
}
```

**特性**:
- 设置了数据库级别的默认值
- 允许为空但有默认值保障
- 字段长度为100字符

### 2. 服务层处理 (src/services/CooperationService.js)

```javascript
// 确保customerPublicSea字段有默认值
if (!data.customerPublicSea) {
  data.customerPublicSea = 'fd18e0d6b1164e7080f0fa91dc43b0d8';
}
```

**特性**:
- 在客户创建时进行二次保障
- 确保即使前端未传递该字段也有默认值
- 与数据库默认值保持一致

### 3. CRM数据映射 (src/services/CrmDataMappingService.js)

#### 字段映射配置
```javascript
this.customerFieldMapping = {
  'seas_id': 'customerPublicSea',  // CRM字段 -> 本地字段
  // ... 其他字段映射
};
```

#### 默认值处理
```javascript
// 在客户数据映射中
addCustomerDefaultFields(mappedData) {
  // 设置默认公海ID（如果CRM数据中没有提供）
  if (!mappedData.customerPublicSea) {
    mappedData.customerPublicSea = 'fd18e0d6b1164e7080f0fa91dc43b0d8';
  }
}

// 在协议数据映射中也有相同处理
addAgreementDefaultFields(mappedData) {
  // 设置默认公海ID（如果CRM数据中没有提供）
  if (!mappedData.customerPublicSea) {
    mappedData.customerPublicSea = 'fd18e0d6b1164e7080f0fa91dc43b0d8';
  }
}
```

**特性**:
- 支持CRM系统中seas_id字段的双向映射
- 在CRM数据同步时自动设置默认值
- 确保数据一致性

### 4. CRM数据整合 (src/services/CrmIntegrationService.js)

```javascript
const customerFields = [
  'customerHomepage',
  'customerPublicSea',  // 包含在客户字段列表中
  'seedingPlatform',
  // ... 其他字段
];
```

**特性**:
- 在客户和协议数据整合时正确处理
- 确保字段在数据合并时不丢失

### 5. 前端表单 (frontend/src/components/CooperationForm.vue)

#### 表单字段定义
```vue
<a-form-item label="所属公海" field="customerPublicSea">
  <a-select v-model="form.customerPublicSea" placeholder="请选择" :loading="loadingDictionaries">
    <a-option
      v-for="item in dictionaryOptions['customerPublicSea'] || []"
      :key="item.dictKey"
      :value="item.dictKey"
    >
      {{ item.dictLabel }}
    </a-option>
  </a-select>
</a-form-item>
```

#### 默认值设置
```javascript
const form = reactive({
  customerPublicSea: 'fd18e0d6b1164e7080f0fa91dc43b0d8',
  // ... 其他字段
});
```

**特性**:
- 使用下拉选择框，支持字典数据
- 前端表单默认值与后端保持一致
- 支持动态加载字典选项

## 数据流程

### 客户创建流程
1. **前端表单** → 默认值: `fd18e0d6b1164e7080f0fa91dc43b0d8`
2. **控制器验证** → 基础验证通过
3. **服务层处理** → 确保默认值存在
4. **数据库插入** → 使用模型默认值作为最后保障

### CRM数据同步流程
1. **CRM数据获取** → seas_id字段
2. **字段映射** → seas_id → customerPublicSea
3. **默认值处理** → 如果为空则设置默认值
4. **数据库存储** → 完整的客户数据

## 兼容性保障

### 现有数据兼容
- 提供SQL脚本更新现有记录的默认值
- 不影响已有的非空customerPublicSea值
- 向后兼容所有现有功能

### 多层默认值保障
1. **数据库级别**: 模型定义中的defaultValue
2. **服务层级别**: 创建时的显式检查
3. **CRM映射级别**: 数据映射时的默认值设置
4. **前端级别**: 表单的初始默认值

## 测试验证

提供了完整的测试脚本 `test/customer_public_sea_test.js`，包括：
- 数据库模型默认值测试
- 服务层默认值处理测试
- CRM数据映射默认值测试
- CRM字段映射测试

## 维护说明

### 修改默认公海ID
如需修改默认公海ID，需要同时更新以下位置：
1. `src/models/CooperationManagement.js` - 数据库模型默认值
2. `src/services/CooperationService.js` - 服务层默认值
3. `src/services/CrmDataMappingService.js` - CRM映射默认值
4. `frontend/src/components/CooperationForm.vue` - 前端表单默认值

### 数据库迁移
使用提供的SQL脚本 `sql/update_customer_public_sea_default.sql` 来：
- 更新表结构添加默认值
- 更新现有记录的空值
- 验证更新结果

## 总结

customerPublicSea字段的实现确保了：
- ✅ 数据完整性：多层默认值保障
- ✅ 系统兼容性：向后兼容现有数据
- ✅ CRM集成：正确的字段映射和同步
- ✅ 用户体验：前端表单的友好交互
- ✅ 可维护性：清晰的代码结构和文档
