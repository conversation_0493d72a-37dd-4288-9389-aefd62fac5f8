# 视频字段迁移文档

## 概述

本次迁移将 `videoStats` 和 `videoDetails` 两个字段从 `CrawlResult` 模型迁移到 `Influencer` 模型中，以便在达人表中直接存储和管理视频统计数据和详细信息。

## 迁移内容

### 1. 字段定义

#### videoStats 字段
- **类型**: `DataTypes.JSON`
- **数据库字段名**: `video_stats`
- **注释**: '视频统计数据（播放量、点赞数等）'
- **数据结构**:
  ```json
  {
    "videoCount": 25,
    "averagePlay": 35000,
    "totalPlay": 875000,
    "totalLike": 87500,
    "totalComment": 8750,
    "totalShare": 1750
  }
  ```

#### videoDetails 字段
- **类型**: `DataTypes.JSON`
- **数据库字段名**: `video_details`
- **注释**: '详细视频信息列表'
- **数据结构**:
  ```json
  [
    {
      "videoId": "video_001",
      "title": "视频标题",
      "playCount": 45000,
      "likeCount": 4500,
      "commentCount": 450,
      "shareCount": 225,
      "publishTime": "2025-01-12T16:30:00Z",
      "duration": 180,
      "videoUrl": "https://example.com/video.mp4",
      "videoCover": "https://example.com/cover.jpg"
    }
  ]
  ```

### 2. 修改的文件

#### 2.1 模型文件
- **文件**: `src/models/Influencer.js`
- **修改**: 在第68-72行之间添加了两个新字段定义

#### 2.2 数据库初始化文件
- **文件**: `sql/init.sql`
- **修改**: 在达人表创建语句中添加了两个新字段

#### 2.3 控制器文件
- **文件**: `src/controllers/CrawlerController.js`
- **修改**: 在 `importResults` 方法中添加了对这两个字段的处理

### 3. 数据库变更

#### 3.1 表结构变更
```sql
ALTER TABLE influencers 
ADD COLUMN video_stats JSON COMMENT '视频统计数据（播放量、点赞数等）' AFTER cooperation_history;

ALTER TABLE influencers 
ADD COLUMN video_details JSON COMMENT '详细视频信息列表' AFTER video_stats;
```

#### 3.2 字段位置
新字段位于 `cooperation_history` 字段之后，`notes` 字段之前。

### 4. 爬虫逻辑更新

#### 4.1 数据流程
1. **爬虫采集**: 爬虫继续将数据保存到 `CrawlResult` 表
2. **数据导入**: 导入时将 `videoStats` 和 `videoDetails` 从 `CrawlResult` 复制到 `Influencer`
3. **数据存储**: 达人表现在包含完整的视频统计和详情信息

#### 4.2 导入逻辑
所有导入方法都已更新以处理新字段：
- `importResults()` - 批量导入
- `batchImportResults()` - 批量导入

### 5. 测试验证

#### 5.1 测试脚本
创建了以下测试脚本来验证迁移：
- `scripts/migrate_video_fields.js` - 数据库迁移脚本
- `scripts/test_video_fields.js` - 字段功能测试
- `scripts/test_crawl_import.js` - 导入功能测试
- `scripts/test_complete_migration.js` - 完整流程测试
- `scripts/verify_table_structure.js` - 表结构验证

#### 5.2 测试结果
所有测试均通过，验证了：
- ✅ 数据库字段正确添加
- ✅ 模型定义正确
- ✅ 数据可以正确保存和读取
- ✅ 导入逻辑正确处理新字段
- ✅ 数据完整性保持

### 6. 兼容性说明

#### 6.1 向后兼容
- 现有的 `CrawlResult` 表结构保持不变
- 现有的爬虫逻辑继续正常工作
- 现有的导入功能得到增强，不会破坏原有功能

#### 6.2 数据迁移
- 新字段为可选字段（允许 NULL）
- 现有达人记录不受影响
- 新导入的达人将包含视频数据

### 7. 使用示例

#### 7.1 查询达人的视频统计
```javascript
const influencer = await Influencer.findByPk(id);
if (influencer.videoStats) {
  console.log('视频总数:', influencer.videoStats.videoCount);
  console.log('平均播放量:', influencer.videoStats.averagePlay);
  console.log('总播放量:', influencer.videoStats.totalPlay);
}
```

#### 7.2 查询达人的视频详情
```javascript
const influencer = await Influencer.findByPk(id);
if (influencer.videoDetails && Array.isArray(influencer.videoDetails)) {
  influencer.videoDetails.forEach(video => {
    console.log(`视频: ${video.title}, 播放量: ${video.playCount}`);
  });
}
```

### 8. 注意事项

#### 8.1 数据格式
- 确保 `videoStats` 包含所有必要的统计字段
- 确保 `videoDetails` 是一个数组格式
- 日期字段使用 ISO 8601 格式

#### 8.2 性能考虑
- JSON 字段查询性能相对较低，如需频繁查询可考虑添加索引
- 大量视频详情数据可能影响查询性能

#### 8.3 维护建议
- 定期检查数据完整性
- 监控字段使用情况
- 根据业务需求优化数据结构

## 总结

本次迁移成功将视频相关字段从爬虫结果表迁移到达人表，实现了：
1. 达人信息的完整性提升
2. 数据查询的便利性增强
3. 业务逻辑的简化
4. 系统架构的优化

迁移过程保持了系统的稳定性和数据的完整性，所有功能正常运行。
