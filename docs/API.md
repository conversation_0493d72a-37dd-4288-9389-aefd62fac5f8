# 达人信息管理系统 API 文档

## 基础信息

- **基础URL**: `http://localhost:3000`
- **API版本**: v1.0.0
- **认证方式**: JWT Bearer Token

## 认证说明

除了登录和注册接口外，其他接口都需要在请求头中携带JWT令牌：

```
Authorization: Bearer <your_jwt_token>
```

## 响应格式

所有API响应都遵循统一格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {}
}
```

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `409`: 资源冲突
- `422`: 数据验证失败
- `500`: 服务器内部错误

## API接口

### 1. 认证相关

#### 1.1 用户注册

**POST** `/api/auth/register`

**请求参数:**
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "user",
    "status": "active"
  }
}
```

#### 1.2 用户登录

**POST** `/api/auth/login`

**请求参数:**
```json
{
  "username": "admin",
  "password": "admin123456"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### 1.3 获取当前用户信息

**GET** `/api/auth/me`

**响应示例:**
```json
{
  "success": true,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

#### 1.4 修改密码

**PUT** `/api/auth/change-password`

**请求参数:**
```json
{
  "oldPassword": "oldpassword123",
  "newPassword": "newpassword123"
}
```

### 2. 达人管理

#### 2.1 获取达人列表

**GET** `/api/influencers`

**查询参数:**
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）
- `platform`: 平台筛选（xiaohongshu/juxingtu）
- `category`: 分类筛选
- `keyword`: 关键词搜索
- `minFollowers`: 最小粉丝数
- `maxFollowers`: 最大粉丝数
- `sortBy`: 排序字段（默认createdAt）
- `sortOrder`: 排序方向（ASC/DESC，默认DESC）

**响应示例:**
```json
{
  "success": true,
  "message": "获取达人列表成功",
  "data": [
    {
      "id": 1,
      "nickname": "美妆小仙女",
      "platform": "xiaohongshu",
      "followersCount": 150000,
      "category": "美妆"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 5,
    "pages": 1
  }
}
```

#### 2.2 获取达人详情

**GET** `/api/influencers/:id`

#### 2.3 创建达人

**POST** `/api/influencers`

**请求参数:**
```json
{
  "nickname": "新达人",
  "platform": "xiaohongshu",
  "platformId": "xhs_new",
  "followersCount": 10000,
  "category": "美妆",
  "tags": ["护肤", "彩妆"],
  "contactInfo": {
    "phone": "13800138000",
    "wechat": "new_influencer"
  },
  "priceInfo": {
    "post": 3000,
    "video": 5000
  },
  "notes": "新签约达人"
}
```

#### 2.4 更新达人

**PUT** `/api/influencers/:id`

#### 2.5 删除达人

**DELETE** `/api/influencers/:id`

#### 2.6 批量删除达人

**POST** `/api/influencers/batch-delete`

**请求参数:**
```json
{
  "ids": [1, 2, 3]
}
```

#### 2.7 获取统计信息

**GET** `/api/influencers/stats`

#### 2.8 导出Excel

**GET** `/api/influencers/export`

**查询参数:** 与获取达人列表相同的筛选参数

**响应:** Excel文件下载

## 默认账户

系统初始化时会创建一个默认管理员账户：

- **用户名**: admin
- **密码**: admin123456
- **角色**: admin

## 数据字段说明

### 用户字段
- `username`: 用户名（3-20位字母、数字、下划线）
- `email`: 邮箱地址
- `password`: 密码（至少8位，包含字母和数字）
- `role`: 角色（admin/user）
- `status`: 状态（active/inactive）

### 达人字段
- `nickname`: 达人昵称
- `platform`: 平台（xiaohongshu/juxingtu）
- `platformId`: 平台ID
- `avatarUrl`: 头像链接
- `followersCount`: 粉丝数量
- `category`: 分类
- `tags`: 标签数组
- `contactInfo`: 联系方式对象
- `priceInfo`: 报价信息对象
- `cooperationHistory`: 合作历史数组
- `notes`: 备注
- `status`: 状态（active/inactive）
