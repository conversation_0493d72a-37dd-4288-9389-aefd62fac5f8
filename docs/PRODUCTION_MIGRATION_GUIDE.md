# 生产环境播放量中位数字段迁移指南

## 📋 迁移概述

本指南用于在生产环境数据库中安全地添加播放量中位数（play_mid）字段。

## ⚠️ 重要提醒

**在执行任何生产环境数据库操作前，请务必：**

1. **备份数据库** - 创建完整的数据库备份
2. **维护窗口** - 在低峰时段执行迁移
3. **测试验证** - 在测试环境先验证迁移脚本
4. **回滚准备** - 准备回滚方案

## 🗄️ 生产环境信息

- **服务器**: *************:3306
- **数据库**: daren_db
- **用户**: daren_db
- **影响表**: my_influencers, public_influencers

## 🚀 迁移步骤

### 步骤1: 数据库备份

```bash
# 在生产服务器上执行
mysqldump -h ************* -u daren_db -p daren_db > daren_db_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 步骤2: 验证当前状态

```bash
# 连接到生产数据库检查当前表结构
mysql -h ************* -u daren_db -p daren_db

# 检查表记录数
SELECT 'my_influencers' as table_name, COUNT(*) as record_count FROM my_influencers
UNION ALL
SELECT 'public_influencers' as table_name, COUNT(*) as record_count FROM public_influencers;

# 检查是否已有 play_mid 字段
SELECT TABLE_NAME, COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'daren_db' 
  AND TABLE_NAME IN ('my_influencers', 'public_influencers')
  AND COLUMN_NAME = 'play_mid';
```

### 步骤3: 执行迁移

#### 方法A: 使用Node.js脚本（推荐）

```bash
# 在项目根目录执行
node scripts/migrate_production_play_mid.js
```

#### 方法B: 使用SQL脚本

```bash
# 执行SQL迁移脚本
mysql -h ************* -u daren_db -p daren_db < sql/migrations/production_add_play_mid_field.sql
```

### 步骤4: 验证迁移结果

```bash
# 再次执行验证脚本
node scripts/migrate_production_play_mid.js
```

预期输出：
```
✅ 生产环境数据库连接成功
📋 当前数据库: daren_db
📋 play_mid 字段信息:
   my_influencers.play_mid: varchar(50), NULL=YES
   public_influencers.play_mid: varchar(50), NULL=YES
🎉 生产环境播放量中位数字段迁移完成！
```

## 🔍 迁移验证清单

- [ ] 数据库备份已创建
- [ ] my_influencers 表包含 play_mid 字段
- [ ] public_influencers 表包含 play_mid 字段
- [ ] 字段类型为 VARCHAR(50)
- [ ] 字段允许 NULL 值
- [ ] 现有数据记录数量未变化
- [ ] 应用程序正常启动
- [ ] 前端页面显示播放量中位数列

## 🔄 回滚方案

如果迁移出现问题，可以执行以下回滚操作：

```sql
-- 删除添加的字段
ALTER TABLE my_influencers DROP COLUMN play_mid;
ALTER TABLE public_influencers DROP COLUMN play_mid;

-- 或者从备份恢复
-- mysql -h ************* -u daren_db -p daren_db < daren_db_backup_YYYYMMDD_HHMMSS.sql
```

## 📊 迁移后操作

### 1. 重启应用服务

```bash
# 在生产服务器上重启Node.js应用
pm2 restart daren-server
# 或
systemctl restart daren-server
```

### 2. 验证应用功能

- [ ] 访问我的达人页面，确认播放量中位数列显示
- [ ] 访问达人公海页面，确认播放量中位数列显示
- [ ] 测试爬虫功能，确认新爬取的数据包含播放量中位数
- [ ] 测试达人导入功能

### 3. 监控日志

```bash
# 监控应用日志
pm2 logs daren-server
# 或
tail -f /var/log/daren-server/app.log
```

## 🚨 故障排除

### 常见问题

1. **字段已存在错误**
   - 检查字段是否已经添加
   - 使用 `IF NOT EXISTS` 语法

2. **权限不足**
   - 确认数据库用户有 ALTER 权限
   - 联系数据库管理员

3. **应用启动失败**
   - 检查模型定义是否正确
   - 验证数据库连接配置

### 联系信息

如遇到问题，请联系：
- 开发团队
- 数据库管理员
- 运维团队

## 📝 迁移记录

| 日期 | 操作者 | 操作内容 | 结果 | 备注 |
|------|--------|----------|------|------|
| 2025-07-21 | [操作者] | 添加play_mid字段 | [成功/失败] | [备注] |

---

**重要提醒**: 生产环境操作需谨慎，确保在维护窗口期间执行，并做好充分的备份和测试。
