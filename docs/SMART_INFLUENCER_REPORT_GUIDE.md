# 智能达人提报功能使用指南

## 📋 功能概述

智能达人提报功能是对传统提报流程的重大升级，通过四个阶段的智能化流程，大幅提升提报效率和准确性。

## 🚀 功能特性

### 1. 智能链接解析
- 支持小红书蒲公英平台链接自动解析
- 支持巨量星图平台链接自动解析
- 支持直接输入平台用户ID
- 自动识别平台类型和提取用户ID

### 2. 自动数据拉取
- 自动获取达人基础信息（昵称、粉丝数、作品数量等）
- 自动拉取达人所有作品数据
- 可选择是否关联作品库进行数据存储
- 实时显示数据拉取进度

### 3. 智能作品筛选
- 支持按作品标题关键词快速筛选
- 多选关联相关作品到提报单
- 显示作品关键指标（播放量、点赞量、收藏量）
- 作品列表分页展示

### 4. 自动表单填充
- 基于拉取的数据自动填充提报表单
- 智能生成达人主页链接
- 自动计算和填充关键指标
- 保留手动调整空间

## 📖 使用流程

### 第一阶段：达人信息预解析

1. 点击"智能提报"按钮
2. 在输入框中填入以下任一格式：
   - 小红书蒲公英链接：`https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/xxx`
   - 巨量星图链接：`https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/xxx`
   - 直接输入平台用户ID
3. 系统自动解析并显示平台类型和用户ID
4. 点击"解析并继续"进入下一步

### 第二阶段：达人数据拉取

1. 系统自动从对应平台拉取达人基础信息
2. 同时获取该达人的所有作品数据
3. 可选择"关联作品库"将作品数据保存到系统中
4. 等待数据拉取完成后点击"下一步"

### 第三阶段：作品筛选与关联

1. 浏览该达人的所有作品列表
2. 使用搜索框按标题关键词筛选作品
3. 勾选要关联到提报单的作品（支持多选）
4. 查看已选作品的统计信息
5. 点击"下一步（创建提报单）"

### 第四阶段：提报单创建

1. 系统自动填充达人基础信息：
   - 平台类型
   - 达人昵称
   - 粉丝数量
   - 主页链接
   - 播放量中位数
   - 平台报价信息
2. 手动完善必填信息：
   - 运营负责人（自动填充当前用户）
   - 选择理由（必填）
   - 合作价格（可选）
   - 备注信息（可选）
3. 查看关联的作品列表
4. 点击"提交提报"完成创建

## 🔧 技术实现

### 前端组件
- `SmartInfluencerReportModal.vue` - 智能提报主组件
- `InfluencerReportForm.vue` - 提报表单组件（已扩展支持预填充和关联作品）

### 后端接口
- `POST /api/influencer-reports/smart/author-info` - 获取达人基础信息
- `POST /api/influencer-reports/smart/author-videos` - 获取达人作品数据
- `POST /api/influencer-reports` - 创建提报（已扩展支持关联作品）

### 数据库扩展
- `influencer_reports.related_videos` - JSON字段，存储关联作品详情

### 工具函数
- `parseInfluencerInput()` - 链接解析函数
- `validatePlatformUserId()` - 平台用户ID验证函数

## 📊 数据格式

### 关联作品数据格式
```json
[
  {
    "videoId": "视频ID",
    "title": "视频标题",
    "playCount": 播放量,
    "likeCount": 点赞量,
    "collectCount": 收藏量,
    "publishTime": "发布时间",
    "videoUrl": "视频链接",
    "videoCover": "视频封面"
  }
]
```

## 🎯 优势对比

### 传统提报 vs 智能提报

| 功能 | 传统提报 | 智能提报 |
|------|----------|----------|
| 数据录入 | 手动输入所有信息 | 自动拉取并填充 |
| 作品关联 | 无 | 支持多作品关联 |
| 数据准确性 | 依赖人工 | 系统自动获取 |
| 操作效率 | 较低 | 显著提升 |
| 错误率 | 较高 | 大幅降低 |

## 🔍 注意事项

1. **网络要求**：需要稳定的网络连接以获取平台数据
2. **平台限制**：目前支持小红书和巨量星图，其他平台后续扩展
3. **数据时效**：拉取的数据为实时数据，建议及时完成提报
4. **权限要求**：需要相应的平台访问权限
5. **作品库关联**：可选功能，建议开启以便后续数据分析

## 🚀 后续扩展

1. 支持更多平台（抖音、微博、B站等）
2. 增加达人数据分析功能
3. 支持批量智能提报
4. 增加作品表现预测功能
5. 集成CRM系统自动同步

## 📞 技术支持

如遇到问题，请联系技术支持团队或查看系统日志获取详细错误信息。
