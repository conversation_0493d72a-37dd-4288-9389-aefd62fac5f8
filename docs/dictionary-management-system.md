# 字典管理系统

## 概述

字典管理系统是达人管理系统的核心功能模块，用于统一管理系统中的字典数据，支持本地字典维护和CRM系统数据同步。该系统为合作对接单模块提供了完整的字典数据支持，确保与客户CRM系统的数据一致性。

## 功能特性

### 🏗️ 核心功能
- **本地字典管理**: 支持字典项的增删改查操作
- **CRM字典同步**: 从客户CRM系统自动同步字典数据
- **数据合并策略**: 智能合并本地和CRM字典数据
- **分类管理**: 支持按分类组织字典数据
- **批量操作**: 支持批量导入和导出字典数据

### 🔄 CRM集成
- **自动同步**: 定期从CRM系统拉取最新字典数据
- **增量更新**: 支持增量同步，避免重复数据
- **错误处理**: 完善的错误处理和重试机制
- **数据映射**: 灵活的CRM字段到本地字典的映射配置

### 🎯 表单集成
- **动态下拉**: 为表单提供动态下拉选项
- **数据验证**: 确保表单数据的有效性
- **优先级策略**: CRM数据优先，本地数据补充
- **缓存机制**: 提高数据加载性能

## 系统架构

### 数据库设计

#### 本地字典表 (dictionaries)
```sql
CREATE TABLE dictionaries (
  id INT PRIMARY KEY AUTO_INCREMENT,
  category VARCHAR(50) NOT NULL,           -- 字典分类
  dict_key VARCHAR(100) NOT NULL,          -- 字典键值
  dict_label VARCHAR(200) NOT NULL,        -- 字典标签
  dict_value VARCHAR(200),                 -- 字典值
  sort_order INT DEFAULT 0,                -- 排序顺序
  status ENUM('active', 'inactive'),       -- 状态
  description TEXT,                        -- 描述
  created_by INT NOT NULL,                 -- 创建人
  updated_by INT NOT NULL,                 -- 更新人
  created_at TIMESTAMP,                    -- 创建时间
  updated_at TIMESTAMP                     -- 更新时间
);
```

#### CRM字典表 (crm_dictionaries)
```sql
CREATE TABLE crm_dictionaries (
  id INT PRIMARY KEY AUTO_INCREMENT,
  deploy_id VARCHAR(50) NOT NULL,          -- CRM部署ID
  field_name VARCHAR(200) NOT NULL,        -- CRM字段名称
  field_code VARCHAR(100) NOT NULL,        -- CRM字段代码
  field_dom_type VARCHAR(50) NOT NULL,     -- CRM字段类型
  data_id VARCHAR(100) NOT NULL,           -- CRM字典项ID
  data_name VARCHAR(200) NOT NULL,         -- CRM字典项名称
  data_value VARCHAR(200),                 -- CRM字典项值
  data_order INT DEFAULT 0,                -- CRM字典项排序
  local_category VARCHAR(100),             -- 本地分类映射
  local_key VARCHAR(100),                  -- 本地键值映射
  local_label VARCHAR(200),                -- 本地标签映射
  sync_status ENUM('pending', 'synced', 'failed', 'disabled'),
  sync_time DATETIME,                      -- 同步时间
  sync_version VARCHAR(50),                -- 同步版本
  sync_error TEXT,                         -- 同步错误
  raw_data JSON,                           -- 原始数据
  is_active BOOLEAN DEFAULT TRUE,          -- 是否启用
  is_deleted BOOLEAN DEFAULT FALSE,        -- 是否删除
  created_by INT,                          -- 创建人
  updated_by INT,                          -- 更新人
  created_at TIMESTAMP,                    -- 创建时间
  updated_at TIMESTAMP                     -- 更新时间
);
```

### 服务层架构

#### 1. DictionaryService (本地字典服务)
- 负责本地字典数据的CRUD操作
- 提供分类管理和搜索功能
- 支持批量操作和数据验证

#### 2. CrmDictionaryService (CRM字典服务)
- 负责从CRM系统获取字典数据
- 实现数据解析和本地存储
- 提供同步状态管理和错误处理

#### 3. DictionaryMixinService (前端字典混合服务)
- 统一管理本地和CRM字典数据
- 实现数据合并和优先级策略
- 提供缓存机制和性能优化

## API接口

### 本地字典管理 API

#### 获取字典分类
```http
GET /api/dictionaries/categories
```

#### 按分类获取字典项
```http
GET /api/dictionaries/category/{category}?activeOnly=true
```

#### 获取字典列表（分页）
```http
GET /api/dictionaries?page=1&pageSize=20&category=cooperation_form&status=active&keyword=搜索关键词
```

#### 创建字典项
```http
POST /api/dictionaries
Content-Type: application/json

{
  "category": "cooperation_form",
  "dictKey": "video",
  "dictLabel": "视频",
  "dictValue": "video",
  "sortOrder": 2,
  "status": "active",
  "description": "视频合作形式"
}
```

#### 更新字典项
```http
PUT /api/dictionaries/{id}
Content-Type: application/json

{
  "dictLabel": "更新后的标签",
  "status": "inactive"
}
```

#### 删除字典项
```http
DELETE /api/dictionaries/{id}
```

#### 批量创建字典项
```http
POST /api/dictionaries/batch
Content-Type: application/json

{
  "dictionaries": [
    {
      "category": "cooperation_form",
      "dictKey": "live",
      "dictLabel": "直播",
      "sortOrder": 3,
      "status": "active"
    }
  ]
}
```

### CRM字典管理 API

#### 测试CRM连接
```http
GET /api/crm-dictionaries/test
```

#### 获取CRM字典统计
```http
GET /api/crm-dictionaries/stats
```

#### 获取CRM字典数据
```http
GET /api/crm-dictionaries?deployId=customer&fieldCode=customer_select_1&activeOnly=true&page=1&pageSize=50
```

#### 获取字典选项（表单用）
```http
GET /api/crm-dictionaries/options/{deployId}/{fieldName}
```

#### 同步所有字典数据
```http
POST /api/crm-dictionaries/sync
```

#### 同步指定部署类型
```http
POST /api/crm-dictionaries/sync/{deployId}
```

#### 清理过期数据
```http
DELETE /api/crm-dictionaries/cleanup
Content-Type: application/json

{
  "daysOld": 30
}
```

## 前端使用

### 字典管理界面

访问 `/dictionaries` 路径可以打开字典管理界面，包含两个主要功能：

1. **CRM字典管理**: 查看和同步CRM字典数据
2. **本地字典管理**: 管理本地字典数据

### 在表单中使用字典

```javascript
import dictionaryService from '@/services/dictionaryService'

// 获取字典数据
const cooperationFormOptions = await dictionaryService.getDictionary('cooperation_form')

// 在表单中使用
<select v-model="form.cooperationForm">
  <option v-for="option in cooperationFormOptions" :key="option.value" :value="option.value">
    {{ option.label }}
  </option>
</select>
```

### 字典服务配置

```javascript
// 配置CRM字段映射
const crmFieldMapping = {
  customer: {
    'customer_select_1': 'cooperation_form',
    'customer_select_2': 'cooperation_brand'
  },
  contract: {
    'contract_select_1': 'content_implant_coefficient'
  }
}

// 获取合并字典数据
const options = await dictionaryService.getMergedDictionary('cooperation_form', {
  crmFirst: true,        // CRM数据优先
  includeLocal: true,    // 包含本地数据
  includeCrm: true       // 包含CRM数据
})
```

## 部署和配置

### 1. 数据库初始化

```bash
# 运行数据库迁移脚本
mysql -u username -p database_name < sql/crm_dictionaries.sql
```

### 2. CRM配置

在 `.env` 文件中配置CRM连接信息：

```env
# CRM配置
CRM_BASE_URL=http://*************:8788
CRM_CORP_ACCESS_TOKEN=your_token
CRM_CORP_ID=your_corp_id
```

### 3. 启动服务

```bash
# 启动后端服务
npm start

# 启动前端服务
cd frontend && npm run dev
```

## 测试和验证

### 运行测试脚本

```bash
# 运行完整测试
node scripts/test-dictionary-system.js

# 运行功能演示
node scripts/demo-dictionary-system.js
```

### 手动测试步骤

1. **访问字典管理界面**: 打开 `/dictionaries` 页面
2. **测试CRM连接**: 点击"测试连接"按钮
3. **同步CRM数据**: 点击"同步所有字典"按钮
4. **创建本地字典**: 在本地字典管理中创建测试数据
5. **验证表单集成**: 在合作对接表单中查看下拉选项

## 故障排除

### 常见问题

1. **CRM连接失败**
   - 检查网络连接
   - 验证CRM配置信息
   - 查看服务器日志

2. **字典数据不显示**
   - 检查数据库连接
   - 验证字典数据状态
   - 清除浏览器缓存

3. **同步失败**
   - 查看同步错误日志
   - 检查CRM API权限
   - 验证数据格式

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看数据库日志
tail -f logs/database.log
```

## 维护和监控

### 定期维护任务

1. **数据清理**: 定期清理过期的同步失败记录
2. **性能监控**: 监控字典数据加载性能
3. **数据备份**: 定期备份字典数据
4. **同步监控**: 监控CRM数据同步状态

### 性能优化

1. **缓存策略**: 合理设置缓存过期时间
2. **数据分页**: 大量数据使用分页加载
3. **索引优化**: 优化数据库查询索引
4. **批量操作**: 使用批量操作提高效率

## 扩展开发

### 添加新的字典分类

1. 在CRM字段映射中添加新的映射关系
2. 创建对应的本地字典分类
3. 在表单中使用新的字典数据

### 自定义数据合并策略

```javascript
// 自定义合并策略
const customOptions = await dictionaryService.getMergedDictionary('custom_category', {
  crmFirst: false,       // 本地数据优先
  includeLocal: true,
  includeCrm: false      // 不包含CRM数据
})
```

## 版本历史

- **v1.0.0**: 初始版本，支持基础字典管理和CRM同步
- **v1.1.0**: 添加批量操作和性能优化
- **v1.2.0**: 增强错误处理和监控功能

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。
