# 达人视频作品数据表设计文档

## 📋 概述

`author_videos` 表用于存储从巨量星图等平台爬取的达人视频详细信息，支持多平台扩展，为后续支持小红书等其他平台做准备。

## 🗃️ 表结构

### 基本信息字段

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| `id` | INT | 主键ID | 自增 |
| `author_id` | INT | 关联达人ID | 外键，关联 influencers 表 |
| `platform` | ENUM | 平台类型 | xiaohongshu, juxingtu, douyin |
| `platform_user_id` | VARCHAR(100) | 平台用户ID | 星图ID等 |
| `video_id` | VARCHAR(100) | 视频ID | 平台视频唯一标识 |

### 视频内容字段

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| `title` | VARCHAR(500) | 视频标题 | 必填 |
| `video_url` | VARCHAR(1000) | 视频链接 | 完整播放链接 |
| `video_cover` | VARCHAR(1000) | 视频封面 | 封面图片链接 |
| `duration` | INT | 视频时长 | 单位：秒 |
| `publish_time` | DATETIME | 发布时间 | 视频发布时间 |
| `description` | TEXT | 视频描述 | 视频详细描述 |
| `location` | VARCHAR(100) | 拍摄地点 | 地理位置信息 |

### 统计数据字段

| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| `like_count` | INT | 点赞数 | 0 |
| `play_count` | INT | 播放数 | 0 |
| `share_count` | INT | 分享数 | 0 |
| `comment_count` | INT | 评论数 | 0 |
| `collect_count` | INT | 收藏数 | 0 |

### 扩展字段

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| `tags` | JSON | 视频标签 | 标签数组 |
| `music_info` | JSON | 背景音乐信息 | 音乐相关数据 |
| `video_stats` | JSON | 视频统计数据 | 扩展统计信息 |
| `raw_data` | JSON | 原始爬取数据 | 完整原始数据 |

### 管理字段

| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| `status` | ENUM | 视频状态 | active |
| `crawl_task_id` | INT | 爬虫任务ID | 外键，可为空 |
| `last_updated` | DATETIME | 数据更新时间 | 数据最后更新时间 |
| `created_at` | DATETIME | 创建时间 | 自动生成 |
| `updated_at` | DATETIME | 更新时间 | 自动更新 |

## 🔗 关联关系

### 与达人表的关联
```javascript
// 一个达人有多个视频
Influencer.hasMany(AuthorVideo, {
  foreignKey: 'authorId',
  as: 'videos'
});

// 一个视频属于一个达人
AuthorVideo.belongsTo(Influencer, {
  foreignKey: 'authorId',
  as: 'author'
});
```

### 与爬虫任务的关联
```javascript
// 一个爬虫任务可以产生多个视频
CrawlTask.hasMany(AuthorVideo, {
  foreignKey: 'crawlTaskId',
  as: 'videos'
});

// 一个视频可能来自某个爬虫任务
AuthorVideo.belongsTo(CrawlTask, {
  foreignKey: 'crawlTaskId',
  as: 'crawlTask'
});
```

## 📊 索引设计

### 基础索引
- `idx_author_id`: 按达人ID查询
- `idx_platform`: 按平台查询
- `idx_platform_user_id`: 按平台用户ID查询
- `idx_video_id`: 按视频ID查询
- `idx_publish_time`: 按发布时间查询
- `idx_play_count`: 按播放量查询
- `idx_like_count`: 按点赞数查询
- `idx_crawl_task_id`: 按爬虫任务查询

### 复合索引
- `unique_platform_user_video`: 唯一索引，防止重复数据
- `idx_author_publish_time`: 优化按达人查询视频列表

## 🚀 使用示例

### 基本查询
```javascript
const { AuthorVideo, Influencer } = require('../src/models');

// 查询某个达人的所有视频
const videos = await AuthorVideo.findAll({
  where: { authorId: 1 },
  order: [['publishTime', 'DESC']]
});

// 查询热门视频（按播放量排序）
const hotVideos = await AuthorVideo.findAll({
  where: { platform: 'juxingtu' },
  order: [['playCount', 'DESC']],
  limit: 10
});
```

### 关联查询
```javascript
// 查询视频及其达人信息
const videosWithAuthor = await AuthorVideo.findAll({
  include: [{
    model: Influencer,
    as: 'author',
    attributes: ['nickname', 'followersCount']
  }],
  limit: 20
});

// 查询某个达人的视频统计
const authorStats = await AuthorVideo.findAll({
  where: { authorId: 1 },
  attributes: [
    [sequelize.fn('COUNT', sequelize.col('id')), 'videoCount'],
    [sequelize.fn('SUM', sequelize.col('playCount')), 'totalPlays'],
    [sequelize.fn('AVG', sequelize.col('likeCount')), 'avgLikes']
  ]
});
```

### 数据插入
```javascript
// 插入新视频数据
const newVideo = await AuthorVideo.create({
  authorId: 1,
  platform: 'juxingtu',
  platformUserId: 'user123',
  videoId: 'video456',
  title: '精彩视频标题',
  videoUrl: 'https://example.com/video/456',
  duration: 60,
  publishTime: new Date(),
  likeCount: 100,
  playCount: 1000,
  tags: ['标签1', '标签2'],
  status: 'active'
});
```

## 🔧 维护建议

### 数据清理
- 定期清理已删除的视频数据
- 归档历史数据以保持表性能

### 性能优化
- 监控查询性能，必要时添加新索引
- 考虑分表策略（按时间或平台分表）

### 数据一致性
- 确保 `author_id` 对应的达人存在
- 定期验证统计数据的准确性

## 📝 注意事项

1. **唯一性约束**: 通过 `unique_platform_user_video` 索引确保同一平台同一用户的同一视频不会重复存储
2. **外键约束**: `author_id` 删除时级联删除视频，`crawl_task_id` 删除时设为NULL
3. **扩展性**: 支持多平台，可轻松添加新平台类型
4. **JSON字段**: 使用JSON字段存储复杂数据，便于扩展和查询
