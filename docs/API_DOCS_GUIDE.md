# 达人信息管理系统 - API文档使用指南

## 🎯 概述

本系统提供了完整的动态API文档系统，基于OpenAPI 3.0规范，支持在线测试和代码生成。

## 📖 访问地址

- **API文档界面**: http://localhost:3001/api-docs
- **API基本信息**: http://localhost:3001/api
- **OpenAPI规范JSON**: http://localhost:3001/api-docs.json
- **健康检查**: http://localhost:3001/health

## 🚀 快速开始

### 1. 启动服务器
```bash
npm run dev
```

### 2. 访问API文档
在浏览器中打开 http://localhost:3001/api-docs

### 3. 认证设置
1. 调用 `/auth/login` 接口获取JWT token
2. 点击页面右上角的 **Authorize** 按钮
3. 输入 `Bearer <your-token>`
4. 点击 **Authorize** 确认

## 📋 功能特性

### ✨ 完整的API文档
- **自动生成**: 基于代码注释自动生成文档
- **实时更新**: 代码变更时文档自动同步
- **中文界面**: 完全中文化的文档界面
- **详细说明**: 包含参数、响应、错误码等完整信息

### 🧪 在线测试功能
- **Try it out**: 直接在文档页面测试API
- **参数填写**: 可视化参数输入界面
- **实时响应**: 查看完整的请求和响应数据
- **错误调试**: 详细的错误信息和状态码

### 🔐 认证支持
- **JWT认证**: 自动添加认证头
- **Token管理**: 浏览器本地存储token
- **自动刷新**: 认证过期自动提示

### 📊 模块分类

#### 🏠 系统模块
- 健康检查
- API信息查询

#### 🔑 认证模块
- 用户注册
- 用户登录
- 获取当前用户信息
- 修改密码

#### 👥 达人管理模块
- 获取达人列表（支持分页、筛选、排序）
- 获取达人详情
- 创建达人
- 更新达人信息
- 删除达人
- 批量导入导出

#### 🤖 爬虫管理模块
- 创建爬虫任务
- 获取任务列表
- 获取任务详情
- 控制任务（启动、暂停、停止）
- 获取任务结果
- 导入爬虫结果

#### 🍪 Cookie管理模块
- 添加Cookie
- 获取Cookie列表
- 更新Cookie
- 删除Cookie
- 验证Cookie
- Cookie轮换策略

## 🛠️ 使用技巧

### 1. 快速测试流程
```
1. 登录获取token → 2. 设置认证 → 3. 测试其他接口
```

### 2. 参数说明
- **必填参数**: 标有红色星号(*)
- **可选参数**: 有默认值或可为空
- **枚举值**: 下拉选择框
- **JSON对象**: 可展开查看结构

### 3. 响应格式
所有API响应都遵循统一格式：
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "pagination": {} // 分页接口才有
}
```

### 4. 错误处理
- **400**: 参数验证失败
- **401**: 认证失败
- **403**: 权限不足
- **404**: 资源不存在
- **409**: 资源冲突
- **500**: 服务器错误

## 🔧 开发者工具

### 浏览器控制台方法
```javascript
// 设置认证token
setApiToken("your-jwt-token");

// 清除认证token
clearApiToken();
```

### 代码生成
文档支持生成多种语言的客户端代码：
- cURL命令
- JavaScript (fetch/axios)
- Python (requests)
- Java (OkHttp)
- PHP (cURL)

### API规范导出
- JSON格式: http://localhost:3001/api-docs.json
- 可导入到Postman、Insomnia等工具

## 📝 示例用法

### 1. 用户登录
```bash
curl -X POST "http://localhost:3001/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123456"
  }'
```

**默认管理员账户**：
- 用户名：`admin`
- 密码：`admin123456`
- 邮箱：`<EMAIL>`

### 2. 获取达人列表
```bash
curl -X GET "http://localhost:3001/api/influencers?page=1&limit=10&platform=xiaohongshu" \
  -H "Authorization: Bearer your-token"
```

### 3. 创建达人
```bash
curl -X POST "http://localhost:3001/api/influencers" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "nickname": "美妆达人小红",
    "platform": "xiaohongshu",
    "followersCount": 50000,
    "category": "美妆"
  }'
```

## 🎨 界面特色

### 美观的UI设计
- 现代化的界面风格
- 清晰的模块分类
- 直观的操作流程

### 智能的交互体验
- 自动完成参数
- 实时验证输入
- 友好的错误提示

### 丰富的文档内容
- 详细的接口说明
- 完整的示例数据
- 清晰的参数描述

## 🔍 故障排除

### 常见问题

1. **文档页面无法访问**
   - 检查服务器是否启动
   - 确认端口3001未被占用

2. **认证失败**
   - 检查token格式是否正确
   - 确认token未过期

3. **接口测试失败**
   - 检查参数格式
   - 查看错误响应信息

### 调试技巧
- 使用浏览器开发者工具查看网络请求
- 检查控制台错误信息
- 对比文档示例和实际请求

## 📞 技术支持

如有问题，请：
1. 查看API文档的错误说明
2. 检查服务器日志
3. 联系开发团队

---

**🎉 享受使用达人信息管理系统API文档！**
