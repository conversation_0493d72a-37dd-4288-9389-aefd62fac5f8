# 数据库设计文档

## 数据库概述

- **数据库名称**: daren_db
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB

## 表结构设计

### 1. 用户表 (users)

用于存储系统用户信息，包括管理员和普通用户。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | NO | AUTO_INCREMENT | 主键，用户ID |
| username | VARCHAR | 50 | NO | - | 用户名，唯一 |
| email | VARCHAR | 100 | NO | - | 邮箱地址，唯一 |
| password | VARCHAR | 255 | NO | - | 密码（加密存储） |
| role | ENUM | - | NO | 'user' | 用户角色：admin/user |
| status | ENUM | - | NO | 'active' | 用户状态：active/inactive |
| last_login_at | DATETIME | - | YES | NULL | 最后登录时间 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 更新时间 |

**索引:**
- PRIMARY KEY (id)
- UNIQUE KEY (username)
- UNIQUE KEY (email)

### 2. 达人信息表 (influencers)

用于存储达人的详细信息，包括基本信息、联系方式、报价等。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | NO | AUTO_INCREMENT | 主键，达人ID |
| nickname | VARCHAR | 100 | NO | - | 达人昵称 |
| platform | ENUM | - | NO | - | 平台类型：xiaohongshu/juxingtu |
| platform_id | VARCHAR | 100 | YES | NULL | 平台ID |
| avatar_url | VARCHAR | 500 | YES | NULL | 头像链接 |
| followers_count | INT | - | NO | 0 | 粉丝数量 |
| category | VARCHAR | 50 | YES | NULL | 分类 |
| tags | JSON | - | YES | NULL | 标签 |
| contact_info | JSON | - | YES | NULL | 联系方式 |
| price_info | JSON | - | YES | NULL | 报价信息 |
| cooperation_history | JSON | - | YES | NULL | 合作历史 |
| notes | TEXT | - | YES | NULL | 备注 |
| status | ENUM | - | NO | 'active' | 状态：active/inactive |
| created_by | INT | - | YES | NULL | 创建者ID |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 更新时间 |

**索引:**
- PRIMARY KEY (id)
- INDEX idx_platform (platform)
- INDEX idx_nickname (nickname)
- INDEX idx_followers (followers_count)
- INDEX idx_category (category)
- FOREIGN KEY (created_by) REFERENCES users(id)

### 3. 爬虫任务表 (crawl_tasks)

用于存储爬虫任务的信息和执行状态。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | NO | AUTO_INCREMENT | 主键，任务ID |
| task_name | VARCHAR | 100 | NO | - | 任务名称 |
| platform | ENUM | - | NO | - | 目标平台：xiaohongshu/juxingtu |
| keywords | VARCHAR | 500 | NO | - | 搜索关键词 |
| status | ENUM | - | NO | 'pending' | 任务状态：pending/running/completed/failed |
| progress | INT | - | NO | 0 | 进度百分比 |
| total_count | INT | - | NO | 0 | 总数量 |
| success_count | INT | - | NO | 0 | 成功数量 |
| error_message | TEXT | - | YES | NULL | 错误信息 |
| started_at | DATETIME | - | YES | NULL | 开始时间 |
| completed_at | DATETIME | - | YES | NULL | 完成时间 |
| created_by | INT | - | YES | NULL | 创建者ID |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP | 更新时间 |

**索引:**
- PRIMARY KEY (id)
- INDEX idx_status (status)
- INDEX idx_platform (platform)
- FOREIGN KEY (created_by) REFERENCES users(id)

## JSON字段结构说明

### contact_info 联系方式结构
```json
{
  "phone": "13800138000",
  "email": "<EMAIL>",
  "wechat": "wechat_id",
  "qq": "123456789",
  "other": "其他联系方式"
}
```

### price_info 报价信息结构
```json
{
  "post": 5000,
  "video": 8000,
  "live": 12000,
  "story": 2000,
  "other": {
    "description": "其他服务",
    "price": 3000
  }
}
```

### tags 标签结构
```json
["美妆", "护肤", "种草", "测评"]
```

### cooperation_history 合作历史结构
```json
[
  {
    "brand": "品牌名称",
    "campaign": "活动名称",
    "date": "2024-01-01",
    "type": "post",
    "result": "效果描述"
  }
]
```

## 表关系说明

### 外键关系
1. `influencers.created_by` → `users.id`
2. `crawl_tasks.created_by` → `users.id`

### 关系类型
- 用户与达人：一对多关系（一个用户可以创建多个达人记录）
- 用户与爬虫任务：一对多关系（一个用户可以创建多个爬虫任务）

## 数据初始化

### 默认用户
系统初始化时会创建一个默认管理员用户：
- 用户名：admin
- 邮箱：<EMAIL>
- 密码：admin123456（BCrypt加密）
- 角色：admin

### 初始数据
系统启动时会自动创建必要的数据库表结构，无需手动初始化。

## 性能优化建议

1. **索引优化**
   - 在经常查询的字段上建立索引
   - 复合索引用于多字段查询

2. **分页查询**
   - 使用LIMIT和OFFSET进行分页
   - 避免深度分页，考虑使用游标分页

3. **JSON字段查询**
   - 使用MySQL 5.7+的JSON函数进行查询
   - 考虑为常用JSON字段建立虚拟列索引

4. **数据归档**
   - 定期归档历史数据
   - 保持主表数据量在合理范围内

## 备份策略

1. **全量备份**：每日凌晨进行全量备份
2. **增量备份**：每小时进行增量备份
3. **备份保留**：保留30天的备份数据
4. **恢复测试**：定期进行备份恢复测试
