# 达人提报重复控制机制

## 功能概述

达人提报系统新增了重复提报控制机制，确保同一个达人只能由同一个运营负责人进行提报，防止不同运营负责人之间的冲突。

## 功能特性

### 1. 新建提报功能
- ✅ 支持创建新的达人提报记录
- ✅ 采用简化的处理流程，减少复杂的验证步骤
- ✅ 确保基本的必填字段验证（达人信息、运营负责人等）
- ✅ 保持现有功能的向后兼容性

### 2. 重复提报控制机制
- ✅ 当同一个达人被再次提报时，进行运营负责人验证
- ✅ 检查该达人之前的提报记录中的`operationManager`字段
- ✅ 如果当前提报的运营负责人与历史记录中的运营负责人相同，则允许提报
- ✅ **增强的时间和状态综合判断**：
  - **时间条件**：最新提报记录的创建时间（createdAt）距离当前时间超过30天
  - **状态条件**：最新提报记录的当前状态不是"审核通过"（status !== 'approved'）
  - **判断逻辑**：只有当同时满足以上两个条件时，才允许不同运营负责人进行重复提报
- ✅ **智能错误提示**：根据具体不满足的条件显示明确的错误原因
- ✅ 在前端表单提交时进行验证，并在后端API中实现相应的业务逻辑

## 技术实现

### 后端实现

#### 1. 新增控制器方法

**文件**: `src/controllers/influencerReportController.js`

```javascript
/**
 * 内部方法：检查重复提报控制机制（增强版）
 * @param {string} influencerName 达人名称
 * @param {string} platform 平台
 * @param {string} operationManager 当前运营负责人
 * @returns {Promise<Object>} 检查结果
 */
static async checkDuplicateReport(influencerName, platform, operationManager) {
  // 查询该达人在该平台的所有历史提报记录
  const existingReports = await InfluencerReport.findAll({
    where: { influencerName, platform },
    order: [['createdAt', 'DESC']], // 按创建时间排序
    limit: 10
  });

  if (existingReports.length === 0) {
    return {
      canSubmit: true,
      message: '首次提报，无重复提报限制'
    };
  }

  // 检查是否有不同运营负责人的提报记录
  const differentManagerReports = existingReports.filter(
    report => report.operationManager !== operationManager
  );

  if (differentManagerReports.length > 0) {
    const latestDifferentReport = differentManagerReports[0];

    // 计算时间差（天数）
    const now = new Date();
    const reportCreatedAt = new Date(latestDifferentReport.createdAt);
    const daysDiff = Math.floor((now - reportCreatedAt) / (1000 * 60 * 60 * 24));

    // 综合判断：时间条件 AND 状态条件
    const timeConditionMet = daysDiff > 30; // 超过30天
    const statusConditionMet = latestDifferentReport.status !== 'approved'; // 状态不是审核通过

    if (timeConditionMet && statusConditionMet) {
      // 两个条件都满足，允许重复提报
      return {
        canSubmit: true,
        message: `允许重复提报：该达人的最新提报记录已超过30天且状态非审核通过`,
        allowReason: 'time_and_status_conditions_met',
        daysSinceLastReport: daysDiff,
        lastReportStatus: latestDifferentReport.status,
        lastReportManager: latestDifferentReport.operationManager
      };
    } else {
      // 条件不满足，阻止重复提报
      let reason = '';
      let reasonCode = '';

      if (!timeConditionMet && !statusConditionMet) {
        reason = `该达人已由其他运营负责人（${latestDifferentReport.operationManager}）提报，且提报时间不足30天（${daysDiff}天）并且状态为审核通过，您无权重复提报`;
        reasonCode = 'time_and_status_not_met';
      } else if (!timeConditionMet) {
        reason = `该达人已由其他运营负责人（${latestDifferentReport.operationManager}）提报，提报时间不足30天（还需等待${30 - daysDiff}天），您无权重复提报`;
        reasonCode = 'time_condition_not_met';
      } else if (!statusConditionMet) {
        reason = `该达人已由其他运营负责人（${latestDifferentReport.operationManager}）提报且状态为审核通过，您无权重复提报`;
        reasonCode = 'status_condition_not_met';
      }

      return {
        canSubmit: false,
        message: reason,
        reasonCode: reasonCode,
        conflictManager: latestDifferentReport.operationManager,
        conflictReportId: latestDifferentReport.id,
        daysSinceLastReport: daysDiff,
        lastReportStatus: latestDifferentReport.status,
        timeConditionMet: timeConditionMet,
        statusConditionMet: statusConditionMet
      };
    }
  }

  return {
    canSubmit: true,
    message: '运营负责人验证通过，允许提报',
    existingReportsCount: existingReports.length
  };
}
```

#### 2. 新增API接口

**文件**: `src/routes/influencerReports.js`

```javascript
/**
 * @swagger
 * /api/influencer-reports/check-duplicate:
 *   post:
 *     summary: 检查重复提报控制
 *     tags: [达人提报]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - influencerName
 *               - platform
 *               - operationManager
 *             properties:
 *               influencerName:
 *                 type: string
 *               platform:
 *                 type: string
 *                 enum: [xiaohongshu, juxingtu]
 *               operationManager:
 *                 type: string
 */
router.post('/check-duplicate', InfluencerReportController.checkDuplicateReportAPI);
```

#### 3. 集成到创建流程

在`createReport`方法中添加重复提报检查：

```javascript
// 检查重复提报控制机制
const duplicateCheck = await InfluencerReportController.checkDuplicateReport(
  influencerName, platform, operationManager
);
if (!duplicateCheck.canSubmit) {
  return ResponseUtil.error(ctx, duplicateCheck.message, 409);
}
```

### 前端实现

#### 1. API服务扩展

**文件**: `frontend/src/services/api.js`

```javascript
// 达人提报 API
export const influencerReportAPI = {
  // ... 其他方法
  
  // 检查重复提报控制
  checkDuplicateReport: data => api.post('/influencer-reports/check-duplicate', data),
};
```

#### 2. 表单组件增强

**文件**: `frontend/src/components/InfluencerReportForm.vue`

```vue
<template>
  <!-- 重复提报警告 -->
  <a-alert
    v-if="duplicateWarning"
    type="error"
    :title="duplicateWarning"
    style="margin-bottom: 16px"
  />
</template>

<script setup>
// 检查重复提报控制（增强版）
const checkDuplicateReport = async () => {
  if (!form.value.influencerName || !form.value.platform || !form.value.operationManager) return;

  try {
    const response = await influencerReportAPI.checkDuplicateReport({
      influencerName: form.value.influencerName,
      platform: form.value.platform,
      operationManager: form.value.operationManager
    });

    if (response.success) {
      const {
        canSubmit,
        message,
        reasonCode,
        daysSinceLastReport,
        lastReportStatus,
        timeConditionMet,
        statusConditionMet
      } = response.data;

      if (!canSubmit) {
        // 根据具体原因显示详细的错误信息
        let detailedMessage = message;

        if (reasonCode === 'time_and_status_not_met') {
          detailedMessage += `（时间：${daysSinceLastReport}天 < 30天，状态：${lastReportStatus}）`;
        } else if (reasonCode === 'time_condition_not_met') {
          detailedMessage += `（距离上次提报仅${daysSinceLastReport}天）`;
        } else if (reasonCode === 'status_condition_not_met') {
          detailedMessage += `（当前状态：${lastReportStatus}）`;
        }

        duplicateWarning.value = detailedMessage;
      } else {
        duplicateWarning.value = '';

        // 如果是因为满足时间和状态条件而允许的重复提报，显示提示信息
        if (response.data.allowReason === 'time_and_status_conditions_met') {
          console.log(`✅ 允许重复提报: ${message} (距离上次提报${daysSinceLastReport}天，上次状态：${lastReportStatus})`);
        }
      }
    }
  } catch (error) {
    console.error('检查重复提报失败:', error);
  }
};

// 监听运营负责人字段变化
watch(() => form.value.operationManager, async (newVal, oldVal) => {
  if (newVal && newVal !== oldVal && !props.isEditMode) {
    await checkDuplicateReport();
  }
});
</script>
```

## 使用流程

### 1. 首次提报
1. 用户填写达人信息和运营负责人
2. 系统检查：该达人无历史提报记录
3. 结果：允许提报 ✅

### 2. 同一运营负责人重复提报
1. 用户填写达人信息和运营负责人
2. 系统检查：该达人有历史提报记录，且运营负责人相同
3. 结果：允许提报 ✅

### 3. 不同运营负责人重复提报（增强逻辑）
1. 用户填写达人信息和运营负责人
2. 系统检查：该达人有历史提报记录，但运营负责人不同
3. **综合判断**：
   - **时间条件**：最新提报记录创建时间 > 30天
   - **状态条件**：最新提报记录状态 ≠ 'approved'
4. **判断结果**：
   - ✅ **允许提报**：时间 > 30天 AND 状态 ≠ 'approved'
   - ❌ **阻止提报**：时间 ≤ 30天 OR 状态 = 'approved'

### 4. 详细错误提示
- **时间不足30天**：`该达人已由其他运营负责人（XXX）提报，提报时间不足30天（还需等待X天），您无权重复提报`
- **状态为审核通过**：`该达人已由其他运营负责人（XXX）提报且状态为审核通过，您无权重复提报`
- **时间和状态都不满足**：`该达人已由其他运营负责人（XXX）提报，且提报时间不足30天（X天）并且状态为审核通过，您无权重复提报`

## 错误处理

### 前端错误提示
- 保护期警告：黄色警告框
- 重复提报警告：红色错误框
- 表单验证：阻止提交并显示错误消息

### 后端错误响应
- HTTP 409：业务逻辑冲突（保护期内或重复提报）
- HTTP 400：参数验证失败
- HTTP 500：服务器内部错误

## 测试验证

系统包含完整的测试用例：

```bash
# 运行重复提报控制机制测试
node test_influencer_report_duplicate_check.js

# 测试API接口
node test_duplicate_api.js
```

测试覆盖场景：
- ✅ 首次提报检查
- ✅ 同一运营负责人重复提报
- ✅ **增强的时间和状态条件测试**：
  - 时间 > 30天 且 状态 ≠ 'approved'：允许重复提报
  - 时间 > 30天 但 状态 = 'approved'：阻止重复提报
  - 时间 ≤ 30天 且 状态 ≠ 'approved'：阻止重复提报
  - 时间 ≤ 30天 且 状态 = 'approved'：阻止重复提报
- ✅ 错误原因码验证（time_condition_not_met, status_condition_not_met, time_and_status_not_met）
- ✅ 详细错误信息显示
- ✅ API接口参数验证

## 性能优化

1. **查询优化**：限制查询最近10条记录，提高性能
2. **索引支持**：利用现有的复合索引 `idx_protection_check`
3. **缓存策略**：前端实时验证，减少重复请求
4. **错误处理**：完善的异常捕获和用户友好的错误提示

## 兼容性说明

- ✅ 保持现有API接口不变
- ✅ 新增功能不影响现有提报流程
- ✅ 支持编辑模式下跳过重复检查
- ✅ 向后兼容所有现有功能
