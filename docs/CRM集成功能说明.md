# CRM集成功能说明

## 项目概述

本项目成功实现了钉钉CRM系统与达人管理系统的数据同步功能，采用Koa2 + MySQL + MVC架构，提供完整的客户和协议数据集成解决方案。

## 技术架构

### 核心组件
- **CrmIntegrationService**: 核心集成服务，负责API调用和数据处理
- **CrmDataMappingService**: 数据映射服务，处理字段转换和验证
- **CrmIntegrationController**: 控制器，提供HTTP API接口
- **路由配置**: 完整的RESTful API路由和Swagger文档

### 技术特性
- ✅ **Token缓存机制**: 自动管理corpAccessToken，避免频繁请求
- ✅ **频率限制处理**: 实现请求队列，遵循API调用限制（60次/20秒）
- ✅ **数据映射转换**: 完整的字段映射和数据类型转换
- ✅ **错误处理机制**: 完善的错误捕获、重试和日志记录
- ✅ **分页同步支持**: 支持大量数据的分页处理
- ✅ **预览模式**: 支持数据预览，避免误操作

## API接口列表

### 基础功能
- `GET /api/crm-integration/test-connection` - 测试CRM连接
- `GET /api/crm-integration/status` - 获取系统状态
- `GET /api/crm-integration/config` - 获取配置信息
- `POST /api/crm-integration/refresh-token` - 手动刷新Token

### 数据查询
- `GET /api/crm-integration/customers` - 获取客户列表
- `GET /api/crm-integration/customers/:customerName/agreements` - 获取客户协议

### 数据同步
- `POST /api/crm-integration/sync/customers` - 同步客户数据
- `POST /api/crm-integration/sync/complete` - 完整数据同步（客户+协议）
- `POST /api/crm-integration/sync/all` - 全量数据同步

## 配置说明

### 环境变量配置
```javascript
// CRM集成配置
crm: {
  // 代理服务器配置
  proxyUrl: 'http://*************:8788',
  
  // 钉钉CRM认证配置
  corpId: 'ding96479bbd378e45bf35c2f4657eb6378f',
  appId: '3a3acf7ff6c9486d8d39342f24b0b887',
  appSecret: '13DBA19386A53548B7573B754E639958',
  
  // API配置
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 2000,
  
  // 频率限制配置
  rateLimit: {
    maxRequests: 60,     // 最大60次/20秒
    windowMs: 20000,     // 20秒窗口
    requestDelay: 500    // 请求间隔500ms
  },
  
  // Token缓存配置
  tokenCache: {
    ttl: 7200,           // token有效期7200秒
    refreshBuffer: 300   // 提前5分钟刷新
  }
}
```

## 数据映射说明

### 客户数据字段映射
| CRM字段 | 本地字段 | 说明 |
|---------|----------|------|
| id | externalCustomerId | 客户ID（主键） |
| custom_name | customerName | 客户名称 |
| customer_textarea_11 | customerHomepage | 星图链接 |
| customer_check_box_2 | seedingPlatform | 种草平台 |
| customer_select_28 | bloggerFansCount | 博主粉丝量级 |
| customer_textarea_13 | influencerPlatformId | 平台ID |
| custom_remark | bloggerWechatAndNotes | 备注信息 |

### 协议数据字段映射
| CRM字段 | 本地字段 | 说明 |
|---------|----------|------|
| id | externalAgreementId | 协议ID（主键） |
| contract_title | title | 协议标题 |
| custom_name | customerName | 关联客户名称 |
| contract_amount | cooperationAmount | 合作金额 |
| contract_date_4 | scheduledPublishTime | 约定发布时间 |
| contract_url_0 | publishUrl | 发布链接 |

### 特殊处理逻辑
- **多平台处理**: 自动将平台ID数组转换为逗号分隔的字符串
- **金额解析**: 自动去除货币符号和格式化字符
- **日期转换**: 支持多种日期格式的自动转换
- **星图ID提取**: 从链接中自动提取星图ID

## 使用示例

### 1. 测试连接
```bash
curl -X GET "http://localhost:3001/api/crm-integration/test-connection" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 同步客户数据
```bash
curl -X POST "http://localhost:3001/api/crm-integration/sync/customers" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"page": 1, "limit": 20}'
```

### 3. 全量数据同步（预览模式）
```bash
curl -X POST "http://localhost:3001/api/crm-integration/sync/all" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "maxPages": 10,
    "pageSize": 20,
    "dryRun": true
  }'
```

### 4. 全量数据同步（实际同步）
```bash
curl -X POST "http://localhost:3001/api/crm-integration/sync/all" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "maxPages": 50,
    "pageSize": 20,
    "dryRun": false
  }'
```

## 数据同步流程

### 完整同步流程
1. **获取Token**: 自动获取并缓存corpAccessToken
2. **分页拉取**: 分页获取客户数据列表
3. **数据映射**: 将CRM字段映射到本地数据库字段
4. **数据验证**: 验证必填字段和数据格式
5. **存储处理**: 创建新记录或更新现有记录
6. **协议同步**: 为每个客户查询并同步协议数据
7. **状态更新**: 更新CRM关联状态

### 频率限制处理
- 请求队列机制，确保不超过API限制
- 自动延迟处理，避免频率限制错误
- 页面间延迟，保证稳定性

### 错误处理
- 完整的错误捕获和日志记录
- 详细的错误统计和报告
- 失败记录的详细信息记录

## 监控和维护

### 系统状态监控
- Token有效性检查
- 请求队列状态
- 同步统计信息
- 错误日志记录

### 性能优化
- Token缓存减少API调用
- 批量处理提高效率
- 分页处理避免超时
- 频率控制保证稳定性

## 注意事项

1. **API限制**: 严格遵循钉钉CRM的API调用限制
2. **数据一致性**: 确保CRM和本地数据的一致性
3. **错误处理**: 及时处理同步错误和异常情况
4. **性能监控**: 定期检查同步性能和数据质量
5. **安全性**: 保护API密钥和敏感数据

## 项目成果

✅ **第一阶段完成**: 基础服务和接口调用功能
✅ **第二阶段完成**: 数据映射和存储逻辑
✅ **第三阶段完成**: 完整同步流程测试验证
✅ **第四阶段完成**: 全量数据同步功能

### 测试验证结果
- 成功连接钉钉CRM系统
- 正确获取客户和协议数据
- 完整的字段映射和数据转换
- 稳定的分页和全量同步
- 完善的错误处理和监控

项目已完全满足需求，可以投入生产使用。
