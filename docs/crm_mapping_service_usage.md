# CRM数据映射服务使用指南 - 简化版

## 概述

CrmDataMappingService已经简化为专注于基本的字段名转换功能，移除了复杂的特殊字段处理、默认值添加和数据解析逻辑。

## 核心功能

### 1. 双向字段映射

#### 从外部CRM同步数据时
```javascript
const mappingService = new CrmDataMappingService();

// 客户数据映射
const crmCustomerData = {
  id: 'crm_123',
  custom_name: '张三',
  seas_id: 'sea_456',
  customer_textarea_11: 'https://example.com'
};

const localCustomerData = mappingService.mapFromCrm(crmCustomerData, 'customer');
// 结果：
// {
//   externalCustomerId: 'crm_123',
//   customerName: '张三',
//   customerPublicSea: 'sea_456',
//   customerHomepage: 'https://example.com'
// }

// 协议数据映射
const crmAgreementData = {
  id: 'agreement_789',
  contract_title: '合作协议',
  contract_amount: '5000',
  contract_url_0: 'https://publish.link'
};

const localAgreementData = mappingService.mapFromCrm(crmAgreementData, 'agreement');
// 结果：
// {
//   externalAgreementId: 'agreement_789',
//   title: '合作协议',
//   cooperationAmount: '5000',
//   publishLink: 'https://publish.link'
// }
```

#### 更新到外部CRM时
```javascript
// 客户数据转换
const localCustomerData = {
  externalCustomerId: 'crm_123',
  customerName: '张三',
  customerPublicSea: 'sea_456',
  customerHomepage: 'https://example.com'
};

const crmCustomerData = mappingService.mapToCrm(localCustomerData, 'customer');
// 结果：
// {
//   id: 'crm_123',
//   custom_name: '张三',
//   seas_id: 'sea_456',
//   customer_textarea_11: 'https://example.com'
// }

// 协议数据转换
const localAgreementData = {
  externalAgreementId: 'agreement_789',
  title: '合作协议',
  cooperationAmount: '5000',
  publishLink: 'https://publish.link'
};

const crmAgreementData = mappingService.mapToCrm(localAgreementData, 'agreement');
// 结果：
// {
//   id: 'agreement_789',
//   contract_title: '合作协议',
//   contract_amount: '5000',
//   contract_url_0: 'https://publish.link'
// }
```

### 2. 向后兼容方法

为了保持与现有代码的兼容性，保留了原有的方法名：

```javascript
// 原有方法仍然可用
const customerData = mappingService.mapCustomerData(crmCustomerData);
const agreementData = mappingService.mapAgreementData(crmAgreementData, customerName);
const crmCustomer = mappingService.mapCooperationToCustomer(cooperationData);
const crmAgreement = mappingService.mapCooperationToAgreement(cooperationData);
```

### 3. 数据验证

```javascript
const validationResult = mappingService.validateMappedData(mappedData, 'customer');
// 结果：
// {
//   isValid: true/false,
//   errors: ['错误信息'],
//   warnings: ['警告信息']
// }
```

### 4. 关键字段提取

```javascript
const keyFields = mappingService.extractKeyFields(data, 'customer');
// 结果：
// {
//   customerName: '张三',
//   externalCustomerId: 'crm_123',
//   seedingPlatform: '小红书'
// }
```

## 字段映射配置

### 客户数据字段映射
```javascript
customerFieldMapping = {
  // CRM字段 -> 本地字段
  id: 'externalCustomerId',
  custom_name: 'customerName',
  seas_id: 'customerPublicSea',
  customer_textarea_11: 'customerHomepage',
  customer_check_box_2: 'seedingPlatform',
  customer_select_28: 'bloggerFansCount',
  customer_textarea_13: 'influencerPlatformId',
  custom_remark: 'bloggerWechatAndNotes',
  created: 'createdAt',
  modified: 'updatedAt'
}
```

### 协议数据字段映射
```javascript
agreementFieldMapping = {
  // CRM字段 -> 本地字段
  id: 'externalAgreementId',
  contract_title: 'title',
  custom_name: 'customerName',
  contract_select_0: 'cooperationForm',
  contract_select_26: 'publishPlatform',
  contract_select_25: 'cooperationBrand',
  contract_textarea_0: 'cooperationProduct',
  contract_textarea_1: 'cooperationNotes',
  contract_date_4: 'scheduledPublishTime',
  contract_amount: 'cooperationAmount',
  contract_percent_2: 'influencerCommissionRate',
  contract_input_13: 'payeeName',
  contract_input_14: 'bankAccount',
  contract_textarea_2: 'bankName',
  contract_select_5: 'rebateStatus',
  contract_url_0: 'publishLink',
  contract_end_date: 'actualPublishTime',
  contract_date_5: 'dataRegistrationTime',
  contract_integer_1: 'viewCount',
  contract_integer_2: 'likeCount',
  contract_integer_3: 'collectCount',
  contract_integer_4: 'commentCount',
  contract_select_6: 'brandTopicStatus',
  contract_amount_8: 'storeSales',
  contract_select_2: 'selfEvaluation'
}
```

## 简化的优势

1. **代码简洁**：移除了复杂的特殊字段处理逻辑
2. **易于维护**：只专注于字段名转换，逻辑清晰
3. **性能提升**：减少了不必要的数据处理步骤
4. **向后兼容**：保持了原有API接口不变
5. **易于扩展**：新增字段映射只需修改配置对象

## 注意事项

1. **数据类型处理**：简化版不再进行复杂的数据类型转换，保持原始数据类型
2. **默认值**：不再自动添加默认值，需要在业务层处理
3. **特殊解析**：不再进行金额、百分比、日期等特殊格式解析
4. **验证逻辑**：只进行基本的必填字段验证

如需要特殊的数据处理逻辑，建议在业务层单独实现，保持映射服务的简洁性。
