# 批量更新达人平台信息脚本使用指南

## 概述

本脚本用于批量检测和更新合作对接记录中的达人平台信息，主要功能包括：

1. **数据检测**：扫描合作对接记录，筛选包含达人主页链接的记录
2. **链接解析**：解析达人主页链接，提取平台类型和平台用户ID
3. **结果输出**：生成详细的对照文件和可执行的批量更新脚本
4. **接口测试**：测试CRM客户信息更新接口
5. **安全执行**：提供干运行模式和用户确认机制

## 支持的平台

### 小红书（xiaohongshu）
- 蒲公英平台链接：`https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/xxx`
- 合作者链接：`https://pgy.xiaohongshu.com/solar/cooperator/blogger/xxx`
- 用户主页链接：`https://www.xiaohongshu.com/user/profile/xxx`
- 直接用户ID：20-30位字母数字混合

### 巨量星图（juxingtu）
- 创作者主页链接：`https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/xxx`
- 简化链接：`https://www.xingtu.cn/ad/creator/author-homepage/xxx`
- 直接用户ID：8-20位纯数字

## 使用方法

### 基本语法
```bash
node scripts/batch-update-influencer-platform-info.js [选项]
```

### 命令行选项

| 选项 | 说明 | 默认值 |
|------|------|--------|
| `--dry-run` | 仅检测和生成报告，不实际更新 | false |
| `--batch-size N` | 批处理大小 | 50 |
| `--test-only` | 仅进行接口测试 | false |
| `--output-dir DIR` | 输出目录 | ./batch-output |
| `--help` | 显示帮助信息 | - |

### 使用示例

#### 1. 干运行模式（推荐首次使用）
```bash
node scripts/batch-update-influencer-platform-info.js --dry-run
```
- 仅检测和分析数据，不进行任何修改
- 生成详细的对照文件
- 适合了解数据现状和验证解析逻辑

#### 2. 接口测试模式
```bash
node scripts/batch-update-influencer-platform-info.js --test-only --batch-size 10
```
- 解析链接并测试CRM更新接口
- 小批量处理，验证接口可用性
- 不实际更新数据

#### 3. 正式批量处理
```bash
node scripts/batch-update-influencer-platform-info.js --batch-size 20
```
- 执行完整的批量处理流程
- 生成可执行的更新脚本
- 需要用户确认才能继续

#### 4. 自定义输出目录
```bash
node scripts/batch-update-influencer-platform-info.js --dry-run --output-dir ./custom-output
```

## 输出文件

### 1. 对照文件（comparison）
**文件名格式**：`influencer-platform-comparison-YYYY-MM-DD_HH-mm-ss.txt`

**内容包含**：
- 合作记录ID
- 客户名称
- 达人主页链接
- 解析状态（成功/失败）
- 平台类型（xiaohongshu/juxingtu）
- 平台用户ID
- CRM客户ID
- 原平台信息
- 原博主名称
- 解析错误信息

**示例内容**：
```
合作记录ID	客户名称	达人主页链接	解析状态	平台类型	平台用户ID	CRM客户ID	原平台	原博主名	解析错误
1001	测试客户A	https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/abc123	成功	xiaohongshu	abc123	12345	xiaohongshu	测试博主A	
1002	测试客户B	https://www.xingtu.cn/ad/creator/author-homepage/987654321	成功	juxingtu	987654321	12346	juxingtu	测试博主B	
```

### 2. 批量更新脚本（仅正式模式）
**文件名格式**：`batch-update-script-YYYY-MM-DD_HH-mm-ss.js`

**功能**：
- 包含所有可更新记录的完整信息
- 自动调用CRM更新接口
- 包含错误处理和进度显示
- 可独立执行

## 执行流程

### 1. 数据扫描阶段
- 查询包含达人主页链接的合作记录
- 显示找到的记录总数
- 按ID升序排列确保处理顺序

### 2. 数据处理阶段
- 分批处理记录（默认50条/批）
- 解析每条记录的达人主页链接
- 提取平台类型和用户ID
- 记录处理结果和错误信息

### 3. 接口测试阶段（可选）
- 测试CRM客户更新接口
- 验证平台映射配置
- 记录接口响应状态

### 4. 文件生成阶段
- 生成详细的对照文件
- 生成可执行的更新脚本（正式模式）
- 输出执行报告和统计信息

## 安全机制

### 1. 用户确认
- 正式执行前需要用户确认
- 显示执行模式和影响范围
- 支持取消操作

### 2. 分批处理
- 避免一次性处理过多数据
- 批次间自动延迟
- 减少系统负载

### 3. 错误处理
- 详细记录每个错误
- 不因单个错误中断整体流程
- 提供完整的错误报告

### 4. 干运行模式
- 完全不修改数据
- 验证解析逻辑
- 预估处理结果

## 平台映射配置

脚本使用以下平台映射关系：

```javascript
const platformMapping = {
  'xiaohongshu': '1ebf642a94dc44f28e5aa7eb6959ed21',  // 小红书
  'juxingtu': 'c02d8d5e77684038811e57595513c154'     // 巨量星图
};
```

## 注意事项

### 1. 执行前准备
- 确保数据库连接正常
- 验证CRM集成服务可用
- 备份重要数据（推荐）

### 2. 执行建议
- 首次使用建议先执行干运行模式
- 小批量测试接口可用性
- 在业务低峰期执行正式处理

### 3. 错误处理
- 仔细查看对照文件中的解析错误
- 手动验证无法解析的链接
- 必要时调整解析规则

### 4. 结果验证
- 检查生成的更新脚本内容
- 验证平台映射是否正确
- 确认CRM客户ID的有效性

## 故障排除

### 常见问题

#### 1. 无法找到记录
**现象**：显示"没有找到需要处理的记录"
**解决**：检查数据库中是否存在包含`influencerHomepage`字段的记录

#### 2. 解析失败率高
**现象**：大量记录解析状态为"失败"
**解决**：检查链接格式是否符合支持的模式，必要时扩展解析规则

#### 3. CRM接口测试失败
**现象**：CRM测试状态为"失败"或"异常"
**解决**：检查CRM服务配置和网络连接

#### 4. 权限错误
**现象**：无法创建输出目录或文件
**解决**：检查脚本执行用户的文件系统权限

### 日志分析
脚本提供详细的控制台输出，包括：
- 处理进度和状态
- 错误详情和位置
- 执行统计和报告

## 扩展和定制

### 1. 添加新平台支持
在`parseInfluencerInput`方法中添加新的解析规则：

```javascript
// 新平台解析规则
const newPlatformPatterns = [
  /https?:\/\/new-platform\.com\/user\/([a-zA-Z0-9]+)/
];

for (const pattern of newPlatformPatterns) {
  const match = trimmedInput.match(pattern);
  if (match) {
    return {
      platform: 'newplatform',
      platformUserId: match[1]
    };
  }
}
```

### 2. 自定义输出格式
修改`generateComparisonFile`方法中的内容格式。

### 3. 调整批处理逻辑
修改`processRecord`方法中的处理逻辑。

## 相关文件

- `scripts/batch-update-influencer-platform-info.js` - 主脚本文件
- `src/models/CooperationManagement.js` - 合作记录模型
- `src/services/CrmIntegrationService.js` - CRM集成服务
- `frontend/src/utils/platformUtils.js` - 平台工具函数（参考）
