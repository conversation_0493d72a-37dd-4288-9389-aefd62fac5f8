# 达人管理系统 - 开发指南

## 📋 概述

本文档为达人管理系统的开发指南，包含开发环境搭建、代码结构说明、开发流程和最佳实践。

## 🛠️ 开发环境搭建

### 必需软件
- **Node.js** >= 16.0.0
- **MySQL** >= 5.7 或 8.0
- **Git** >= 2.20.0
- **VSCode** (推荐) 或其他代码编辑器

### 推荐的VSCode插件
```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### 开发环境配置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd daren-server
```

#### 2. 安装依赖
```bash
npm install
```

#### 3. 配置环境变量
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
NODE_ENV=development
PORT=3001
DB_HOST=localhost
DB_PORT=3306
DB_NAME=daren_db_dev
DB_USER=root
DB_PASSWORD=your_password
JWT_SECRET=dev-jwt-secret-key
COOKIE_TEST_MODE=true
DB_LOGGING=true
LOG_LEVEL=debug
```

#### 4. 数据库设置
```bash
# 创建开发数据库
mysql -u root -p -e "CREATE DATABASE daren_db_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 创建测试数据库
mysql -u root -p -e "CREATE DATABASE daren_db_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

#### 5. 启动开发服务器
```bash
npm run dev
```

## 📁 项目架构

### 分层架构
```
┌─────────────────┐
│   Routes Layer  │  路由层 - 处理HTTP请求路由
├─────────────────┤
│ Controller Layer│  控制器层 - 处理业务逻辑调用
├─────────────────┤
│  Service Layer  │  服务层 - 核心业务逻辑
├─────────────────┤
│   Model Layer   │  模型层 - 数据访问和ORM
├─────────────────┤
│ Database Layer  │  数据库层 - MySQL数据存储
└─────────────────┘
```

### 模块化设计
- **auth**: 用户认证和授权
- **crawler**: 爬虫任务管理
- **cookie**: Cookie管理和轮换
- **influencer**: 达人信息管理

## 🔧 开发工作流

### 1. 功能开发流程
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发功能
# - 编写代码
# - 添加注释
# - 编写测试

# 3. 代码检查
npm run lint
npm run format

# 4. 提交代码
git add .
git commit -m "feat: add new feature"

# 5. 推送分支
git push origin feature/new-feature

# 6. 创建Pull Request
```

### 2. 代码规范检查
```bash
# ESLint检查
npm run lint

# Prettier格式化
npm run format

# 修复ESLint问题
npm run lint:fix
```

### 3. 数据库迁移
```bash
# 创建新的迁移文件
npx sequelize-cli migration:generate --name add-new-field

# 运行迁移
npx sequelize-cli db:migrate

# 回滚迁移
npx sequelize-cli db:migrate:undo
```

## 🧪 测试指南

### 测试类型
- **单元测试**: 测试单个函数或方法
- **集成测试**: 测试模块间的交互
- **API测试**: 测试HTTP接口

### 运行测试
```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- userController.test.js

# 生成测试覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

### 测试文件结构
```javascript
describe('UserController', () => {
  beforeEach(() => {
    // 测试前的准备工作
  });

  afterEach(() => {
    // 测试后的清理工作
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      // 测试用例
    });

    it('should return error for invalid data', async () => {
      // 错误情况测试
    });
  });
});
```

## 🔍 调试技巧

### 1. 日志调试
```javascript
// 使用console.log进行调试
console.log('Debug info:', { userId, userData });

// 使用console.error记录错误
console.error('Error occurred:', error);

// 使用console.table显示表格数据
console.table(users);
```

### 2. VSCode调试配置
创建 `.vscode/launch.json`：
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Node.js",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/app.js",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "runtimeExecutable": "nodemon",
      "skipFiles": ["<node_internals>/**"]
    }
  ]
}
```

### 3. 数据库调试
```javascript
// 启用SQL日志
const sequelize = new Sequelize(config, {
  logging: console.log
});

// 查看生成的SQL
const users = await User.findAll({
  logging: (sql) => console.log('SQL:', sql)
});
```

## 📊 性能优化

### 1. 数据库优化
```javascript
// 使用索引
await User.findOne({
  where: { email: userEmail }, // email字段有索引
  attributes: ['id', 'username'] // 只查询需要的字段
});

// 使用连接查询
await User.findAll({
  include: [{
    model: Profile,
    attributes: ['avatar', 'bio']
  }]
});
```

### 2. 缓存策略
```javascript
// 内存缓存示例
const cache = new Map();

async function getCachedUser(userId) {
  if (cache.has(userId)) {
    return cache.get(userId);
  }
  
  const user = await User.findByPk(userId);
  cache.set(userId, user);
  return user;
}
```

### 3. 异步处理
```javascript
// 使用Promise.all并行处理
const [users, posts, comments] = await Promise.all([
  User.findAll(),
  Post.findAll(),
  Comment.findAll()
]);

// 避免在循环中使用await
const userPromises = userIds.map(id => User.findByPk(id));
const users = await Promise.all(userPromises);
```

## 🔐 安全最佳实践

### 1. 输入验证
```javascript
// 验证用户输入
const { error } = userSchema.validate(userData);
if (error) {
  throw new ValidationError(error.details[0].message);
}

// 防止SQL注入
const users = await User.findAll({
  where: {
    username: {
      [Op.like]: `%${searchTerm}%` // 使用参数化查询
    }
  }
});
```

### 2. 敏感信息处理
```javascript
// 不在日志中输出敏感信息
console.log('User login:', {
  userId: user.id,
  username: user.username
  // 不包含密码
});

// 使用环境变量存储密钥
const jwtSecret = process.env.JWT_SECRET;
```

### 3. 错误处理
```javascript
// 不暴露内部错误信息
try {
  // 业务逻辑
} catch (error) {
  console.error('Internal error:', error);
  throw new Error('操作失败'); // 返回通用错误信息
}
```

## 📚 常用命令

### 开发命令
```bash
# 启动开发服务器
npm run dev

# 代码检查和格式化
npm run lint
npm run format

# 数据库操作
npm run db:migrate
npm run db:seed

# 测试
npm test
npm run test:watch
```

### Git命令
```bash
# 查看状态
git status

# 添加文件
git add .

# 提交更改
git commit -m "feat: add new feature"

# 推送代码
git push origin branch-name

# 拉取最新代码
git pull origin main
```

## 🐛 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库配置信息
- 确认数据库用户权限

### 2. 端口占用
```bash
# 查找占用端口的进程
lsof -i :3001

# 杀死进程
kill -9 <PID>
```

### 3. 依赖安装失败
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

## 📖 参考资源

- [Koa.js官方文档](https://koajs.com/)
- [Sequelize官方文档](https://sequelize.org/)
- [MySQL官方文档](https://dev.mysql.com/doc/)
- [Node.js最佳实践](https://github.com/goldbergyoni/nodebestpractices)
- [JavaScript代码规范](https://github.com/airbnb/javascript)

---

**注意**: 本指南会根据项目发展持续更新，建议定期查看最新版本。
