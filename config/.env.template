# MCP客户端环境配置模板
# 复制此文件为 .env 并根据实际情况修改配置

# ===========================================
# MCP服务器配置
# ===========================================

# MCP服务器地址
MCP_SERVER_URL=http://localhost:3000

# 连接超时时间（毫秒）
MCP_TIMEOUT=30000

# 重试次数
MCP_RETRY_COUNT=3

# 重试延迟（毫秒）
MCP_RETRY_DELAY=1000

# ===========================================
# Agent配置
# ===========================================

# Agent唯一标识
MCP_AGENT_ID=external_client_001

# Agent名称
MCP_AGENT_NAME=外部客户端

# Agent版本
MCP_AGENT_VERSION=1.0.0

# Agent描述
MCP_AGENT_DESCRIPTION=外部客户端接入MCP服务

# 运行环境 (development/production/test)
MCP_ENVIRONMENT=development

# ===========================================
# SSE配置
# ===========================================

# 是否启用自动重连
MCP_SSE_AUTO_RECONNECT=true

# 重连延迟（毫秒）
MCP_SSE_RECONNECT_DELAY=5000

# 最大重连次数
MCP_SSE_MAX_RECONNECT_ATTEMPTS=10

# 心跳间隔（毫秒）
MCP_SSE_HEARTBEAT_INTERVAL=30000

# ===========================================
# 爬虫默认配置
# ===========================================

# 默认平台 (xiaohongshu/juxingtu)
MCP_DEFAULT_PLATFORM=xiaohongshu

# 默认最大页数
MCP_DEFAULT_MAX_PAGES=3

# 默认页面大小
MCP_DEFAULT_PAGE_SIZE=20

# 默认延迟范围（毫秒）
MCP_DEFAULT_DELAY_MIN=1000
MCP_DEFAULT_DELAY_MAX=2000

# ===========================================
# 日志配置
# ===========================================

# 日志级别 (debug/info/warn/error)
MCP_LOG_LEVEL=info

# 是否启用控制台日志
MCP_LOG_CONSOLE=true

# 是否启用文件日志
MCP_LOG_FILE_ENABLED=false

# 日志文件路径
MCP_LOG_FILE_PATH=./logs/mcp-client.log

# ===========================================
# 功能开关
# ===========================================

# 是否启用SSE
MCP_ENABLE_SSE=true

# 是否启用自动订阅
MCP_ENABLE_AUTO_SUBSCRIBE=true

# 是否启用心跳检测
MCP_ENABLE_HEARTBEAT=true

# 是否启用重试机制
MCP_ENABLE_RETRY=true

# ===========================================
# 安全配置（可选）
# ===========================================

# API密钥（如果服务器需要认证）
# MCP_API_KEY=your-api-key-here

# 客户端证书路径（如果使用HTTPS客户端认证）
# MCP_CLIENT_CERT_PATH=./certs/client.crt
# MCP_CLIENT_KEY_PATH=./certs/client.key

# ===========================================
# 高级配置
# ===========================================

# 连接池大小
MCP_CONNECTION_POOL_SIZE=10

# 请求队列大小
MCP_REQUEST_QUEUE_SIZE=100

# 事件缓冲区大小
MCP_EVENT_BUFFER_SIZE=1000

# 是否启用调试模式
MCP_DEBUG_MODE=false

# 是否启用性能监控
MCP_ENABLE_METRICS=false

# 监控数据上报间隔（秒）
MCP_METRICS_INTERVAL=60
