{"server": {"url": "http://localhost:3000", "timeout": 30000, "retryCount": 3, "retryDelay": 1000}, "agent": {"id": "external_client_001", "name": "外部客户端", "version": "1.0.0", "capabilities": ["crawler", "data_analysis", "excel_export"], "metadata": {"description": "外部客户端接入MCP服务", "environment": "development", "clientType": "external_application"}}, "sse": {"autoReconnect": true, "reconnectDelay": 5000, "maxReconnectAttempts": 10, "heartbeatInterval": 30000}, "crawler": {"defaultPlatform": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultMaxPages": 3, "defaultPageSize": 20, "defaultDelay": {"min": 1000, "max": 2000}}, "logging": {"level": "info", "enableConsole": true, "enableFile": false, "logFile": "./logs/mcp-client.log"}, "features": {"enableSSE": true, "enableAutoSubscribe": true, "enableHeartbeat": true, "enableRetry": true}}