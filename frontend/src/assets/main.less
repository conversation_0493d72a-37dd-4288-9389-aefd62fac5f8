@import './base.css';

// 字体变量定义
@font-primary: '<PERSON><PERSON><PERSON><PERSON>', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Helvetica Neue', Helvetica,
  Arial, sans-serif;

// 颜色变量
@color-primary: hsla(160, 100%, 37%, 1);
@color-primary-hover: hsla(160, 100%, 37%, 0.2);

// 引入字体
@font-face {
  font-family: 'PuHui';
  src: url('@/assets/fonts/puHui.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

// 链接样式
a,
.green {
  text-decoration: none;
  color: @color-primary;
  transition: 0.4s;
  padding: 3px;

  @media (hover: hover) {
    &:hover {
      background-color: @color-primary-hover;
    }
  }
}

// 响应式布局
@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }
}

// 全局字体设置
* {
  font-family: @font-primary;
}

body {
  font-family: @font-primary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

// 标题字体优化
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: @font-primary;
  font-weight: 600;
}

// 表单元素字体继承
input,
textarea,
select,
button {
  font-family: inherit;
}

// Arco Design 组件样式覆盖
.arco-card-bordered {
  border: none !important;
}

.arco-card-header {
  border-bottom-color: var(--color-border-1) !important;
}

.layout-auto {
  :deep(.arco-table-element) {
    table-layout: auto !important;
  }
}

// 卡片样式变量和混合
@card-border-radius: 8px;
@card-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
@card-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.1);
@card-border-color: #f5f5f5;

// 卡片混合样式
.card-base() {
  border-radius: @card-border-radius;
  box-shadow: @card-shadow;
  background: white;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: @card-shadow-hover;
  }
}

// 间距变量
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 12px;
@spacing-lg: 16px;
@spacing-xl: 20px;
@spacing-xxl: 24px;

// 字体大小变量
@font-size-xs: 11px;
@font-size-sm: 12px;
@font-size-base: 13px;
@font-size-md: 14px;
@font-size-lg: 15px;
@font-size-xl: 16px;
@font-size-xxl: 18px;
@font-size-title: 24px;
@font-size-large-title: 28px;

// 颜色系统
@color-text-primary: #1d2129;
@color-text-secondary: #86909c;
@color-text-disabled: #c9cdd4;
@color-border: #e5e6eb;
@color-border-light: #f5f5f5;
@color-background-light: #fafbfc;
@color-background-card: #f8f9fa;

// 状态颜色
@color-danger: #f53f3f;
@color-warning: #ff7d00;

// 渐变色定义
@gradient-blue: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
@gradient-pink: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
@gradient-cyan: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
@gradient-green: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

// 成功色系
@color-success: #00b42a;
@color-success-light: #52c41a;

// 状态颜色混合
.status-colors() {
  &.create {
    background: linear-gradient(135deg, @color-success 0%, @color-success-light 100%);
  }

  &.start {
    background: linear-gradient(135deg, #165dff 0%, #4080ff 100%);
  }

  &.complete {
    background: linear-gradient(135deg, @color-success 0%, @color-success-light 100%);
  }

  &.update {
    background: linear-gradient(135deg, #ff7d00 0%, #ffa940 100%);
  }

  &.stop {
    background: linear-gradient(135deg, #f53f3f 0%, #ff7875 100%);
  }
}

.arco-card-bordered {
  border: none !important;
}

.arco-card-header {
  border-bottom-color: var(--color-border-1) !important;
}

.layout-auto {
  .arco-table-element {
    table-layout: auto !important;
  }
}

.table-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
