<script setup>
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 初始化应用
onMounted(() => {
  // 初始化用户状态
  userStore.initializeUser()
})
</script>

<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
}

#app {
  width: 100%;
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 链接样式 */
a {
  color: #165dff;
  text-decoration: none;
}

a:hover {
  color: #0e42d2;
}

/* 表格样式优化 */
.arco-table-th {
  background-color: #fafafa !important;
  font-weight: 600 !important;
}

/* 卡片样式优化 */
.arco-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

/* 按钮样式优化 */
.arco-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}

.arco-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
}
</style>
