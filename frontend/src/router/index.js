import { createRouter, createWebHistory } from 'vue-router';
import { useUserStore } from '@/stores/user';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/particle-test',
      name: 'particle-test',
      component: () => import('../views/ParticleTest.vue')
    },
    {
      path: '/',
      name: 'layout',
      component: () => import('../layouts/MainLayout.vue'),
      meta: { requiresAuth: true },
      redirect: '/dashboard',
      children: [
        {
          path: '/dashboard',
          name: 'dashboard',
          component: () => import('../views/DashboardView.vue'),
          meta: { title: '仪表板' }
        },
        {
          path: '/influencers',
          name: 'influencers',
          component: () => import('../views/InfluencerView.vue'),
          meta: { title: '我的达人' }
        },
        {
          path: '/influencers/:id/detail',
          name: 'influencer-detail',
          component: () => import('../views/InfluencerDetailView.vue'),
          meta: { title: '达人详情' }
        },
        {
          path: '/public-influencers',
          name: 'public-influencers',
          component: () => import('../views/PublicInfluencerView.vue'),
          meta: { title: '达人公海' }
        },
        {
          path: '/influencer-reports',
          name: 'influencer-reports',
          component: () => import('../views/InfluencerReportView.vue'),
          meta: { title: '达人提报' }
        },
        {
          path: '/author-videos',
          name: 'author-videos',
          component: () => import('../views/AuthorVideoView.vue'),
          meta: { title: '达人作品库' }
        },
        {
          path: '/crawler',
          name: 'crawler',
          component: () => import('../views/CrawlerView.vue'),
          meta: { title: '爬虫管理' }
        },
        {
          path: '/cookies',
          name: 'cookies',
          component: () => import('../views/CookieView.vue'),
          meta: { title: 'Cookie管理' }
        },
        {
          path: '/agent',
          name: 'agent',
          component: () => import('../views/AgentView.vue'),
          meta: { title: 'AI智能助手' }
        },
        {
          path: '/users',
          name: 'users',
          component: () => import('../views/UserManagementView.vue'),
          meta: { title: '账号管理', requiresAdmin: true }
        },
        {
          path: '/cooperation',
          name: 'cooperation',
          component: () => import('../views/CooperationView.vue'),
          meta: { title: '合作对接管理' }
        },
        {
          path: '/data-fetch-tasks',
          name: 'data-fetch-tasks',
          component: () => import('../components/DataFetchTaskPanel.vue'),
          meta: { title: '笔记数据拉取' }
        },
        {
          path: '/crm-integration',
          name: 'crm-integration',
          component: () => import('../views/CrmIntegrationView.vue'),
          meta: { title: 'CRM集成管理' }
        },
        {
          path: '/dictionaries',
          name: 'dictionaries',
          component: () => import('../views/DictionaryView.vue'),
          meta: { title: '字典管理' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFoundView.vue')
    }
  ]
});

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();

  // 需要认证的路由
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login');
    return;
  }

  // 只允许游客访问的路由（如登录页）
  if (to.meta.requiresGuest && userStore.isLoggedIn) {
    next('/');
    return;
  }

  // 需要管理员权限的路由
  if (to.meta.requiresAdmin && !userStore.isAdmin) {
    next('/dashboard'); // 重定向到首页
    return;
  }

  next();
});

export default router;
