/**
 * 字典数据服务
 * 
 * 功能说明：
 * - 统一管理本地字典和CRM字典数据
 * - 提供字典数据的获取、缓存和合并功能
 * - 支持优先级策略：CRM字典优先，本地字典补充
 * - 提供表单下拉选项的标准化格式
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { dictionaryAPI, crmDictionaryAPI } from './api'

class DictionaryService {
  constructor() {
    // 缓存字典数据
    this.cache = {
      local: new Map(),
      crm: new Map(),
      merged: new Map()
    }
    
    // 缓存过期时间（5分钟）
    this.cacheExpiry = 5 * 60 * 1000
    
    // CRM字典字段映射配置（使用实际存在的field_name作为分类标识符）
    this.crmFieldMapping = {
      // 客户字典字段映射（field_name -> localCategory）
      customer: {
        'seas_id': 'customerPublicSea',      // 所属公海 
        'customer_select_28': 'bloggerFansCount',      // 博主粉丝量级
        'customer_check_box_2': 'seedingPlatform'      // 平台
      },
      // 协议字典字段映射（field_name -> localCategory）
      contract: {
        'contract_select_0': 'cooperationForm',      // 合作形式 (8项)
        'contract_select_26': 'publishPlatform',     // 发布平台 (8项)
        'contract_select_25': 'cooperationBrand',     // 合作品牌
        'contract_textarea_0':'cooperationProduct',   //合作产品
        'contract_select_5': 'rebateCompleted',     //返点完成状态
        'contract_multilevel_select_1': 'contentImplantCoefficient',      // 内容植入系数 (2项)
        'contract_select_27': 'commentMaintenanceCoefficient',  // 评论维护系数 (10项)
        'contract_select_6': 'brandTopicIncluded',            // 品牌话题
        'contract_select_2': 'selfEvaluation'                  // 自我评价
      }
    }
  }

  /**
   * 获取本地字典数据
   * @param {string} category 字典分类
   * @returns {Promise<Array>} 字典项数组
   */
  async getLocalDictionary(category) {
    const cacheKey = `local_${category}`
    const cached = this.cache.local.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data
    }
    
    try {
      const response = await dictionaryAPI.getByCategory(category)
      const data = response.data?.data || []
      
      // 标准化数据格式
      const standardizedData = data.map(item => ({
        value: item.dictKey,
        label: item.dictLabel,
        key: item.dictKey,
        order: item.sortOrder || 0,
        source: 'local',
        category: item.category,
        description: item.description
      }))
      
      // 缓存数据
      this.cache.local.set(cacheKey, {
        data: standardizedData,
        timestamp: Date.now()
      })
      
      return standardizedData
    } catch (error) {
      console.error(`获取本地字典失败 - 分类: ${category}`, error)
      return []
    }
  }

  /**
   * 获取CRM字典数据
   * @param {string} category 本地字典分类
   * @returns {Promise<Array>} 字典项数组
   */
  async getCrmDictionary(category) {
    const cacheKey = `crm_${category}`
    const cached = this.cache.crm.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data
    }
    
    try {
      // 查找对应的CRM字段
      const crmField = this.findCrmFieldByCategory(category)
      if (!crmField) {
        return []
      }
      
      // 使用field_name作为查询参数
      const response = await crmDictionaryAPI.getDictionaryOptions(crmField.deployId, crmField.fieldName)
      const data = response.data?.options || []

      console.log(`🔍 CRM字典查询 - 分类: ${category}, field_name: ${crmField.fieldName}, 数据量: ${data.length}`)

      // 标准化数据格式
      const standardizedData = data.map(item => ({
        value: item.value,
        label: item.label,
        key: item.key,
        order: item.order || 0,
        source: 'crm',
        category: category,
        crmFieldName: crmField.fieldName  // 保存field_name用于调试
      }))
      
      // 缓存数据
      this.cache.crm.set(cacheKey, {
        data: standardizedData,
        timestamp: Date.now()
      })
      
      return standardizedData
    } catch (error) {
      console.error(`❌ 获取CRM字典失败 - 分类: ${category}`, error)

      // 详细错误信息记录
      if (error.response) {
        console.error(`  HTTP状态: ${error.response.status}`)
        console.error(`  错误信息: ${error.response.data?.message || '未知错误'}`)
      } else if (error.request) {
        console.error(`  网络请求失败: ${error.message}`)
      } else {
        console.error(`  请求配置错误: ${error.message}`)
      }

      return []
    }
  }

  /**
   * 根据本地分类查找对应的CRM字段
   * @param {string} category 本地字典分类
   * @returns {Object|null} CRM字段信息
   */
  findCrmFieldByCategory(category) {
    for (const [deployId, fieldMap] of Object.entries(this.crmFieldMapping)) {
      for (const [fieldName, localCategory] of Object.entries(fieldMap)) {
        if (localCategory === category) {
          // fieldName现在是field_name值（如customer_select_1）
          return { deployId, fieldName }
        }
      }
    }
    return null
  }

  /**
   * 获取合并后的字典数据（CRM优先，本地补充）
   * @param {string} category 字典分类
   * @param {Object} options 选项
   * @param {boolean} options.crmFirst 是否CRM优先（默认true）
   * @param {boolean} options.includeLocal 是否包含本地数据（默认true）
   * @param {boolean} options.includeCrm 是否包含CRM数据（默认true）
   * @returns {Promise<Array>} 合并后的字典项数组
   */
  async getMergedDictionary(category, options = {}) {
    const {
      crmFirst = true,
      includeLocal = true,
      includeCrm = true
    } = options
    
    const cacheKey = `merged_${category}_${JSON.stringify(options)}`
    const cached = this.cache.merged.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data
    }
    
    try {
      const promises = []
      
      if (includeLocal) {
        promises.push(this.getLocalDictionary(category))
      }
      
      if (includeCrm) {
        promises.push(this.getCrmDictionary(category))
      }
      
      const results = await Promise.all(promises)
      let localData = []
      let crmData = []
      
      if (includeLocal) {
        localData = results[0] || []
      }
      
      if (includeCrm) {
        crmData = includeLocal ? (results[1] || []) : (results[0] || [])
      }
      
      // 合并数据
      let mergedData = []

      if (crmFirst) {
        // CRM数据优先策略
        mergedData = [...crmData]

        // 添加本地数据中CRM没有的项作为补充
        const crmValues = new Set(crmData.map(item => item.value))
        const uniqueLocalData = localData.filter(item => !crmValues.has(item.value))
        mergedData.push(...uniqueLocalData)

        // 记录数据源使用情况
        if (crmData.length > 0) {
          console.log(`🎯 ${category}: 优先使用CRM数据 (${crmData.length}项)${uniqueLocalData.length > 0 ? ` + 本地补充 (${uniqueLocalData.length}项)` : ''}`)
        } else if (localData.length > 0) {
          console.log(`📚 ${category}: CRM数据不可用，降级使用本地数据 (${localData.length}项)`)
        } else {
          console.warn(`⚠️ ${category}: 无可用数据`)
        }
      } else {
        // 本地数据优先策略
        mergedData = [...localData]

        // 添加CRM数据中本地没有的项
        const localValues = new Set(localData.map(item => item.value))
        const uniqueCrmData = crmData.filter(item => !localValues.has(item.value))
        mergedData.push(...uniqueCrmData)

        console.log(`📚 ${category}: 使用本地数据优先策略 (本地:${localData.length}项, CRM补充:${uniqueCrmData.length}项)`)
      }
      
      // 按排序字段排序
      mergedData.sort((a, b) => {
        if (a.order !== b.order) {
          return a.order - b.order
        }
        return a.label.localeCompare(b.label, 'zh-CN')
      })
      
      // 缓存数据
      this.cache.merged.set(cacheKey, {
        data: mergedData,
        timestamp: Date.now()
      })
      
      return mergedData
    } catch (error) {
      console.error(`获取合并字典失败 - 分类: ${category}`, error)
      return []
    }
  }

  /**
   * 获取字典数据（默认使用合并策略）
   * @param {string} category 字典分类
   * @param {Object} options 选项
   * @returns {Promise<Array>} 字典项数组
   */
  async getDictionary(category, options = {}) {
    return this.getMergedDictionary(category, options)
  }

  /**
   * 清除缓存
   * @param {string} type 缓存类型（local/crm/merged/all）
   */
  clearCache(type = 'all') {
    if (type === 'all') {
      this.cache.local.clear()
      this.cache.crm.clear()
      this.cache.merged.clear()
    } else if (this.cache[type]) {
      this.cache[type].clear()
    }
  }

  /**
   * 预加载常用字典数据
   * @param {Array} categories 字典分类数组
   * @returns {Promise<void>}
   */
  async preloadDictionaries(categories) {
    const promises = categories.map(category => this.getDictionary(category))
    await Promise.all(promises)
    console.log(`✅ 预加载字典数据完成 - 分类: ${categories.join(', ')}`)
  }

  /**
   * 批量获取字典数据（支持本地和CRM混合）
   * @param {Array} categories 字典分类数组
   * @param {Object} options 选项
   * @returns {Promise<Object>} 批量字典数据
   */
  async getBatchDictionaries(categories, options = {}) {
    const {
      crmFirst = true,
      includeLocal = true,
      includeCrm = true
    } = options

    const cacheKey = `batch_${categories.join(',')}_${JSON.stringify(options)}`
    const cached = this.cache.merged.get(cacheKey)

    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data
    }

    try {
      console.log('🔄 批量获取字典数据:', categories)

      const result = {}

      // 并发获取所有分类的字典数据
      const promises = categories.map(async (category) => {
        try {
          const mergedData = await this.getMergedDictionary(category, options)
          result[category] = mergedData
          console.log(`✅ 获取字典数据成功 - ${category}: ${mergedData.length} 项`)
        } catch (error) {
          console.error(`❌ 获取字典数据失败 - ${category}:`, error)
          result[category] = []
        }
      })

      await Promise.all(promises)

      // 缓存结果
      this.cache.merged.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      })

      console.log('✅ 批量字典数据获取完成')
      return result

    } catch (error) {
      console.error('❌ 批量获取字典数据失败:', error)
      throw new Error(`批量获取字典数据失败: ${error.message}`)
    }
  }

  /**
   * 获取字典统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStats() {
    try {
      const [localStats, crmStats] = await Promise.all([
        dictionaryAPI.getCategories(),
        crmDictionaryAPI.getStats()
      ])

      return {
        local: {
          categories: localStats.data?.data?.length || 0
        },
        crm: crmStats.data?.data || {},
        cache: {
          localEntries: this.cache.local.size,
          crmEntries: this.cache.crm.size,
          mergedEntries: this.cache.merged.size
        }
      }
    } catch (error) {
      console.error('获取字典统计信息失败:', error)
      return null
    }
  }
}

// 创建单例实例
const dictionaryService = new DictionaryService()

export default dictionaryService
