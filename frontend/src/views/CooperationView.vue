<template>
  <div class="cooperation-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">合作对接管理</h2>
        <p class="page-description">管理合作对接记录，支持笔记链接解析和数据拉取</p>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <a-card class="search-card">
      <a-form :model="searchForm" layout="inline">
        <a-form-item label="客户名称">
          <a-input v-model="searchForm.customerName" placeholder="请输入客户名称" style="width: 160px" allow-clear />
        </a-form-item>
        <a-form-item label="博主名称">
          <a-input v-model="searchForm.bloggerName" placeholder="请输入博主名称" style="width: 160px" allow-clear />
        </a-form-item>
        <!-- <a-form-item label="种草平台">
          <a-input v-model="searchForm.seedingPlatform" placeholder="请输入种草平台" style="width: 120px" allow-clear />
        </a-form-item> -->
        <!-- <a-form-item label="合作品牌">
          <a-input
            v-model="searchForm.cooperationBrand"
            placeholder="请输入合作品牌"
            style="width: 120px"
            allow-clear
          />
        </a-form-item> -->
        <a-form-item label="平台">
          <a-select v-model="searchForm.platform" placeholder="请选择平台" style="width: 120px" allow-clear>
            <a-option value="xiaohongshu">小红书</a-option>
            <a-option value="juxingtu">巨量星图</a-option>
            <a-option value="淘宝">淘宝</a-option>
            <a-option value="快手">快手</a-option>
            <a-option value="B站">B站</a-option>
            <a-option value="微信&视频号">微信&视频号</a-option>
            <a-option value="微博">微博</a-option>
            <a-option value="知乎">知乎</a-option>
            <a-option value="其他">其他</a-option>
            <a-option value="置换">置换</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="CRM状态">
          <a-select v-model="searchForm.crmLinkStatus" placeholder="请选择CRM状态" style="width: 120px" allow-clear>
            <a-option value="unlinked">未关联</a-option>
            <a-option value="customer_linked">客户已关联</a-option>
            <a-option value="fully_linked">完全关联</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="负责人">
          <a-input v-model="searchForm.responsiblePerson" placeholder="请输入负责人" style="width: 120px" allow-clear />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">重置</a-button>
          <a-button @click="showColumnSettings" style="margin-left: 8px">列设置</a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 合作记录列表 -->
    <a-card class="table-card">
      <div class="table-action">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <template #icon>
              <icon-plus />
            </template>
            新增合作记录
          </a-button>
          <!-- <a-button @click="getScheduleStatus">
            <template #icon>
              <icon-clock-circle />
            </template>
            定时任务状态
          </a-button>
          <a-button @click="checkCookieStatus">
            <template #icon>
              <icon-user />
            </template>
            Cookie状态
          </a-button>
          <a-button @click="triggerDataFetch">
            <template #icon>
              <icon-refresh />
            </template>
            手动拉取数据
          </a-button> -->
        </a-space>
      </div>

      <a-table
        :columns="getDisplayColumns()"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        class="layout-auto"
        :border="false"
        :scroll="{ x: 'max-content' }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @sorter-change="handleSorterChange"
      >
        <!-- 平台列 -->
        <template #platform="{ record }">
          <a-tag :color="getPlatformColor(record.platform)">
            {{ getPlatformName(record.platform) }}
          </a-tag>
        </template>

        <!-- 合作金额列 -->
        <template #cooperationAmount="{ record }">
          <span>{{ record.cooperationAmount || '-' }}</span>
        </template>

        <!-- 工作进度列 -->
        <template #workProgress="{ record }">
          <span>{{ record.workProgress || '-' }}</span>
        </template>

        <!-- 合作价格列 -->
        <template #cooperationPrice="{ record }">
          <span>{{ record.cooperationPrice || '-' }}</span>
        </template>

        <!-- 笔记ID列 -->
        <template #noteId="{ record }">
          <span v-if="record.noteId" class="note-id">{{ record.noteId }}</span>
          <span v-else class="text-gray">未解析</span>
        </template>

        <!-- 数据拉取状态列 -->
        <template #dataFetchStatus="{ record }">
          <a-tag :color="getFetchStatusColor(record.dataFetchStatus)">
            {{ getFetchStatusText(record.dataFetchStatus) }}
          </a-tag>
        </template>

        <!-- 定时拉取时间列 -->
        <template #scheduledFetchTime="{ record }">
          <span v-if="record.scheduledFetchTime">
            {{ formatDateTime(record.scheduledFetchTime) }}
          </span>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 达人平台主页列 -->
        <template #influencerHomepage="{ record }">
          <a-link :href="record.influencerHomepage" target="_blank" v-if="record.influencerHomepage">
            {{ record.influencerHomepage }}
          </a-link>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 合作笔记链接列 -->
        <template #cooperationNoteLink="{ record }">
          <a-link :href="record.cooperationNoteLink" target="_blank" v-if="record.cooperationNoteLink">
            {{ record.cooperationNoteLink }}
          </a-link>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 阅读量列 -->
        <template #readNum="{ record }">
          <span v-if="record.readNum">{{ record.readNum }}</span>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- CRM关联状态列 -->
        <template #crmLinkStatus="{ record }">
          <a-tag :color="getCrmStatusColor(record.crmLinkStatus)">
            {{ getCrmStatusText(record.crmLinkStatus) }}
          </a-tag>
        </template>

        <!-- 约定发布时间列 -->
        <template #scheduledPublishTime="{ record }">
          <span v-if="record.scheduledPublishTime">
            {{ formatDate(record.scheduledPublishTime) }}
          </span>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 实际发布日期列 -->
        <template #actualPublishDate="{ record }">
          <span v-if="record.actualPublishDate">
            {{ formatDate(record.actualPublishDate) }}
          </span>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 观看量列 -->
        <template #viewCount="{ record }">
          <span v-if="record.viewCount">{{ formatNumber(record.viewCount) }}</span>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 点赞数列 -->
        <template #likeCount="{ record }">
          <span v-if="record.likeCount">{{ formatNumber(record.likeCount) }}</span>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 发布链接列 -->
        <template #publishLink="{ record }">
          <a-link :href="record.publishLink" target="_blank" v-if="record.publishLink">
            {{ record.publishLink }}
          </a-link>
          <span v-else class="text-gray">-</span>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <a-space wrap>
            <a-link type="text" size="small" @click="showEditModal(record)"> 编辑 </a-link>
            <a-link type="text" size="small" @click="viewDetail(record)" v-if="record.noteId"> 详情 </a-link>

            <!-- CRM重新创建按钮 -->
            <a-dropdown v-if="needCrmActions(record)" trigger="hover">
              <a-link type="text" size="small">
                CRM操作
                <IconDown />
              </a-link>
              <template #content>
                <a-doption v-if="!record.externalCustomerId" @click="recreateCrmCustomer(record)"> 创建客户 </a-doption>
                <a-doption
                  v-if="record.externalCustomerId && !record.externalAgreementId"
                  @click="recreateCrmAgreement(record)"
                >
                  创建协议
                </a-doption>
                <a-doption v-if="!record.externalCustomerId" @click="recreateCrmBoth(record)">
                  创建客户和协议
                </a-doption>
              </template>
            </a-dropdown>

            <a-popconfirm content="确定要删除这条记录吗？" position="left" @ok="deleteRecord(record.id)">
              <a-link type="text" size="small" status="danger"> 删除 </a-link>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑模态框 -->
    <CooperationForm
      :visible="formVisible"
      :is-edit-mode="isEditMode"
      :edit-data="editData"
      @cancel="handleFormCancel"
      @success="handleFormSuccess"
    />

    <!-- 数据详情模态框 -->
    <CooperationDataModal
      :visible="dataModalVisible"
      :cooperation-data="selectedCooperation"
      @cancel="dataModalVisible = false"
    />

    <!-- 列设置模态框 -->
    <a-modal
      v-model:visible="columnSettingsVisible"
      title="列显示设置"
      width="600px"
      @ok="saveColumnSettings"
      @cancel="columnSettingsVisible = false"
    >
      <div class="column-settings">
        <p class="settings-tip">选择要显示的列：</p>
        <a-checkbox-group v-model="selectedColumns" direction="vertical">
          <div v-for="column in allColumns" :key="column.dataIndex" class="column-item">
            <a-checkbox :value="column.dataIndex">
              {{ column.title }}
            </a-checkbox>
            <span class="column-description">{{ column.description || '' }}</span>
          </div>
        </a-checkbox-group>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconPlus, IconClockCircle, IconRefresh, IconUser, IconDown } from '@arco-design/web-vue/es/icon';
import { cooperationAPI } from '@/services/api';
import CooperationForm from '@/components/CooperationForm.vue';
import CooperationDataModal from '@/components/CooperationDataModal.vue';
import { getPlatformColor, getPlatformName } from '@/utils/platformUtils';

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const formVisible = ref(false);
const dataModalVisible = ref(false);
const isEditMode = ref(false);
const editData = ref({});
const selectedCooperation = ref({});

// 列设置相关
const columnSettingsVisible = ref(false);
const selectedColumns = ref([]);
const allColumns = ref([]);

// 搜索表单
const searchForm = reactive({
  // 新字段搜索
  customerName: '',
  seedingPlatform: '',
  cooperationBrand: '',
  crmLinkStatus: '',

  // 原有字段搜索（保持兼容）
  bloggerName: '',
  platform: '',
  workProgress: '',
  cooperationMonth: '',
  responsiblePerson: '',
  keyword: ''
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 排序配置
const sortConfig = reactive({
  sortBy: 'externalCustomerId', // 默认按CRM客户ID排序
  sortOrder: 'DESC' // 默认降序
});

// 表格列配置
const columns = [
  // 核心信息列（优先显示）
  {
    title: '客户名称',
    dataIndex: 'customerName',
    fixed: 'left',
    sortable: {
      sortDirections: ['ascend', 'descend']
    }
  },
  {
    title: '平台',
    dataIndex: 'platform',
    slotName: 'platform'
  },
  // {
  //   title: '发布平台',
  //   dataIndex: 'publishPlatform'
  // },
  {
    title: '标题',
    dataIndex: 'title',
    ellipsis: true,
    tooltip: true
  },
  // {
  //   title: '种草平台',
  //   dataIndex: 'seedingPlatform'
  // },
  // {
  //   title: '合作形式',
  //   dataIndex: 'cooperationForm'
  // },
  // {
  //   title: '合作品牌',
  //   dataIndex: 'cooperationBrand'
  // },
  {
    title: '合作金额',
    dataIndex: 'cooperationAmount',
    slotName: 'cooperationAmount',
    align: 'right'
  },
  // 原有重要字段（保持兼容）
  // {
  //   title: '博主名称',
  //   dataIndex: 'bloggerName'
  // },

  {
    title: '负责人',
    dataIndex: 'responsiblePerson'
  },
  {
    title: '合作价格',
    dataIndex: 'cooperationPrice',
    slotName: 'cooperationPrice',
    align: 'right'
  },

  // 状态和进度信息
  // {
  //   title: '工作进度',
  //   dataIndex: 'workProgress',
  //   slotName: 'workProgress'
  // },

  {
    title: '数据状态',
    dataIndex: 'dataFetchStatus',
    slotName: 'dataFetchStatus'
  },

  // 时间信息
  {
    title: '约定发布时间',
    dataIndex: 'scheduledPublishTime',
    slotName: 'scheduledPublishTime'
  },
  {
    title: '实际发布日期',
    dataIndex: 'actualPublishDate',
    slotName: 'actualPublishDate'
  },

  // 数据指标
  {
    title: '观看量',
    dataIndex: 'viewCount',
    align: 'right',
    slotName: 'viewCount',
    sortable: {
      sortDirections: ['ascend', 'descend']
    }
  },
  {
    title: '点赞数',
    dataIndex: 'likeCount',
    align: 'right',
    slotName: 'likeCount',
    sortable: {
      sortDirections: ['ascend', 'descend']
    }
  },

  // 链接信息（可选显示）
  {
    title: '发布链接',
    dataIndex: 'publishLink',
    slotName: 'publishLink',
    width: 320
  },
  // 创建时间
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    slotName: 'createdAt'
  },
  {
    title: 'CRM关联状态',
    dataIndex: 'crmLinkStatus',
    slotName: 'crmLinkStatus',
    fixed: 'right'
  },

  // 操作列
  {
    title: '操作',
    slotName: 'actions',
    fixed: 'right',
    width: 120
  }
];

// 初始化列设置
const initializeColumnSettings = () => {
  // 设置所有可用列
  allColumns.value = columns
    .filter(col => col.dataIndex && col.dataIndex !== 'actions')
    .map(col => ({
      ...col,
      description: getColumnDescription(col.dataIndex)
    }));

  // 设置默认显示的列（核心字段）
  const defaultColumns = [
    'customerName',
    'title',
    'seedingPlatform',
    'cooperationForm',
    'cooperationBrand',
    'cooperationAmount',
    'bloggerName',
    'platform',
    'responsiblePerson',
    'crmLinkStatus',
    'workProgress'
  ];

  selectedColumns.value = defaultColumns;
};

// 获取列描述
const getColumnDescription = dataIndex => {
  const descriptions = {
    customerName: '客户/达人名称',
    title: '合作记录标题',
    seedingPlatform: '种草推广平台',
    cooperationForm: '合作方式类型',
    cooperationBrand: '合作品牌名称',
    cooperationAmount: '合作金额',
    publishPlatform: '内容发布平台',
    bloggerName: '博主名称（原字段）',
    platform: '平台类型（原字段）',
    responsiblePerson: '项目负责人',
    cooperationPrice: '合作价格（原字段）',
    workProgress: '当前工作进度',
    crmLinkStatus: 'CRM系统关联状态',
    dataFetchStatus: '数据拉取状态',
    scheduledPublishTime: '计划发布时间',
    actualPublishDate: '实际发布日期',
    viewCount: '内容观看量',
    likeCount: '点赞数量',
    publishLink: '发布内容链接'
  };
  return descriptions[dataIndex] || '';
};

// 获取合作记录列表
const getCooperationList = async () => {
  try {
    loading.value = true;

    // 过滤掉空值参数
    const filteredParams = {};
    Object.keys(searchForm).forEach(key => {
      if (searchForm[key] !== '' && searchForm[key] !== null && searchForm[key] !== undefined) {
        filteredParams[key] = searchForm[key];
      }
    });

    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      sortBy: sortConfig.sortBy,
      sortOrder: sortConfig.sortOrder,
      ...filteredParams
    };

    console.log('发送筛选参数:', params); // 调试日志
    const response = await cooperationAPI.getList(params);
    tableData.value = response.data;
    pagination.total = response.pagination?.total || 0;
  } catch (error) {
    console.error('获取合作记录列表失败:', error);
    Message.error('获取合作记录列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  getCooperationList();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });

  // 重置排序为默认值
  sortConfig.sortBy = 'externalCustomerId';
  sortConfig.sortOrder = 'DESC';

  pagination.current = 1;
  getCooperationList();
};

// 分页处理
const handlePageChange = page => {
  pagination.current = page;
  getCooperationList();
};

const handlePageSizeChange = pageSize => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  getCooperationList();
};

// 排序处理
const handleSorterChange = (dataIndex, direction) => {
  console.log('排序变化:', { dataIndex, direction });

  if (direction) {
    // 设置新的排序
    sortConfig.sortBy = dataIndex;
    sortConfig.sortOrder = direction === 'ascend' ? 'ASC' : 'DESC';
  } else {
    // 重置为默认排序
    sortConfig.sortBy = 'externalCustomerId';
    sortConfig.sortOrder = 'DESC';
  }

  // 重置到第一页并重新加载数据
  pagination.current = 1;
  getCooperationList();
};

// 显示创建模态框
const showCreateModal = () => {
  isEditMode.value = false;
  editData.value = {};
  formVisible.value = true;
};

// 显示编辑模态框
const showEditModal = record => {
  isEditMode.value = true;
  editData.value = { ...record };
  formVisible.value = true;
};

// 表单取消
const handleFormCancel = () => {
  formVisible.value = false;
};

// 表单成功
const handleFormSuccess = () => {
  formVisible.value = false;
  getCooperationList();
};

// 查看详情
const viewDetail = record => {
  selectedCooperation.value = record;
  dataModalVisible.value = true;
};

// 拉取笔记数据
const fetchNoteData = async record => {
  try {
    loading.value = true;
    await cooperationAPI.fetchNoteData(record.id);
    Message.success('笔记数据拉取已启动');
    getCooperationList();
  } catch (error) {
    console.error('拉取笔记数据失败:', error);
    Message.error(error.response.data.message || '拉取笔记数据失败');
  } finally {
    loading.value = false;
  }
};

// 删除记录
const deleteRecord = async id => {
  try {
    await cooperationAPI.delete(id);
    Message.success('删除成功');
    getCooperationList();
  } catch (error) {
    console.error('删除失败:', error);
    Message.error(error.response.data.message || '删除失败');
  }
};

// 获取定时任务状态
const getScheduleStatus = async () => {
  try {
    const response = await cooperationAPI.getScheduleStatus();
    const status = response.data;
    Message.info(`定时任务状态: ${status.initialized ? '运行中' : '未启动'}, 任务数: ${status.totalTasks}`);
  } catch (error) {
    console.error('获取定时任务状态失败:', error);
    Message.error(error.response.data.message || '获取定时任务状态失败');
  }
};

// 检查Cookie状态
const checkCookieStatus = async () => {
  try {
    const response = await cooperationAPI.checkCookieStatus();
    const status = response.data;

    if (status.xiaohongshu.hasAvailable) {
      const cookieInfo = status.xiaohongshu.cookieInfo;
      Message.success(`小红书Cookie状态正常: ${cookieInfo.accountName} (优先级: ${cookieInfo.priority})`);
    } else {
      Message.warning('小红书没有可用Cookie，请先添加有效的Cookie');
    }
  } catch (error) {
    console.error('检查Cookie状态失败:', error);
    Message.error('检查Cookie状态失败');
  }
};

// 手动触发数据拉取
const triggerDataFetch = async () => {
  try {
    loading.value = true;
    await cooperationAPI.triggerDataFetch();
    Message.success('笔记数据拉取已触发');
  } catch (error) {
    console.error('触发数据拉取失败:', error);
    Message.error(error.response.data.message || '触发数据拉取失败');
  } finally {
    loading.value = false;
  }
};

// 工具函数

const getFetchStatusColor = status => {
  const colors = {
    pending: 'gray',
    fetching: 'blue',
    success: 'green',
    failed: 'red'
  };
  return colors[status] || 'gray';
};

const getFetchStatusText = status => {
  const texts = {
    pending: '待拉取',
    fetching: '拉取中',
    success: '成功',
    failed: '失败'
  };
  return texts[status] || status;
};

const formatDateTime = dateTime => {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

const formatDate = date => {
  if (!date) return '-';
  const dateObj = new Date(date);
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};

const formatNumber = number => {
  if (!number) return '-';
  return number.toLocaleString('zh-CN');
};

// CRM状态相关方法
const getCrmStatusColor = status => {
  const colors = {
    unlinked: 'gray',
    customer_linked: 'orange',
    fully_linked: 'green'
  };
  return colors[status] || 'gray';
};

const getCrmStatusText = status => {
  const texts = {
    unlinked: '未关联',
    customer_linked: '客户已关联',
    fully_linked: '完全关联'
  };
  return texts[status] || '未知';
};

const needCrmActions = record => {
  return record.crmLinkStatus !== 'fully_linked';
};

// CRM重新创建方法
const recreateCrmCustomer = async record => {
  try {
    const response = await cooperationAPI.recreateCrmCustomer(record.id);
    if (response.success) {
      Message.success('CRM客户创建成功');
      await getCooperationList(); // 刷新列表
    } else {
      Message.error(response.message || 'CRM客户创建失败');
    }
  } catch (error) {
    console.error('创建CRM客户失败:', error);
    Message.error('创建CRM客户失败');
  }
};

const recreateCrmAgreement = async record => {
  // 打开编辑弹窗
  Message.info('请编辑协议信息，保存后将自动创建CRM协议');
  showEditModal(record);
  // try {
  //   const response = await cooperationAPI.recreateCrmAgreement(record.id);
  //   if (response.success) {
  //     Message.success('CRM协议创建成功');
  //     await getCooperationList(); // 刷新列表
  //   } else {
  //     Message.error(response.message || 'CRM协议创建失败');
  //   }
  // } catch (error) {
  //   console.error('创建CRM协议失败:', error);
  //   Message.error('创建CRM协议失败');
  // }
};

const recreateCrmBoth = async record => {
  try {
    // 先创建客户
    const customerResponse = await cooperationAPI.recreateCrmCustomer(record.id);
    if (!customerResponse.success) {
      Message.error(customerResponse.message || 'CRM客户创建失败');
      return;
    }

    // 再创建协议
    const agreementResponse = await cooperationAPI.recreateCrmAgreement(record.id);
    if (agreementResponse.success) {
      Message.success('CRM客户和协议创建成功');
      await getCooperationList(); // 刷新列表
    } else {
      Message.error(agreementResponse.message || 'CRM协议创建失败');
    }
  } catch (error) {
    console.error('创建CRM客户和协议失败:', error);
    Message.error('创建CRM客户和协议失败');
  }
};

// 列设置相关方法
const showColumnSettings = () => {
  columnSettingsVisible.value = true;
};

const saveColumnSettings = () => {
  // 这里可以保存用户的列设置到localStorage
  localStorage.setItem('cooperation-columns', JSON.stringify(selectedColumns.value));
  columnSettingsVisible.value = false;
  Message.success('列设置已保存');
};

const loadColumnSettings = () => {
  const saved = localStorage.getItem('cooperation-columns');
  if (saved) {
    try {
      selectedColumns.value = JSON.parse(saved);
    } catch (error) {
      console.error('加载列设置失败:', error);
    }
  }
};

// 根据选中的列过滤显示的列
const getDisplayColumns = () => {
  const displayColumns = columns.filter(col => {
    if (col.slotName === 'actions') return true; // 操作列始终显示
    return selectedColumns.value.includes(col.dataIndex);
  });
  return displayColumns;
};

// 组件挂载时获取数据
onMounted(() => {
  initializeColumnSettings();
  loadColumnSettings();
  getCooperationList();
});
</script>

<style scoped>
.cooperation-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1d2129;
}

.page-description {
  color: #86909c;
  margin: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  background: white;
}

.table-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.note-id {
  font-family: monospace;
  font-size: 12px;
  background: #f2f3f5;
  padding: 2px 4px;
  border-radius: 4px;
}

.text-gray {
  color: #86909c;
}

/* 列设置样式 */
.column-settings {
  max-height: 400px;
  overflow-y: auto;
}

.settings-tip {
  margin-bottom: 16px;
  color: #86909c;
  font-size: 14px;
}

.column-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f2f3f5;
}

.column-item:last-child {
  border-bottom: none;
}

.column-description {
  color: #86909c;
  font-size: 12px;
  margin-left: 8px;
}
</style>
