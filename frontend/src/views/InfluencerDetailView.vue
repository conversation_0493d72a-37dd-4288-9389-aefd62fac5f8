<template>
  <div class="influencer-detail-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button type="text" @click="goBack" class="back-button">
          <template #icon>
            <icon-arrow-left />
          </template>
          返回
        </a-button>
        <h2 class="page-title">达人详情</h2>
        <p class="page-description">查看达人的完整信息和数据统计</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button type="primary" @click="editInfluencer">
            <template #icon>
              <icon-edit />
            </template>
            编辑达人
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <a-result status="error" :title="error" />
    </div>

    <!-- 详情内容 -->
    <div v-else-if="detailData" class="detail-content">
      <!-- 基础资料卡片 -->
      <a-card class="basic-info-card" title="基础资料">
        <a-row :gutter="24">
          <a-col :span="6">
            <div class="avatar-section">
              <a-avatar :size="120" class="influencer-avatar">
                <img
                  v-if="detailData.basicInfo.avatarUrl"
                  :src="detailData.basicInfo.avatarUrl"
                  alt="avatar"
                  @error="handleImageError"
                />
                <span v-else class="avatar-text">
                  {{ detailData.basicInfo.nickname?.charAt(0) }}
                </span>
              </a-avatar>
              <h3 class="influencer-name">{{ detailData.basicInfo.nickname }}</h3>
              <a-tag :color="getPlatformColor(detailData.basicInfo.platform)" class="platform-tag">
                {{ getPlatformName(detailData.basicInfo.platform) }}
              </a-tag>
            </div>
          </a-col>
          <a-col :span="18">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="达人昵称">
                {{ detailData.basicInfo.nickname }}
              </a-descriptions-item>
              <a-descriptions-item label="平台类型">
                <a-tag :color="getPlatformColor(detailData.basicInfo.platform)">
                  {{ getPlatformName(detailData.basicInfo.platform) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="平台ID">
                {{ detailData.basicInfo.platformId || '暂无' }}
              </a-descriptions-item>
              <a-descriptions-item label="粉丝数量">
                <span class="followers-count">
                  {{ formatNumber(detailData.basicInfo.followersCount) }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="分类">
                <div
                  v-for="(subTags, mainTag) in parseTagsRelation(
                    detailData?.basicInfo?.influencerTags || detailData?.basicInfo?.authorExtInfo?.tags_relation
                  )"
                  :key="mainTag"
                  class="tag-group"
                >
                  <a-tag size="small" color="blue" style="margin-right: 4px; margin-bottom: 2px">
                    {{ mainTag }}
                  </a-tag>
                  <a-tag
                    v-for="subTag in subTags"
                    :key="subTag"
                    size="small"
                    color="green"
                    style="margin-right: 4px; margin-bottom: 2px"
                  >
                    {{ subTag }}
                  </a-tag>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-tag :color="detailData.basicInfo.status === 'active' ? 'green' : 'red'">
                  {{ detailData.basicInfo.status === 'active' ? '活跃' : '非活跃' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="内容主题" :span="2">
                <a-space
                  v-if="
                    detailData.basicInfo.contentTheme ||
                    (detailData.basicInfo.authorExtInfo &&
                      detailData.basicInfo.authorExtInfo?.content_theme_labels_180d)
                  "
                >
                  <a-tag
                    v-for="theme in formatContentTheme(
                      detailData?.basicInfo?.contentTheme ||
                        detailData?.basicInfo?.authorExtInfo?.content_theme_labels_180d
                    )"
                    :key="theme"
                    size="small"
                    color="blue"
                    style="margin-right: 4px; margin-bottom: 2px"
                  >
                    {{ theme }}
                  </a-tag>
                </a-space>
                <span v-else class="text-gray">暂无标签</span>
              </a-descriptions-item>
              <a-descriptions-item label="联系方式" :span="2">
                <div v-if="detailData.basicInfo.contactInfo">
                  <div v-if="detailData.basicInfo.contactInfo.wechat">
                    微信：{{ detailData.basicInfo.contactInfo.wechat }}
                  </div>
                  <div v-if="detailData.basicInfo.contactInfo.phone">
                    电话：{{ detailData.basicInfo.contactInfo.phone }}
                  </div>
                </div>
                <span v-else class="text-gray">暂无联系方式</span>
              </a-descriptions-item>
              <a-descriptions-item label="备注" :span="2">
                {{ detailData.basicInfo.notes || '暂无备注' }}
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">
                {{ formatDate(detailData.basicInfo.createdAt) }}
              </a-descriptions-item>
              <a-descriptions-item label="更新时间">
                {{ formatDate(detailData.basicInfo.updatedAt) }}
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
        </a-row>
      </a-card>

      <!-- 视频数据概览卡片 -->
      <a-card class="video-stats-card" title="视频数据概览">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总视频数" :value="detailData.videoStats.totalVideos || 0" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="总播放量" :value="detailData.videoStats.totalPlays || 0" :formatter="formatNumber" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均播放量" :value="detailData.videoStats.avgPlays || 0" :formatter="formatNumber" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="最高播放量" :value="detailData.videoStats.maxPlays || 0" :formatter="formatNumber" />
          </a-col>
        </a-row>
        <a-divider />
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic title="总点赞数" :value="detailData.videoStats.totalLikes || 0" :formatter="formatNumber" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="总评论数" :value="detailData.videoStats.totalComments || 0" :formatter="formatNumber" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="总分享数" :value="detailData.videoStats.totalShares || 0" :formatter="formatNumber" />
          </a-col>
        </a-row>
      </a-card>

      <!-- 扩展信息卡片 -->
      <a-card class="extended-info-card" title="扩展信息">
        <div v-if="hasExtendedInfo">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item
              v-for="(value, key) in filteredExtendedInfo"
              :key="key"
              :label="formatExtInfoLabel(key)"
            >
              <span v-if="typeof value === 'object'">
                {{ JSON.stringify(value, null, 2) }}
              </span>
              <span v-else>{{ value }}</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div v-else class="no-extended-info">
          <a-empty description="暂无扩展信息" />
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import { influencerAPI } from '@/services/api';
import { IconArrowLeft, IconEdit } from '@arco-design/web-vue/es/icon';

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const error = ref('');
const detailData = ref(null);

// 计算属性
const hasExtendedInfo = computed(() => {
  return (
    detailData.value?.extendedInfo &&
    Object.keys(detailData.value.extendedInfo).length > 0 &&
    !detailData.value.extendedInfo.error
  );
});

const filteredExtendedInfo = computed(() => {
  if (!detailData.value?.extendedInfo) return {};

  // 过滤掉内部字段
  const filtered = {};
  Object.keys(detailData.value.extendedInfo).forEach(key => {
    if (!key.startsWith('_') && key !== 'error') {
      filtered[key] = detailData.value.extendedInfo[key];
    }
  });
  return filtered;
});

// 方法
const loadInfluencerDetail = async () => {
  try {
    loading.value = true;
    error.value = '';

    const response = await influencerAPI.getDetailById(route.params.id);
    if (response.success) {
      detailData.value = response.data;
    } else {
      error.value = response.message || '获取达人详情失败';
    }
  } catch (err) {
    console.error('获取达人详情失败:', err);
    error.value = '获取达人详情失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push('/influencers');
};

const editInfluencer = () => {
  // 这里可以跳转到编辑页面或打开编辑弹窗
  Message.info('编辑功能待实现');
};

const getPlatformName = platform => {
  const platformMap = {
    xiaohongshu: '小红书',
    juxingtu: '巨量星图'
  };
  return platformMap[platform] || platform;
};

const getPlatformColor = platform => {
  const colorMap = {
    xiaohongshu: 'red',
    juxingtu: 'blue'
  };
  return colorMap[platform] || 'gray';
};

const formatNumber = num => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num?.toLocaleString() || '0';
};

const formatDate = dateStr => {
  if (!dateStr) return '暂无';
  return new Date(dateStr).toLocaleString('zh-CN');
};

const formatExtInfoLabel = key => {
  const labelMap = {
    star_level: '达人等级',
    cooperation_price: '合作价格',
    tags: '标签',
    region: '地区',
    age_range: '年龄段',
    gender: '性别',
    mcn_name: 'MCN机构',
    cooperation_count: '合作次数',
    avg_view_count: '平均观看量',
    engagement_rate: '互动率'
  };
  return labelMap[key] || key;
};

const handleImageError = e => {
  e.target.style.display = 'none';
};

// 解析标签关系数据
const parseTagsRelation = tagsData => {
  if (!tagsData) return {};

  // 如果是字符串，尝试解析为JSON
  if (typeof tagsData === 'string') {
    try {
      const parsed = JSON.parse(tagsData);
      // 如果解析后是数组（小红书格式），转换为对象格式
      if (Array.isArray(parsed)) {
        return { 标签: parsed };
      }
      return parsed;
    } catch {
      return {};
    }
  }

  // 如果是数组（小红书的influencerTags直接是数组格式）
  if (Array.isArray(tagsData)) {
    return { 标签: tagsData };
  }

  // 如果已经是对象，直接返回
  if (typeof tagsData === 'object' && tagsData !== null) {
    return tagsData;
  }

  return {};
};

const formatContentTheme = themeData => {
  if (!themeData) return [];
  if (Array.isArray(themeData)) return themeData.slice(0, 3); // 最多显示3个主题
  if (typeof themeData === 'string') {
    try {
      const parsed = JSON.parse(themeData);
      return Array.isArray(parsed) ? parsed.slice(0, 3) : [];
    } catch {
      return [themeData];
    }
  }
  return [];
};

// 生命周期
onMounted(() => {
  loadInfluencerDetail();
});
</script>

<style scoped>
.influencer-detail-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.back-button {
  align-self: flex-start;
  margin-bottom: 8px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

.page-description {
  margin: 0;
  color: #86909c;
  font-size: 14px;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.basic-info-card,
.video-stats-card,
.extended-info-card {
  border-radius: 8px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.influencer-avatar {
  border: 3px solid #f2f3f5;
}

.avatar-text {
  font-size: 48px;
  font-weight: bold;
  color: #86909c;
}

.influencer-name {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

.platform-tag {
  font-size: 12px;
}

.followers-count {
  font-weight: 600;
  color: #f53f3f;
}

.text-gray {
  color: #86909c;
}

.no-extended-info {
  text-align: center;
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    align-self: flex-start;
  }
}
</style>
