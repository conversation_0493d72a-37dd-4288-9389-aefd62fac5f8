<template>
  <div class="cookie-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">Cookie管理</h2>
        <p class="page-description">管理各平台的Cookie信息，确保爬虫正常运行</p>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <a-card class="search-card">
      <a-form :model="searchForm" layout="inline" @submit="handleSearch">
        <a-form-item label="平台">
          <a-select v-model="searchForm.platform" placeholder="请选择平台" style="width: 180px" allow-clear>
            <a-option value="douyin">抖音</a-option>
            <a-option value="xiaohongshu">小红书</a-option>
            <a-option value="weibo">微博</a-option>
            <a-option value="kuaishou">快手</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model="searchForm.status" placeholder="请选择状态" style="width: 120px" allow-clear>
            <a-option value="valid">有效</a-option>
            <a-option value="invalid">无效</a-option>
            <a-option value="expired">已过期</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="账号">
          <a-input v-model="searchForm.account" placeholder="请输入账号" style="width: 200px" allow-clear />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">搜索</a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">重置</a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- Cookie列表 -->
    <a-card class="table-card">
      <div class="table-action">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <template #icon>
              <icon-plus />
            </template>
            添加Cookie
          </a-button>
        </a-space>
        <a-space> </a-space>
      </div>
      <a-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        class="layout-auto"
        :scroll="{ x: 'max-content' }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <!-- 平台列 -->
        <template #platform="{ record }">
          <a-tag :color="getPlatformColor(record.platform)">
            {{ getPlatformName(record.platform) }}
          </a-tag>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- Cookie值列 -->
        <template #cookiePreview="{ record }">
          <div class="cookie-value">
            <span class="cookie-preview">{{ getCookiePreview(record.cookiePreview) }}</span>
            <a-button type="text" size="small" @click="showCookieDetail(record)"> 查看完整 </a-button>
          </div>
        </template>

        <!-- 过期时间列 -->
        <template #expiresAt="{ record }">
          <span :class="{ expired: isExpired(record.expiresAt) }">
            {{ record.expiresAt || '永不过期' }}
          </span>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <a-space>
            <a-link type="text" size="small" @click="validateCookie(record.id)"> 验证 </a-link>
            <a-link type="text" size="small" @click="editCookie(record)"> 编辑 </a-link>
            <a-popconfirm content="确定要删除这个Cookie吗？" @ok="deleteCookie(record.id)">
              <a-link type="text" size="small" status="danger"> 删除 </a-link>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑Cookie弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑Cookie' : '添加Cookie'"
      width="700px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="平台" field="platform">
              <a-select v-model="form.platform" placeholder="请选择平台">
                <a-option value="douyin">抖音</a-option>
                <a-option value="xiaohongshu">小红书</a-option>
                <a-option value="weibo">微博</a-option>
                <a-option value="kuaishou">快手</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="账号" field="account">
              <a-input v-model="form.account" placeholder="请输入账号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="Cookie值" field="cookieValue">
          <a-textarea v-model="form.cookieValue" placeholder="请粘贴完整的Cookie字符串" :rows="6" />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="过期时间" field="expiresAt">
              <a-date-picker
                v-model="form.expiresAt"
                style="width: 100%"
                placeholder="请选择过期时间"
                show-time
                format="YYYY/MM/DD HH:mm:ss"
                value-format="YYYY/MM/DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="优先级" field="priority">
              <a-input-number
                v-model="form.priority"
                placeholder="数值越大优先级越高"
                :min="1"
                :max="100"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="备注" field="remark">
          <a-textarea v-model="form.remark" placeholder="请输入备注信息" :rows="2" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Cookie详情弹窗 -->
    <a-modal v-model:visible="detailModalVisible" title="Cookie详情" width="800px" :footer="false">
      <div class="cookie-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="平台">
            <a-tag :color="getPlatformColor(selectedCookie?.platform)">
              {{ getPlatformName(selectedCookie?.platform) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="账号">{{ selectedCookie?.accountName }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(selectedCookie?.status)">
              {{ getStatusText(selectedCookie?.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="过期时间">{{ selectedCookie?.expiresAt || '永不过期' }}</a-descriptions-item>
          <a-descriptions-item label="最后验证时间">{{ selectedCookie?.lastValidatedAt }}</a-descriptions-item>
          <a-descriptions-item label="备注">{{ selectedCookie?.remark || '无' }}</a-descriptions-item>
        </a-descriptions>

        <div class="cookie-value-section">
          <h4>Cookie值</h4>
          <a-textarea :model-value="selectedCookie?.cookiePreview" readonly :rows="8" class="cookie-textarea" />
          <div class="cookie-actions">
            <a-button @click="copyCookie">复制Cookie</a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { cookieAPI } from '@/services/api';
import { IconPlus } from '@arco-design/web-vue/es/icon';

// 表格列定义
const columns = [
  { title: '平台', dataIndex: 'platform', slotName: 'platform', fixed: 'left' },
  { title: '账号', dataIndex: 'accountName' },
  { title: '状态', dataIndex: 'status', slotName: 'status' },
  { title: 'Cookie值', dataIndex: 'cookiePreview', slotName: 'cookiePreview' },
  { title: '过期时间', dataIndex: 'expiresAt', slotName: 'expiresAt' },
  { title: '最后验证', dataIndex: 'lastValidatedAt' },
  { title: '优先级', dataIndex: 'priority' },
  { title: '操作', slotName: 'actions', width: 160, fixed: 'right' }
];

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const modalVisible = ref(false);
const detailModalVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();
const selectedCookie = ref(null);

// 搜索表单
const searchForm = reactive({
  platform: '',
  status: '',
  account: ''
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: true
});

// 表单数据
const form = reactive({
  id: '',
  platform: '',
  account: '',
  cookieValue: '',
  expiresAt: '',
  priority: 50,
  remark: ''
});

// 表单验证规则
const rules = {
  platform: [{ required: true, message: '请选择平台' }],
  account: [{ required: true, message: '请输入账号' }],
  cookieValue: [{ required: true, message: '请输入Cookie值' }]
};

// 获取平台颜色和名称
const getPlatformColor = platform => {
  const colorMap = {
    douyin: 'red',
    xiaohongshu: 'pink',
    weibo: 'orange',
    kuaishou: 'blue'
  };
  return colorMap[platform] || 'gray';
};

const getPlatformName = platform => {
  const nameMap = {
    douyin: '抖音',
    xiaohongshu: '小红书',
    weibo: '微博',
    kuaishou: '快手'
  };
  return nameMap[platform] || platform;
};

// 获取状态颜色和文本
const getStatusColor = status => {
  const colorMap = {
    valid: 'green',
    invalid: 'red',
    expired: 'orange',
    active: 'green'
  };
  return colorMap[status] || 'gray';
};

const getStatusText = status => {
  const textMap = {
    valid: '有效',
    invalid: '无效',
    expired: '已过期'
  };
  return textMap[status] || status;
};

// 获取Cookie预览
const getCookiePreview = cookieValue => {
  if (!cookieValue) return '';
  return cookieValue.length > 50 ? cookieValue.substring(0, 50) + '...' : cookieValue;
};

// 检查是否过期
const isExpired = expiresAt => {
  if (!expiresAt) return false;
  return new Date(expiresAt) < new Date();
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...searchForm
    };

    const response = await cookieAPI.getList(params);
    if (response.success) {
      tableData.value = response.data || response.data.list;
      pagination.total = response.pagination.total;
    }
  } catch (error) {
    console.error('Load data error:', error);
    Message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索和重置
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const resetSearch = () => {
  Object.assign(searchForm, {
    platform: '',
    status: '',
    account: ''
  });
  handleSearch();
};

// 分页处理
const handlePageChange = page => {
  pagination.current = page;
  loadData();
};

const handlePageSizeChange = pageSize => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  loadData();
};

// Cookie操作
const validateCookie = async id => {
  try {
    const response = await cookieAPI.validate(id);
    if (response.success) {
      Message.success('验证成功');
      loadData();
    }
  } catch (error) {
    Message.error('验证失败');
    console.error('Validate error:', error);
  }
};

const deleteCookie = async id => {
  try {
    const response = await cookieAPI.delete(id);
    if (response.success) {
      Message.success('删除成功');
      loadData();
    }
  } catch (error) {
    Message.error('删除失败');
    console.error('Delete error:', error);
  }
};

// 弹窗操作
const showCreateModal = () => {
  isEdit.value = false;
  resetForm();
  modalVisible.value = true;
};

const editCookie = async record => {
  try {
    isEdit.value = true;

    // 先获取完整的Cookie详情数据
    const response = await cookieAPI.getById(record.id);
    if (response.success) {
      const cookieData = response.data;

      // 正确映射后端字段到前端表单字段
      Object.assign(form, {
        id: cookieData.id,
        platform: cookieData.platform,
        account: cookieData.accountName, // 后端accountName -> 前端account
        cookieValue: cookieData.cookieValue || cookieData.cookieData || '', // 支持多种字段名
        expiresAt: cookieData.expiresAt || '',
        priority: cookieData.priority || 50,
        remark: cookieData.notes || '' // 后端notes -> 前端remark
      });

      modalVisible.value = true;
      console.log('编辑数据回显:', form);
    } else {
      Message.error('获取Cookie详情失败');
    }
  } catch (error) {
    console.error('编辑Cookie失败:', error);
    Message.error('获取Cookie详情失败');
  }
};

const showCookieDetail = async record => {
  try {
    // 获取完整的Cookie详情
    const response = await cookieAPI.getById(record.id);
    if (response.success) {
      selectedCookie.value = response.data;
      detailModalVisible.value = true;
    } else {
      Message.error('获取Cookie详情失败');
    }
  } catch (error) {
    console.error('获取Cookie详情失败:', error);
    Message.error('获取Cookie详情失败');
  }
};

const copyCookie = () => {
  const cookieValue = selectedCookie.value?.cookieValue || selectedCookie.value?.cookieData;
  if (cookieValue) {
    navigator.clipboard.writeText(cookieValue);
    Message.success('Cookie已复制到剪贴板');
  } else {
    Message.warning('没有可复制的Cookie数据');
  }
};

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate();
    if (valid) return;

    // 映射前端表单字段到后端API字段
    const submitData = {
      accountName: form.account, // 前端account -> 后端accountName
      platform: form.platform,
      cookieString: form.cookieValue, // 前端cookieValue -> 后端cookieString
      expiresAt: form.expiresAt,
      priority: form.priority,
      notes: form.remark // 前端remark -> 后端notes
    };

    const apiCall = isEdit.value ? cookieAPI.update(form.id, submitData) : cookieAPI.create(submitData);

    const response = await apiCall;
    if (response.success) {
      Message.success(isEdit.value ? '更新成功' : '添加成功');
      modalVisible.value = false;
      loadData();
    }
  } catch (error) {
    Message.error('操作失败');
    console.error('Submit error:', error);
  }
};

const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

const resetForm = () => {
  Object.assign(form, {
    id: '',
    platform: '',
    account: '',
    cookieValue: '',
    expiresAt: '',
    priority: 50,
    remark: ''
  });
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.cookie-view {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 8px 0;
}

.page-description {
  color: #86909c;
  margin: 0;
}

.search-card {
  margin-bottom: 16px;
  border-radius: 8px;
}

.table-card {
  border-radius: 8px;
}

.cookie-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cookie-preview {
  font-family: monospace;
  font-size: 12px;
  color: #86909c;
}

.expired {
  color: #f53f3f;
}

.cookie-detail {
  padding: 16px 0;
}

.cookie-value-section {
  margin-top: 24px;
}

.cookie-value-section h4 {
  margin-bottom: 16px;
  color: #1d2129;
}

.cookie-textarea {
  font-family: monospace;
  font-size: 12px;
}

.cookie-actions {
  margin-top: 16px;
  text-align: right;
}
</style>
