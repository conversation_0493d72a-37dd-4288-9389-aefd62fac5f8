<template>
  <div class="crm-integration-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <icon-link />
          CRM集成管理
        </h1>
        <p class="page-description">钉钉CRM系统数据同步管理，支持客户和协议数据的实时同步与监控</p>
      </div>
      <div class="header-actions">
        <a-button type="outline" @click="refreshData">
          <template #icon>
            <icon-refresh />
          </template>
          刷新数据
        </a-button>
      </div>
    </div>

    <!-- 系统状态卡片 -->
    <div class="status-cards">
      <a-row :gutter="16">
        <!-- 连接状态 -->
        <a-col :span="6">
          <a-card class="status-card">
            <div class="status-item">
              <div class="status-icon" :class="connectionStatus.class">
                <icon-wifi v-if="connectionStatus.connected" />
                <icon-poweroff v-else />
              </div>
              <div class="status-content">
                <div class="status-title">连接状态</div>
                <div class="status-value">{{ connectionStatus.text }}</div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- Token状态 -->
        <a-col :span="6">
          <a-card class="status-card">
            <div class="status-item">
              <div class="status-icon" :class="tokenStatus.class">
                <icon-safe v-if="tokenStatus.valid" />
                <icon-exclamation-circle v-else />
              </div>
              <div class="status-content">
                <div class="status-title">Token状态</div>
                <div class="status-value">{{ tokenStatus.text }}</div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 客户总数 -->
        <a-col :span="6">
          <a-card class="status-card">
            <div class="status-item">
              <div class="status-icon success">
                <icon-user-group />
              </div>
              <div class="status-content">
                <div class="status-title">客户总数</div>
                <div class="status-value">{{ formatNumber(systemStatus.customerCount) }}</div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 请求队列 -->
        <a-col :span="6">
          <a-card class="status-card">
            <div class="status-item">
              <div class="status-icon" :class="queueStatus.class">
                <icon-clock-circle />
              </div>
              <div class="status-content">
                <div class="status-title">请求队列</div>
                <div class="status-value">{{ systemStatus.queueLength || 0 }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-content">
      <!-- 功能标签页 -->
      <a-tabs v-model:active-key="activeTab" type="card" class="main-tabs">
        <!-- 数据同步标签页 -->
        <a-tab-pane key="sync" title="数据同步">
          <a-row :gutter="24">
            <!-- 左侧：数据同步控制面板 -->
            <a-col :span="16">
              <CrmSyncControlPanel
                :connection-status="connectionStatus"
                @sync-start="handleSyncStart"
                @sync-pause="handleSyncPause"
                @sync-stop="handleSyncStop"
                @sync-resume="handleSyncResume"
                @config-change="handleConfigChange"
                ref="syncControlPanelRef"
              />

              <!-- 实时监控组件 -->
              <CrmSyncMonitor
                v-if="syncProgress.visible"
                :sync-data="syncProgress"
                :is-active="syncLoading"
                @log-clear="handleLogClear"
                @log-export="handleLogExport"
                @data-refresh="handleMonitorRefresh"
                ref="syncMonitorRef"
              />
            </a-col>

            <!-- 右侧：系统信息和配置 -->
            <a-col :span="8">
              <!-- 系统配置信息 -->
              <a-card title="系统配置" class="config-panel">
                <template #extra>
                  <a-button type="text" size="small" @click="testConnection" :loading="testLoading">
                    <template #icon>
                      <icon-link />
                    </template>
                    测试连接
                  </a-button>
                </template>

                <div class="config-item" v-for="(value, key) in systemConfig" :key="key">
                  <div class="config-label">{{ getConfigLabel(key) }}</div>
                  <div class="config-value">{{ formatConfigValue(key, value) }}</div>
                </div>
              </a-card>

              <!-- Token信息 -->
              <a-card title="Token信息" class="token-panel">
                <template #extra>
                  <a-button type="text" size="small" @click="refreshToken" :loading="refreshTokenLoading">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    刷新Token
                  </a-button>
                </template>

                <div class="token-info">
                  <div class="token-item">
                    <div class="token-label">Token状态</div>
                    <div class="token-value">
                      <a-tag :color="tokenStatus.valid ? 'green' : 'red'">
                        {{ tokenStatus.text }}
                      </a-tag>
                    </div>
                  </div>
                  <div class="token-item" v-if="systemStatus.lastTokenRefresh">
                    <div class="token-label">上次刷新</div>
                    <div class="token-value">{{ formatDateTime(systemStatus.lastTokenRefresh) }}</div>
                  </div>
                  <div class="token-item" v-if="systemStatus.nextTokenRefresh">
                    <div class="token-label">下次刷新</div>
                    <div class="token-value">{{ formatDateTime(systemStatus.nextTokenRefresh) }}</div>
                  </div>
                </div>
              </a-card>

              <!-- 快速操作 -->
              <a-card title="快速操作" class="quick-actions">
                <a-space direction="vertical" fill>
                  <a-button type="outline" long @click="viewCustomerData">
                    <template #icon>
                      <icon-user-group />
                    </template>
                    查看客户数据
                  </a-button>
                  <a-button type="outline" long @click="viewSyncHistory">
                    <template #icon>
                      <icon-history />
                    </template>
                    查看同步历史
                  </a-button>
                  <a-button type="outline" long @click="downloadLogs">
                    <template #icon>
                      <icon-download />
                    </template>
                    下载错误日志
                  </a-button>
                  <a-button type="outline" long @click="openApiDocs">
                    <template #icon>
                      <icon-book />
                    </template>
                    API文档
                  </a-button>
                </a-space>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- 任务管理标签页 -->
        <a-tab-pane key="tasks" title="任务管理">
          <CrmTaskManager />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import { crmIntegrationAPI } from '@/services/api';
import CrmSyncControlPanel from '@/components/CrmSyncControlPanel.vue';
import CrmSyncMonitor from '@/components/CrmSyncMonitor.vue';
import CrmTaskManager from '@/components/CrmTaskManager.vue';
import {
  IconLink,
  IconRefresh,
  IconWifi,
  IconSafe,
  IconExclamationCircle,
  IconUserGroup,
  IconClockCircle,
  IconHistory,
  IconPlayArrow,
  IconPause,
  IconThunderbolt,
  IconDownload,
  IconBook,
  IconDelete,
  IconPoweroff
} from '@arco-design/web-vue/es/icon';

// 组件引用
const syncControlPanelRef = ref(null);
const syncMonitorRef = ref(null);

// 响应式数据
const loading = ref(false);
const testLoading = ref(false);
const refreshTokenLoading = ref(false);
const syncLoading = ref(false);
const showSyncHistory = ref(false);
const activeTab = ref('sync');

// 系统状态
const systemStatus = reactive({
  tokenValid: false,
  customerCount: 0,
  lastTokenRefresh: null,
  nextTokenRefresh: null,
  queueLength: 0,
  isProcessingQueue: false
});

// 系统配置
const systemConfig = reactive({});

// 当前同步配置（由子组件管理）
const currentSyncConfig = ref({});

// 同步进度
const syncProgress = reactive({
  visible: false,
  percentage: 0,
  success: 0,
  failed: 0,
  duration: 0,
  status: 'normal'
});

// 同步日志
const syncLogs = ref([]);

// 计算属性
const connectionStatus = computed(() => {
  // 处理 tokenValid 为 null、true、false 的情况
  const isConnected = systemStatus.tokenValid === true;
  const hasTokenInfo = systemStatus.tokenValid !== null;

  return {
    connected: isConnected,
    text: isConnected ? '已连接' : hasTokenInfo ? '未连接' : '检测中',
    class: isConnected ? 'success' : hasTokenInfo ? 'error' : 'warning'
  };
});

const tokenStatus = computed(() => {
  const isValid = systemStatus.tokenValid === true;
  const hasTokenInfo = systemStatus.tokenValid !== null;

  return {
    valid: isValid,
    text: isValid ? '有效' : hasTokenInfo ? '无效' : '检测中',
    class: isValid ? 'success' : hasTokenInfo ? 'error' : 'warning'
  };
});

const queueStatus = computed(() => {
  const length = systemStatus.queueLength || 0;
  return {
    class: length > 0 ? 'warning' : 'success'
  };
});

// 方法
const refreshData = async () => {
  loading.value = true;
  try {
    await Promise.all([loadSystemStatus(), loadSystemConfig()]);
  } catch (error) {
    console.error('刷新数据失败:', error);
    Message.error('刷新数据失败，请检查后端服务');
  } finally {
    loading.value = false;
  }
};

const loadSystemStatus = async () => {
  try {
    const response = await crmIntegrationAPI.getSystemStatus();
    if (response.success) {
      // 处理后端返回的数据，确保 tokenValid 是布尔值
      const statusData = { ...response.data };

      // 如果 tokenValid 是 null，根据其他信息判断
      if (statusData.tokenValid === null) {
        // 如果有 lastTokenRefresh 说明之前有过有效的token
        statusData.tokenValid = !!statusData.lastTokenRefresh;
      }

      Object.assign(systemStatus, statusData);
    }
  } catch (error) {
    console.error('获取系统状态失败:', error);
    throw error;
  }
};

const loadSystemConfig = async () => {
  try {
    const response = await crmIntegrationAPI.getConfig();
    if (response.success) {
      Object.assign(systemConfig, response.data);
    }
  } catch (error) {
    console.error('获取系统配置失败:', error);
  }
};

const testConnection = async () => {
  testLoading.value = true;
  try {
    const response = await crmIntegrationAPI.testConnection();
    if (response.success) {
      Message.success('CRM连接测试成功');
      // 连接成功时，明确设置 tokenValid 为 true
      systemStatus.tokenValid = true;
      // 然后重新加载系统状态
      await loadSystemStatus();
    } else {
      Message.error(`连接测试失败: ${response.message}`);
      // 连接失败时，设置 tokenValid 为 false
      systemStatus.tokenValid = false;
    }
  } catch (error) {
    console.error('连接测试失败:', error);
    Message.error('连接测试失败，请检查网络和配置');
    // 连接异常时，设置 tokenValid 为 false
    systemStatus.tokenValid = false;
  } finally {
    testLoading.value = false;
  }
};

const refreshToken = async () => {
  refreshTokenLoading.value = true;
  try {
    const response = await crmIntegrationAPI.refreshToken();
    if (response.success) {
      Message.success('Token刷新成功');
      await loadSystemStatus();
    } else {
      Message.error(`Token刷新失败: ${response.message}`);
    }
  } catch (error) {
    console.error('Token刷新失败:', error);
    Message.error('Token刷新失败');
  } finally {
    refreshTokenLoading.value = false;
  }
};

// 新的同步控制面板事件处理方法
const handleSyncStart = async config => {
  if (!connectionStatus.value.connected) {
    Message.error('请先测试CRM连接');
    return;
  }

  syncLoading.value = true;
  syncProgress.visible = true;
  syncProgress.percentage = 0;
  syncProgress.success = 0;
  syncProgress.failed = 0;
  syncProgress.duration = 0;
  syncProgress.status = 'normal';
  syncLogs.value = [];

  const startTime = Date.now();

  try {
    addLog('info', `开始${getSyncModeText(config.mode)}...`);

    let response;
    const params = { ...config };

    switch (config.mode) {
      case 'customers':
        response = await crmIntegrationAPI.syncCustomerData(params);
        break;
      case 'complete':
        response = await crmIntegrationAPI.syncCompleteData(params);
        break;
      case 'all':
        response = await crmIntegrationAPI.syncAllData(params);
        break;
    }

    if (response.success) {
      const result = response.data.result;
      syncProgress.success = result.customerSync?.success || 0;
      syncProgress.failed = result.customerSync?.failed || 0;
      syncProgress.percentage = 100;
      syncProgress.status = 'success';
      syncProgress.duration = Math.round((Date.now() - startTime) / 1000);

      addLog('success', `同步完成 - 成功: ${syncProgress.success}, 失败: ${syncProgress.failed}`);
      Message.success('数据同步完成');

      // 更新同步控制面板状态
      if (syncControlPanelRef.value) {
        syncControlPanelRef.value.updateSyncStatus({
          isRunning: false,
          isPaused: false,
          processed: syncProgress.success + syncProgress.failed,
          success: syncProgress.success,
          failed: syncProgress.failed,
          duration: syncProgress.duration,
          statusText: '同步完成',
          alertType: 'success'
        });
      }

      // 刷新系统状态
      await loadSystemStatus();
    } else {
      throw new Error(response.message);
    }
  } catch (error) {
    console.error('同步失败:', error);
    syncProgress.status = 'exception';
    addLog('error', `同步失败: ${error.message}`);
    Message.error(`同步失败: ${error.message}`);

    // 更新同步控制面板状态
    if (syncControlPanelRef.value) {
      syncControlPanelRef.value.updateSyncStatus({
        isRunning: false,
        isPaused: false,
        statusText: '同步失败',
        alertType: 'error'
      });
    }
  } finally {
    syncLoading.value = false;
  }
};

const handleSyncPause = () => {
  syncLoading.value = false;
  syncProgress.status = 'exception';
  addLog('warning', '同步已暂停');
  Message.info('同步已暂停');
};

const handleSyncStop = () => {
  syncLoading.value = false;
  syncProgress.status = 'exception';
  addLog('warning', '同步已停止');
  Message.info('同步已停止');

  // 隐藏进度面板
  setTimeout(() => {
    syncProgress.visible = false;
  }, 3000);
};

const handleSyncResume = () => {
  syncLoading.value = true;
  syncProgress.status = 'normal';
  addLog('info', '同步已恢复');
  Message.info('同步已恢复');
};

const handleConfigChange = config => {
  currentSyncConfig.value = config;
};

const getSyncModeText = mode => {
  const modes = {
    customers: '客户数据同步',
    complete: '完整数据同步',
    all: '全量数据同步'
  };
  return modes[mode] || '数据同步';
};

const addLog = (type, message) => {
  const logEntry = {
    type,
    message,
    time: new Date()
  };

  syncLogs.value.push(logEntry);

  // 同步到监控组件
  if (syncMonitorRef.value) {
    syncMonitorRef.value.addLog(type, message);
  }

  // 限制日志数量
  if (syncLogs.value.length > 100) {
    syncLogs.value = syncLogs.value.slice(-100);
  }
};

const clearLogs = () => {
  syncLogs.value = [];
  Message.success('日志已清空');
};

// 监控组件事件处理
const handleLogClear = () => {
  clearLogs();
};

const handleLogExport = () => {
  // 监控组件已处理导出逻辑
};

const handleMonitorRefresh = () => {
  // 刷新监控数据
  if (syncMonitorRef.value) {
    const monitorData = {
      status: syncLoading.value ? 'running' : syncProgress.status === 'success' ? 'completed' : 'idle',
      totalProgress: syncProgress.percentage,
      processingRate: Math.round(Math.random() * 100), // 模拟处理速度
      estimatedTime: syncLoading.value ? '约5分钟' : '--',
      customerSync: {
        current: syncProgress.success,
        total: syncProgress.success + syncProgress.failed + 50, // 模拟总数
        percentage: syncProgress.percentage,
        status: syncProgress.status
      },
      agreementSync: {
        current: Math.round(syncProgress.success * 0.3), // 模拟协议数据
        total: Math.round((syncProgress.success + syncProgress.failed + 50) * 0.3),
        percentage: Math.round(syncProgress.percentage * 0.8),
        status: syncProgress.status
      },
      validation: {
        current: Math.round(syncProgress.success * 0.9), // 模拟验证数据
        total: syncProgress.success + syncProgress.failed + 50,
        percentage: Math.round(syncProgress.percentage * 0.95),
        status: syncProgress.status
      }
    };

    syncMonitorRef.value.updateMonitorData(monitorData);
  }
};

// 工具函数
const formatNumber = num => {
  if (num === null || num === undefined) return '0';
  return num.toLocaleString();
};

const formatTime = time => {
  return new Date(time).toLocaleTimeString();
};

const formatDateTime = dateTime => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString();
};

const getConfigLabel = key => {
  const labels = {
    proxyUrl: '代理地址',
    corpId: '企业ID',
    appId: '应用ID',
    appSecret: '应用密钥',
    timeout: '超时时间',
    retryAttempts: '重试次数',
    retryDelay: '重试延迟'
  };
  return labels[key] || key;
};

const formatConfigValue = (key, value) => {
  if (key === 'timeout' || key === 'retryDelay') {
    return `${value}ms`;
  }
  if (key === 'appSecret') {
    return value; // 已经在后端脱敏处理
  }
  return value;
};

const viewCustomerData = () => {
  // 这里可以打开一个模态框显示客户数据
  Modal.info({
    title: '客户数据查看',
    content: '此功能将在后续版本中实现，可以查看从CRM同步的客户数据详情。',
    width: 500
  });
};

const viewSyncHistory = () => {
  // 这里可以打开同步历史页面
  Modal.info({
    title: '同步历史',
    content: '此功能将在后续版本中实现，可以查看历史同步记录和详细日志。',
    width: 500
  });
};

const downloadLogs = () => {
  // 下载错误日志
  if (syncLogs.value.length === 0) {
    Message.info('暂无日志可下载');
    return;
  }

  const logs = syncLogs.value
    .map(log => `[${formatDateTime(log.time)}] ${log.type.toUpperCase()}: ${log.message}`)
    .join('\n');

  const blob = new Blob([logs], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `crm-sync-logs-${new Date().toISOString().split('T')[0]}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  Message.success('日志下载成功');
};

const openApiDocs = () => {
  window.open('/api-docs', '_blank');
};

// 生命周期
onMounted(() => {
  refreshData();
});
</script>

<style lang="less" scoped>
.crm-integration-view {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #1d2129;

        .arco-icon {
          font-size: 28px;
          color: #165dff;
        }
      }

      .page-description {
        margin: 0;
        color: #86909c;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .header-actions {
      .arco-btn {
        height: 40px;
        padding: 0 20px;
      }
    }
  }

  .status-cards {
    margin-bottom: 24px;

    .status-card {
      height: 100%;

      :deep(.arco-card-body) {
        padding: 20px;
      }

      .status-item {
        display: flex;
        align-items: center;
        gap: 16px;

        .status-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;

          &.success {
            background: #f6ffed;
            color: #52c41a;
          }

          &.error {
            background: #fff2f0;
            color: #ff4d4f;
          }

          &.warning {
            background: #fffbe6;
            color: #faad14;
          }
        }

        .status-content {
          flex: 1;

          .status-title {
            font-size: 14px;
            color: #86909c;
            margin-bottom: 4px;
          }

          .status-value {
            font-size: 20px;
            font-weight: 600;
            color: #1d2129;
          }
        }
      }
    }
  }

  .main-content {
    .main-tabs {
      :deep(.arco-tabs-nav) {
        margin-bottom: 24px;

        .arco-tabs-tab {
          padding: 12px 24px;
          font-size: 16px;
          font-weight: 500;

          &.arco-tabs-tab-active {
            background: linear-gradient(135deg, #165dff 0%, #246fff 100%);
            color: white;
            border-radius: 8px 8px 0 0;
          }
        }
      }

      :deep(.arco-tabs-content) {
        padding: 0;
      }
    }

    .sync-panel {
      margin-bottom: 24px;

      :deep(.arco-card-header) {
        border-bottom: 1px solid #f2f3f5;
        padding: 20px 24px;

        .arco-card-header-title {
          font-size: 16px;
          font-weight: 600;
        }
      }

      :deep(.arco-card-body) {
        padding: 24px;
      }

      .sync-modes {
        margin-bottom: 32px;

        h3 {
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1d2129;
        }

        :deep(.arco-radio-group) {
          .arco-radio-button {
            height: 40px;
            line-height: 38px;
            padding: 0 20px;
          }
        }
      }

      .sync-config {
        margin-bottom: 32px;

        h3 {
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1d2129;
        }

        :deep(.arco-form-item) {
          margin-bottom: 16px;

          .arco-form-item-label {
            font-weight: 500;
            color: #4e5969;
          }
        }
      }

      .sync-controls {
        margin-bottom: 32px;
        padding: 24px;
        background: #f7f8fa;
        border-radius: 8px;
        text-align: center;
      }

      .sync-progress {
        margin-top: 32px;
        padding: 24px;
        background: #f7f8fa;
        border-radius: 8px;

        h3 {
          margin: 0 0 20px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1d2129;
        }

        .progress-info {
          margin-bottom: 24px;

          .progress-stats {
            display: flex;
            gap: 32px;
            margin-bottom: 16px;

            :deep(.arco-statistic) {
              text-align: center;

              .arco-statistic-title {
                font-size: 14px;
                color: #86909c;
              }

              .arco-statistic-content {
                font-size: 24px;
                font-weight: 600;
              }
            }
          }
        }

        .sync-logs {
          h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #1d2129;
          }

          .log-container {
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border-radius: 6px;
            padding: 12px;

            .log-item {
              display: flex;
              gap: 12px;
              padding: 4px 0;
              font-size: 12px;
              line-height: 1.5;

              .log-time {
                color: #86909c;
                white-space: nowrap;
              }

              .log-message {
                flex: 1;
              }

              &.success .log-message {
                color: #52c41a;
              }

              &.error .log-message {
                color: #ff4d4f;
              }

              &.warning .log-message {
                color: #faad14;
              }

              &.info .log-message {
                color: #165dff;
              }
            }
          }
        }
      }
    }

    .config-panel,
    .token-panel,
    .quick-actions {
      margin-bottom: 16px;

      :deep(.arco-card-header) {
        border-bottom: 1px solid #f2f3f5;
        padding: 16px 20px;

        .arco-card-header-title {
          font-size: 14px;
          font-weight: 600;
        }
      }

      :deep(.arco-card-body) {
        padding: 20px;
      }
    }

    .config-panel {
      .config-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f2f3f5;

        &:last-child {
          border-bottom: none;
        }

        .config-label {
          font-size: 14px;
          color: #4e5969;
        }

        .config-value {
          font-size: 14px;
          color: #1d2129;
          font-weight: 500;
        }
      }
    }

    .token-panel {
      .token-info {
        .token-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #f2f3f5;

          &:last-child {
            border-bottom: none;
          }

          .token-label {
            font-size: 14px;
            color: #4e5969;
          }

          .token-value {
            font-size: 14px;
            color: #1d2129;
            font-weight: 500;
          }
        }
      }
    }

    .quick-actions {
      :deep(.arco-space-vertical) {
        width: 100%;
      }

      .arco-btn {
        height: 40px;
        justify-content: flex-start;
        gap: 8px;
      }
    }
  }

  .sync-progress-panel {
    margin-top: 24px;

    :deep(.arco-card-header) {
      border-bottom: 1px solid #f2f3f5;
      padding: 20px 24px;

      .arco-card-header-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    :deep(.arco-card-body) {
      padding: 24px;
    }

    .progress-info {
      margin-bottom: 24px;

      .progress-stats {
        display: flex;
        gap: 32px;
        margin-bottom: 16px;

        :deep(.arco-statistic) {
          text-align: center;

          .arco-statistic-title {
            font-size: 14px;
            color: #86909c;
          }

          .arco-statistic-content {
            font-size: 24px;
            font-weight: 600;
          }
        }
      }
    }

    .sync-logs {
      .log-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h4 {
          margin: 0;
          font-size: 14px;
          font-weight: 600;
          color: #1d2129;
        }
      }

      .log-container {
        max-height: 300px;
        overflow-y: auto;
        background: #f7f8fa;
        border-radius: 6px;
        padding: 12px;

        .log-item {
          display: flex;
          gap: 12px;
          padding: 4px 0;
          font-size: 12px;
          line-height: 1.5;

          .log-time {
            color: #86909c;
            white-space: nowrap;
          }

          .log-message {
            flex: 1;
          }

          &.success .log-message {
            color: #52c41a;
          }

          &.error .log-message {
            color: #ff4d4f;
          }

          &.warning .log-message {
            color: #faad14;
          }

          &.info .log-message {
            color: #165dff;
          }
        }

        .log-empty {
          text-align: center;
          color: #86909c;
          font-size: 14px;
          padding: 20px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .crm-integration-view {
    .main-content {
      .arco-col:first-child {
        margin-bottom: 24px;
      }
    }
  }
}

@media (max-width: 768px) {
  .crm-integration-view {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .status-cards {
      .arco-col {
        margin-bottom: 16px;
      }
    }

    .main-content {
      .sync-panel {
        .progress-stats {
          flex-wrap: wrap;
          gap: 16px !important;
        }
      }
    }
  }
}
</style>
