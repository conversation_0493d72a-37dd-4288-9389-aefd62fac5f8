<template>
  <div class="public-influencer-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">达人公海</h2>
        <p class="page-description">查看爬虫获取的达人信息，可收藏到我的达人</p>
      </div>
      <div class="header-right">
        <!-- 爬虫任务状态摘要 -->
        <crawler-task-summary v-if="activeCrawlerTask" :task="activeCrawlerTask" @refresh="refreshCrawlerStatus" />
      </div>
    </div>

    <!-- 爬虫任务状态监控 -->
    <a-card v-if="crawlerStatus.hasRunningTasks" class="crawler-status-card">
      <div class="crawler-status-content">
        <div class="status-icon">
          <a-spin :size="20" />
        </div>
        <div class="status-info">
          <h4>正在获取新的达人信息</h4>
          <p>{{ crawlerStatus.runningTasksCount }} 个爬虫任务正在运行中，新的达人数据将自动更新到公海</p>
        </div>
        <div class="status-actions">
          <a-button type="text" @click="viewCrawlerTasks"> 查看任务详情 </a-button>
        </div>
      </div>
    </a-card>

    <!-- 搜索和筛选 -->
    <a-card class="search-card">
      <a-form :model="searchForm" layout="inline" @submit="handleSearch">
        <a-form-item label="达人昵称">
          <a-input v-model="searchForm.keyword" placeholder="请输入达人昵称" style="width: 160px" allow-clear />
        </a-form-item>
        <a-form-item label="平台">
          <a-select v-model="searchForm.platform" placeholder="请选择平台" style="width: 180px" allow-clear>
            <a-option value="xiaohongshu">小红书</a-option>
            <a-option value="juxingtu">巨量星图</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model="searchForm.status" placeholder="请选择状态" style="width: 120px" allow-clear>
            <a-option value="pending">待处理</a-option>
            <a-option value="processed">已处理</a-option>
            <a-option value="collected">已收藏</a-option>
            <a-option value="failed">失败</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="星图/小红书ID">
          <a-input v-model="searchForm.platformUserId" placeholder="请输入星图ID" style="width: 200px" allow-clear />
        </a-form-item>
        <a-form-item label="爬虫任务ID">
          <a-input-number
            v-model="searchForm.crawlTaskId"
            placeholder="请输入任务ID"
            style="width: 160px"
            :min="1"
            :precision="0"
            allow-clear
          >
            <template #suffix>
              <a-tooltip content="输入爬虫任务ID可以查看该任务获取的所有达人数据">
                <icon-question-circle style="color: #86909c" />
              </a-tooltip>
            </template>
          </a-input-number>
        </a-form-item>
        <a-form-item label="粉丝数范围">
          <a-input-number
            v-model="searchForm.minFollowers"
            placeholder="最小值"
            style="width: 100px; margin-right: 8px"
            :min="0"
          />
          <span style="margin: 0 8px">-</span>
          <a-input-number v-model="searchForm.maxFollowers" placeholder="最大值" style="width: 100px" :min="0" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">搜索</a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">重置</a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 达人公海列表 -->
    <a-card class="table-card">
      <div class="table-action">
        <a-space>
          <a-button type="primary" @click="refreshData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新数据
          </a-button>
          <a-button
            @click="batchImport"
            :disabled="!rowSelection.selectedRowKeys.length || batchCollecting"
            :loading="batchCollecting"
          >
            <template #icon>
              <icon-import />
            </template>
            {{ batchCollecting ? '收藏中...' : `批量收藏 (${rowSelection.selectedRowKeys.length})` }}
          </a-button>
          <a-button @click="exportToExcel" :loading="exporting" type="outline">
            <template #icon>
              <icon-download />
            </template>
            {{ exporting ? '导出中...' : '导出Excel' }}
          </a-button>
        </a-space>
      </div>
      <a-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        class="layout-auto"
        :border="false"
        :scroll="{ x: 'max-content' }"
        :row-selection="rowSelection"
        row-key="id"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @selection-change="selectionChange"
      >
        <!-- 头像列 -->
        <template #avatar="{ record }">
          <a-avatar :size="40">
            <img v-if="record.avatarUrl" :src="record.avatarUrl" alt="avatar" @error="handleImageError" />
            <span v-else>{{ record.nickname?.charAt(0) }}</span>
          </a-avatar>
        </template>

        <!-- 平台列 -->
        <template #platform="{ record }">
          <a-tag :color="getPlatformColor(record.platform)">
            {{ getPlatformName(record.platform) }}
          </a-tag>
        </template>

        <!-- 爬虫任务列 -->
        <template #crawlTask="{ record }">
          <div v-if="record.crawlTask" class="crawl-task-info">
            <a-tooltip :content="`任务名称: ${record.crawlTask.taskName || '未命名'}`">
              <a-tag color="blue" size="small"> ID: {{ record.crawlTask.id }} </a-tag>
            </a-tooltip>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>

        <!-- 粉丝数列 -->
        <template #followersCount="{ record }">
          <span>{{ formatNumber(record.followersCount) }}</span>
        </template>

        <!-- 达人报价列 -->
        <template #price="{ record }">
          <span>{{ formatPrice(record.authorExtInfo?.price_20_60) }}</span>
        </template>

        <!-- 达人标签列 -->
        <template #tags="{ record }">
          <div class="tags-container">
            <template v-if="record.influencerTags || record.authorExtInfo?.tags_relation">
              <div
                v-for="(subTags, mainTag) in parseTagsRelation(
                  record.influencerTags || record.authorExtInfo?.tags_relation
                )"
                :key="mainTag"
                class="tag-group"
              >
                <a-tag size="small" color="blue" style="margin-right: 4px; margin-bottom: 2px">
                  {{ mainTag }}
                </a-tag>
                <a-tag
                  v-for="subTag in subTags"
                  :key="subTag"
                  size="small"
                  color="green"
                  style="margin-right: 4px; margin-bottom: 2px"
                >
                  {{ subTag }}
                </a-tag>
              </div>
            </template>
          </div>
        </template>

        <!-- 内容主题列 -->
        <template #contentTheme="{ record }">
          <div
            class="content-theme-container"
            v-if="formatContentTheme(record.contentTheme || record.authorExtInfo?.content_theme_labels_180d).length"
          >
            <a-tag
              v-for="theme in formatContentTheme(
                record.contentTheme || record.authorExtInfo?.content_theme_labels_180d
              )"
              :key="theme"
              size="small"
              color="blue"
              style="margin-right: 4px; margin-bottom: 2px"
            >
              {{ theme }}
            </a-tag>
          </div>
          <span v-else>-</span>
        </template>

        <!-- 播放量中位数列 -->
        <template #playMid="{ record }">
          <span v-if="record.playMid">{{ formatNumber(record.playMid) }}</span>
          <span v-else class="text-gray-400">-</span>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusName(record.status) }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <a-space>
            <a-link type="text" size="small" @click="viewInfluencerPage(record)"> 达人主页 </a-link>
            <!-- <a-link type="text" size="small" @click="viewDetail(record)"> 查看 </a-link> -->
            <a-button
              type="text"
              size="small"
              @click="collectToMyInfluencers(record)"
              :disabled="record.status === 'collected' || collectingIds.includes(record.id)"
              :loading="collectingIds.includes(record.id)"
            >
              {{ getCollectButtonText(record) }}
            </a-button>
            <a-button type="text" size="small" @click="showReportForm(record)" status="success"> 提报 </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 详情弹窗 -->
    <a-modal v-model:visible="detailModalVisible" title="达人详情" width="900px" :footer="false">
      <div v-if="currentRecord" class="detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="达人昵称">{{ currentRecord.nickname }}</a-descriptions-item>
          <a-descriptions-item label="平台">{{ getPlatformName(currentRecord.platform) }}</a-descriptions-item>
          <a-descriptions-item label="平台用户ID">{{ currentRecord.platformUserId }}</a-descriptions-item>
          <a-descriptions-item label="粉丝数">{{ formatNumber(currentRecord.followersCount) }}</a-descriptions-item>
          <a-descriptions-item label="城市">{{ currentRecord.city || '未知' }}</a-descriptions-item>
          <a-descriptions-item label="唯一标识">{{ currentRecord.uniqueId || '无' }}</a-descriptions-item>
          <a-descriptions-item label="达人报价">{{
            formatPrice(currentRecord.authorExtInfo?.price_20_60)
          }}</a-descriptions-item>
          <a-descriptions-item label="状态">{{ getStatusName(currentRecord.status) }}</a-descriptions-item>
          <a-descriptions-item label="创建时间" :span="2">{{
            formatDate(currentRecord.createdAt)
          }}</a-descriptions-item>
        </a-descriptions>

        <!-- 扩展信息 -->
        <div v-if="currentRecord.authorExtInfo" class="ext-info-section">
          <h4>扩展信息</h4>
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="达人标签">
              <div class="tags-container">
                <template v-if="currentRecord.influencerTags || currentRecord.authorExtInfo?.tags_relation">
                  <div
                    v-for="(subTags, mainTag) in parseTagsRelation(
                      currentRecord.influencerTags || currentRecord.authorExtInfo?.tags_relation
                    )"
                    :key="mainTag"
                    class="tag-group"
                  >
                    <a-tag size="small" color="blue" style="margin-right: 4px; margin-bottom: 2px">
                      {{ mainTag }}
                    </a-tag>
                    <a-tag
                      v-for="subTag in subTags"
                      :key="subTag"
                      size="small"
                      color="green"
                      style="margin-right: 4px; margin-bottom: 2px"
                    >
                      {{ subTag }}
                    </a-tag>
                  </div>
                </template>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="内容主题">
              <div class="content-theme-container">
                <a-tag
                  v-for="theme in formatContentTheme(
                    currentRecord.contentTheme || currentRecord.authorExtInfo?.content_theme_labels_180d
                  )"
                  :key="theme"
                  size="small"
                  color="blue"
                  style="margin-right: 4px; margin-bottom: 2px"
                >
                  {{ theme }}
                </a-tag>
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 视频统计 -->
        <div v-if="currentRecord.videoStats" class="video-stats-section">
          <h4>视频统计</h4>
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="视频数量">{{ currentRecord.videoStats.videoCount || 0 }}</a-descriptions-item>
            <a-descriptions-item label="平均播放量">{{
              formatNumber(currentRecord.videoStats.averagePlay)
            }}</a-descriptions-item>
            <a-descriptions-item label="总播放量">{{
              formatNumber(currentRecord.videoStats.totalPlay)
            }}</a-descriptions-item>
            <a-descriptions-item label="总点赞数">{{
              formatNumber(currentRecord.videoStats.totalLike)
            }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-modal>

    <!-- 达人提报表单 -->
    <InfluencerReportForm
      v-model:visible="reportFormVisible"
      :influencer-data="reportInfluencerData"
      @success="handleReportSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconRefresh, IconImport, IconDownload, IconQuestionCircle } from '@arco-design/web-vue/es/icon';
import { publicInfluencerAPI, crawlerAPI } from '@/services/api';
import { cloneDeep, uniqBy } from 'lodash';
import CrawlerTaskSummary from '@/components/CrawlerTaskSummary.vue';
import InfluencerReportForm from '@/components/InfluencerReportForm.vue';
import { useRouter } from 'vue-router';
import {
  openInfluencerDetailPage,
  getPlatformName,
  getPlatformColor,
  formatNumber,
  formatPrice,
  formatDate
} from '@/utils/platformUtils';

const router = useRouter();

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const detailModalVisible = ref(false);
const currentRecord = ref(null);

// 提报相关状态
const reportFormVisible = ref(false);
const reportInfluencerData = ref({});

// 收藏状态管理
const collectingIds = ref([]);
const batchCollecting = ref(false);

// 导出状态管理
const exporting = ref(false);

// 爬虫状态监控
const crawlerStatus = reactive({
  hasRunningTasks: false,
  runningTasksCount: 0,
  lastUpdate: null
});
const activeCrawlerTask = ref(null);
const statusCheckInterval = ref(null);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  platform: '',
  status: '',
  platformUserId: '',
  crawlTaskId: null,
  minFollowers: null,
  maxFollowers: null
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  selectedRowKeys: [],
  selectedRows: []
});
const selectionChange = (selectedRowKeys, selectedRows) => {
  rowSelection.selectedRowKeys = selectedRowKeys;
};

// 表格列配置
const columns = [
  {
    title: '头像',
    dataIndex: 'avatarUrl',
    slotName: 'avatar',
    width: 80,
    align: 'center'
  },
  {
    title: '达人昵称',
    dataIndex: 'nickname',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '平台',
    dataIndex: 'platform',
    slotName: 'platform',
    align: 'center'
  },
  {
    title: '爬虫任务',
    dataIndex: 'crawlTask',
    slotName: 'crawlTask',
    align: 'center',
    width: 120
  },
  {
    title: '粉丝数',
    dataIndex: 'followersCount',
    slotName: 'followersCount',
    align: 'right'
  },
  {
    title: '播放量/阅读量中位数',
    dataIndex: 'playMid',
    slotName: 'playMid',
    align: 'right'
  },
  {
    title: '达人报价',
    dataIndex: 'price',
    slotName: 'price',
    align: 'right'
  },
  {
    title: '达人标签',
    dataIndex: 'tags',
    slotName: 'tags',
    ellipsis: true
  },
  {
    title: '内容主题',
    dataIndex: 'contentTheme',
    slotName: 'contentTheme',
    ellipsis: true
  },

  {
    title: '城市',
    dataIndex: 'city',
    ellipsis: true
  },
  {
    title: '平台ID',
    dataIndex: 'platformUserId',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    render: ({ record }) => formatDate(record.createdAt)
  },
  {
    title: '操作',
    slotName: 'actions',
    align: 'center',
    fixed: 'right'
  }
];

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      sortBy: 'updatedAt',
      sortOrder: 'DESC',
      ...searchForm
    };

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await publicInfluencerAPI.getList(params);

    if (response.success) {
      tableData.value = response.data;
      pagination.total = response.pagination.total;
    } else {
      Message.error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取达人公海数据失败:', error);
    Message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  searchForm.crawlTaskId = null;
  searchForm.minFollowers = null;
  searchForm.maxFollowers = null;
  pagination.current = 1;
  fetchData();
};

// 刷新数据
const refreshData = () => {
  fetchData();
};

// 导出Excel
const exportToExcel = async () => {
  try {
    exporting.value = true;

    // 构建导出参数（使用当前的筛选条件）
    const exportParams = {
      ...searchForm
    };

    // 移除空值
    Object.keys(exportParams).forEach(key => {
      if (exportParams[key] === '' || exportParams[key] === null || exportParams[key] === undefined) {
        delete exportParams[key];
      }
    });

    console.log('🔄 开始导出Excel，筛选条件:', exportParams);

    const response = await publicInfluencerAPI.export(exportParams);

    if (response.success) {
      // 创建下载链接并自动下载
      const downloadUrl = response.data.downloadUrl;
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = response.data.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      Message.success(`导出成功！共导出 ${response.data.recordCount} 条记录`);
    } else {
      Message.error(response.message || '导出失败');
    }
  } catch (error) {
    console.error('导出Excel失败:', error);
    Message.error('导出Excel失败');
  } finally {
    exporting.value = false;
  }
};

// 分页变化
const handlePageChange = page => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = pageSize => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

// 查看详情
const viewDetail = record => {
  currentRecord.value = record;
  detailModalVisible.value = true;
};

// 查看达人主页
const viewInfluencerPage = record => {
  // 使用公共工具函数打开达人详情页面
  openInfluencerDetailPage(record);
};

// 显示提报表单
const showReportForm = record => {
  reportInfluencerData.value = record;
  reportFormVisible.value = true;
};

// 提报成功回调
const handleReportSuccess = () => {
  // Message.success('提报成功');
  // 可以选择刷新数据或其他操作
  router.push('/influencer-reports');
};

// 收藏到我的达人
const collectToMyInfluencers = async record => {
  if (record.status === 'collected') {
    Message.warning('该达人已经收藏过了');
    return;
  }

  // 添加到收藏中列表
  collectingIds.value.push(record.id);

  try {
    const response = await publicInfluencerAPI.importToMyInfluencers(record.id);

    // 检查响应结构 - 注意：axios拦截器已经返回了response.data
    console.log('收藏响应:', response);

    if (response && response.success) {
      const nickname = response.data?.nickname || record.nickname || '达人';
      Message.success(`收藏成功: ${nickname}`);
      // 更新状态
      record.status = 'collected';
      if (response.data?.influencerId) {
        record.importedInfluencerId = response.data.influencerId;
      }
    } else {
      const errorMessage = response?.message || '收藏失败';
      Message.error(errorMessage);
    }
  } catch (error) {
    console.error('收藏失败:', error);

    // 处理不同类型的错误
    if (error.response) {
      // HTTP错误响应
      const status = error.response.status;
      const errorData = error.response.data;

      if (status === 409) {
        const message = errorData?.message || '该达人已存在';
        Message.warning(message);
        // 如果是重复收藏，也更新状态
        record.status = 'collected';
        if (errorData?.data?.existingInfluencerId) {
          record.importedInfluencerId = errorData.data.existingInfluencerId;
        }
      } else if (status === 404) {
        Message.error('达人记录不存在');
      } else {
        const message = errorData?.message || `收藏失败 (${status})`;
        Message.error(message);
      }
    } else if (error.request) {
      // 网络错误
      Message.error('网络连接失败，请检查网络');
    } else {
      // 其他错误
      Message.error('收藏失败，请重试');
    }
  } finally {
    // 从收藏中列表移除
    const index = collectingIds.value.indexOf(record.id);
    if (index > -1) {
      collectingIds.value.splice(index, 1);
    }
  }
};

// 批量收藏
const batchImport = async () => {
  if (rowSelection.selectedRowKeys.length === 0) {
    Message.warning('请选择要收藏的达人');
    return;
  }

  // 过滤掉已收藏的记录
  const collectableIds = rowSelection.selectedRowKeys.filter(id => {
    const record = tableData.value.find(item => item.id === id);
    return record && record.status !== 'collected';
  });

  if (collectableIds.length === 0) {
    Message.warning('选中的达人都已经收藏过了');
    return;
  }

  batchCollecting.value = true;

  try {
    const response = await publicInfluencerAPI.batchImport(collectableIds);

    // 检查响应结构 - 注意：axios拦截器已经返回了response.data
    console.log('批量收藏响应:', response);

    if (response && response.success) {
      const data = response.data || {};
      const successCount = data.successCount || 0;
      const failedCount = data.failedCount || 0;
      const skippedCount = data.skippedCount || 0;
      const errors = data.errors || [];
      const imported = data.imported || [];

      // 显示详细结果
      if (successCount > 0) {
        Message.success(`批量收藏完成: 成功 ${successCount} 个，失败 ${failedCount} 个，跳过 ${skippedCount} 个`);
      } else {
        Message.warning(`批量收藏完成: 成功 ${successCount} 个，失败 ${failedCount} 个，跳过 ${skippedCount} 个`);
      }

      // 显示错误详情
      if (errors.length > 0) {
        console.log('收藏错误详情:', errors);
        // 可以选择显示前几个错误给用户
        if (errors.length <= 3) {
          errors.forEach(error => {
            console.warn(`${error.nickname}: ${error.error}`);
          });
        }
      }

      // 更新表格数据状态
      tableData.value.forEach(record => {
        if (collectableIds.includes(record.id)) {
          const collectedItem = imported.find(item => item.publicInfluencerId === record.id);
          if (collectedItem) {
            record.status = 'collected';
            record.importedInfluencerId = collectedItem.influencerId;
          }
        }
      });

      // 清空选择
      rowSelection.selectedRowKeys = [];
      rowSelection.selectedRows = [];

      // 刷新数据
      fetchData();
    } else {
      const errorMessage = response?.message || '批量收藏失败';
      Message.error(errorMessage);
    }
  } catch (error) {
    console.error('批量收藏失败:', error);

    // 处理不同类型的错误
    if (error.response) {
      const status = error.response.status;
      const errorData = error.response.data;
      const message = errorData?.message || `批量收藏失败 (${status})`;
      Message.error(message);
    } else if (error.request) {
      Message.error('网络连接失败，请检查网络');
    } else {
      Message.error('批量收藏失败，请重试');
    }
  } finally {
    batchCollecting.value = false;
  }
};

// 工具函数

const formatTags = tagsData => {
  if (!tagsData) return [];
  if (Array.isArray(tagsData)) return tagsData.slice(0, 3); // 最多显示3个标签
  if (typeof tagsData === 'string') {
    try {
      const parsed = JSON.parse(tagsData);
      return Array.isArray(parsed) ? parsed.slice(0, 3) : [];
    } catch {
      return [tagsData];
    }
  }
  return [];
};

const formatContentTheme = themeData => {
  if (!themeData) return [];
  if (Array.isArray(themeData)) return themeData.slice(0, 3); // 最多显示3个主题
  if (typeof themeData === 'string') {
    try {
      const parsed = JSON.parse(themeData);
      return Array.isArray(parsed) ? parsed.slice(0, 3) : [];
    } catch {
      return [themeData];
    }
  }
  return [];
};

// 解析标签关系数据
const parseTagsRelation = tagsData => {
  if (!tagsData) return {};

  // 如果是字符串，尝试解析为JSON
  if (typeof tagsData === 'string') {
    try {
      const parsed = JSON.parse(tagsData);
      // 如果解析后是数组（小红书格式），转换为对象格式
      if (Array.isArray(parsed)) {
        return { 标签: parsed };
      }
      return parsed;
    } catch {
      return {};
    }
  }

  // 如果是数组（小红书的influencerTags直接是数组格式）
  if (Array.isArray(tagsData)) {
    return { 标签: tagsData };
  }

  // 如果已经是对象，直接返回
  if (typeof tagsData === 'object' && tagsData !== null) {
    return tagsData;
  }

  return {};
};

const getStatusName = status => {
  const statusMap = {
    pending: '待处理',
    processed: '已处理',
    collected: '已收藏',
    failed: '失败',
    imported: '已收藏'
  };
  return statusMap[status] || status;
};

const getStatusColor = status => {
  const colorMap = {
    pending: 'orange',
    processed: 'blue',
    collected: 'green',
    failed: 'red'
  };
  return colorMap[status] || 'gray';
};

const handleImageError = event => {
  event.target.style.display = 'none';
};

// 获取收藏按钮文本
const getCollectButtonText = record => {
  if (record.status === 'collected') {
    return '已收藏';
  }
  if (collectingIds.value.includes(record.id)) {
    return '收藏中...';
  }
  return '收藏';
};

// 爬虫状态监控
const checkCrawlerStatus = async () => {
  try {
    const response = await crawlerAPI.getStats();
    if (response.success) {
      const runningTasks = response.data.taskStats.find(stat => stat.status === 'running');
      crawlerStatus.hasRunningTasks = runningTasks && runningTasks.count > 0;
      crawlerStatus.runningTasksCount = runningTasks ? runningTasks.count : 0;
      crawlerStatus.lastUpdate = new Date();

      // 如果有运行中的任务，获取最新的任务信息
      if (crawlerStatus.hasRunningTasks) {
        const tasksResponse = await crawlerAPI.getList({
          status: 'running',
          limit: 1,
          page: 1
        });
        if (tasksResponse.success && tasksResponse.data.length > 0) {
          activeCrawlerTask.value = tasksResponse.data[0];
        }
      } else {
        activeCrawlerTask.value = null;
      }
    }
  } catch (error) {
    console.error('获取爬虫状态失败:', error);
  }
};

// 刷新爬虫状态
const refreshCrawlerStatus = () => {
  checkCrawlerStatus();
};

// 查看爬虫任务
const viewCrawlerTasks = () => {
  // 跳转到爬虫任务页面
  router.push('/crawler');
};

// 启动状态检查定时器
const startStatusCheck = () => {
  // 立即检查一次
  checkCrawlerStatus();

  // 每30秒检查一次
  statusCheckInterval.value = setInterval(() => {
    checkCrawlerStatus();
  }, 30000);
};

// 停止状态检查定时器
const stopStatusCheck = () => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value);
    statusCheckInterval.value = null;
  }
};

// 生命周期
onMounted(() => {
  fetchData();
  startStatusCheck();
});

onUnmounted(() => {
  stopStatusCheck();
});

// 注册组件
const components = {
  CrawlerTaskSummary
};
</script>

<style scoped>
.public-influencer-view {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.crawler-status-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #1890ff;
}

.crawler-status-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #f0f9ff;
  border-radius: 50%;
}

.status-info {
  flex: 1;
}

.status-info h4 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.status-info p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.status-actions {
  display: flex;
  align-items: center;
}

.search-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tags-container {
  max-width: 180px;
  overflow: hidden;
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 4px;
}

.content-theme-container {
  max-width: 180px;
  display: flex;
  flex-wrap: wrap;
}

.crawl-task-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.ext-info-section,
.video-stats-section {
  margin-top: 24px;
}

.ext-info-section h4,
.video-stats-section h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

/* 表格样式优化 */
:deep(.arco-table-th) {
  background-color: #f8fafc;
  font-weight: 600;
  color: #374151;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.arco-table-tr:hover .arco-table-td) {
  background-color: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .table-action {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
