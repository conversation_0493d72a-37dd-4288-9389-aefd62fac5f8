<template>
  <div class="login-container">
    <!-- 左侧品牌展示区域 -->
    <div class="brand-section">
      <div class="brand-content">
        <!-- 品牌标识 -->
        <div class="brand-logo">
          <div class="logo-icon">
            <img src="@/assets/img/logo.svg" alt="" />
          </div>
          <h1 class="brand-name">AgentX</h1>
        </div>

        <!-- 品牌描述 -->
        <div class="brand-description">
          <h2 class="brand-title">专业达人内容管理平台</h2>
          <p class="brand-subtitle">量身打造的智能化达人内容管理解决方案</p>
        </div>
      </div>

      <!-- 装饰性背景元素 -->
      <div class="brand-decoration">
        <ParticleCanvas />
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-grid"></div>
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="login-section">
      <div class="login-card">
        <div class="login-header">
          <h3 class="login-title">欢迎回来</h3>
          <p class="login-subtitle">请登录您的账户以继续</p>
        </div>

        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" class="login-form" @submit="handleSubmit">
          <a-form-item field="username" label="用户名" class="form-item">
            <a-input
              v-model="form.username"
              placeholder="请输入用户名"
              size="large"
              class="form-input"
            >
              <template #prefix>
                <IconUser />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item field="password" label="密码" class="form-item">
            <a-input-password
              v-model="form.password"
              placeholder="请输入密码"
              size="large"
              class="form-input"
            >
              <template #prefix>
                <IconLock />
              </template>
            </a-input-password>
          </a-form-item>

          <div class="form-options">
            <a-checkbox>记住我</a-checkbox>
            <a-link class="forgot-link">忘记密码？</a-link>
          </div>

          <a-form-item class="submit-item">
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              long
              :loading="userStore.loading"
              class="login-btn"
            >
              立即登录
            </a-button>
          </a-form-item>
        </a-form>

        <!-- 登录提示 -->
        <div class="login-tips">
          <a-alert type="info" show-icon :closable="false" class="demo-alert">
            <template #icon>
              <IconInfoCircle />
            </template>
            <span class="demo-text">演示账号：admin / admin123456</span>
          </a-alert>
        </div>

        <!-- 底部链接 -->
        <div class="login-footer">
          <span class="footer-text">还没有账户？</span>
          <a-link class="register-link">立即注册</a-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { Message } from '@arco-design/web-vue';
import { 
  IconUser, 
  IconLock, 
  IconInfoCircle 
} from '@arco-design/web-vue/es/icon';
import ParticleCanvas from '@/components/ParticleCanvas.vue';

const router = useRouter();
const userStore = useUserStore();

// 表单引用
const formRef = ref();

// 表单数据
const form = reactive({
  username: 'admin',
  password: 'admin123456'
});

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名' },
    { minLength: 3, message: '用户名至少3个字符' }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { minLength: 6, message: '密码至少6个字符' }
  ]
};

// 提交表单
const handleSubmit = async data => {
  if (!data.errors) {
    try {
      const result = await userStore.login({
        username: form.username,
        password: form.password
      });

      if (result.success) {
        Message.success('登录成功');
        router.push('/');
      } else {
        Message.error(result.message || '登录失败');
      }
    } catch (error) {
      console.error('Login error:', error);
      Message.error('登录失败，请稍后重试');
    }
  }
};
</script>

<style scoped>
/* 主容器 */
.login-container {
  min-height: 100vh;
  display: flex;
  background: #f8fafc;
  position: relative;
}

/* 左侧品牌展示区域 */
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  min-height: 100vh;
  pointer-events: none;
}

.brand-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 500px;
  padding: 40px;
}

/* 品牌标识 */
.brand-logo {
  margin-bottom: 48px;
}

.logo-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.logo-icon svg {
  width: 32px;
  height: 32px;
}

.brand-name {
  font-size: 62px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 品牌描述 */
.brand-description {
  margin-bottom: 48px;
}

.brand-title {
  font-size: 38px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #f1f5f9;
}

.brand-subtitle {
  font-size: 18px;
  line-height: 1.6;
  color: #cbd5e1;
  margin: 0;
}

/* 特色功能展示 */
.feature-highlights {
  display: flex;
  justify-content: center;
  gap: 32px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-item span {
  font-size: 14px;
  color: #e2e8f0;
  font-weight: 500;
}

/* 装饰性背景元素 */
.brand-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background: url('@/assets/img/bg2.webp');
  background-size: cover;
  background-position: center;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(20px);
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation: float 8s ease-in-out infinite;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation: float 8s ease-in-out infinite reverse;
  animation-delay: 2s;
}

.decoration-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

/* 右侧登录区域 */
.login-section {
  flex: 0 0 480px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: white;
}

.login-card {
  width: 100%;
  max-width: 400px;
}

/* 登录表单头部 */
.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-title {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.login-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

/* 表单样式 */
.login-form {
  margin-bottom: 32px;
}

.form-item {
  margin-bottom: 24px;
}

.form-item :deep(.arco-form-item-label) {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-input {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.form-input:hover {
  border-color: #3b82f6;
}

.form-input:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.forgot-link {
  color: #3b82f6;
  font-weight: 500;
  text-decoration: none;
}

.forgot-link:hover {
  color: #1d4ed8;
}

.submit-item {
  margin-bottom: 0;
}

.login-btn {
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.login-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.login-btn:active {
  transform: translateY(0);
}

/* 登录提示 */
.login-tips {
  margin-bottom: 24px;
}

.demo-alert {
  border-radius: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
}

.demo-text {
  color: #0369a1;
  font-weight: 500;
}

/* 底部链接 */
.login-footer {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.footer-text {
  color: #6b7280;
  margin-right: 8px;
}

.register-link {
  color: #3b82f6;
  font-weight: 600;
  text-decoration: none;
}

.register-link:hover {
  color: #1d4ed8;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }

  .brand-section {
    flex: 0 0 auto;
    min-height: 40vh;
  }

  .brand-content {
    padding: 32px 20px;
  }

  .brand-title {
    font-size: 20px;
  }

  .brand-subtitle {
    font-size: 14px;
  }

  .feature-highlights {
    gap: 24px;
  }

  .login-section {
    flex: 1;
    padding: 32px 20px;
  }
}

@media (max-width: 768px) {
  .brand-section {
    min-height: 30vh;
  }

  .brand-name {
    font-size: 24px;
  }

  .login-title {
    font-size: 24px;
  }

  .feature-highlights {
    flex-direction: column;
    gap: 16px;
  }

  .feature-item {
    flex-direction: row;
    gap: 12px;
  }

  .form-options {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}
</style>
