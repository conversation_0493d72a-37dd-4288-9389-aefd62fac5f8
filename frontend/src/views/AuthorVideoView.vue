<template>
  <div class="author-video-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">达人作品库</h2>
      <p class="page-description">查看和管理所有达人的视频作品数据</p>
    </div>

    <!-- 筛选器 -->
    <a-card class="filter-card">
      <a-form :model="filters" layout="inline" class="filter-form">
        <a-form-item label="关键词搜索">
          <a-input
            v-model="filters.keyword"
            placeholder="搜索视频标题、描述、达人昵称"
            style="width: 310px"
            allow-clear
            @press-enter="handleSearch"
          >
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item label="达人昵称">
          <a-input
            v-model="filters.authorNickname"
            placeholder="搜索达人昵称"
            style="width: 180px"
            allow-clear
            @press-enter="handleSearch"
          >
            <template #prefix>
              <icon-user />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item label="星图/小红书ID">
          <a-input
            v-model="filters.platformUserId"
            placeholder="输入星图/小红书ID"
            style="width: 200px"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-form-item>

        <a-form-item label="视频ID">
          <a-input
            v-model="filters.videoId"
            placeholder="输入视频ID"
            style="width: 150px"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-form-item>

        <a-form-item label="平台">
          <a-select
            v-model="filters.platform"
            placeholder="选择平台"
            style="width: 120px"
            allow-clear
            @change="handleSearch"
          >
            <a-option value="xiaohongshu">小红书</a-option>
            <a-option value="juxingtu">巨量星图</a-option>
            <a-option value="douyin">抖音</a-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 作品列表 -->
    <a-card class="table-card">
      <div class="table-action">
        <a-space>
          <span class="total-count">共 {{ pagination.total }} 个作品</span>
        </a-space>
        <a-space>
          <a-select v-model="sortConfig.sortBy" style="width: 120px" @change="handleSortChange">
            <a-option value="updatedAt">更新时间</a-option>
            <a-option value="publishTime">发布时间</a-option>
            <a-option value="playCount">播放量</a-option>
            <a-option value="likeCount">点赞数</a-option>
            <a-option value="createdAt">创建时间</a-option>
          </a-select>
          <a-select v-model="sortConfig.sortOrder" style="width: 80px" @change="handleSortChange">
            <a-option value="DESC">降序</a-option>
            <a-option value="ASC">升序</a-option>
          </a-select>
        </a-space>
      </div>

      <a-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        class="layout-auto"
        :border="false"
        :scroll="{ x: 'max-content' }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <!-- 封面图列 -->
        <template #videoCover="{ record }">
          <div class="video-cover-wrapper">
            <!-- 如果是巨量星图，展示默认封面 -->
            <div v-if="record.platform === 'juxingtu'" @click="openVideoLink(record)" class="no-cover">
              <icon-play-circle />
              <span>点击查看</span>
            </div>
            <a-image
              v-else-if="record.videoCover"
              :src="record.videoCover"
              :width="80"
              :height="60"
              fit="cover"
              :preview="false"
              class="video-cover"
              @error="handleImageError"
              @click="handleXiaohongshuWatch(record)"
            />
            <div v-else class="no-cover">
              <icon-image />
              <span>暂无封面</span>
            </div>
          </div>
        </template>

        <!-- 标题列 -->
        <template #title="{ record }">
          <div class="title-cell">
            <a-link v-if="record.videoUrl" :href="record.videoUrl" target="_blank" class="video-title">
              {{ record.title }}
            </a-link>
            <span v-else class="video-title">{{ record.title }}</span>
            <div v-if="record.description" class="video-description">
              {{ record.description.substring(0, 50) }}{{ record.description.length > 50 ? '...' : '' }}
            </div>
          </div>
        </template>

        <!-- 达人信息列 -->
        <template #author="{ record }">
          <div v-if="record.author" class="author-info">
            <a-avatar :size="32">
              <img
                v-if="record.author.avatarUrl"
                :src="record.author.avatarUrl"
                alt="avatar"
                @error="handleImageError"
              />
              <span v-else>{{ record.author.nickname?.charAt(0) }}</span>
            </a-avatar>
            <div class="author-details">
              <div class="author-name">{{ record.author.nickname }}</div>
              <!-- <div class="author-platform">{{ getPlatformName(record.author.platform) }}</div> -->
              <div class="author-platform">{{ record }}</div>
            </div>
          </div>
          <div v-else-if="record.authorNickname" class="author-info-simple">
            <a-avatar :size="32">
              <span>{{ record.authorNickname?.charAt(0) }}</span>
            </a-avatar>
            <div class="author-details">
              <div class="author-name">{{ record.authorNickname }}</div>
              <div class="author-platform">{{ record.platformUserId }}</div>
            </div>
          </div>
          <div v-else class="no-author">
            <span>{{ record.platformUserId }}</span>
            <div class="platform-tag">{{ getPlatformName(record.platform) }}</div>
          </div>
        </template>

        <!-- 平台列 -->
        <template #platform="{ record }">
          <a-tag :color="getPlatformColor(record.platform)">
            {{ getPlatformName(record.platform) }}
          </a-tag>
        </template>

        <!-- 统计数据列 -->
        <template #stats="{ record }">
          <div class="stats-cell">
            <div class="stat-item">
              <icon-play-arrow />
              <span>{{ formatNumber(record.playCount) }}</span>
            </div>
            <div class="stat-item">
              <icon-heart />
              <span>{{ formatNumber(record.likeCount) }}</span>
            </div>
            <div class="stat-item">
              <icon-star />
              <span>{{ formatNumber(record.collectCount) }}</span>
            </div>
          </div>
        </template>

        <!-- 发布时间列 -->
        <template #publishTime="{ record }">
          <div class="time-cell">
            <div>{{ formatDate(record.publishTime) }}</div>
            <!-- <div class="time-ago">{{ getTimeAgo(record.publishTime) }}</div> -->
          </div>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <a-space>
            <a-button
              v-if="record.platform === 'xiaohongshu'"
              type="text"
              size="small"
              :loading="loadingNotes[record.videoId]"
              @click="handleXiaohongshuWatch(record)"
            >
              观看
            </a-button>
            <a-link v-else-if="record.videoUrl" :href="record.videoUrl" target="_blank" type="text" size="small">
              观看
            </a-link>
            <!-- <a-link type="text" size="small" @click="viewDetail(record)"> 详情 </a-link> -->
          </a-space>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { authorVideoAPI } from '@/services/api';
import { Message } from '@arco-design/web-vue';
import {
  IconSearch,
  IconRefresh,
  IconImage,
  IconPlayArrow,
  IconHeart,
  IconMessage,
  IconPlayCircle,
  IconUser,
  IconStar
} from '@arco-design/web-vue/es/icon';

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const loadingNotes = ref({}); // 用于跟踪每个帖子的加载状态

// 筛选器
const filters = reactive({
  keyword: '',
  authorNickname: '',
  platformUserId: '',
  videoId: '',
  platform: ''
});

// 排序配置
const sortConfig = reactive({
  sortBy: 'updatedAt',
  sortOrder: 'DESC'
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 表格列配置
const columns = [
  {
    title: '封面',
    dataIndex: 'videoCover',
    slotName: 'videoCover',
    align: 'center'
  },
  {
    title: '标题',
    dataIndex: 'title',
    slotName: 'title'
  },
  {
    title: '达人',
    dataIndex: 'author',
    slotName: 'author',
    align: 'left'
  },

  {
    title: '平台',
    dataIndex: 'platform',
    slotName: 'platform',
    align: 'center'
  },
  {
    title: '统计数据',
    dataIndex: 'stats',
    slotName: 'stats',
    align: 'center'
  },
  {
    title: '时长',
    dataIndex: 'duration',
    align: 'center',
    render: ({ record }) =>
      record.duration
        ? `${Math.floor(record.duration / 60)}:${(record.duration % 60).toString().padStart(2, '0')}`
        : '-'
  },
  // 星图/小红书ID
  // {
  //   title: '星图/小红书ID',
  //   dataIndex: 'platformUserId',
  //   align: 'center'
  // },
  {
    title: '发布时间',
    dataIndex: 'publishTime',
    slotName: 'publishTime',
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'actions',
    slotName: 'actions',
    align: 'center',
    fixed: 'right'
  }
];

// 方法
const loadAuthorVideos = async () => {
  try {
    loading.value = true;

    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      sortBy: sortConfig.sortBy,
      sortOrder: sortConfig.sortOrder,
      ...filters
    };

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await authorVideoAPI.getList(params);
    if (response.success) {
      tableData.value = response.data;
      pagination.total = response.pagination.total;
    } else {
      console.error('获取作品列表失败:', response.message);
    }
  } catch (error) {
    console.error('获取作品列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  loadAuthorVideos();
};

// 重置筛选
const handleReset = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = '';
  });
  pagination.current = 1;
  loadAuthorVideos();
};

// 排序变化
const handleSortChange = () => {
  pagination.current = 1;
  loadAuthorVideos();
};

// 分页变化
const handlePageChange = page => {
  pagination.current = page;
  loadAuthorVideos();
};

const handlePageSizeChange = pageSize => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  loadAuthorVideos();
};

// 工具方法
const getPlatformName = platform => {
  const platformMap = {
    xiaohongshu: '小红书',
    juxingtu: '巨量星图',
    douyin: '抖音'
  };
  return platformMap[platform] || platform;
};

const getPlatformColor = platform => {
  const colorMap = {
    xiaohongshu: 'red',
    juxingtu: 'blue',
    douyin: 'purple'
  };
  return colorMap[platform] || 'gray';
};

const formatNumber = num => {
  if (!num) return '0';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};

const formatDate = dateStr => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};

const getTimeAgo = dateStr => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const now = new Date();
  const diff = now - date;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) return '今天';
  if (days === 1) return '昨天';
  if (days < 7) return `${days}天前`;
  if (days < 30) return `${Math.floor(days / 7)}周前`;
  if (days < 365) return `${Math.floor(days / 30)}月前`;
  return `${Math.floor(days / 365)}年前`;
};

const handleImageError = e => {
  // 图片加载失败时的处理
  console.log('图片加载失败:', e);
};

const openVideoLink = record => {
  if (record.videoUrl) {
    window.open(record.videoUrl, '_blank');
  }
};

const viewDetail = record => {
  // 这里可以打开详情弹窗或跳转到详情页
  console.log('查看详情:', record);
};

/**
 * 处理小红书帖子观看按钮点击
 * 调用API获取最新帖子详情并打开链接
 */
const handleXiaohongshuWatch = async record => {
  try {
    // 设置加载状态
    loadingNotes.value[record.videoId] = true;

    // 调用API获取帖子详情
    const response = await authorVideoAPI.getXiaohongshuNoteDetail(record.videoId);

    if (response.success && response.data) {
      // 打开帖子链接
      window.open(response.data.noteLink, '_blank');

      // 如果数据有更新，刷新列表
      if (response.data.updated) {
        loadAuthorVideos();
      }
    } else {
      // 显示错误消息
      Message.error(response.message || '获取帖子详情失败');
    }
  } catch (error) {
    console.error('获取小红书帖子详情失败:', error);
    Message.error('获取帖子详情失败，请稍后重试');
  } finally {
    // 清除加载状态
    loadingNotes.value[record.videoId] = false;
  }
};

// 生命周期
onMounted(() => {
  loadAuthorVideos();
});
</script>

<style scoped>
.author-video-view {
  padding: 0;
}

.page-header {
  margin-bottom: 16px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 16px;
}

.filter-form {
  margin: 0;
}

.filter-form .arco-form-item {
  margin-bottom: 10px;
}

.table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.total-count {
  color: #6b7280;
  font-size: 14px;
}

/* 视频封面样式 */
.video-cover-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.video-cover {
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.video-cover:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.no-cover {
  width: 80px;
  height: 60px;
  background: #f5f5f5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #9ca3af;
  font-size: 12px;
}

.no-cover .arco-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

/* 标题单元格样式 */
.title-cell {
  max-width: 280px;
}

.video-title {
  font-weight: 500;
  color: #1f2937;
  display: block;
  margin-bottom: 4px;
  line-height: 1.4;
}

.video-description {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.3;
}

/* 达人信息样式 */
.author-info,
.author-info-simple {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-details {
  flex: 1;
  min-width: 0;
}

.author-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.author-platform {
  color: #6b7280;
  font-size: 11px;
}

.no-author {
  text-align: center;
}

.no-author span {
  display: block;
  font-size: 12px;
  color: #374151;
  margin-bottom: 2px;
}

.platform-tag {
  font-size: 10px;
  color: #6b7280;
}

/* 统计数据样式 */
.stats-cell {
  display: flex;
  /* flex-direction: column; */
  gap: 4px;
  justify-content: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.stat-item .arco-icon {
  font-size: 14px;
}

/* 时间单元格样式 */
.time-cell {
  text-align: center;
}

.time-cell > div:first-child {
  font-size: 13px;
  color: #374151;
  margin-bottom: 2px;
}

.time-ago {
  font-size: 11px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
    gap: 12px;
  }

  .filter-form .arco-form-item {
    width: 100%;
  }

  .table-action {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
