<template>
  <div class="dictionary-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>字典管理</h1>
      <p>管理系统字典数据，支持CRM数据同步和本地字典维护</p>
    </div>

    <!-- 功能选项卡 -->
    <div class="tabs-container">
      <div class="tabs">
        <button 
          :class="['tab-button', { active: activeTab === 'crm' }]"
          @click="activeTab = 'crm'"
        >
          CRM字典管理
        </button>
        <button 
          :class="['tab-button', { active: activeTab === 'local' }]"
          @click="activeTab = 'local'"
        >
          本地字典管理
        </button>
      </div>
    </div>

    <!-- CRM字典管理 -->
    <div v-if="activeTab === 'crm'" class="tab-content">
      <CrmDictionaryManagement />
    </div>

    <!-- 本地字典管理 -->
    <div v-if="activeTab === 'local'" class="tab-content">
      <LocalDictionaryManagement />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CrmDictionaryManagement from '@/components/CrmDictionaryManagement.vue'
import LocalDictionaryManagement from '@/components/LocalDictionaryManagement.vue'

// 响应式数据
const activeTab = ref('crm')
</script>

<style scoped>
.dictionary-view {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.tabs-container {
  margin-bottom: 20px;
}

.tabs {
  display: flex;
  border-bottom: 2px solid #f0f0f0;
}

.tab-button {
  padding: 12px 24px;
  border: none;
  background: none;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #1890ff;
  background-color: #f6f8fa;
}

.tab-button.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
  background-color: #f6f8fa;
}

.tab-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
</style>
