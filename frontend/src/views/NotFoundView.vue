<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="error-icon">
        <span class="icon-404">404</span>
      </div>
      <h1 class="error-title">页面未找到</h1>
      <p class="error-description">
        抱歉，您访问的页面不存在或已被移除
      </p>
      <div class="error-actions">
        <a-button type="primary" @click="goHome">
          返回首页
        </a-button>
        <a-button @click="goBack" style="margin-left: 16px">
          返回上页
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.not-found-content {
  text-align: center;
  padding: 48px;
}

.error-icon {
  margin-bottom: 32px;
}

.icon-404 {
  font-size: 120px;
  font-weight: bold;
  color: #165dff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 16px 0;
}

.error-description {
  font-size: 16px;
  color: #86909c;
  margin: 0 0 32px 0;
}

.error-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
