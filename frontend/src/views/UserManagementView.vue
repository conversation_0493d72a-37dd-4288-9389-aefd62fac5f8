<template>
  <div class="user-management">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">账号管理</h2>
        <p class="page-description">管理系统用户账号，包括创建、编辑、删除等操作</p>
      </div>
      <div class="header-right">
        <a-button type="primary" @click="showCreateModal">
          <template #icon>
            <icon-plus />
          </template>
          新建账号
        </a-button>
      </div>
    </div>

    <!-- 搜索和筛选栏 -->
    <div class="search-bar">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、邮箱或中文名称"
            allow-clear
            @clear="handleSearch"
            @press-enter="handleSearch"
          >
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select v-model="searchForm.role" placeholder="选择角色" allow-clear @change="handleSearch">
            <a-option value="admin">管理员</a-option>
            <a-option value="user">普通用户</a-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select v-model="searchForm.status" placeholder="选择状态" allow-clear @change="handleSearch">
            <a-option value="active">正常</a-option>
            <a-option value="inactive">禁用</a-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" @click="handleSearch">
            <template #icon>
              <icon-search />
            </template>
            搜索
          </a-button>
        </a-col>
        <a-col :span="4">
          <a-button @click="handleReset">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 用户列表表格 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data="userList"
        :loading="loading"
        :pagination="pagination"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        row-key="id"
      >
        <!-- 用户名列 -->
        <template #username="{ record }">
          <div class="user-info">
            <a-avatar class="user-avatar">
              <img v-if="record.crmUserPicUrl" :src="record.crmUserPicUrl" alt="avatar" @error="handleImageError" />
              <span>
                {{ (record.chineseName || record.username)?.charAt(0).toUpperCase() }}
              </span>
            </a-avatar>
            <div class="user-names">
              <div class="username">{{ record.username }}</div>
              <div v-if="record.chineseName" class="chinese-name">{{ record.chineseName }}</div>
            </div>
          </div>
        </template>

        <!-- 角色列 -->
        <template #role="{ record }">
          <a-tag :color="record.role === 'admin' ? 'red' : 'blue'">
            {{ record.role === 'admin' ? '管理员' : '普通用户' }}
          </a-tag>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="record.status === 'active' ? 'green' : 'gray'">
            {{ record.status === 'active' ? '正常' : '禁用' }}
          </a-tag>
        </template>

        <!-- 最后登录时间列 -->
        <template #lastLoginAt="{ record }">
          <span v-if="record.lastLoginAt">
            {{ formatDateTime(record.lastLoginAt) }}
          </span>
          <span v-else class="text-gray">从未登录</span>
        </template>

        <!-- 创建时间列 -->
        <template #createdAt="{ record }">
          {{ formatDateTime(record.createdAt) }}
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <a-space>
            <a-button type="text" size="small" @click="showEditModal(record)">
              <template #icon>
                <icon-edit />
              </template>
              编辑
            </a-button>
            <a-button type="text" size="small" @click="showResetPasswordModal(record)">
              <template #icon>
                <icon-lock />
              </template>
              重置密码
            </a-button>
            <a-button
              type="text"
              size="small"
              status="danger"
              @click="showDeleteConfirm(record)"
              :disabled="record.id === currentUser?.id"
            >
              <template #icon>
                <icon-delete />
              </template>
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </div>

    <!-- 创建/编辑用户弹窗 -->
    <a-modal
      v-model:visible="userModalVisible"
      :title="isEdit ? '编辑用户' : '新建用户'"
      width="500px"
      @ok="handleUserSubmit"
      @cancel="handleUserCancel"
      :confirm-loading="submitting"
    >
      <a-form ref="userFormRef" :model="userForm" :rules="userFormRules" layout="vertical">
        <a-form-item label="用户名" field="username">
          <a-input
            v-model="userForm.username"
            placeholder="请输入用户名（3-20位字母、数字、下划线）"
            :disabled="isEdit"
          />
        </a-form-item>

        <a-form-item label="姓名" field="chineseName" :rules="[{ required: true, message: '请输入姓名' }]">
          <a-input-group style="width: 100%">
            <a-input v-model="userForm.chineseName" placeholder="请输入姓名" />
            <a-button type="outline" @click="showCrmUserSelector"> 绑定CRM用户 </a-button>
          </a-input-group>
        </a-form-item>

        <!-- CRM用户信息卡片 -->
        <div v-if="userForm.crmUserId" class="crm-user-card">
          <div class="crm-card-header">
            <span class="crm-card-title">已绑定CRM用户</span>
            <a-button type="text" size="small" status="danger" @click="handleUnbindCrmUser"> 解除绑定 </a-button>
          </div>
          <div class="crm-card-content">
            <div class="crm-user-avatar">
              <a-avatar :size="40">
                <img
                  v-if="userForm.crmUserPicUrl"
                  :src="userForm.crmUserPicUrl"
                  alt="CRM用户头像"
                  @error="handleCrmAvatarError"
                />
                <span v-else class="crm-avatar-text">
                  {{ userForm.chineseName?.charAt(0) || '?' }}
                </span>
              </a-avatar>
            </div>
            <div class="crm-user-info">
              <div class="crm-user-name">{{ userForm.chineseName }}</div>
              <div class="crm-user-department">{{ userForm.crmDepartmentName }}</div>
              <div class="crm-user-id">用户ID: {{ userForm.crmUserId }}</div>
            </div>
          </div>
        </div>

        <a-form-item label="邮箱" field="email">
          <a-input v-model="userForm.email" placeholder="请输入邮箱地址" />
        </a-form-item>
        <a-form-item v-if="!isEdit" label="密码" field="password">
          <a-input-password v-model="userForm.password" placeholder="请输入密码（至少8位，包含字母和数字）" />
        </a-form-item>
        <a-form-item label="角色" field="role">
          <a-select v-model="userForm.role" placeholder="请选择用户角色">
            <a-option value="admin">管理员</a-option>
            <a-option value="user">普通用户</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态" field="status">
          <a-select v-model="userForm.status" placeholder="请选择用户状态">
            <a-option value="active">正常</a-option>
            <a-option value="inactive">禁用</a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- CRM用户选择器 -->
    <CrmUserSelector
      v-model:visible="crmUserSelectorVisible"
      :user-name="userForm.chineseName"
      @confirm="handleCrmUserSelected"
      @cancel="handleCrmUserCancel"
    />

    <!-- 重置密码弹窗 -->
    <a-modal
      v-model:visible="passwordModalVisible"
      title="重置密码"
      width="400px"
      @ok="handlePasswordSubmit"
      @cancel="handlePasswordCancel"
      :confirm-loading="submitting"
    >
      <a-form ref="passwordFormRef" :model="passwordForm" :rules="passwordFormRules" layout="vertical">
        <a-form-item label="新密码" field="newPassword">
          <a-input-password v-model="passwordForm.newPassword" placeholder="请输入新密码（至少8位，包含字母和数字）" />
        </a-form-item>
        <a-form-item label="确认密码" field="confirmPassword">
          <a-input-password v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import { useUserStore } from '@/stores/user';
import { userAPI } from '@/services/api';
import { IconPlus, IconSearch, IconRefresh, IconEdit, IconLock, IconDelete } from '@arco-design/web-vue/es/icon';
import CrmUserSelector from '@/components/CrmUserSelector.vue';

const userStore = useUserStore();

// 当前登录用户
const currentUser = computed(() => userStore.user);

// 数据状态
const loading = ref(false);
const submitting = ref(false);
const userList = ref([]);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  role: '',
  status: ''
});

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 表格列定义
const columns = [
  {
    title: '用户名',
    dataIndex: 'username',
    slotName: 'username',
    width: 180
  },
  {
    title: '中文名称',
    dataIndex: 'chineseName',
    width: 120
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 220
  },
  {
    title: '角色',
    dataIndex: 'role',
    slotName: 'role',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 100
  },
  {
    title: '最后登录',
    dataIndex: 'lastLoginAt',
    slotName: 'lastLoginAt',
    width: 180
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    slotName: 'createdAt',
    width: 180
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 200,
    fixed: 'right'
  }
];

// 弹窗状态
const userModalVisible = ref(false);
const passwordModalVisible = ref(false);
const isEdit = ref(false);
const currentEditUser = ref(null);

// 表单引用
const userFormRef = ref();
const passwordFormRef = ref();

// 用户表单
const userForm = reactive({
  username: '',
  email: '',
  chineseName: '',
  password: '',
  role: 'user',
  status: 'active',
  // CRM相关字段
  crmUserId: '',
  crmUserPicUrl: '',
  crmDepartment: '',
  crmDepartmentName: '',
  crmDataId: null
});

// 密码表单
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
});

// CRM用户选择器相关
const crmUserSelectorVisible = ref(false);

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名' },
    { min: 3, max: 20, message: '用户名长度为3-20位' },
    { match: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字、下划线' }
  ],
  email: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式' }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 8, message: '密码至少8位' },
    { match: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/, message: '密码必须至少8位且包含字母和数字' }
  ],
  role: [{ required: true, message: '请选择用户角色' }],
  status: [{ required: true, message: '请选择用户状态' }]
};

const passwordFormRules = {
  newPassword: [
    { required: true, message: '请输入新密码' },
    { min: 8, message: '密码至少8位' },
    { match: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/, message: '密码必须至少8位且包含字母和数字' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' },
    {
      validator: (value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback('两次输入的密码不一致');
        } else {
          callback();
        }
      }
    }
  ]
};

// 工具函数
const formatDateTime = dateString => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...searchForm
    };

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await userAPI.getUsers(params);
    if (response.success) {
      userList.value = response.data;
      pagination.total = response.pagination.total;
    } else {
      Message.error(response.message || '获取用户列表失败');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    Message.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchUsers();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    role: '',
    status: ''
  });
  pagination.current = 1;
  fetchUsers();
};

// 分页处理
const handlePageChange = page => {
  pagination.current = page;
  fetchUsers();
};

const handlePageSizeChange = pageSize => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchUsers();
};

// 显示创建用户弹窗
const showCreateModal = () => {
  isEdit.value = false;
  currentEditUser.value = null;
  Object.assign(userForm, {
    username: '',
    email: '',
    chineseName: '',
    password: '',
    role: 'user',
    status: 'active',
    // 重置CRM相关字段
    crmUserId: '',
    crmUserPicUrl: '',
    crmDepartment: '',
    crmDepartmentName: '',
    crmDataId: null
  });
  userModalVisible.value = true;
};

// 显示编辑用户弹窗
const showEditModal = user => {
  isEdit.value = true;
  currentEditUser.value = user;
  Object.assign(userForm, {
    username: user.username,
    email: user.email,
    chineseName: user.chineseName || '',
    password: '', // 编辑时不显示密码
    role: user.role,
    status: user.status,
    // 填充CRM相关字段
    crmUserId: user.crmUserId || '',
    crmUserPicUrl: user.crmUserPicUrl || '',
    crmDepartment: user.crmDepartment || '',
    crmDepartmentName: user.crmDepartmentName || '',
    crmDataId: user.crmDataId || null
  });
  userModalVisible.value = true;
};

// 用户表单提交
const handleUserSubmit = async () => {
  try {
    const valid = await userFormRef.value.validate();
    if (valid) return;

    submitting.value = true;

    if (isEdit.value) {
      // 编辑用户
      const updateData = {
        username: userForm.username,
        email: userForm.email,
        chineseName: userForm.chineseName,
        role: userForm.role,
        status: userForm.status,
        // 包含CRM相关字段
        crmUserId: userForm.crmUserId,
        crmUserPicUrl: userForm.crmUserPicUrl,
        crmDepartment: userForm.crmDepartment,
        crmDepartmentName: userForm.crmDepartmentName,
        crmDataId: userForm.crmDataId
      };
      const response = await userAPI.updateUser(currentEditUser.value.id, updateData);
      if (response.success) {
        Message.success('用户信息更新成功');
        userModalVisible.value = false;
        fetchUsers();
      } else {
        Message.error(response.message || '更新用户失败');
      }
    } else {
      // 创建用户
      const response = await userAPI.createUser(userForm);
      if (response.success) {
        Message.success('用户创建成功');
        userModalVisible.value = false;
        fetchUsers();
      } else {
        Message.error(response.message || '创建用户失败');
      }
    }
  } catch (error) {
    console.error('用户操作失败:', error);
    Message.error('操作失败');
  } finally {
    submitting.value = false;
  }
};

// 用户表单取消
const handleUserCancel = () => {
  userModalVisible.value = false;
  userFormRef.value?.resetFields();
};

// 显示CRM用户选择器
const showCrmUserSelector = () => {
  if (!userForm.chineseName.trim()) {
    Message.warning('请先输入姓名');
    return;
  }
  crmUserSelectorVisible.value = true;
};

// CRM用户选择确认
const handleCrmUserSelected = async crmUser => {
  try {
    // 填充CRM用户信息到表单
    userForm.crmUserId = crmUser.userId;
    userForm.crmUserPicUrl = crmUser.userPicUrl;
    userForm.crmDepartment = crmUser.department;
    userForm.crmDepartmentName = crmUser.departmentName;
    userForm.crmDataId = crmUser.dataId;

    Message.success(`已选择CRM用户：${crmUser.userName}`);
  } catch (error) {
    console.error('选择CRM用户失败:', error);
    Message.error(error.response.data.message || '选择CRM用户失败');
  }
};

// CRM用户选择取消
const handleCrmUserCancel = () => {
  // 不做任何操作，保持现有表单数据
};

// 解除CRM用户绑定
const handleUnbindCrmUser = () => {
  try {
    // 清空CRM相关字段
    userForm.crmUserId = '';
    userForm.crmUserPicUrl = '';
    userForm.crmDepartment = '';
    userForm.crmDepartmentName = '';
    userForm.crmDataId = null;

    Message.success('已解除CRM用户绑定');
  } catch (error) {
    console.error('解除CRM用户绑定失败:', error);
    Message.error('解除CRM用户绑定失败');
  }
};

// CRM头像加载失败处理
const handleCrmAvatarError = event => {
  // 隐藏失败的图片，显示文字头像
  event.target.style.display = 'none';
};

// 用户列表头像加载失败处理
const handleImageError = event => {
  // 隐藏失败的图片，显示文字头像
  event.target.style.display = 'none';
};

// 显示重置密码弹窗
const showResetPasswordModal = user => {
  currentEditUser.value = user;
  Object.assign(passwordForm, {
    newPassword: '',
    confirmPassword: ''
  });
  passwordModalVisible.value = true;
};

// 密码表单提交
const handlePasswordSubmit = async () => {
  try {
    const valid = await passwordFormRef.value.validate();
    if (valid) return;

    submitting.value = true;

    const response = await userAPI.resetPassword(currentEditUser.value.id, passwordForm.newPassword);
    if (response.success) {
      Message.success('密码重置成功');
      passwordModalVisible.value = false;
    } else {
      Message.error(response.message || '密码重置失败');
    }
  } catch (error) {
    console.error('密码重置失败:', error);
    Message.error(error.response.data.message || '密码重置失败');
  } finally {
    submitting.value = false;
  }
};

// 密码表单取消
const handlePasswordCancel = () => {
  passwordModalVisible.value = false;
  passwordFormRef.value?.resetFields();
};

// 显示删除确认
const showDeleteConfirm = user => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
    okText: '确认删除',
    cancelText: '取消',
    okButtonProps: { status: 'danger' },
    onOk: () => handleDeleteUser(user)
  });
};

// 删除用户
const handleDeleteUser = async user => {
  try {
    const response = await userAPI.deleteUser(user.id);
    if (response.success) {
      Message.success('用户删除成功');
      fetchUsers();
    } else {
      Message.error(response.message || '删除用户失败');
    }
  } catch (error) {
    console.error('删除用户失败:', error);
    Message.error('删除用户失败');
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.user-management {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

.page-description {
  margin: 0;
  color: #86909c;
  font-size: 14px;
}

.header-right {
  flex-shrink: 0;
}

.search-bar {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 6px;
}

.table-container {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  font-size: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  flex-shrink: 0;
}

.user-names {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 500;
  color: #1d2129;
  font-size: 14px;
}

.chinese-name {
  font-size: 12px;
  color: #86909c;
}

.text-gray {
  color: #86909c;
}

/* CRM用户信息卡片样式 */
.crm-user-card {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.crm-user-card:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.crm-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.crm-card-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.crm-card-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.crm-user-avatar {
  flex-shrink: 0;
}

.crm-avatar-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.crm-user-info {
  flex: 1;
  min-width: 0;
}

.crm-user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.crm-user-department {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

.crm-user-id {
  font-size: 11px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .search-bar .arco-row {
    flex-direction: column;
  }

  .search-bar .arco-col {
    width: 100% !important;
    margin-bottom: 8px;
  }
}

/* 表格样式优化 */
:deep(.arco-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #f5f5f5;
}

:deep(.arco-table-tbody .arco-table-tr:hover .arco-table-td) {
  background-color: #f8f9fa;
}

/* 标签样式优化 */
:deep(.arco-tag) {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 按钮样式优化 */
:deep(.arco-btn-text) {
  padding: 4px 8px;
  height: auto;
}

:deep(.arco-btn-text:hover) {
  background-color: rgba(22, 93, 255, 0.08);
}

:deep(.arco-btn-text[status='danger']:hover) {
  background-color: rgba(245, 63, 63, 0.08);
}
</style>
