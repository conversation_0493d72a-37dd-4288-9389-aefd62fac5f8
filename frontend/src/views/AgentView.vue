<template>
  <div class="agent-view">
    <!-- Agent聊天界面 -->
    <div class="agent-container">
      <div class="agent-wrapper">
        <iframe
          ref="agentIframe"
          :src="agentUrl"
          class="agent-iframe"
          allow="microphone"
          frameborder="0"
          @load="onIframeLoad"
          @error="onIframeError"
        ></iframe>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
          <a-spin size="large" tip="正在加载AI助手...">
            <div class="loading-content">
              <icon-robot class="loading-icon" />
              <p class="loading-text">AI助手正在启动中，请稍候...</p>
            </div>
          </a-spin>
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error-overlay">
          <div class="error-content">
            <icon-exclamation-circle class="error-icon" />
            <h3 class="error-title">加载失败</h3>
            <p class="error-message">{{ errorMessage }}</p>
            <a-button type="primary" @click="retryLoad" class="retry-btn">
              <template #icon>
                <icon-refresh />
              </template>
              重试
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用提示 -->
    <div class="tips-section">
      <a-card title="使用提示" class="tips-card">
        <div class="tips-content">
          <div class="tip-item">
            <icon-bulb class="tip-icon" />
            <div class="tip-text"><strong>语音交互：</strong>点击麦克风图标可以使用语音与AI助手对话</div>
          </div>
          <div class="tip-item">
            <icon-message class="tip-icon" />
            <div class="tip-text"><strong>文字聊天：</strong>在输入框中输入问题，AI助手会为您提供专业解答</div>
          </div>
          <div class="tip-item">
            <icon-settings class="tip-icon" />
            <div class="tip-text"><strong>智能建议：</strong>AI助手可以帮助您优化达人管理和内容策略</div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import {
  IconRefresh,
  IconRobot,
  IconExclamationCircle,
  IconBulb,
  IconMessage,
  IconSettings
} from '@arco-design/web-vue/es/icon';

// Agent配置
const agentUrl = 'http://dify.epochy.cn/chatbot/vwx203kISiUq1Nkl';

// 响应式数据
const loading = ref(true);
const error = ref(false);
const errorMessage = ref('');
const agentIframe = ref(null);

// iframe加载完成事件
const onIframeLoad = () => {
  loading.value = false;
  error.value = false;
  console.log('Agent iframe loaded successfully');
};

// iframe加载错误事件
const onIframeError = event => {
  loading.value = false;
  error.value = true;
  errorMessage.value = '无法连接到AI助手服务，请检查网络连接或稍后重试';
  console.error('Agent iframe load error:', event);
  Message.error('AI助手加载失败');
};

// 重新加载Agent
const refreshAgent = () => {
  if (agentIframe.value) {
    loading.value = true;
    error.value = false;
    agentIframe.value.src = agentUrl;
  }
};

// 重试加载
const retryLoad = () => {
  refreshAgent();
};

// 组件挂载时的处理
onMounted(() => {
  // 设置iframe加载超时
  const timeout = setTimeout(() => {
    if (loading.value) {
      loading.value = false;
      error.value = true;
      errorMessage.value = '加载超时，请检查网络连接';
      Message.error('AI助手加载超时');
    }
  }, 600000); //

  // 清理定时器
  onUnmounted(() => {
    clearTimeout(timeout);
  });
});
</script>

<style scoped lang="less">
// 导入全局变量
@import '@/assets/main.less';

// 局部变量定义
@agent-padding: 0;
@header-margin: @spacing-xl;
@header-padding: @spacing-xl;
@iframe-min-height: 700px;

.agent-view {
  padding: @agent-padding;
  min-height: 100vh;
}

.page-header {
  margin-bottom: @header-margin;
  padding: @header-padding;
  border-radius: @card-border-radius;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-text {
    flex: 1;
  }

  .page-title {
    font-size: @font-size-large-title;
    font-weight: 600;
    color: @color-text-primary;
    margin: 0 0 6px 0;
  }

  .page-description {
    color: @color-text-secondary;
    margin: 0;
    font-size: @font-size-lg;
  }

  .header-actions {
    display: flex;
    gap: @spacing-md;
  }

  .refresh-btn {
    border-color: @color-border;
    color: @color-text-secondary;

    &:hover {
      border-color: @color-primary;
      color: @color-primary;
    }
  }
}

.agent-container {
  margin-bottom: @spacing-xl;
}

.agent-card {
  min-height: 700px;
  .card-base();
  min-height: calc(@iframe-min-height + 48px);

  :deep(.arco-card-body) {
    padding: 0;
    height: 100%;
  }
}

.agent-wrapper {
  position: relative;
  width: 100%;
  height: @iframe-min-height;
  min-height: @iframe-min-height;
  overflow: hidden;
  border-radius: @card-border-radius;
}

.agent-iframe {
  width: 100%;
  height: 100%;
  min-height: @iframe-min-height;
  border: none;
  border-radius: @card-border-radius;
  background: #fff;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-content {
  text-align: center;
  padding: @spacing-xl;
}

.loading-icon {
  font-size: 48px;
  color: @color-primary;
  margin-bottom: @spacing-md;
  animation: pulse 2s infinite;
}

.loading-text {
  color: @color-text-secondary;
  font-size: @font-size-md;
  margin: 0;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fafbfc;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.error-content {
  text-align: center;
  padding: @spacing-xl;
  max-width: 400px;
}

.error-icon {
  font-size: 64px;
  color: @color-danger;
  margin-bottom: @spacing-lg;
}

.error-title {
  font-size: @font-size-title;
  font-weight: 600;
  color: @color-text-primary;
  margin: 0 0 @spacing-md 0;
}

.error-message {
  color: @color-text-secondary;
  font-size: @font-size-md;
  margin: 0 0 @spacing-lg 0;
  line-height: 1.5;
}

.retry-btn {
  background: @color-primary;
  border-color: @color-primary;

  &:hover {
    background: darken(@color-primary, 10%);
    border-color: darken(@color-primary, 10%);
  }
}

.tips-section {
  margin-bottom: @spacing-xl;
}

.tips-card {
  .card-base();
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: @spacing-lg;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: @spacing-md;
  padding: @spacing-md;
  background: #f8f9fa;
  border-radius: @spacing-sm;
  border-left: 3px solid @color-primary;
}

.tip-icon {
  font-size: @font-size-lg;
  color: @color-primary;
  margin-top: 2px;
  flex-shrink: 0;
}

.tip-text {
  color: @color-text-primary;
  font-size: @font-size-md;
  line-height: 1.5;

  strong {
    color: @color-text-primary;
    font-weight: 600;
  }
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .agent-view {
    padding: 0 @spacing-sm;
  }

  .page-header {
    padding: @spacing-lg @spacing-md;
    margin-bottom: @spacing-lg;

    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: @spacing-md;
    }

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }

    .page-title {
      font-size: @font-size-title;
    }
  }

  .agent-wrapper {
    height: 600px;
    min-height: 600px;
  }

  .agent-iframe {
    min-height: 600px;
  }

  .tips-content {
    gap: @spacing-md;
  }

  .tip-item {
    padding: @spacing-sm;
    gap: @spacing-sm;
  }

  .tip-text {
    font-size: @font-size-sm;
  }
}

@media (max-width: 480px) {
  .page-header {
    .page-title {
      font-size: @font-size-lg;
    }

    .page-description {
      font-size: @font-size-sm;
    }
  }

  .agent-wrapper {
    height: 500px;
    min-height: 500px;
  }

  .agent-iframe {
    min-height: 500px;
  }

  .loading-icon {
    font-size: 36px;
  }

  .error-icon {
    font-size: 48px;
  }

  .error-title {
    font-size: @font-size-lg;
  }

  .error-message {
    font-size: @font-size-sm;
  }
}
</style>
