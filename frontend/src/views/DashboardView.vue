<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h2 class="page-title">仪表板</h2>
          <p class="page-description">欢迎使用 AgentX 达人内容管理系统</p>
        </div>
        <div class="header-actions">
          <a-button type="outline" :loading="refreshing" @click="refreshData" class="refresh-btn">
            <template #icon>
              <icon-refresh />
            </template>
            刷新数据
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="[16, 16]" class="stats-row">
      <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <a-card class="stat-card" :loading="loading">
          <div class="stat-content">
            <div class="stat-icon public-influencers">
              <icon-user />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(stats.publicInfluencersTotal) }}</div>
              <div class="stat-label">达人公海数量</div>
            </div>
          </div>
          <div class="stat-trend">
            <span class="trend-text">总的可用达人数</span>
            <span class="trend-value neutral">{{ formatNumber(stats.publicInfluencersTotal) }}</span>
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <a-card class="stat-card" :loading="loading">
          <div class="stat-content">
            <div class="stat-icon my-influencers">
              <icon-heart />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(stats.myInfluencersTotal) }}</div>
              <div class="stat-label">我的达人数量</div>
            </div>
          </div>
          <div class="stat-trend">
            <span class="trend-text">收藏/关注的达人</span>
            <span class="trend-value neutral">{{ formatNumber(stats.myInfluencersTotal) }}</span>
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <a-card class="stat-card" :loading="loading">
          <div class="stat-content">
            <div class="stat-icon new-influencers">
              <icon-plus />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(stats.newInfluencersToday) }}</div>
              <div class="stat-label">新增达人数量</div>
            </div>
          </div>
          <div class="stat-trend">
            <span class="trend-text">较昨日</span>
            <span class="trend-value" :class="growthClass">{{ growthDisplay }}</span>
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <a-card class="stat-card" :loading="loading">
          <div class="stat-content">
            <div class="stat-icon crawl-results">
              <icon-file />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(stats.crawlResultsTotal) }}</div>
              <div class="stat-label">采集作品数量</div>
            </div>
          </div>
          <div class="stat-trend">
            <span class="trend-text">今日新增</span>
            <span class="trend-value" :class="stats.crawlResultsToday > 0 ? 'positive' : 'neutral'">
              {{ stats.crawlResultsToday > 0 ? '+' + stats.crawlResultsToday : stats.crawlResultsToday }}
            </span>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="[16, 16]" class="content-row">
      <!-- 达人采集数量趋势图表 -->
      <a-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <a-card title="达人采集数量趋势" class="chart-card" :loading="loading">
          <div v-if="influencerTrends.length > 0" class="chart-container">
            <v-chart class="trend-chart" :option="influencerTrendChartOption" autoresize />
          </div>
          <div v-else class="chart-placeholder">
            <icon-bar-chart class="chart-icon" />
            <p>暂无趋势数据</p>
            <p class="chart-desc">显示最近7天的达人采集数量趋势</p>
          </div>
        </a-card>
      </a-col>

      <!-- 平台分布 -->
      <a-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <a-card title="平台分布" class="platform-card" :loading="loading">
          <div class="platform-stats" v-if="platformStats.length > 0">
            <div class="platform-item" v-for="platform in platformStats" :key="platform.platform">
              <div class="platform-header">
                <div class="platform-dot" :style="{ backgroundColor: platform.color }"></div>
                <span class="platform-name">{{ platform.name }}</span>
                <span class="platform-percent">{{ platform.percent }}%</span>
              </div>
              <div class="platform-count">{{ platform.count }} 个达人</div>
              <div class="platform-progress">
                <a-progress :percent="platform.percent" :color="platform.color" :show-text="false" size="small" />
              </div>
            </div>

            <!-- 平台总览 -->
            <div class="platform-summary">
              <div class="summary-item">
                <span class="summary-label">总计</span>
                <span class="summary-value">{{ platformStats.reduce((sum, p) => sum + p.count, 0) }}</span>
              </div>
            </div>
          </div>
          <div v-else class="empty-state">
            <icon-file class="empty-icon" />
            <p>暂无平台数据</p>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 最近活动 -->
    <a-row :gutter="[16, 16]" class="activity-row">
      <a-col :span="24">
        <a-card title="最近活动" class="activity-card" :loading="loading">
          <template #extra>
            <a-button type="text" size="small" @click="refreshData" :loading="refreshing">
              <template #icon>
                <icon-refresh />
              </template>
              刷新
            </a-button>
          </template>
          <div v-if="recentActivities.length > 0">
            <a-list :data="recentActivities" :bordered="false">
              <template #item="{ item }">
                <a-list-item>
                  <div class="activity-item">
                    <div class="activity-icon" :class="item.type">
                      <component :is="getActivityIcon(item.type)" />
                    </div>
                    <div class="activity-content">
                      <div class="activity-title">{{ item.title }}</div>
                      <div class="activity-desc">{{ item.description }}</div>
                    </div>
                    <div class="activity-time">{{ item.timeDisplay || item.time }}</div>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <div v-else class="empty-state">
            <icon-robot class="empty-icon" />
            <p>暂无活动记录</p>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import { statsAPI } from '@/services/api';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart, BarChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
import {
  IconUser,
  IconRobot,
  IconFile,
  IconCheckCircle,
  IconBarChart,
  IconPlus,
  IconStop,
  IconRefresh,
  IconLoading,
  IconHeart
} from '@arco-design/web-vue/es/icon';

// 注册ECharts组件
use([CanvasRenderer, LineChart, BarChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent]);

// 加载状态
const loading = ref(true);
const refreshing = ref(false);

// 统计数据
const stats = ref({
  publicInfluencersTotal: 0,
  myInfluencersTotal: 0,
  newInfluencersToday: 0,
  newInfluencersGrowth: 0,
  crawlResultsTotal: 0,
  crawlResultsToday: 0,
  successRate: 0
});

// 平台统计
const platformStats = ref([]);

// 最近活动
const recentActivities = ref([]);

// 达人采集趋势数据
const influencerTrends = ref([]);

// 计算属性：格式化增长趋势显示
const growthDisplay = computed(() => {
  const growth = stats.value.newInfluencersGrowth;
  if (growth > 0) {
    return `+${growth}`;
  } else if (growth < 0) {
    return growth.toString();
  } else {
    return '0';
  }
});

// 计算属性：增长趋势样式类
const growthClass = computed(() => {
  const growth = stats.value.newInfluencersGrowth;
  if (growth > 0) {
    return 'positive';
  } else if (growth < 0) {
    return 'negative';
  } else {
    return 'neutral';
  }
});

// 计算属性：达人采集趋势图表配置
const influencerTrendChartOption = computed(() => {
  if (!influencerTrends.value.length) return {};

  const dates = influencerTrends.value.map(item => item.dateDisplay);
  const publicInfluencers = influencerTrends.value.map(item => item.publicInfluencers);
  const myInfluencers = influencerTrends.value.map(item => item.myInfluencers);
  const crawlResults = influencerTrends.value.map(item => item.crawlResults);

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e6eb',
      borderWidth: 1,
      textStyle: {
        color: '#1d2129'
      }
    },
    legend: {
      data: ['达人公海新增', '我的达人新增', '采集作品数量'],
      bottom: 0,
      textStyle: {
        color: '#4e5969'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#e5e6eb'
        }
      },
      axisLabel: {
        color: '#86909c'
      }
    },
    yAxis: {
      type: 'value',
      name: '数量',
      axisLine: {
        lineStyle: {
          color: '#e5e6eb'
        }
      },
      axisLabel: {
        color: '#86909c'
      },
      splitLine: {
        lineStyle: {
          color: '#f2f3f5'
        }
      }
    },
    series: [
      {
        name: '达人公海新增',
        type: 'line',
        smooth: true,
        data: publicInfluencers,
        lineStyle: {
          color: '#3370ff',
          width: 3
        },
        itemStyle: {
          color: '#3370ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(51, 112, 255, 0.3)' },
              { offset: 1, color: 'rgba(51, 112, 255, 0.05)' }
            ]
          }
        }
      },
      {
        name: '我的达人新增',
        type: 'line',
        smooth: true,
        data: myInfluencers,
        lineStyle: {
          color: '#00d0b6',
          width: 3
        },
        itemStyle: {
          color: '#00d0b6'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 208, 182, 0.3)' },
              { offset: 1, color: 'rgba(0, 208, 182, 0.05)' }
            ]
          }
        }
      },
      {
        name: '采集作品数量',
        type: 'line',
        smooth: true,
        data: crawlResults,
        lineStyle: {
          color: '#ff7d00',
          width: 3
        },
        itemStyle: {
          color: '#ff7d00'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 125, 0, 0.3)' },
              { offset: 1, color: 'rgba(255, 125, 0, 0.05)' }
            ]
          }
        }
      }
    ]
  };
});

// 格式化数字
const formatNumber = num => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};

// 获取活动图标
const getActivityIcon = type => {
  const iconMap = {
    create: IconPlus,
    start: IconPlus,
    complete: IconCheckCircle,
    update: IconRefresh,
    stop: IconStop,
    error: IconStop
  };
  return iconMap[type] || IconRefresh;
};

// 加载仪表板数据
const loadDashboardData = async (showMessage = false) => {
  try {
    if (showMessage) {
      refreshing.value = true;
    } else {
      loading.value = true;
    }

    const response = await statsAPI.getDashboard();

    if (response.success && response.data) {
      const {
        overview,
        platformStats: platforms,
        recentActivities: activities,
        influencerTrends: trends
      } = response.data;

      // 更新统计数据
      stats.value = overview;

      // 更新平台统计
      platformStats.value = platforms || [];

      // 更新最近活动
      recentActivities.value = activities || [];

      // 更新达人采集趋势
      influencerTrends.value = trends || [];

      if (showMessage) {
        Message.success('数据刷新成功');
      }
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error);

    // 显示错误信息
    const errorMessage = error.response?.data?.message || error.message || '加载数据失败';
    Message.error(errorMessage);

    // 使用默认数据
    if (!stats.value.publicInfluencersTotal) {
      stats.value = {
        publicInfluencersTotal: 0,
        myInfluencersTotal: 0,
        newInfluencersToday: 0,
        newInfluencersGrowth: 0,
        crawlResultsTotal: 0,
        crawlResultsToday: 0,
        successRate: 0
      };
      platformStats.value = [];
      recentActivities.value = [];
      influencerTrends.value = [];
    }
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 手动刷新数据
const refreshData = () => {
  loadDashboardData(true);
};

// 组件挂载时加载数据
onMounted(() => {
  loadDashboardData();
});
</script>

<style scoped lang="less">
// 导入全局变量
@import '@/assets/main.less';

// 局部变量定义
@dashboard-padding: 0;
@header-margin: @spacing-xl;
@header-padding: @spacing-xl;
@row-margin: @spacing-xl;

.dashboard {
  padding: @dashboard-padding;
  min-height: 100vh;
}

.page-header {
  margin-bottom: @header-margin;
  padding: @header-padding;
  // background: white;
  border-radius: @card-border-radius;
  // box-shadow: @card-shadow;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-text {
    flex: 1;
  }

  .page-title {
    font-size: @font-size-large-title;
    font-weight: 600;
    color: @color-text-primary;
    margin: 0 0 6px 0;
  }

  .page-description {
    color: @color-text-secondary;
    margin: 0;
    font-size: @font-size-lg;
  }

  .header-actions {
    display: flex;
    gap: @spacing-md;
  }

  .refresh-btn {
    border-color: @color-border;
    color: @color-text-secondary;

    &:hover {
      border-color: @color-primary;
      color: @color-primary;
    }
  }
}

.stats-row {
  margin-bottom: @row-margin;
}

.stat-card {
  .card-base();

  &:hover {
    transform: translateY(-2px);
  }
}

.stat-content {
  display: flex;
  align-items: center;
  gap: @spacing-md + 2px;
  margin-bottom: @spacing-md;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: @spacing-xl + 4px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &.public-influencers {
    background: @gradient-blue;
  }

  &.my-influencers {
    background: @gradient-pink;
  }

  &.new-influencers {
    background: @gradient-green;
  }

  &.crawl-results {
    background: @gradient-cyan;
  }
}

.stat-info {
  flex: 1;

  .stat-value {
    font-size: @font-size-title;
    font-weight: 700;
    color: @color-text-primary;
    line-height: 1;
    margin-bottom: 3px;
  }

  .stat-label {
    font-size: @font-size-md;
    color: @color-text-secondary;
    font-weight: 500;
  }
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: @font-size-sm;
  padding: 6px 10px;
  background: @color-background-card;
  border-radius: 6px;
  margin-top: 6px;

  .trend-text {
    color: @color-text-secondary;
  }

  .trend-value {
    font-weight: 600;

    &.positive {
      color: @color-success;
    }

    &.negative {
      color: @color-danger;
    }

    &.neutral {
      color: @color-text-secondary;
    }
  }
}

.content-row {
  margin-bottom: @row-margin;
}

.chart-card {
  .card-base();
}

.chart-container {
  height: 300px;
  padding: 12px;
}

.trend-chart {
  width: 100%;
  height: 100%;
}

.chart-placeholder {
  text-align: center;
  padding: 32px 16px;
  color: #86909c;
  background: #fafbfc;
  border-radius: 6px;
  margin: 12px;
}

// 平台分布卡片样式
.platform-card {
  .card-base();
  height: 100%;

  .arco-card-body {
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
  }
}

.platform-stats {
  height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px 0;
}

.platform-item {
  margin-bottom: @spacing-lg;
  padding: @spacing-md;
  background: #fafbfc;
  border-radius: @spacing-sm;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
  overflow: hidden; // 防止内容溢出

  &:hover {
    background: #f2f3f5;
    transform: translateX(2px);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.platform-header {
  display: flex;
  align-items: center;
  margin-bottom: @spacing-sm;
  gap: @spacing-sm;
}

.platform-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.platform-name {
  font-weight: 600;
  color: @color-text-primary;
  flex: 1;
  font-size: @font-size-md;
}

.platform-percent {
  font-weight: 600;
  color: @color-text-secondary;
  font-size: @font-size-sm;
}

.platform-count {
  color: @color-text-secondary;
  font-size: @font-size-sm;
  margin-bottom: @spacing-sm;
  padding-left: 16px;
}

.platform-progress {
  padding-left: 16px;
  padding-right: 8px;
  width: 100%;
  box-sizing: border-box;

  :deep(.arco-progress) {
    width: 100%;
  }

  :deep(.arco-progress-line) {
    width: 100%;
    max-width: 100%;
  }

  :deep(.arco-progress-line-bar) {
    border-radius: 2px;
  }

  :deep(.arco-progress-line-track) {
    background-color: #f2f3f5;
    width: 100%;
  }
}

.platform-summary {
  margin-top: auto;
  padding-top: @spacing-lg;
  border-top: 1px solid @color-border-light;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: @spacing-sm @spacing-md;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: @spacing-sm;
}

.summary-label {
  font-weight: 600;
  color: @color-text-secondary;
  font-size: @font-size-sm;
}

.summary-value {
  font-weight: 700;
  color: @color-text-primary;
  font-size: @font-size-lg;
}

.chart-icon {
  font-size: 48px;
  margin-bottom: 12px;
  color: #c9cdd4;
}

.chart-desc {
  font-size: 13px;
  margin: 6px 0 0 0;
  color: #a0a5ab;
}

// 旧的平台样式已移除，使用新的样式定义

.activity-row {
  margin-bottom: @row-margin;
}

.activity-card {
  .card-base();
}

.activity-item {
  display: flex;
  align-items: center;
  gap: @spacing-md + 2px;
  padding: @spacing-md 0;
  border-bottom: 1px solid #f7f8fa;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: @color-background-light;
  }
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: @card-border-radius;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: @font-size-md;
  color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);

  .status-colors();
}

.activity-content {
  flex: 1;

  .activity-title {
    font-weight: 600;
    color: @color-text-primary;
    margin-bottom: @spacing-xs;
    font-size: @font-size-md;
  }

  .activity-desc {
    font-size: @font-size-base;
    color: @color-text-secondary;
    line-height: 1.4;
  }
}

.activity-time {
  font-size: @font-size-xs;
  color: @color-text-disabled;
  font-weight: 500;
  background: #f7f8fa;
  padding: 3px 6px;
  border-radius: @spacing-xs;
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: @color-text-disabled;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 12px;
    color: #c9cdd4;
  }

  p {
    margin: 0;
    font-size: @font-size-md;
    color: @color-text-secondary;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    padding: 0 @spacing-sm;
  }

  .page-header {
    padding: @spacing-lg @spacing-md;
    margin-bottom: @spacing-lg;

    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: @spacing-md;
    }

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }

    .page-title {
      font-size: @font-size-title;
    }
  }

  .stat-card {
    margin-bottom: @spacing-md;

    .stat-content {
      gap: @spacing-sm;
    }

    .stat-icon {
      width: 40px;
      height: 40px;
      font-size: @spacing-lg;
    }

    .stat-value {
      font-size: @font-size-lg;
    }
  }

  .chart-container {
    height: 250px;
    padding: @spacing-sm;
  }

  .activity-item {
    padding: @spacing-sm 0;
    gap: @spacing-sm;

    .activity-icon {
      width: 28px;
      height: 28px;
      font-size: @font-size-sm;
    }

    .activity-content {
      .activity-title {
        font-size: @font-size-sm;
      }

      .activity-desc {
        font-size: @font-size-xs;
        line-height: 1.3;
      }
    }

    .activity-time {
      font-size: 10px;
      padding: 2px 4px;
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    .page-title {
      font-size: @font-size-lg;
    }

    .page-description {
      font-size: @font-size-sm;
    }
  }

  .stat-value {
    font-size: @font-size-md !important;
  }

  .stat-label {
    font-size: @font-size-xs;
  }

  .chart-container {
    height: 200px;
  }

  .platform-card {
    margin-top: @spacing-md;

    .platform-stats {
      height: auto;
    }

    .platform-item {
      padding: @spacing-sm;
      margin-bottom: @spacing-sm;
    }

    .platform-header {
      gap: @spacing-xs;
    }

    .platform-name {
      font-size: @font-size-sm;
    }

    .platform-count {
      margin-left: 12px;
    }

    .platform-progress {
      margin-left: 12px;
    }
  }
}
</style>
