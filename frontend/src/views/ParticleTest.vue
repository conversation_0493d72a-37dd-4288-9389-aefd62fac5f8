<template>
  <div class="particle-test">
    <!-- 全屏测试区域 -->
    <div class="test-container full-screen">
      <ParticleCanvas
        :particle-count="800"
        background-color="hsla(242, 30%, 5%, .65)"
        :particle-colors="['#3b82f6', '#1d4ed8', '#2563eb', '#1e40af']"
        :force-multiplier="0.12"
      />
      <div class="test-content">
        <h1>全屏粒子动画测试</h1>
        <p>移动鼠标查看粒子交互效果</p>
        <p>点击鼠标增强交互力度</p>
        <button @click="toggleSize" class="toggle-btn">切换到小尺寸测试</button>
      </div>
    </div>

    <!-- 小尺寸测试区域 -->
    <div v-if="showSmallTest" class="test-container small-size">
      <ParticleCanvas
        :particle-count="300"
        background-color="hsla(120, 30%, 5%, .65)"
        :particle-colors="['#10b981', '#059669', '#047857']"
        :force-multiplier="0.08"
      />
      <div class="test-content">
        <h2>小尺寸容器测试</h2>
        <p>自适应容器尺寸</p>
      </div>
    </div>

    <!-- 响应式测试区域 -->
    <div v-if="showSmallTest" class="responsive-grid">
      <div class="grid-item">
        <ParticleCanvas
          :particle-count="150"
          background-color="hsla(0, 30%, 5%, .65)"
          :particle-colors="['#ef4444', '#dc2626', '#b91c1c']"
          :force-multiplier="0.06"
        />
        <div class="grid-content">网格1</div>
      </div>
      <div class="grid-item">
        <ParticleCanvas
          :particle-count="150"
          background-color="hsla(45, 30%, 5%, .65)"
          :particle-colors="['#f59e0b', '#d97706', '#b45309']"
          :force-multiplier="0.06"
        />
        <div class="grid-content">网格2</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ParticleCanvas from '@/components/ParticleCanvas.vue'

const showSmallTest = ref(false)

const toggleSize = () => {
  showSmallTest.value = !showSmallTest.value
}
</script>

<style scoped>
.particle-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  padding: 20px;
  overflow-x: hidden;
}

.test-container {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.full-screen {
  width: 100%;
  height: 80vh;
}

.small-size {
  width: 60%;
  height: 400px;
  margin: 20px auto;
}

.test-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 10;
  pointer-events: none;
}

.test-content h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.test-content h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.test-content p {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.toggle-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
  pointer-events: auto;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.toggle-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.responsive-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.grid-item {
  position: relative;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.grid-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  z-index: 10;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  pointer-events: none;
}

@media (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: 1fr;
  }

  .small-size {
    width: 90%;
    height: 300px;
  }

  .test-content h1 {
    font-size: 2rem;
  }

  .test-content h2 {
    font-size: 1.5rem;
  }

  .test-content p {
    font-size: 1rem;
  }
}
</style>
