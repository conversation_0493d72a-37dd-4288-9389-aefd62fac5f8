<template>
  <a-modal
    :visible="visible"
    :title="isEditMode ? '编辑达人提报' : '达人提报'"
    width="800px"
    :on-before-ok="handleSubmit"
    @cancel="handleCancel"
    @update:visible="handleCancel"
    :confirm-loading="submitting"
  >
    <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="平台" field="platform">
            <a-select v-model="form.platform" placeholder="请选择平台">
              <a-option value="xiaohongshu">小红书</a-option>
              <a-option value="juxingtu">巨量星图</a-option>
              <a-option value="淘宝">淘宝</a-option>
              <a-option value="快手">快手</a-option>
              <a-option value="B站">B站</a-option>
              <a-option value="微信&视频号">微信&视频号</a-option>
              <a-option value="微博">微博</a-option>
              <a-option value="知乎">知乎</a-option>
              <a-option value="其他">其他</a-option>
              <a-option value="置换">置换</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="达人名称" field="influencerName">
            <a-input v-model="form.influencerName" placeholder="请输入达人名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="运营负责人" field="operationManager">
            <a-input v-model="form.operationManager" placeholder="请输入运营负责人" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="粉丝量" field="followersCount">
            <a-input-number v-model="form.followersCount" placeholder="请输入粉丝量" :min="0" style="width: 100%" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="达人主页链接" field="influencerUrl">
            <a-input v-model="form.influencerUrl" placeholder="请输入达人主页链接" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="播放量中位数" field="playMid">
            <a-input v-model="form.playMid" placeholder="请输入播放量中位数" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="达人平台ID" field="cooperationPrice">
            <a-textarea v-model="form.platformUserId" placeholder="请输入合作价格" :rows="2" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="平台达人报价" field="platformPrice">
            <a-textarea v-model="form.platformPrice" placeholder="请输入平台达人报价" :rows="2" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="内容方向" field="contentDirection">
            <a-input v-model="form.contentDirection" placeholder="请输入内容方向" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="合作品" field="cooperationProduct">
            <a-input v-model="form.cooperationProduct" placeholder="请输入合作品名称（可选）" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="合作价格" field="cooperationPrice">
            <a-textarea v-model="form.cooperationPrice" placeholder="请输入合作价格" :rows="2" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" field="notes">
            <a-textarea
              v-model="form.notes"
              placeholder="请输入备注（可选）"
              :rows="2"
              :max-length="200"
              show-word-limit
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="选择理由" field="selectionReason">
        <a-textarea
          v-model="form.selectionReason"
          placeholder="请输入选择理由（必填）"
          :rows="3"
          :max-length="500"
          show-word-limit
        />
      </a-form-item>

      <!-- 关联作品管理 -->
      <a-form-item label="关联作品" v-if="['xiaohongshu', 'juxingtu'].includes(form.platform)">
        <div class="related-videos-management">
          <div class="videos-header">
            <span v-if="form.relatedVideos && form.relatedVideos.length > 0">
              已选择 {{ form.relatedVideos.length }} 个作品
            </span>
            <span v-else>暂未关联作品</span>
            <div class="videos-actions">
              <a-button
                type="text"
                size="small"
                @click="showVideoSelectionModal"
                :disabled="!canManageVideos"
                :loading="loadingAuthorVideos"
              >
                {{ form.relatedVideos && form.relatedVideos.length > 0 ? '重新选择' : '选择作品' }}
              </a-button>
            </div>
          </div>

          <div v-if="form.relatedVideos && form.relatedVideos.length > 0" class="videos-list">
            <div
              v-for="video in form.relatedVideos"
              :key="video.videoId"
              class="video-item clickable"
              @click="previewVideo(video)"
            >
              <div class="video-content">
                <div class="video-title">{{ video.title }}</div>
                <div class="video-stats">
                  <span>播放: {{ formatNumber(video.playCount) }}</span>
                  <span>点赞: {{ formatNumber(video.likeCount) }}</span>
                  <span>收藏: {{ formatNumber(video.collectCount) }}</span>
                </div>
              </div>
              <div class="video-actions">
                <a-button type="text" size="small" status="danger" @click.stop="removeVideo(video.videoId)">
                  删除
                </a-button>
              </div>
            </div>
          </div>

          <div v-else class="no-videos">
            <a-empty description="暂无关联作品" size="small" />
          </div>
        </div>
      </a-form-item>

      <!-- 编辑模式下显示审核相关信息 -->
      <template v-if="isEditMode && editData">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="当前状态">
              <a-tag :color="getStatusColor(editData?.status)">
                {{ getStatusText(editData?.status) }}
              </a-tag>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="重新提报次数">
              <span>{{ editData?.resubmitCount || 0 }} 次</span>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item v-if="editData?.reviewComment" label="最新审核意见">
          <div class="review-comment">{{ editData.reviewComment }}</div>
        </a-form-item>

        <a-form-item v-if="editData?.resubmitReason" label="重新提报说明">
          <div class="resubmit-reason">{{ editData.resubmitReason }}</div>
        </a-form-item>
      </template>

      <!-- 保护期警告 -->
      <a-alert v-if="protectionWarning" type="warning" :title="protectionWarning" style="margin-bottom: 16px" />

      <!-- 重复提报警告 -->
      <a-alert v-if="duplicateWarning" type="error" :title="duplicateWarning" style="margin-bottom: 16px" />

      <!-- 二次提报提示 -->
      <a-alert
        v-if="isResubmit"
        type="info"
        title="检测到该达人已超过30天保护期，此次提报将标记为二次提报"
        style="margin-bottom: 16px"
      />
    </a-form>
  </a-modal>

  <!-- 作品选择模态窗口 -->
  <a-modal
    v-model:visible="videoSelectionVisible"
    title="选择关联作品"
    width="1000px"
    :footer="false"
    @cancel="closeVideoSelection"
  >
    <div v-if="loadingAuthorVideos" class="loading-container">
      <a-spin size="large">
        <div class="loading-text">正在获取达人作品数据...</div>
      </a-spin>
    </div>

    <div v-else-if="authorVideos.length > 0" class="video-selection-content">
      <!-- 作品筛选工具 -->
      <div class="video-filter">
        <a-input-search
          v-model="videoFilter"
          placeholder="输入关键词筛选作品标题..."
          style="width: 300px"
          @search="filterVideos"
        />
        <div class="filter-stats">
          共 {{ filteredVideos.length }} 个作品
          <span v-if="selectedVideoIds.length > 0">，已选择 {{ selectedVideoIds.length }} 个</span>
        </div>
      </div>

      <!-- 作品列表 -->
      <div class="video-list">
        <a-table
          :data="filteredVideos"
          :pagination="{ pageSize: 10 }"
          :scroll="{ x: 'max-content' }"
          row-key="videoId"
          :row-selection="rowSelection"
          @selection-change="selectionChange"
        >
          <template #columns>
            <a-table-column title="作品标题" data-index="title" :width="200">
              <template #cell="{ record }">
                <div class="video-title">
                  <a-tooltip :content="record.title">
                    {{ record.title }}
                  </a-tooltip>
                </div>
              </template>
            </a-table-column>
            <a-table-column
              title="播放量"
              data-index="playCount"
              :sortable="{
                sortDirections: ['ascend', 'descend']
              }"
              :width="100"
            >
              <template #cell="{ record }">
                {{ formatNumber(record.playCount) }}
              </template>
            </a-table-column>
            <a-table-column
              title="点赞量"
              :sortable="{
                sortDirections: ['ascend', 'descend']
              }"
              data-index="likeCount"
              :width="100"
            >
              <template #cell="{ record }">
                {{ formatNumber(record.likeCount) }}
              </template>
            </a-table-column>
            <a-table-column
              title="收藏量"
              :sortable="{
                sortDirections: ['ascend', 'descend']
              }"
              data-index="collectCount"
              :width="100"
            >
              <template #cell="{ record }">
                {{ formatNumber(record.collectCount) }}
              </template>
            </a-table-column>
            <a-table-column
              title="发布时间"
              :sortable="{
                sortDirections: ['ascend', 'descend']
              }"
              data-index="publishTime"
            >
              <template #cell="{ record }">
                {{ formatDate(record.publishTime) }}
              </template>
            </a-table-column>
            <a-table-column title="操作" :width="120" fixed="right">
              <template #cell="{ record }">
                <a-button type="text" size="small" @click="previewVideoInSelection(record)"> 预览 </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>

      <div class="selection-actions">
        <a-space>
          <a-button @click="closeVideoSelection">取消</a-button>
          <a-button type="primary" @click="confirmVideoSelection">
            确定选择 ({{ rowSelection.selectedRowKeys.length }})
          </a-button>
        </a-space>
      </div>
    </div>

    <div v-else class="no-videos-available">
      <a-empty description="暂无作品数据" />
      <p class="no-videos-tip">请确保该达人在系统中有作品数据</p>
    </div>
  </a-modal>

  <!-- 作品预览模态窗口 -->
  <a-modal
    v-model:visible="videoPreviewVisible"
    title="作品预览"
    width="600px"
    :footer="false"
    @cancel="closeVideoPreview"
  >
    <div v-if="currentPreviewVideo" class="video-preview">
      <div class="video-info">
        <h3>{{ currentPreviewVideo.title }}</h3>
        <div class="video-stats-detail">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="发布时间">
              {{ formatDate(currentPreviewVideo.publishTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="播放量">
              {{ formatNumber(currentPreviewVideo.playCount) }}
            </a-descriptions-item>
            <a-descriptions-item label="点赞量">
              {{ formatNumber(currentPreviewVideo.likeCount) }}
            </a-descriptions-item>
            <a-descriptions-item label="收藏量">
              {{ formatNumber(currentPreviewVideo.collectCount) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div v-if="currentPreviewVideo.description" class="video-description">
          <h4>作品描述</h4>
          <p>{{ currentPreviewVideo.description }}</p>
        </div>

        <div class="video-actions">
          <a-space>
            <a-button
              type="primary"
              @click="viewVideo(currentPreviewVideo)"
              :loading="loadingNotes[currentPreviewVideo.videoId]"
            >
              观看原作品
            </a-button>
          </a-space>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import { influencerReportAPI, authorVideoAPI } from '@/services/api';
import { useUserStore } from '@/stores/user';
import { formatPrice, formatNumber, formatDate } from '@/utils/platformUtils';
// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  influencerData: {
    type: Object,
    default: () => ({})
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  },
  preFilledData: {
    type: Object,
    default: () => ({})
  },
  relatedVideos: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:visible', 'success']);

// 用户状态
const userStore = useUserStore();

// 响应式数据
const formRef = ref();
const submitting = ref(false);
const protectionWarning = ref('');
const duplicateWarning = ref('');
const isResubmit = ref(false);

// 表单数据
const form = ref({
  platform: '',
  influencerName: '',
  operationManager: '',
  influencerUrl: '',
  followersCount: 0,
  playMid: '',
  selectionReason: '',
  contentDirection: '',
  cooperationProduct: '',
  platformPrice: '',
  cooperationPrice: '',
  notes: '',
  platformUserId: null,
  relatedVideos: []
});

// 表单验证规则
const rules = {
  platform: [{ required: true, message: '请选择平台' }],
  influencerName: [{ required: true, message: '请输入达人名称' }],
  operationManager: [{ required: true, message: '请输入运营负责人' }],
  selectionReason: [
    { required: true, message: '请输入选择理由' },
    { minLength: 10, message: '选择理由至少10个字符' }
  ],
  contentDirection: [{ required: true, message: '请输入内容方向' }]
};

// 作品管理相关状态
const videoSelectionVisible = ref(false);
const videoPreviewVisible = ref(false);
const currentPreviewVideo = ref(null);
const loadingAuthorVideos = ref(false);
const authorVideos = ref([]);
const videoFilter = ref('');
const selectedVideoIds = ref([]);
const loadingNotes = ref({});

// 计算属性
const canManageVideos = computed(() => {
  // 只有在有平台用户ID时才能管理作品
  return form.value.platformUserId && form.value.platform;
});

const filteredVideos = computed(() => {
  if (!videoFilter.value) return authorVideos.value;
  return authorVideos.value.filter(
    video => video.title && video.title.toLowerCase().includes(videoFilter.value.toLowerCase())
  );
});

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  selectedRowKeys: [],
  selectedRows: []
});
const selectionChange = selectedRowKeys => {
  rowSelection.selectedRowKeys = selectedRowKeys;
  rowSelection.selectedRows = authorVideos.value.filter(video => selectedRowKeys.includes(video.videoId));
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  async newVal => {
    if (newVal) {
      // 确保用户信息已加载
      if (!userStore.user && userStore.token) {
        await userStore.getCurrentUser();
      }
      await initForm();
    } else {
      // 只在弹窗关闭时重置表单
      console.log('弹窗关闭，重置表单');
      resetForm();
    }
  }
);

// 监听运营负责人字段变化，触发重复提报检查
watch(
  () => form.value.operationManager,
  async (newVal, oldVal) => {
    if (newVal && newVal !== oldVal && !props.isEditMode) {
      await checkDuplicateReport();
    }
  }
);

// 初始化表单
const initForm = async () => {
  if (props.isEditMode && props.editData) {
    // 编辑模式：使用editData填充表单
    const data = props.editData;
    console.log('编辑模式初始化表单数据:', data);

    form.value.platform = data?.platform || '';
    form.value.influencerName = data?.influencerName || '';
    form.value.operationManager = data?.operationManager || '';
    form.value.influencerUrl = data?.influencerUrl || '';
    form.value.followersCount = data?.followersCount || 0;
    form.value.playMid = data?.playMid || '';
    form.value.selectionReason = data?.selectionReason || '';
    form.value.contentDirection = data?.contentDirection || '';
    form.value.cooperationProduct = data?.cooperationProduct || '';
    form.value.platformPrice = data?.platformPrice || '';
    form.value.cooperationPrice = data?.cooperationPrice || '';
    form.value.notes = data?.notes || '';
    form.value.platformUserId = data?.platformUserId || null;
    form.value.relatedVideos = data?.relatedVideos || [];
  } else if (props.preFilledData && Object.keys(props.preFilledData).length > 0) {
    // 智能提报模式：使用预填充数据
    const data = props.preFilledData;
    console.log('智能提报模式初始化表单数据:', data);

    form.value.platform = data.platform || '';
    form.value.influencerName = data.influencerName || '';
    form.value.operationManager = userStore.user?.chineseName || '未知用户';
    form.value.influencerUrl = data.influencerUrl || '';
    form.value.followersCount = data.followersCount || 0;
    form.value.playMid = data.playMid || '';
    form.value.selectionReason = '';
    form.value.contentDirection = '';
    form.value.cooperationProduct = '';
    form.value.platformPrice = data.platformPrice || '';
    form.value.cooperationPrice = data.cooperationPrice || '';
    form.value.notes = '';
    form.value.platformUserId = data.platformUserId || null;
    form.value.relatedVideos = props.relatedVideos || [];

    // 检查30天保护期和重复提报控制
    await checkProtectionPeriod();
    await checkDuplicateReport();
  } else {
    // 传统新建模式：使用influencerData填充表单
    const data = props.influencerData;
    console.log('传统新建模式初始化表单数据:', data);

    form.value.platform = data.platform || '';
    form.value.influencerName = data.nickname || data.influencerName || '';
    form.value.operationManager = userStore.user?.chineseName || '未知用户';
    form.value.influencerUrl =
      data.influencerUrl || (data.platformUserId || data.platformId ? getInfluencerUrl(data) : '');
    form.value.followersCount = data.followersCount || 0;
    form.value.playMid = data.playMid || '';
    form.value.selectionReason = '';
    form.value.contentDirection = '';
    form.value.cooperationProduct = '';
    form.value.platformPrice = data.authorExtInfo ? formatPrice(data.authorExtInfo?.price_20_60) : '';
    form.value.cooperationPrice = '';
    form.value.notes = '';
    form.value.platformUserId = data.platformUserId || data.platformId || null;
    form.value.relatedVideos = [];

    // 只在新建模式下检查30天保护期和重复提报控制
    await checkProtectionPeriod();
    await checkDuplicateReport();
  }

  console.log('表单初始化后:', form.value);
};

// 获取达人主页链接
const getInfluencerUrl = data => {
  if (data.influencerUrl) return data.influencerUrl;

  // 根据平台和用户ID构建链接
  if (data.platform === 'xiaohongshu' && data.platformUserId) {
    return `https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/${data.platformUserId}?source=Advertiser_Kol`;
  } else if (data.platform === 'xiaohongshu' && data.platformId) {
    return `https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/${data.platformId}?source=Advertiser_Kol`;
  } else if (data.platform === 'juxingtu' && data.platformUserId) {
    return `https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/${data.platformUserId}`;
  } else if (data.platform === 'juxingtu' && data.platformId) {
    return `https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/${data.platformId}`;
  }

  return '';
};

// 检查30天保护期
const checkProtectionPeriod = async () => {
  if (!form.value.influencerName || !form.value.platform) return;

  try {
    const response = await influencerReportAPI.checkProtection({
      influencerName: form.value.influencerName,
      platform: form.value.platform
    });

    if (response.success) {
      const { canSubmit, isFirstSubmit, isResubmit: resubmit, message } = response.data;

      if (!canSubmit) {
        protectionWarning.value = message;
      } else {
        protectionWarning.value = '';
        isResubmit.value = resubmit;
      }
    }
  } catch (error) {
    console.error('检查保护期失败:', error);
  }
};

// 检查重复提报控制
const checkDuplicateReport = async () => {
  if (!form.value.influencerName || !form.value.platform || !form.value.operationManager) return;

  try {
    const response = await influencerReportAPI.checkDuplicateReport({
      influencerName: form.value.influencerName,
      platform: form.value.platform,
      operationManager: form.value.operationManager
    });

    if (response.success) {
      const {
        canSubmit,
        message,
        reasonCode,
        daysSinceLastReport,
        lastReportStatus,
        timeConditionMet,
        statusConditionMet
      } = response.data;

      if (!canSubmit) {
        // 根据具体原因显示详细的错误信息
        let detailedMessage = message;

        if (reasonCode === 'time_and_status_not_met') {
          detailedMessage += `（时间：${daysSinceLastReport}天 < 30天，状态：${lastReportStatus}）`;
        } else if (reasonCode === 'time_condition_not_met') {
          detailedMessage += `（距离上次提报仅${daysSinceLastReport}天）`;
        } else if (reasonCode === 'status_condition_not_met') {
          detailedMessage += `（当前状态：${lastReportStatus}）`;
        }

        duplicateWarning.value = detailedMessage;
      } else {
        duplicateWarning.value = '';

        // 如果是因为满足时间和状态条件而允许的重复提报，显示提示信息
        if (response.data.allowReason === 'time_and_status_conditions_met') {
          console.log(
            `✅ 允许重复提报: ${message} (距离上次提报${daysSinceLastReport}天，上次状态：${lastReportStatus})`
          );
        }
      }
    }
  } catch (error) {
    console.error('检查重复提报失败:', error);
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    console.log('handleSubmit 被调用');
    let res = await formRef.value?.validate();
    console.log('表单验证结果:', res);
    if (res) return false;
    // 先保存表单数据的副本，防止被清空
    const formDataBackup = { ...form };
    console.log('提交前表单数据备份:', formDataBackup);

    // 暂时跳过Arco表单验证，直接进行手动验证
    console.log('跳过Arco表单验证，进行手动验证...');

    // 如果有保护期警告，阻止提交
    if (protectionWarning.value) {
      Message.error('该达人在保护期内，无法提报');
      return false;
    }

    // 如果有重复提报警告，阻止提交
    if (duplicateWarning.value) {
      Message.error('重复提报验证失败，无法提报');
      return false;
    }

    submitting.value = true;

    // 检查必填字段
    console.log('表单数据检查:', {
      platform: form.value.platform,
      influencerName: form.value.influencerName,
      operationManager: form.value.operationManager,
      selectionReason: form.value.selectionReason
    });

    // 验证必填字段是否为空
    const missingFields = [];
    if (!form.value.platform) missingFields.push('平台');
    if (!form.value.influencerName) missingFields.push('达人名称');
    if (!form.value.operationManager) missingFields.push('运营负责人');
    if (!form.value.selectionReason) missingFields.push('选择理由');
    if (!form.value.contentDirection) missingFields.push('内容方向');

    if (missingFields.length > 0) {
      Message.error(`请填写以下必填字段: ${missingFields.join(', ')}`);
      return false;
    }

    // 转换字段名为后端期望的格式
    const submitData = {
      platform: form.value.platform.trim(),
      influencerName: form.value.influencerName.trim(),
      operationManager: form.value.operationManager.trim(),
      influencerUrl: form.value.influencerUrl?.trim() || '',
      followersCount: parseInt(form.value.followersCount) || 0,
      playMid: form.value.playMid?.trim() || '',
      selectionReason: form.value.selectionReason.trim(),
      contentDirection: form.value.contentDirection.trim(),
      cooperationProduct: form.value.cooperationProduct?.trim() || '',
      platformPrice: form.value.platformPrice?.trim() || '',
      cooperationPrice: form.value.cooperationPrice?.trim() || '',
      notes: form.value.notes?.trim() || '',
      platformUserId: form.value.platformUserId
    };

    console.log('提交数据:', submitData); // 调试日志

    console.log('发送API请求...');
    let response;

    if (props.isEditMode && props.editData?.id) {
      // 编辑模式：调用更新接口
      response = await influencerReportAPI.update(props.editData.id, submitData);
      console.log('编辑API响应:', response);
    } else {
      // 新建模式：调用创建接口
      response = await influencerReportAPI.create(submitData);
      console.log('创建API响应:', response);
    }

    if (response.success) {
      Message.success(props.isEditMode ? '编辑成功' : '提报成功');
      emit('success');
      handleCancel();
    } else {
      console.error('API返回失败:', response);
      Message.error(response.message || (props.isEditMode ? '编辑失败' : '提报失败'));
    }
  } catch (error) {
    console.error('提报失败详细信息:', {
      error: error,
      response: error.response,
      data: error.response?.data,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    // 显示更详细的错误信息
    if (error.response?.status === 422) {
      // 422 验证错误
      if (error.response.data?.errors) {
        const errorMessages = Object.entries(error.response.data.errors).map(([field, message]) => {
          return `${field}: ${message}`;
        });
        Message.error(`验证失败: ${errorMessages.join(', ')}`);
      } else if (error.response.data?.message) {
        Message.error(`验证失败: ${error.response.data.message}`);
      } else {
        Message.error('表单验证失败，请检查必填字段');
      }
    } else if (error.response?.data?.message) {
      Message.error(error.response.data.message);
    } else if (error.response?.data?.errors) {
      const errorMessages = Object.values(error.response.data.errors).flat();
      Message.error(errorMessages.join(', '));
    } else {
      Message.error('提报失败，请检查网络连接');
    }
  } finally {
    submitting.value = false;
  }
};

// 调试表单数据
const debugFormData = () => {
  console.log('=== 表单数据调试 ===');
  console.log('用户信息:', userStore.user);
  console.log('表单数据:', JSON.stringify(form.value, null, 2));
  console.log('必填字段检查:');
  console.log('  platform:', form.value.platform, '(类型:', typeof form.value.platform, ')');
  console.log('  influencerName:', form.value.influencerName, '(类型:', typeof form.value.influencerName, ')');
  console.log('  operationManager:', form.value.operationManager, '(类型:', typeof form.value.operationManager, ')');
  console.log('  selectionReason:', form.value.selectionReason, '(类型:', typeof form.value.selectionReason, ')');
  console.log('  platformUserId:', form.value.platformUserId, '(类型:', typeof form.value.platformUserId, ')');

  // 检查字段是否为空
  const isEmpty = value => value === '' || value === null || value === undefined;
  console.log('字段为空检查:');
  console.log('  platform 为空:', isEmpty(form.value.platform));
  console.log('  influencerName 为空:', isEmpty(form.value.influencerName));
  console.log('  operationManager 为空:', isEmpty(form.value.operationManager));
  console.log('  selectionReason 为空:', isEmpty(form.value.selectionReason));
  console.log('  platformUserId 为空:', isEmpty(form.value.platformUserId));

  Message.info('表单数据已输出到控制台，请按F12查看');
};

// 取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 重置表单
const resetForm = () => {
  console.log('resetForm 被调用');
  // 手动重置表单数据，而不是使用resetFields()
  form.value.platform = '';
  form.value.influencerName = '';
  form.value.operationManager = '';
  form.value.influencerUrl = '';
  form.value.followersCount = 0;
  form.value.playMid = '';
  form.value.selectionReason = '';
  form.value.platformPrice = '';
  form.value.cooperationPrice = '';
  form.value.notes = '';
  form.value.platformUserId = null;
  form.value.relatedVideos = [];

  protectionWarning.value = '';
  duplicateWarning.value = '';
  isResubmit.value = false;

  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }

  // 重置作品管理相关状态
  resetVideoManagementState();
};

// 重置作品管理状态
const resetVideoManagementState = () => {
  videoSelectionVisible.value = false;
  videoPreviewVisible.value = false;
  currentPreviewVideo.value = null;
  loadingAuthorVideos.value = false;
  authorVideos.value = [];
  videoFilter.value = '';
  selectedVideoIds.value = [];
  loadingNotes.value = {};
};

// 作品管理相关方法
const showVideoSelectionModal = async () => {
  if (!canManageVideos.value) {
    Message.warning('请先填写平台和达人信息');
    return;
  }

  // 获取达人作品数据
  await fetchAuthorVideos();

  // 预选择当前已关联的作品
  if (form.value.relatedVideos && form.value.relatedVideos.length > 0) {
    selectedVideoIds.value = form.value.relatedVideos.map(video => video.videoId);
  }

  videoSelectionVisible.value = true;
};

const fetchAuthorVideos = async () => {
  try {
    loadingAuthorVideos.value = true;

    // 调用智能提报的API获取作品数据
    const response = await influencerReportAPI.getAuthorVideos({
      platform: form.value.platform,
      authorId: form.value.platformUserId,
      associateVideos: false // 不需要关联作品库
    });

    if (response.success && response.data.notes) {
      authorVideos.value = response.data.notes;
    } else {
      Message.error(response.message || '获取作品数据失败');
      authorVideos.value = [];
    }
  } catch (error) {
    console.error('获取作品数据失败:', error);
    Message.error('获取作品数据失败，请稍后重试');
    authorVideos.value = [];
  } finally {
    loadingAuthorVideos.value = false;
  }
};

const closeVideoSelection = () => {
  videoSelectionVisible.value = false;
  videoFilter.value = '';
  selectedVideoIds.value = [];
};

const handleVideoSelection = selectedRowKeys => {
  selectedVideoIds.value = selectedRowKeys;
};

const confirmVideoSelection = () => {
  // 根据选中的ID获取完整的作品数据
  const selectedVideos = authorVideos.value.filter(video => rowSelection.selectedRowKeys.includes(video.videoId));

  // 更新表单中的关联作品
  form.value.relatedVideos = selectedVideos;

  // 关闭选择窗口
  closeVideoSelection();

  Message.success(`已选择 ${selectedVideos.length} 个作品`);
};

const removeVideo = videoId => {
  const index = form.value.relatedVideos.findIndex(video => video.videoId === videoId);
  if (index > -1) {
    form.value.relatedVideos.splice(index, 1);
    Message.success('作品已移除');
  }
};

const previewVideo = video => {
  currentPreviewVideo.value = video;
  videoPreviewVisible.value = true;
};

const previewVideoInSelection = video => {
  previewVideo(video);
};

const closeVideoPreview = () => {
  videoPreviewVisible.value = false;
  currentPreviewVideo.value = null;
};

const filterVideos = () => {
  // 筛选逻辑已在计算属性中实现
};

// 观看作品（复用智能提报模块的逻辑）
const viewVideo = async video => {
  // 如果是小红书平台，调用API获取最新详情
  if (form.value.platform === 'xiaohongshu' && video.videoId) {
    await handleXiaohongshuWatch(video);
  } else if (video.videoUrl) {
    // 其他平台直接打开链接
    window.open(video.videoUrl, '_blank');
  } else {
    Message.warning('该作品暂无观看链接');
  }
};

const handleXiaohongshuWatch = async video => {
  try {
    loadingNotes.value[video.videoId] = true;
    const response = await authorVideoAPI.getXiaohongshuNoteDetail(video.videoId);

    if (response.success && response.data) {
      window.open(response.data.noteLink, '_blank');
      Message.success('已打开最新帖子链接');
    } else {
      Message.error(response.message || '获取帖子详情失败');
    }
  } catch (error) {
    console.error('获取小红书帖子详情失败:', error);
    Message.error('获取帖子详情失败，请稍后重试');
  } finally {
    loadingNotes.value[video.videoId] = false;
  }
};

// 状态相关工具方法
const getStatusColor = status => {
  const colors = {
    pending: 'blue',
    approved: 'green',
    rejected: 'red',
    need_confirmation: 'orange'
  };
  return colors[status] || 'gray';
};

const getStatusText = status => {
  const texts = {
    pending: '审核中',
    approved: '审核通过',
    rejected: '审核拒绝',
    need_confirmation: '需二次确认'
  };
  return texts[status] || status;
};
</script>

<style scoped>
.arco-form-item {
  margin-bottom: 16px;
}

.arco-textarea {
  resize: vertical;
}

.review-comment,
.resubmit-reason {
  padding: 8px 12px;
  background-color: #f7f8fa;
  border-radius: 4px;
  border-left: 3px solid #165dff;
  color: #4e5969;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 100px;
  overflow-y: auto;
}

.related-videos {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
  background-color: #f8f9fa;
}

.videos-header {
  margin-bottom: 12px;
  font-weight: 500;
  color: #1d2129;
}

.videos-list {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.video-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
}

.video-item:last-child {
  margin-bottom: 0;
}

.video-title {
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #86909c;
}

.video-stats span {
  display: flex;
  align-items: center;
}

/* 关联作品管理样式 */
.related-videos-management {
  width: 100%;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
  background-color: #f8f9fa;
}

.videos-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #1d2129;
}

.videos-actions {
  display: flex;
  gap: 8px;
}

.video-item.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.video-item.clickable:hover {
  border-color: #165dff;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
}

.video-content {
  flex: 1;
  min-width: 0;
}

.video-actions {
  flex-shrink: 0;
  margin-left: 12px;
}

.no-videos {
  text-align: center;
  padding: 20px;
  color: #86909c;
}

/* 作品选择模态窗口样式 */
.loading-container {
  text-align: center;
  padding: 40px 20px;
}

.loading-text {
  margin-top: 16px;
  color: #666;
}

.video-selection-content {
  padding: 16px 0;
}

.video-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-stats {
  color: #666;
  font-size: 14px;
}

.video-list {
  margin: 16px 0;
}

.selection-actions {
  margin-top: 16px;
  text-align: center;
}

.no-videos-available {
  text-align: center;
  padding: 40px 20px;
}

.no-videos-tip {
  margin-top: 8px;
  color: #86909c;
  font-size: 14px;
}

/* 作品预览样式 */
.video-preview {
  padding: 16px 0;
}

.video-info h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  line-height: 1.4;
}

.video-stats-detail {
  margin-bottom: 20px;
}

.video-description {
  margin-bottom: 20px;
}

.video-description h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
}

.video-description p {
  margin: 0;
  color: #4e5969;
  line-height: 1.6;
  word-break: break-word;
  max-height: 120px;
  overflow-y: auto;
}

.video-actions {
  padding-top: 16px;
  border-top: 1px solid #e5e6eb;
}
</style>
