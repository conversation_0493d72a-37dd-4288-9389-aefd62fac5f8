<template>
  <a-modal
    :visible="visible"
    title="合作记录数据详情"
    width="900px"
    :footer="false"
    @cancel="handleCancel"
    @update:visible="handleCancel"
  >
    <div v-if="cooperationData && cooperationData.id">
      <!-- 基本信息 -->
      <a-descriptions title="基本信息" :column="2" size="medium" bordered>
        <a-descriptions-item label="合作月份">
          {{ cooperationData.cooperationMonth }}
        </a-descriptions-item>
        <a-descriptions-item label="平台">
          <a-tag :color="getPlatformColor(cooperationData.platform)">
            {{ getPlatformName(cooperationData.platform) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="博主名称">
          {{ cooperationData.bloggerName }}
        </a-descriptions-item>
        <a-descriptions-item label="负责人">
          {{ cooperationData.responsiblePerson }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="合作价格">
          {{ cooperationData.cooperationPrice || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="工作进度">
          {{ cooperationData.workProgress || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="内容方向">
          {{ cooperationData.contentDirection || '-' }}
        </a-descriptions-item> -->
        <a-descriptions-item label="合作产品">
          {{ cooperationData.cooperationProduct || '-' }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 笔记信息 -->
      <a-descriptions title="笔记信息" :column="2" size="medium" bordered style="margin-top: 20px">
        <a-descriptions-item label="笔记ID">
          <span v-if="cooperationData.noteId" class="note-id">
            {{ cooperationData.noteId }}
          </span>
          <span v-else class="text-gray">未解析</span>
        </a-descriptions-item>
        <a-descriptions-item label="数据拉取状态">
          <a-tag :color="getFetchStatusColor(cooperationData.dataFetchStatus)">
            {{ getFetchStatusText(cooperationData.dataFetchStatus) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="笔记链接更新时间">
          {{ formatDateTime(cooperationData.noteLinkUpdateTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="定时拉取时间">
          {{ formatDateTime(cooperationData.scheduledFetchTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="数据拉取时间">
          {{ formatDateTime(cooperationData.dataFetchTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="拉取错误信息" v-if="cooperationData.dataFetchError">
          <a-tag color="red">{{ cooperationData.dataFetchError }}</a-tag>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 数据指标 -->
      <div v-if="hasDataMetrics" style="margin-top: 20px">
        <a-descriptions title="数据指标" :column="1" size="medium" bordered>
          <a-descriptions-item>
            <template #label>
              <div class="metrics-header">
                <span class="metrics-title">内容表现数据</span>
                <span class="metrics-subtitle">基于笔记发布后的实际表现</span>
              </div>
            </template>
            <div class="metrics-container">
              <!-- 核心指标 -->
              <div class="metrics-section">
                <h4 class="section-title">核心指标</h4>
                <div class="metrics-grid">
                  <div class="metric-card primary">
                    <div class="metric-icon">👁️</div>
                    <div class="metric-content">
                      <div class="metric-value">{{ formatNumber(cooperationData.impNum) }}</div>
                      <div class="metric-label">曝光数</div>
                    </div>
                  </div>
                  <div class="metric-card primary">
                    <div class="metric-icon">👍</div>
                    <div class="metric-content">
                      <div class="metric-value">{{ formatNumber(cooperationData.likeCount) }}</div>
                      <div class="metric-label">点赞数</div>
                    </div>
                  </div>
                  <div class="metric-card primary">
                    <div class="metric-icon">📖</div>
                    <div class="metric-content">
                      <div class="metric-value">{{ formatNumber(cooperationData.viewCount) }}</div>
                      <div class="metric-label">阅读数</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 互动指标 -->
              <div class="metrics-section">
                <h4 class="section-title">互动指标</h4>
                <div class="metrics-grid secondary">
                  <div class="metric-card">
                    <div class="metric-icon">💬</div>
                    <div class="metric-content">
                      <div class="metric-value">{{ formatNumber(cooperationData.commentCount) }}</div>
                      <div class="metric-label">评论数</div>
                    </div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-icon">⭐</div>
                    <div class="metric-content">
                      <div class="metric-value">{{ formatNumber(cooperationData.collectCount) }}</div>
                      <div class="metric-label">收藏数</div>
                    </div>
                  </div>
                  <div class="metric-card">
                    <div class="metric-icon">📤</div>
                    <div class="metric-content">
                      <div class="metric-value">{{ formatNumber(cooperationData.shareNum) }}</div>
                      <div class="metric-label">分享数</div>
                    </div>
                  </div>
                  <!-- <div class="metric-card">
                    <div class="metric-icon">➕</div>
                    <div class="metric-content">
                      <div class="metric-value">{{ formatNumber(cooperationData.followCnt) }}</div>
                      <div class="metric-label">关注数</div>
                    </div>
                  </div> -->
                </div>
              </div>

              <!-- 数据比率 -->
              <!-- <div class="metrics-section" v-if="hasEngagementData">
                <h4 class="section-title">互动率分析</h4>
                <div class="ratio-cards">
                  <div class="ratio-card">
                    <div class="ratio-label">点赞率</div>
                    <div class="ratio-value">{{ calculateEngagementRate('like') }}%</div>
                    <div class="ratio-desc">点赞数 / 曝光数</div>
                  </div>
                  <div class="ratio-card">
                    <div class="ratio-label">收藏率</div>
                    <div class="ratio-value">{{ calculateEngagementRate('fav') }}%</div>
                    <div class="ratio-desc">收藏数 / 曝光数</div>
                  </div>
                  <div class="ratio-card">
                    <div class="ratio-label">评论率</div>
                    <div class="ratio-value">{{ calculateEngagementRate('comment') }}%</div>
                    <div class="ratio-desc">评论数 / 曝光数</div>
                  </div>
                </div>
              </div> -->
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 链接信息 -->
      <a-descriptions title="链接信息" :column="1" size="medium" bordered style="margin-top: 20px">
        <a-descriptions-item label="达人主页">
          <a
            v-if="cooperationData.influencerHomepage"
            :href="cooperationData.influencerHomepage"
            target="_blank"
            class="link"
          >
            {{ cooperationData.influencerHomepage }}
          </a>
          <span v-else class="text-gray">-</span>
        </a-descriptions-item>
        <a-descriptions-item label="合作笔记链接">
          <a v-if="cooperationData.publishLink" :href="cooperationData.publishLink" target="_blank" class="link">
            {{ cooperationData.publishLink }}
          </a>
          <span v-else class="text-gray">-</span>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 时间信息 -->
      <a-descriptions title="时间信息" :column="2" size="medium" bordered style="margin-top: 20px">
        <a-descriptions-item label="约定发布时间">
          {{ cooperationData.scheduledPublishTime }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ formatDateTime(cooperationData.createdAt) }}
        </a-descriptions-item>
        <a-descriptions-item label="更新时间">
          {{ formatDateTime(cooperationData.updatedAt) }}
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <div v-else class="empty-state">
      <a-empty description="暂无数据" />
    </div>
  </a-modal>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  cooperationData: {
    type: Object,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits(['cancel']);

// 计算属性
const hasDataMetrics = computed(() => {
  const data = props.cooperationData;
  return data.impNum || data.likeNum || data.favNum || data.cmtNum || data.readNum || data.shareNum || data.followCnt;
});

const hasEngagementData = computed(() => {
  const data = props.cooperationData;
  return data.impNum && data.impNum > 0;
});

// 取消
const handleCancel = () => {
  emit('cancel');
};

// 工具函数
const getPlatformColor = platform => {
  const colors = {
    xiaohongshu: 'red',
    douyin: 'blue',
    weibo: 'orange',
    bilibili: 'cyan'
  };
  return colors[platform] || 'gray';
};

const getPlatformName = platform => {
  const names = {
    xiaohongshu: '小红书',
    douyin: '抖音',
    weibo: '微博',
    bilibili: 'B站'
  };
  return names[platform] || platform;
};

const getFetchStatusColor = status => {
  const colors = {
    pending: 'gray',
    fetching: 'blue',
    success: 'green',
    failed: 'red'
  };
  return colors[status] || 'gray';
};

const getFetchStatusText = status => {
  const texts = {
    pending: '待拉取',
    fetching: '拉取中',
    success: '成功',
    failed: '失败'
  };
  return texts[status] || status;
};

const formatDateTime = dateTime => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

const formatNumber = num => {
  if (!num || num === 0) return '0';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  }
  return num.toString();
};

const calculateEngagementRate = type => {
  const data = props.cooperationData;
  const impNum = data.impNum || 0;

  if (impNum === 0) return '0.0';

  let targetNum = 0;
  switch (type) {
    case 'like':
      targetNum = data.likeNum || 0;
      break;
    case 'fav':
      targetNum = data.favNum || 0;
      break;
    case 'comment':
      targetNum = data.cmtNum || 0;
      break;
    default:
      return '0.0';
  }

  return ((targetNum / impNum) * 100).toFixed(2);
};
</script>

<style scoped>
.note-id {
  font-family: monospace;
  font-size: 12px;
  background: #f2f3f5;
  padding: 2px 4px;
  border-radius: 4px;
}

.text-gray {
  color: #86909c;
}

.link {
  color: #165dff;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

/* 数据指标样式 */
.metrics-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metrics-title {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
}

.metrics-subtitle {
  font-size: 12px;
  color: #86909c;
}

.metrics-container {
  padding: 16px 0;
}

.metrics-section {
  margin-bottom: 24px;
}

.metrics-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f2f3f5;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metrics-grid.secondary {
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
  transition: all 0.2s ease;
}

.metric-card:hover {
  background: #f2f3f5;
  border-color: #c9cdd4;
}

.metric-card.primary {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: #0ea5e9;
}

.metric-card.primary:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
}

.metric-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-value {
  font-size: 20px;
  font-weight: 700;
  color: #1d2129;
  line-height: 1.2;
}

.metric-label {
  font-size: 12px;
  color: #86909c;
  margin-top: 2px;
}

.ratio-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.ratio-card {
  text-align: center;
  padding: 16px 12px;
  background: #fff;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.ratio-card:hover {
  border-color: #165dff;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
}

.ratio-label {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 4px;
}

.ratio-value {
  font-size: 18px;
  font-weight: 700;
  color: #165dff;
  margin-bottom: 4px;
}

.ratio-desc {
  font-size: 10px;
  color: #c9cdd4;
}
</style>
