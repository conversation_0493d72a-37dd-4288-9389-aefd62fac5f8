<template>
  <div class="local-dictionary-management">
    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <button class="btn btn-primary" @click="showCreateDialog = true">新增字典项</button>

        <button class="btn btn-success" @click="showBatchCreateDialog = true">批量导入</button>
      </div>

      <div class="toolbar-right">
        <button class="btn btn-secondary" @click="loadDictionaries" :disabled="loading">刷新数据</button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-group">
        <label>分类：</label>
        <select v-model="filters.category" @change="loadDictionaries">
          <option value="">全部分类</option>
          <option v-for="category in categories" :key="category" :value="category">
            {{ category }}
          </option>
        </select>
      </div>

      <div class="filter-group">
        <label>状态：</label>
        <select v-model="filters.status" @change="loadDictionaries">
          <option value="">全部状态</option>
          <option value="active">启用</option>
          <option value="inactive">禁用</option>
        </select>
      </div>

      <div class="filter-group">
        <label>关键词：</label>
        <input type="text" v-model="filters.keyword" @input="debounceSearch" placeholder="搜索字典键值或标签" />
      </div>
    </div>

    <!-- 字典数据表格 -->
    <div class="table-container">
      <table class="data-table" v-if="dictionaries.length > 0">
        <thead>
          <tr>
            <th>分类</th>
            <th>字典键值</th>
            <th>字典标签</th>
            <th>字典值</th>
            <th>排序</th>
            <th>状态</th>
            <th>创建时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="dict in dictionaries" :key="dict.id">
            <td>
              <span class="category-tag">{{ dict.category }}</span>
            </td>
            <td>
              <code>{{ dict.dictKey }}</code>
            </td>
            <td>{{ dict.dictLabel }}</td>
            <td>{{ dict.dictValue || '-' }}</td>
            <td>{{ dict.sortOrder }}</td>
            <td>
              <span :class="['status-badge', dict.status]">
                {{ dict.status === 'active' ? '启用' : '禁用' }}
              </span>
            </td>
            <td>{{ formatDateTime(dict.createdAt) }}</td>
            <td>
              <div class="action-buttons">
                <button class="btn-small btn-outline" @click="editDictionary(dict)">编辑</button>
                <button class="btn-small btn-danger" @click="deleteDictionary(dict)">删除</button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-else-if="!loading" class="empty-state">
        <p>暂无字典数据</p>
        <button class="btn btn-primary" @click="showCreateDialog = true">创建第一个字典项</button>
      </div>

      <div v-if="loading" class="loading-state">
        <p>加载中...</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="pagination.total > 0">
      <button class="btn btn-outline" @click="changePage(pagination.current - 1)" :disabled="pagination.current <= 1">
        上一页
      </button>

      <span class="pagination-info">
        第 {{ pagination.current }} 页，共 {{ pagination.totalPages }} 页 （总计 {{ pagination.total }} 条记录）
      </span>

      <button
        class="btn btn-outline"
        @click="changePage(pagination.current + 1)"
        :disabled="pagination.current >= pagination.totalPages"
      >
        下一页
      </button>
    </div>

    <!-- 创建/编辑字典项对话框 -->
    <div v-if="showCreateDialog || showEditDialog" class="dialog-overlay" @click="closeDialogs">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>{{ showEditDialog ? '编辑字典项' : '新增字典项' }}</h3>
          <button class="close-btn" @click="closeDialogs">×</button>
        </div>

        <div class="dialog-body">
          <form @submit.prevent="saveDictionary">
            <div class="form-group">
              <label>分类 *</label>
              <input type="text" v-model="formData.category" required placeholder="如：cooperation_form" />
            </div>

            <div class="form-group">
              <label>字典键值 *</label>
              <input type="text" v-model="formData.dictKey" required placeholder="如：image_text" />
            </div>

            <div class="form-group">
              <label>字典标签 *</label>
              <input type="text" v-model="formData.dictLabel" required placeholder="如：图文" />
            </div>

            <div class="form-group">
              <label>字典值</label>
              <input type="text" v-model="formData.dictValue" placeholder="可选的额外数据" />
            </div>

            <div class="form-group">
              <label>排序顺序</label>
              <input type="number" v-model="formData.sortOrder" min="0" placeholder="0" />
            </div>

            <div class="form-group">
              <label>状态</label>
              <select v-model="formData.status">
                <option value="active">启用</option>
                <option value="inactive">禁用</option>
              </select>
            </div>

            <div class="form-group">
              <label>描述</label>
              <textarea v-model="formData.description" rows="3" placeholder="可选的描述信息"></textarea>
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-outline" @click="closeDialogs">取消</button>
              <button type="submit" class="btn btn-primary" :disabled="saving">
                {{ saving ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 批量导入对话框 -->
    <div v-if="showBatchCreateDialog" class="dialog-overlay" @click="closeBatchDialog">
      <div class="dialog large" @click.stop>
        <div class="dialog-header">
          <h3>批量导入字典项</h3>
          <button class="close-btn" @click="closeBatchDialog">×</button>
        </div>

        <div class="dialog-body">
          <div class="batch-import-help">
            <p>请按以下格式输入字典项数据，每行一个：</p>
            <code>分类,键值,标签,值,排序,状态,描述</code>
            <p>示例：</p>
            <code>cooperation_form,image_text,图文,image_text,1,active,图文合作形式</code>
          </div>

          <div class="form-group">
            <label>批量数据</label>
            <textarea v-model="batchData" rows="10" placeholder="请输入批量数据..."></textarea>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-outline" @click="closeBatchDialog">取消</button>
            <button type="button" class="btn btn-primary" @click="batchImport" :disabled="saving">
              {{ saving ? '导入中...' : '开始导入' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作结果提示 -->
    <div v-if="message" :class="['message', message.type]">
      {{ message.text }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { dictionaryAPI } from '@/services/api';

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const dictionaries = ref([]);
const categories = ref([]);
const message = ref(null);

const showCreateDialog = ref(false);
const showEditDialog = ref(false);
const showBatchCreateDialog = ref(false);
const batchData = ref('');

const filters = reactive({
  category: '',
  status: '',
  keyword: ''
});

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0
});

const formData = reactive({
  id: null,
  category: '',
  dictKey: '',
  dictLabel: '',
  dictValue: '',
  sortOrder: 0,
  status: 'active',
  description: ''
});

// 方法
const showMessage = (text, type = 'info') => {
  message.value = { text, type };
  setTimeout(() => {
    message.value = null;
  }, 5000);
};

const loadCategories = async () => {
  try {
    const response = await dictionaryAPI.getCategories();
    if (response.data.success) {
      categories.value = response.data.data;
    }
  } catch (error) {
    console.error('获取分类失败:', error);
  }
};

const loadDictionaries = async () => {
  loading.value = true;
  try {
    const response = await dictionaryAPI.getDictionaryList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      category: filters.category,
      status: filters.status,
      keyword: filters.keyword
    });

    if (response.success) {
      dictionaries.value = response.data;
      pagination.current = response.pagination.current;
      pagination.pageSize = response.pagination.pageSize;
      pagination.total = response.pagination.total;
      pagination.totalPages = response.pagination.totalPages;
    }
  } catch (error) {
    console.error('加载字典数据失败:', error);
    showMessage(`加载字典数据失败: ${error.message}`, 'error');
  } finally {
    loading.value = false;
  }
};

const changePage = page => {
  if (page >= 1 && page <= pagination.totalPages) {
    pagination.current = page;
    loadDictionaries();
  }
};

let searchTimeout = null;
const debounceSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    pagination.current = 1;
    loadDictionaries();
  }, 500);
};

const resetFormData = () => {
  formData.id = null;
  formData.category = '';
  formData.dictKey = '';
  formData.dictLabel = '';
  formData.dictValue = '';
  formData.sortOrder = 0;
  formData.status = 'active';
  formData.description = '';
};

const editDictionary = dict => {
  formData.id = dict.id;
  formData.category = dict.category;
  formData.dictKey = dict.dictKey;
  formData.dictLabel = dict.dictLabel;
  formData.dictValue = dict.dictValue || '';
  formData.sortOrder = dict.sortOrder;
  formData.status = dict.status;
  formData.description = dict.description || '';
  showEditDialog.value = true;
};

const saveDictionary = async () => {
  saving.value = true;
  try {
    if (showEditDialog.value) {
      // 更新
      const response = await dictionaryAPI.updateDictionary(formData.id, formData);
      if (response.data.success) {
        showMessage('字典项更新成功', 'success');
        closeDialogs();
        loadDictionaries();
      }
    } else {
      // 创建
      const response = await dictionaryAPI.createDictionary(formData);
      if (response.data.success) {
        showMessage('字典项创建成功', 'success');
        closeDialogs();
        loadDictionaries();
        loadCategories();
      }
    }
  } catch (error) {
    console.error('保存字典项失败:', error);
    showMessage(`保存失败: ${error.message}`, 'error');
  } finally {
    saving.value = false;
  }
};

const deleteDictionary = async dict => {
  if (!confirm(`确定要删除字典项 "${dict.dictLabel}" 吗？`)) {
    return;
  }

  try {
    const response = await dictionaryAPI.deleteDictionary(dict.id);
    if (response.data.success) {
      showMessage('字典项删除成功', 'success');
      loadDictionaries();
    }
  } catch (error) {
    console.error('删除字典项失败:', error);
    showMessage(`删除失败: ${error.message}`, 'error');
  }
};

const batchImport = async () => {
  if (!batchData.value.trim()) {
    showMessage('请输入批量数据', 'error');
    return;
  }

  saving.value = true;
  try {
    const lines = batchData.value.trim().split('\n');
    const dictionaries = [];

    for (const line of lines) {
      if (!line.trim()) continue;

      const parts = line.split(',').map(part => part.trim());
      if (parts.length < 3) {
        showMessage(`数据格式错误: ${line}`, 'error');
        saving.value = false;
        return;
      }

      dictionaries.push({
        category: parts[0],
        dictKey: parts[1],
        dictLabel: parts[2],
        dictValue: parts[3] || '',
        sortOrder: parseInt(parts[4]) || 0,
        status: parts[5] || 'active',
        description: parts[6] || ''
      });
    }

    const response = await dictionaryAPI.batchCreateDictionaries({ dictionaries });
    if (response.data.success) {
      showMessage(response.data.message, 'success');
      closeBatchDialog();
      loadDictionaries();
      loadCategories();
    }
  } catch (error) {
    console.error('批量导入失败:', error);
    showMessage(`批量导入失败: ${error.message}`, 'error');
  } finally {
    saving.value = false;
  }
};

const closeDialogs = () => {
  showCreateDialog.value = false;
  showEditDialog.value = false;
  resetFormData();
};

const closeBatchDialog = () => {
  showBatchCreateDialog.value = false;
  batchData.value = '';
};

const formatDateTime = dateTime => {
  if (!dateTime) return '';
  const date = new Date(dateTime);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};

// 生命周期
onMounted(async () => {
  await loadCategories();
  await loadDictionaries();
});
</script>

<style scoped>
.local-dictionary-management {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  color: white;
}

.btn-success {
  background: #52c41a;
  border-color: #52c41a;
  color: white;
}

.btn-success:hover {
  background: #73d13d;
  border-color: #73d13d;
  color: white;
}

.btn-outline {
  background: white;
  border-color: #d9d9d9;
  color: #333;
}

.btn-secondary {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #666;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-danger {
  background: #ff4d4f;
  border-color: #ff4d4f;
  color: white;
}

.btn-danger:hover {
  background: #ff7875;
  border-color: #ff7875;
  color: white;
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.filter-group select,
.filter-group input {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.data-table th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.data-table td {
  font-size: 14px;
  color: #666;
}

.data-table code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

.category-tag {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #f6ffed;
  color: #52c41a;
}

.status-badge.inactive {
  background: #f5f5f5;
  color: #999;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.empty-state,
.loading-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 16px;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.dialog.large {
  max-width: 800px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.dialog-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group textarea {
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

.batch-import-help {
  background: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.batch-import-help p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.batch-import-help code {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message.success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.message.error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.message.info {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #1890ff;
}
</style>
