<template>
  <div class="data-fetch-task-panel">
    <!-- 页面标题和操作区 -->
    <div class="panel-header">
      <div class="header-left">
        <h1 class="page-title">📋 笔记数据拉取看板</h1>
        <p class="page-subtitle">实时监控任务执行状态</p>
      </div>
      <div class="header-actions">
        <a-button :type="autoRefreshEnabled ? 'primary' : 'outline'" @click="toggleAutoRefresh" size="small">
          <template #icon><icon-sync /></template>
          {{ autoRefreshEnabled ? '自动刷新' : '手动刷新' }}
        </a-button>
        <a-button type="primary" @click="refreshTasks" :loading="loading">
          <template #icon><icon-refresh /></template>
          刷新
        </a-button>
        <a-button type="outline" @click="triggerBatchFetch" :loading="batchFetching">
          <template #icon><icon-play-arrow /></template>
          批量拉取
        </a-button>
      </div>
    </div>

    <!-- 核心统计指标 -->
    <div class="core-stats" v-if="stats">
      <div class="stat-item stat-pending">
        <div class="stat-icon">
          <icon-clock-circle />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pending }}</div>
          <div class="stat-label">待执行</div>
          <div class="stat-detail">今日可执行 {{ stats.todayExecutable || 0 }} 个</div>
        </div>
      </div>
      <div class="stat-item stat-success">
        <div class="stat-icon">
          <icon-check-circle />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.success }}</div>
          <div class="stat-label">已完成</div>
          <div class="stat-detail">今日已执行 {{ stats.todayExecuted || 0 }} 个</div>
        </div>
      </div>
      <div class="stat-item stat-failed">
        <div class="stat-icon">
          <icon-close-circle />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.failed }}</div>
          <div class="stat-label">失败</div>
          <div class="stat-detail">成功率 {{ calculateSuccessRate() }}%</div>
        </div>
      </div>
    </div>

    <!-- 详细任务列表（可折叠） -->
    <div class="detailed-tasks">
      <a-card>
        <template #title>
          <div class="detailed-header">
            <span>📋 详细任务列表</span>
            <a-button type="text" @click="showDetailedTasks = !showDetailedTasks">
              {{ showDetailedTasks ? '收起' : '展开' }}
              <template #icon>
                <icon-down v-if="!showDetailedTasks" />
                <icon-up v-else />
              </template>
            </a-button>
          </div>
        </template>

        <div v-show="showDetailedTasks">
          <!-- 筛选和搜索 -->
          <div class="filter-controls">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="拉取状态">
                  <a-select v-model="filters.status" placeholder="选择状态" allow-clear @change="handleFilterChange">
                    <a-option value="pending">待拉取</a-option>
                    <a-option value="success">已完成</a-option>
                    <a-option value="failed">失败</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="客户名称">
                  <a-input
                    v-model="filters.customerName"
                    placeholder="输入客户名称搜索"
                    allow-clear
                    @press-enter="handleFilterChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="登记日期">
                  <a-range-picker v-model="dateRange" @change="handleDateRangeChange" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label=" ">
                  <a-space>
                    <a-button @click="handleFilterChange">
                      <template #icon><icon-search /></template>
                      搜索
                    </a-button>
                    <a-button @click="resetFilters">
                      <template #icon><icon-refresh /></template>
                      重置
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 批量操作 -->
          <div class="batch-actions" v-if="selectedRowKeys.length > 0">
            <a-alert type="info" :message="`已选择 ${selectedRowKeys.length} 条记录`" show-icon closable>
              <template #action>
                <a-space>
                  <a-button type="primary" size="small" @click="batchFetchSelected" :loading="batchFetching">
                    批量拉取
                  </a-button>
                  <a-button size="small" @click="clearSelection"> 取消选择 </a-button>
                </a-space>
              </template>
            </a-alert>
          </div>

          <!-- 任务列表表格 -->
          <div class="task-table">
            <a-table
              :columns="columns"
              :data="tasks"
              :loading="loading"
              :pagination="pagination"
              :row-selection="rowSelection"
              :scroll="{ x: 'max-content' }"
              class="layout-auto"
              @page-change="handlePageChange"
              @page-size-change="handlePageSizeChange"
              row-key="id"
            >
              <!-- 状态列 -->
              <template #status="{ record }">
                <a-tag :color="getStatusColor(record.dataFetchStatus)">
                  {{ getStatusText(record.dataFetchStatus) }}
                </a-tag>
              </template>

              <!-- 发布链接列 -->
              <template #publishLink="{ record }">
                <a-tooltip :content="record.publishLink">
                  <a :href="record.publishLink" target="_blank" class="link-text" v-if="record.publishLink">
                    {{ truncateText(record.publishLink, 30) }}
                  </a>
                  <span v-else class="text-placeholder">-</span>
                </a-tooltip>
              </template>

              <!-- 数据指标列 -->
              <template #metrics="{ record }">
                <div class="metrics-cell" v-if="record.dataFetchStatus === 'success'">
                  <div class="metric-item">
                    <span class="metric-label">曝光:</span>
                    <span class="metric-value">{{ formatNumber(record.impNum) }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">阅读数:</span>
                    <span class="metric-value">{{ formatNumber(record.viewCount) }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">点赞:</span>
                    <span class="metric-value">{{ formatNumber(record.likeCount) }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">收藏:</span>
                    <span class="metric-value">{{ formatNumber(record.collectCount) }}</span>
                  </div>
                </div>
                <span v-else class="text-placeholder">-</span>
              </template>

              <!-- 拉取时间列 -->
              <template #fetchTime="{ record }">
                <span v-if="record.dataFetchTime">
                  {{ formatDateTime(record.dataFetchTime) }}
                </span>
                <span v-else class="text-placeholder">-</span>
              </template>

              <!-- 操作列 -->
              <template #actions="{ record }">
                <a-space>
                  <a-button
                    type="text"
                    size="small"
                    @click="fetchSingleTask(record)"
                    :loading="record.fetching"
                    :disabled="!record.noteId || record.dataFetchStatus === 'fetching'"
                  >
                    <template #icon><icon-download /></template>
                    拉取
                  </a-button>
                  <a-button type="text" size="small" @click="viewTaskDetail(record)">
                    <template #icon><icon-eye /></template>
                    详情
                  </a-button>
                  <a-button
                    type="text"
                    size="small"
                    @click="retryTask(record)"
                    v-if="record.dataFetchStatus === 'failed'"
                  >
                    <template #icon><icon-refresh /></template>
                    重试
                  </a-button>
                </a-space>
              </template>
            </a-table>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 任务看板 -->
    <div class="task-board">
      <!-- 待执行任务 -->
      <div class="board-section pending-section">
        <div class="section-header">
          <div class="header-left">
            <h3 class="section-title">
              <span class="title-icon">⏳</span>
              待执行队列
              <span class="task-count">{{ pendingTasks.length }}</span>
            </h3>
            <div class="header-subtitle">
              <span v-if="pendingTasks.length > 0">
                共 {{ pendingTasks.length }} 个任务待执行
                <span v-if="pendingTasks.filter(task => isHighPriority(task)).length > 0" class="priority-count">
                  · {{ pendingTasks.filter(task => isHighPriority(task)).length }} 个高优先级
                </span>
              </span>
              <span v-else>暂无待执行任务</span>
            </div>
          </div>
          <div class="header-actions">
            <a-button v-if="pendingTasks.length > 0" type="primary" @click="batchFetchPending" :loading="batchFetching">
              <template #icon><icon-play-arrow /></template>
              批量执行全部
            </a-button>
          </div>
        </div>
        <div class="task-cards-container">
          <div v-if="pendingTasks.length === 0" class="empty-state">
            <div class="empty-icon">
              <icon-check-circle />
            </div>
            <div class="empty-text">暂无待执行任务</div>
            <div class="empty-subtitle">所有任务都已完成或正在处理中</div>
          </div>
          <div v-else class="task-cards-grid">
            <div
              v-for="task in pendingTasks.slice(0, 12)"
              :key="task.id"
              class="task-card pending-card"
              :class="{ 'high-priority': isHighPriority(task) }"
            >
              <div class="task-header">
                <div class="task-title">
                  <span class="customer-name">{{ task.customerName }}</span>
                  <span class="platform-badge">{{ task.platform || '未知平台' }}</span>
                </div>
                <div class="task-status">
                  <span class="priority-indicator" v-if="isHighPriority(task)" title="高优先级任务">
                    <icon-fire />
                  </span>
                  <span class="waiting-duration">{{ getWaitingDuration(task.dataRegistrationDate) }}</span>
                </div>
              </div>
              <div class="task-content">
                <div class="task-info-compact">
                  <div class="info-row">
                    <icon-file class="info-icon" />
                    <span class="info-text">{{ task.noteId || '未设置笔记ID' }}</span>
                  </div>
                  <div class="info-row">
                    <icon-calendar class="info-icon" />
                    <span class="info-text">{{ task.dataRegistrationDate }}</span>
                  </div>
                  <div class="info-row" v-if="task.publishLink">
                    <icon-link class="info-icon" />
                    <a :href="task.publishLink" target="_blank" class="info-link">
                      {{ truncateText(task.publishLink, 30) }}
                    </a>
                  </div>
                </div>
              </div>
              <div class="task-actions">
                <a-button
                  type="primary"
                  size="small"
                  @click="fetchSingleTask(task)"
                  :loading="task.fetching"
                  :disabled="!task.noteId"
                  class="action-btn-primary"
                >
                  <template #icon><icon-play-arrow /></template>
                  执行
                </a-button>
                <a-button type="text" size="small" @click="viewTaskDetail(task)" class="action-btn-secondary">
                  <template #icon><icon-eye /></template>
                  详情
                </a-button>
              </div>
            </div>
          </div>
          <div v-if="pendingTasks.length > 12" class="more-tasks">
            <a-button type="text" @click="showAllTasks" class="more-tasks-btn">
              <template #icon><icon-more /></template>
              查看全部 {{ pendingTasks.length }} 个待执行任务
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务详情弹窗 -->
    <a-modal v-model:visible="detailModalVisible" title="任务详情" width="800px" :footer="false">
      <div v-if="selectedTask">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="客户名称">
            {{ selectedTask.customerName }}
          </a-descriptions-item>
          <a-descriptions-item label="笔记ID">
            {{ selectedTask.noteId }}
          </a-descriptions-item>
          <a-descriptions-item label="平台">
            {{ selectedTask.platform }}
          </a-descriptions-item>
          <a-descriptions-item label="拉取状态">
            <a-tag :color="getStatusColor(selectedTask.dataFetchStatus)">
              {{ getStatusText(selectedTask.dataFetchStatus) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="数据登记日期">
            {{ selectedTask.dataRegistrationDate }}
          </a-descriptions-item>
          <a-descriptions-item label="拉取时间">
            {{ selectedTask.dataFetchTime ? formatDateTime(selectedTask.dataFetchTime) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="发布链接" :span="2">
            <a :href="selectedTask.publishLink" target="_blank" v-if="selectedTask.publishLink">
              {{ selectedTask.publishLink }}
            </a>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="错误信息" :span="2" v-if="selectedTask.dataFetchError">
            <a-alert type="error" :message="selectedTask.dataFetchError" />
          </a-descriptions-item>
        </a-descriptions>

        <!-- 数据指标 -->
        <div v-if="selectedTask.dataFetchStatus === 'success'" style="margin-top: 20px">
          <h4>📊 数据指标</h4>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic title="阅读数" :value="selectedTask.viewCount || 0" />
            </a-col>

            <a-col :span="8">
              <a-statistic title="点赞数" :value="selectedTask.likeCount || 0" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="收藏数" :value="selectedTask.collectCount || 0" />
            </a-col>
          </a-row>
          <a-row :gutter="16" style="margin-top: 16px">
            <a-col :span="8">
              <a-statistic title="评论数" :value="selectedTask.commentCount || 0" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="分享数" :value="selectedTask.shareNum || 0" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="曝光数" :value="selectedTask.impNum || 0" />
            </a-col>
          </a-row>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import {
  IconRefresh,
  IconPlayArrow,
  IconSearch,
  IconDownload,
  IconEye,
  IconSync,
  IconDown,
  IconUp,
  IconLoading,
  IconClockCircle,
  IconCheckCircle,
  IconCloseCircle,
  IconMoon,
  IconFile,
  IconCalendar,
  IconLink,
  IconUser,
  IconFire,
  IconMore
} from '@arco-design/web-vue/es/icon';
import { cooperationAPI } from '../services/api';

// 响应式数据
const loading = ref(false);
const batchFetching = ref(false);
const tasks = ref([]);
const stats = ref(null);
const selectedRowKeys = ref([]);
const detailModalVisible = ref(false);
const selectedTask = ref(null);
const dateRange = ref([]);
const showDetailedTasks = ref(true);

// 计算属性 - 任务分类
const pendingTasks = computed(() => {
  return tasks.value.filter(task => task.dataFetchStatus === 'pending');
});

// 筛选条件
const filters = reactive({
  status: '',
  customerName: '',
  dateFrom: '',
  dateTo: ''
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 表格列配置
const columns = [
  {
    title: '客户名称',
    dataIndex: 'customerName',
    width: 150,
    ellipsis: true
  },
  {
    title: '发布链接',
    dataIndex: 'publishLink',
    slotName: 'publishLink',
    width: 200,
    ellipsis: true
  },
  {
    title: '笔记ID',
    dataIndex: 'noteId',
    width: 120,
    ellipsis: true
  },
  {
    title: '数据登记日期',
    dataIndex: 'dataRegistrationDate',
    sortable: {
      sortDirections: ['ascend', 'descend']
    }
  },
  {
    title: '拉取状态',
    dataIndex: 'dataFetchStatus',
    slotName: 'status',
    width: 100
  },
  {
    title: '数据指标',
    slotName: 'metrics',
    width: 180
  },
  {
    title: '拉取时间',
    dataIndex: 'dataFetchTime',
    slotName: 'fetchTime'
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 150,
    fixed: 'right'
  }
];

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  selectedRowKeys: selectedRowKeys,
  onSelect: rowKeys => {
    selectedRowKeys.value = rowKeys;
  },
  onSelectAll: selected => {
    if (selected) {
      selectedRowKeys.value = tasks.value.map(task => task.id);
    } else {
      selectedRowKeys.value = [];
    }
  }
});

// 获取任务列表
const fetchTasks = async (silent = false) => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...filters
    };

    const response = await cooperationAPI.getDataFetchTasks(params);

    if (response.success) {
      const { tasks: taskList, pagination: paginationData } = response.data;
      tasks.value = taskList;
      pagination.total = paginationData.totalCount;
    } else {
      throw new Error(response.message || '获取任务列表失败');
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
    if (!silent) {
      const errorMessage = error.response?.data?.message || error.message || '获取任务列表失败';
      Message.error(errorMessage);
    }
  } finally {
    loading.value = false;
  }
};

// 获取统计信息
const fetchStats = async (silent = true) => {
  try {
    const response = await cooperationAPI.getTaskStats();

    if (response.success) {
      stats.value = response.data;
    } else {
      throw new Error(response.message || '获取统计信息失败');
    }
  } catch (error) {
    console.error('获取统计信息失败:', error);
    if (!silent) {
      const errorMessage = error.response?.data?.message || error.message || '获取统计信息失败';
      Message.error(errorMessage);
    }
  }
};

// 刷新任务
const refreshTasks = async () => {
  await Promise.all([fetchTasks(), fetchStats()]);
};

// 筛选变化处理
const handleFilterChange = () => {
  pagination.current = 1;
  fetchTasks();
};

// 日期范围变化处理
const handleDateRangeChange = dates => {
  if (dates && dates.length === 2) {
    filters.dateFrom = dates[0];
    filters.dateTo = dates[1];
  } else {
    filters.dateFrom = '';
    filters.dateTo = '';
  }
  handleFilterChange();
};

// 重置筛选
const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = '';
  });
  dateRange.value = [];
  handleFilterChange();
};

// 分页变化处理
const handlePageChange = page => {
  pagination.current = page;
  fetchTasks();
};

const handlePageSizeChange = pageSize => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchTasks();
};

// 单个任务拉取
const fetchSingleTask = async task => {
  try {
    task.fetching = true;

    // 检查任务状态
    if (!task.noteId) {
      Message.warning('该记录没有笔记ID，无法拉取数据');
      return;
    }

    if (task.dataFetchStatus === 'fetching') {
      Message.warning('该任务正在拉取中，请稍候');
      return;
    }

    const response = await cooperationAPI.fetchNoteData(task.id);

    if (response.success) {
      Message.success('笔记数据拉取已启动，请稍候查看结果');
    } else {
      Message.error(`拉取失败: ${response.message || '未知错误'}`);
    }

    // 延迟刷新，给后端处理时间
    setTimeout(() => {
      refreshTasks();
    }, 2000);
  } catch (error) {
    console.error('拉取任务失败:', error);
    const errorMessage = error.response?.data?.message || error.message || '拉取任务失败';
    Message.error(errorMessage);
  } finally {
    task.fetching = false;
  }
};

// 批量拉取选中的任务
const batchFetchSelected = async () => {
  if (selectedRowKeys.value.length === 0) {
    Message.warning('请先选择要拉取的任务');
    return;
  }

  // 确认对话框
  Modal.confirm({
    title: '确认批量拉取',
    content: `确定要拉取选中的 ${selectedRowKeys.value.length} 个任务吗？`,
    onOk: async () => {
      try {
        batchFetching.value = true;
        const response = await cooperationAPI.batchFetchNoteData(selectedRowKeys.value);

        if (response.success) {
          const { data } = response;
          Message.success({
            content: `批量拉取已启动！成功: ${data.success}, 失败: ${data.failed}`,
            duration: 5000
          });

          // 如果有失败的任务，显示详细信息
          if (data.failed > 0) {
            const failedTasks = data.details.filter(detail => !detail.success);
            console.warn('批量拉取失败的任务:', failedTasks);
          }
        } else {
          Message.error(`批量拉取失败: ${response.message || '未知错误'}`);
        }

        // 清空选择
        selectedRowKeys.value = [];

        // 延迟刷新
        setTimeout(() => {
          refreshTasks();
        }, 3000);
      } catch (error) {
        console.error('批量拉取失败:', error);
        const errorMessage = error.response?.data?.message || error.message || '批量拉取失败';
        Message.error(errorMessage);
      } finally {
        batchFetching.value = false;
      }
    }
  });
};

// 手动触发批量拉取
const triggerBatchFetch = async () => {
  try {
    batchFetching.value = true;
    await cooperationAPI.triggerDataFetch();
    Message.success('批量拉取任务已触发');

    setTimeout(() => {
      refreshTasks();
    }, 3000);
  } catch (error) {
    console.error('触发批量拉取失败:', error);
    Message.error('触发批量拉取失败');
  } finally {
    batchFetching.value = false;
  }
};

// 清空选择
const clearSelection = () => {
  selectedRowKeys.value = [];
};

// 查看任务详情
const viewTaskDetail = task => {
  selectedTask.value = task;
  detailModalVisible.value = true;
};

// 重试任务
const retryTask = async task => {
  await fetchSingleTask(task);
};

// 批量执行待处理任务
const batchFetchPending = async () => {
  if (pendingTasks.value.length === 0) {
    Message.warning('暂无待执行任务');
    return;
  }

  const pendingIds = pendingTasks.value.map(task => task.id);

  // 确认对话框
  Modal.confirm({
    title: '确认批量执行',
    content: `确定要执行所有 ${pendingIds.length} 个待执行任务吗？`,
    onOk: async () => {
      try {
        batchFetching.value = true;
        const response = await cooperationAPI.batchFetchNoteData(pendingIds);

        if (response.success) {
          const { data } = response;
          Message.success({
            content: `批量执行已启动！成功: ${data.success}, 失败: ${data.failed}`,
            duration: 5000
          });

          // 如果有失败的任务，显示详细信息
          if (data.failed > 0) {
            const failedTasks = data.details.filter(detail => !detail.success);
            console.warn('批量执行失败的任务:', failedTasks);
          }
        } else {
          Message.error(`批量执行失败: ${response.message || '未知错误'}`);
        }

        // 延迟刷新
        setTimeout(() => {
          refreshTasks();
        }, 3000);
      } catch (error) {
        console.error('批量执行失败:', error);
        const errorMessage = error.response?.data?.message || error.message || '批量执行失败';
        Message.error(errorMessage);
      } finally {
        batchFetching.value = false;
      }
    }
  });
};

// 显示所有任务（展开详细列表）
const showAllTasks = () => {
  showDetailedTasks.value = true;
};

// 工具函数
const getStatusColor = status => {
  const colors = {
    pending: 'orange',
    fetching: 'blue',
    success: 'green',
    failed: 'red'
  };
  return colors[status] || 'gray';
};

const getStatusText = status => {
  const texts = {
    pending: '待拉取',
    fetching: '拉取中',
    success: '已完成',
    failed: '失败'
  };
  return texts[status] || '未知';
};

const truncateText = (text, length) => {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + '...' : text;
};

const formatNumber = num => {
  if (num === null || num === undefined) return '-';
  return num.toLocaleString();
};

const formatDateTime = dateTime => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 计算成功率
const calculateSuccessRate = () => {
  if (!stats.value || stats.value.success + stats.value.failed === 0) return 0;
  const total = stats.value.success + stats.value.failed;
  return Math.round((stats.value.success / total) * 100);
};

// 判断是否为高优先级任务（等待时间超过3天）
const isHighPriority = task => {
  if (!task.dataRegistrationDate) return false;
  const registrationDate = new Date(task.dataRegistrationDate);
  const now = new Date();
  const diffDays = Math.floor((now - registrationDate) / (1000 * 60 * 60 * 24));
  return diffDays >= 3;
};

// 获取等待时长
const getWaitingDuration = registrationDate => {
  if (!registrationDate) return '-';
  const regDate = new Date(registrationDate);
  const now = new Date();
  const diffMs = now - regDate;

  // 如果登记日期是未来日期，显示"未到期"
  if (diffMs < 0) {
    const absDiffMs = Math.abs(diffMs);
    const diffDays = Math.floor(absDiffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((absDiffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (diffDays > 0) {
      return diffHours > 0 ? `${diffDays}天${diffHours}小时后到期` : `${diffDays}天后到期`;
    } else if (diffHours > 0) {
      const diffMinutes = Math.floor((absDiffMs % (1000 * 60 * 60)) / (1000 * 60));
      return diffMinutes > 0 ? `${diffHours}小时${diffMinutes}分钟后到期` : `${diffHours}小时后到期`;
    } else {
      const diffMinutes = Math.floor((absDiffMs % (1000 * 60 * 60)) / (1000 * 60));
      return `${diffMinutes}分钟后到期`;
    }
  }

  // 正常的等待时长计算
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (diffDays > 0) {
    return diffHours > 0 ? `${diffDays}天${diffHours}小时` : `${diffDays}天`;
  } else if (diffHours > 0) {
    return diffMinutes > 0 ? `${diffHours}小时${diffMinutes}分钟` : `${diffHours}小时`;
  } else {
    return `${diffMinutes}分钟`;
  }
};

// 组件挂载时获取数据
// 实时更新相关
const autoRefreshInterval = ref(null);
const autoRefreshEnabled = ref(true);

// 启动自动刷新
const startAutoRefresh = () => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value);
  }

  autoRefreshInterval.value = setInterval(() => {
    if (!loading.value && !batchFetching.value && autoRefreshEnabled.value) {
      // 静默刷新统计信息
      fetchStats(true);
    }
  }, 15000); // 每15秒刷新一次
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value);
    autoRefreshInterval.value = null;
  }
};

// 切换自动刷新
const toggleAutoRefresh = () => {
  autoRefreshEnabled.value = !autoRefreshEnabled.value;
  if (autoRefreshEnabled.value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

onMounted(() => {
  refreshTasks();
  startAutoRefresh();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<style scoped>
.data-fetch-task-panel {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 页面头部 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 24px 32px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-title {
  margin: 0 0 4px 0;
  color: #1d2129;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 0;
  color: #86909c;
  font-size: 14px;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 核心统计指标 */
.core-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-item {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--stat-color-start), var(--stat-color-end));
}

.stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-pending {
  --stat-color-start: #fa709a;
  --stat-color-end: #fee140;
}

.stat-success {
  --stat-color-start: #a8edea;
  --stat-color-end: #fed6e3;
}

.stat-failed {
  --stat-color-start: #ff9a9e;
  --stat-color-end: #fecfef;
}

.stat-icon {
  font-size: 20px;
  line-height: 1;
  color: #165dff;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1d2129;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #1d2129;
  font-weight: 600;
  margin-bottom: 2px;
}

.stat-detail {
  font-size: 12px;
  color: #86909c;
  font-weight: 400;
}

/* 任务看板 */
.task-board {
  margin-bottom: 32px;
}

.board-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.section-header {
  padding: 24px 32px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-left {
  flex: 1;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 22px;
}

.task-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 4px;
}

.priority-count {
  color: #ffeb3b;
  font-weight: 500;
}

.header-actions {
  margin-left: 16px;
}

.task-cards-container {
  padding: 24px 32px;
}

/* 任务卡片网格布局 */
.task-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.task-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.pending-card {
  border-left: 4px solid #fa709a;
  background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
}

.pending-card.high-priority {
  border-left: 4px solid #ff4757;
  background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
  box-shadow: 0 4px 15px rgba(255, 71, 87, 0.15);
}

.pending-card.high-priority::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-top: 20px solid #ff4757;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.task-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customer-name {
  font-weight: 600;
  color: #1d2129;
  font-size: 15px;
}

.platform-badge {
  background: #165dff;
  color: white;
  padding: 3px 10px;
  border-radius: 14px;
  font-size: 12px;
  font-weight: 500;
  width: fit-content;
}

.task-status {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-direction: column;
  align-items: flex-end;
}

.priority-indicator {
  color: #ff4757;
  font-size: 16px;
  animation: pulse 2s infinite;
}

.waiting-duration {
  font-size: 12px;
  color: #86909c;
  font-weight: 500;
  text-align: right;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.task-content {
  margin-bottom: 16px;
}

.task-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

/* 紧凑信息布局 */
.task-info-compact {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.info-text {
  color: #4e5969;
  font-weight: 400;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.info-icon {
  font-size: 14px;
  color: #86909c;
  flex-shrink: 0;
}

.info-label {
  color: #86909c;
  font-weight: 500;
  min-width: 60px;
}

.info-value {
  color: #1d2129;
  font-weight: 400;
  flex: 1;
}

.info-link {
  color: #165dff;
  text-decoration: none;
  flex: 1;
}

.info-link:hover {
  text-decoration: underline;
}

.task-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 4px;
}

.action-btn-primary {
  flex: 1;
  max-width: 80px;
}

.action-btn-secondary {
  flex: 1;
  max-width: 60px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #86909c;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c9cdd4;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #86909c;
}

.empty-subtitle {
  font-size: 14px;
  color: #c9cdd4;
}

.more-tasks {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.more-tasks-btn {
  font-size: 14px;
  color: #165dff;
}

/* 详细任务列表 */
.detailed-tasks {
  margin-bottom: 24px;
}

.detailed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.filter-controls {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.batch-actions {
  margin-bottom: 16px;
}

.task-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.link-text {
  color: #165dff;
  text-decoration: none;
}

.link-text:hover {
  text-decoration: underline;
}

.text-placeholder {
  color: #c9cdd4;
  font-style: italic;
}

.metrics-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.metric-label {
  color: #86909c;
  margin-right: 8px;
}

.metric-value {
  color: #1d2129;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .task-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }

  .core-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header {
    padding: 20px 24px;
  }

  .task-cards-container {
    padding: 20px 24px;
  }
}

@media (max-width: 768px) {
  .data-fetch-task-panel {
    padding: 16px;
  }

  .panel-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 20px;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .core-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .stat-item {
    padding: 16px;
  }

  .stat-value {
    font-size: 24px;
  }

  .task-cards-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .section-header {
    padding: 16px 20px;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .header-actions {
    margin-left: 0;
  }

  .task-cards-container {
    padding: 16px 20px;
  }

  .task-card {
    padding: 16px;
  }

  .filter-controls .arco-col {
    margin-bottom: 16px;
  }

  .task-table {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .core-stats {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 24px;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-value {
    font-size: 20px;
  }

  .stat-icon {
    font-size: 18px;
  }
}

/* 表格样式优化 */
:deep(.arco-table-th) {
  background-color: #f7f8fa;
  font-weight: 600;
}

:deep(.arco-table-tr:hover) {
  background-color: #f2f3f5;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #e5e6eb;
}

/* 状态标签样式 */
:deep(.arco-tag) {
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 统计卡片样式 */
:deep(.arco-statistic-title) {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 8px;
}

:deep(.arco-statistic-value) {
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

/* 按钮样式优化 */
:deep(.arco-btn-text) {
  padding: 4px 8px;
  height: auto;
}

/* 模态框样式 */
:deep(.arco-modal-header) {
  border-bottom: 1px solid #e5e6eb;
}

:deep(.arco-descriptions-item-label) {
  font-weight: 600;
  color: #1d2129;
}

/* 加载状态样式 */
:deep(.arco-table-loading) {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 分页样式 */
:deep(.arco-pagination) {
  margin-top: 20px;
  justify-content: center;
}
</style>
