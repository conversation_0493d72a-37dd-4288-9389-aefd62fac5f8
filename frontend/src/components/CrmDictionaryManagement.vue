<template>
  <div class="crm-dictionary-management">
    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <button class="btn btn-primary" @click="testConnection" :disabled="loading.test">
          <span v-if="loading.test">测试中...</span>
          <span v-else>测试连接</span>
        </button>

        <button class="btn btn-success" @click="syncAllDictionaries" :disabled="loading.syncAll">
          <span v-if="loading.syncAll">同步中...</span>
          <span v-else>同步所有字典</span>
        </button>

        <div class="sync-buttons">
          <button class="btn btn-outline" @click="syncCustomerDictionaries" :disabled="loading.syncCustomer">
            <span v-if="loading.syncCustomer">同步中...</span>
            <span v-else>同步客户字典</span>
          </button>

          <button class="btn btn-outline" @click="syncContractDictionaries" :disabled="loading.syncContract">
            <span v-if="loading.syncContract">同步中...</span>
            <span v-else>同步协议字典</span>
          </button>
        </div>
      </div>

      <div class="toolbar-right">
        <button class="btn btn-secondary" @click="refreshStats" :disabled="loading.stats">刷新统计</button>
        <button class="btn btn-warning" @click="refreshToken" :disabled="loading.refreshToken">
          <span v-if="loading.refreshToken">刷新中...</span>
          <span v-else>刷新Token</span>
        </button>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-cards" v-if="stats">
      <div class="stats-card">
        <div class="stats-title">客户字典</div>
        <div class="stats-content">
          <div class="stats-number">{{ stats.customer?.length || 0 }}</div>
          <div class="stats-label">字段数量</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-title">协议字典</div>
        <div class="stats-content">
          <div class="stats-number">{{ stats.contract?.length || 0 }}</div>
          <div class="stats-label">字段数量</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-title">总字典项</div>
        <div class="stats-content">
          <div class="stats-number">{{ stats.summary?.totalItems || 0 }}</div>
          <div class="stats-label">字典项数量</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-title">同步成功</div>
        <div class="stats-content">
          <div class="stats-number success">{{ stats.summary?.syncedItems || 0 }}</div>
          <div class="stats-label">已同步</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-title">同步失败</div>
        <div class="stats-content">
          <div class="stats-number error">{{ stats.summary?.failedItems || 0 }}</div>
          <div class="stats-label">失败项</div>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-group">
        <label>部署类型：</label>
        <select v-model="filters.deployId" @change="loadDictionaries">
          <option value="customer">客户字典</option>
          <option value="contract">协议字典</option>
        </select>
      </div>

      <div class="filter-group">
        <label>字段代码：</label>
        <select v-model="filters.fieldCode" @change="loadDictionaries">
          <option value="">全部字段</option>
          <option v-for="field in availableFields" :key="field.fieldCode" :value="field.fieldCode">
            {{ field.fieldName }} ({{ field.fieldCode }})
          </option>
        </select>
      </div>

      <div class="filter-group">
        <label>状态：</label>
        <select v-model="filters.activeOnly" @change="loadDictionaries">
          <option value="true">仅活跃</option>
          <option value="false">全部状态</option>
        </select>
      </div>
    </div>

    <!-- 字典数据表格 -->
    <div class="table-container">
      <table class="data-table" v-if="dictionaries.length > 0">
        <thead>
          <tr>
            <th>字段名称</th>
            <th>字段代码</th>
            <th>字典项名称</th>
            <th>字典项值</th>
            <th>排序</th>
            <th>同步状态</th>
            <th>同步时间</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="dict in paginatedDictionaries" :key="dict.id">
            <td>{{ dict.fieldName }}</td>
            <td>
              <code>{{ dict.fieldCode }}</code>
            </td>
            <td>{{ dict.dataName }}</td>
            <td>{{ dict.dataValue }}</td>
            <td>{{ dict.dataOrder }}</td>
            <td>
              <span :class="['status-badge', dict.syncStatus]">
                {{ getSyncStatusText(dict.syncStatus) }}
              </span>
            </td>
            <td>
              <span v-if="dict.syncTime">
                {{ formatDateTime(dict.syncTime) }}
              </span>
              <span v-else class="text-muted">未同步</span>
            </td>
            <td>
              <span :class="['status-badge', dict.isActive ? 'active' : 'inactive']">
                {{ dict.isActive ? '启用' : '禁用' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-else-if="!loading.dictionaries" class="empty-state">
        <p>暂无字典数据</p>
        <button class="btn btn-primary" @click="loadDictionaries">重新加载</button>
      </div>

      <div v-if="loading.dictionaries" class="loading-state">
        <p>加载中...</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="pagination.total > 0">
      <button class="btn btn-outline" @click="changePage(pagination.current - 1)" :disabled="pagination.current <= 1">
        上一页
      </button>

      <span class="pagination-info">
        第 {{ pagination.current }} 页，共 {{ pagination.totalPages }} 页 （总计 {{ pagination.total }} 条记录）
      </span>

      <button
        class="btn btn-outline"
        @click="changePage(pagination.current + 1)"
        :disabled="pagination.current >= pagination.totalPages"
      >
        下一页
      </button>
    </div>

    <!-- 操作结果提示 -->
    <div v-if="message" :class="['message', message.type]">
      {{ message.text }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { crmDictionaryAPI } from '@/services/api';

// 响应式数据
const loading = reactive({
  test: false,
  syncAll: false,
  syncCustomer: false,
  syncContract: false,
  stats: false,
  dictionaries: false,
  refreshToken: false
});

const stats = ref(null);
const dictionaries = ref([]);
const availableFields = ref([]);
const message = ref(null);

const filters = reactive({
  deployId: 'customer',
  fieldCode: '',
  activeOnly: 'true'
});

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0
});

// 计算属性
const paginatedDictionaries = computed(() => {
  const start = (pagination.current - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  return dictionaries.value.slice(start, end);
});

// 方法
const showMessage = (text, type = 'info') => {
  message.value = { text, type };
  setTimeout(() => {
    message.value = null;
  }, 5000);
};

const testConnection = async () => {
  loading.test = true;
  try {
    const response = await crmDictionaryAPI.testConnection();
    if (response.data.success) {
      showMessage('CRM连接测试成功', 'success');
      console.log('连接测试结果:', response.data.data);
    } else {
      showMessage('CRM连接测试失败', 'error');
    }
  } catch (error) {
    console.error('连接测试失败:', error);
    showMessage(`连接测试失败: ${error.message}`, 'error');
  } finally {
    loading.test = false;
  }
};

const syncAllDictionaries = async () => {
  loading.syncAll = true;
  try {
    const response = await crmDictionaryAPI.syncAll();
    if (response.data.success) {
      showMessage(response.data.message, 'success');
      await refreshStats();
      await loadDictionaries();
    } else {
      showMessage('同步失败', 'error');
    }
  } catch (error) {
    console.error('同步失败:', error);
    showMessage(`同步失败: ${error.message}`, 'error');
  } finally {
    loading.syncAll = false;
  }
};

const syncCustomerDictionaries = async () => {
  loading.syncCustomer = true;
  try {
    const response = await crmDictionaryAPI.syncByDeployId('customer');
    if (response.data.success) {
      showMessage(response.data.message, 'success');
      await refreshStats();
      if (filters.deployId === 'customer') {
        await loadDictionaries();
      }
    } else {
      showMessage('客户字典同步失败', 'error');
    }
  } catch (error) {
    console.error('客户字典同步失败:', error);
    showMessage(`客户字典同步失败: ${error.message}`, 'error');
  } finally {
    loading.syncCustomer = false;
  }
};

const syncContractDictionaries = async () => {
  loading.syncContract = true;
  try {
    const response = await crmDictionaryAPI.syncByDeployId('contract');
    if (response.data.success) {
      showMessage(response.data.message, 'success');
      await refreshStats();
      if (filters.deployId === 'contract') {
        await loadDictionaries();
      }
    } else {
      showMessage('协议字典同步失败', 'error');
    }
  } catch (error) {
    console.error('协议字典同步失败:', error);
    showMessage(`协议字典同步失败: ${error.message}`, 'error');
  } finally {
    loading.syncContract = false;
  }
};

const refreshStats = async () => {
  loading.stats = true;
  try {
    const response = await crmDictionaryAPI.getStats();
    if (response.data.success) {
      stats.value = response.data.data;
    }
  } catch (error) {
    console.error('获取统计信息失败:', error);
  } finally {
    loading.stats = false;
  }
};

const refreshToken = async () => {
  loading.refreshToken = true;
  try {
    const response = await crmDictionaryAPI.refreshToken();
    if (response.data.success) {
      showMessage('CRM访问令牌刷新成功', 'success');
      console.log('Token刷新结果:', response.data.data);
      // 刷新统计信息以显示最新的token状态
      await refreshStats();
    } else {
      showMessage('Token刷新失败', 'error');
    }
  } catch (error) {
    console.error('Token刷新失败:', error);
    showMessage(`Token刷新失败: ${error.message}`, 'error');
  } finally {
    loading.refreshToken = false;
  }
};

const loadDictionaries = async () => {
  loading.dictionaries = true;
  try {
    const response = await crmDictionaryAPI.getDictionaries({
      deployId: filters.deployId,
      fieldCode: filters.fieldCode,
      activeOnly: filters.activeOnly,
      page: 1,
      pageSize: 1000 // 获取所有数据，前端分页
    });

    if (response.success) {
      dictionaries.value = response.data.data || [];

      // 更新分页信息
      pagination.total = dictionaries.value.length;
      pagination.totalPages = Math.ceil(pagination.total / pagination.pageSize);
      pagination.current = 1;

      // 更新可用字段列表
      updateAvailableFields();
    }
  } catch (error) {
    console.error('加载字典数据失败:', error);
    showMessage(`加载字典数据失败: ${error.message}`, 'error');
  } finally {
    loading.dictionaries = false;
  }
};

const updateAvailableFields = () => {
  const fieldsMap = new Map();
  dictionaries.value.forEach(dict => {
    if (!fieldsMap.has(dict.fieldCode)) {
      fieldsMap.set(dict.fieldCode, {
        fieldCode: dict.fieldCode,
        fieldName: dict.fieldName
      });
    }
  });
  availableFields.value = Array.from(fieldsMap.values());
};

const changePage = page => {
  if (page >= 1 && page <= pagination.totalPages) {
    pagination.current = page;
  }
};

const getSyncStatusText = status => {
  const statusMap = {
    pending: '待同步',
    synced: '已同步',
    failed: '失败',
    disabled: '禁用'
  };
  return statusMap[status] || status;
};

const formatDateTime = dateTime => {
  if (!dateTime) return '';
  const date = new Date(dateTime);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};

// 生命周期
onMounted(async () => {
  await refreshStats();
  await loadDictionaries();
});
</script>

<style scoped>
.crm-dictionary-management {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.sync-buttons {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  color: white;
}

.btn-success {
  background: #52c41a;
  border-color: #52c41a;
  color: white;
}

.btn-success:hover {
  background: #73d13d;
  border-color: #73d13d;
  color: white;
}

.btn-outline {
  background: white;
  border-color: #d9d9d9;
  color: #333;
}

.btn-secondary {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #666;
}

.btn-warning {
  background: #faad14;
  border-color: #faad14;
  color: white;
}

.btn-warning:hover {
  background: #ffc53d;
  border-color: #ffc53d;
  color: white;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.stats-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.stats-number.success {
  color: #52c41a;
}

.stats-number.error {
  color: #ff4d4f;
}

.stats-label {
  font-size: 12px;
  color: #999;
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.filter-group select {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.data-table th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.data-table td {
  font-size: 14px;
  color: #666;
}

.data-table code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-badge.synced {
  background: #f6ffed;
  color: #52c41a;
}

.status-badge.failed {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-badge.disabled {
  background: #f5f5f5;
  color: #999;
}

.status-badge.active {
  background: #f6ffed;
  color: #52c41a;
}

.status-badge.inactive {
  background: #f5f5f5;
  color: #999;
}

.text-muted {
  color: #999;
}

.empty-state,
.loading-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 16px;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message.success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.message.error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.message.info {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #1890ff;
}
</style>
