<template>
  <div class="crawler-task-summary">
    <div class="task-info">
      <div class="task-header">
        <a-spin :size="16" />
        <span class="task-title">{{ task.taskName }}</span>
      </div>
      <div class="task-details">
        <span class="task-keyword">{{ task.keywords }}</span>
        <span class="task-progress">{{ task.progress || 0 }}%</span>
      </div>
    </div>
    <div class="task-stats">
      <div class="stat-item">
        <span class="stat-label">已爬取</span>
        <span class="stat-value">{{ task.successCount || 0 }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">当前页</span>
        <span class="stat-value">{{ task.currentPage || 1 }}/{{ task.maxPages || 5 }}</span>
      </div>
    </div>
    <div class="task-actions">
      <a-button type="text" size="small" @click="$emit('refresh')">
        <template #icon>
          <icon-refresh />
        </template>
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { IconRefresh } from '@arco-design/web-vue/es/icon';

// Props
defineProps({
  task: {
    type: Object,
    required: true
  }
});

// Emits
defineEmits(['refresh']);
</script>

<style scoped>
.crawler-task-summary {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  min-width: 300px;
}

.task-info {
  flex: 1;
}

.task-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.task-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.task-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.task-keyword {
  background: #dbeafe;
  color: #1e40af;
  padding: 2px 6px;
  border-radius: 4px;
}

.task-progress {
  font-weight: 500;
  color: #059669;
}

.task-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  font-size: 11px;
  color: #9ca3af;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.task-actions {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .crawler-task-summary {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    min-width: auto;
  }

  .task-stats {
    justify-content: space-around;
  }
}
</style>
