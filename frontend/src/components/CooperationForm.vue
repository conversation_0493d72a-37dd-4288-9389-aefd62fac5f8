<template>
  <a-modal
    :visible="visible"
    :title="isEditMode ? '编辑合作对接记录' : '创建合作对接记录'"
    width="1050px"
    :on-before-ok="handleSubmit"
    unmount-on-close
    @cancel="handleCancel"
    @update:visible="handleCancel"
    :confirm-loading="submitting"
    :body-style="{ maxHeight: '80vh', overflow: 'auto' }"
  >
    <!-- CRM同步状态显示 -->
    <div v-if="crmSyncing || crmSyncStatus" class="crm-sync-status">
      <a-alert :type="getCrmSyncStatusType()" :loading="crmSyncing" show-icon closable style="margin-bottom: 16px">
        {{ crmSyncStatus }}
      </a-alert>
    </div>

    <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
      <!-- ==================== 客户信息模块 ==================== -->
      <a-card class="form-section customer-section" :bordered="false">
        <template #title>
          <div class="section-title">
            <icon-user />
            <span>客户信息</span>
          </div>
        </template>

        <!-- 基础信息 -->
        <div class="field-group">
          <h4 class="group-title">基础信息</h4>
          <a-row :gutter="20">
            <a-col :span="8">
              <a-form-item label="客户名称" field="customerName">
                <a-input v-model="form.customerName" placeholder="请输入客户名称" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="所属公海" field="customerPublicSea">
                <!-- <a-input v-model="form.customerPublicSea" placeholder="请输入所属公海" /> -->
                <a-select v-model="form.customerPublicSea" placeholder="请选择" :loading="loadingDictionaries">
                  <a-option
                    v-for="item in dictionaryOptions['customerPublicSea'] || []"
                    :key="item.dictKey"
                    :value="item.dictKey"
                  >
                    {{ item.dictLabel }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="达人平台ID" field="influencerPlatformId">
                <a-input v-model="form.influencerPlatformId" placeholder="抖音填星图ID，小红书填UID" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 平台信息 -->
        <div class="field-group">
          <h4 class="group-title">平台信息</h4>
          <a-row :gutter="20">
            <a-col :span="8">
              <a-form-item
                label="种草平台"
                field="seedingPlatform"
                :rules="[{ required: true, message: '请选择种草平台' }]"
              >
                <!-- <a-input v-model="form.seedingPlatform" placeholder="请输入种草平台" /> -->
                <a-select v-model="form.seedingPlatform" placeholder="请选择" :loading="loadingDictionaries">
                  <a-option
                    v-for="item in dictionaryOptions['seedingPlatform'] || []"
                    :key="item.dictKey"
                    :value="item.dictKey"
                  >
                    {{ item.dictLabel }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="博主粉丝量" field="bloggerFansCount" :rules="[{ required: true, message: '请选择' }]">
                <!-- <a-input v-model="form.bloggerFansCount" placeholder="请输入博主粉丝量" /> -->
                <a-select v-model="form.bloggerFansCount" placeholder="请选择" :loading="loadingDictionaries">
                  <a-option
                    v-for="item in dictionaryOptions['bloggerFansCount'] || []"
                    :key="item.dictKey"
                    :value="item.dictKey"
                  >
                    {{ item.dictLabel }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 联系方式 -->
        <div class="field-group">
          <h4 class="group-title">联系方式及备注</h4>
          <a-row :gutter="20">
            <a-col :span="12">
              <a-form-item label="主页链接" field="customerHomepage">
                <a-textarea
                  v-model="form.customerHomepage"
                  placeholder="请输入客户主页链接"
                  :auto-size="{ minRows: 4, maxRows: 4 }"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收件地址" field="receiverAddress">
                <a-textarea
                  v-model="form.receiverAddress"
                  placeholder="请输入收件地址信息，格式示例：&#10;收件人：张三&#10;手机：13800138000&#10;地址：北京市朝阳区xxx街道xxx号"
                  :auto-size="{ minRows: 4, maxRows: 4 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="20">
            <a-col :span="24">
              <a-form-item label="达人联系方式及备注" field="bloggerWechatAndNotes">
                <a-textarea
                  v-model="form.bloggerWechatAndNotes"
                  placeholder="请输入达人联系方式和相关备注信息，格式示例：&#10;微信：达人微信号&#10;手机：达人手机号&#10;来源：达人提报/主动联系/其他&#10;选择理由：粉丝画像匹配，互动率高等&#10;平台报价：平台标价或达人报价&#10;备注：其他重要信息"
                  :auto-size="{ minRows: 4, maxRows: 4 }"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-card>

      <!-- ==================== 协议信息模块 ==================== -->
      <a-card class="form-section agreement-section" :bordered="false">
        <template #title>
          <div class="section-title">
            <div>
              <icon-file />
              <span>协议信息</span>
            </div>
            <div class="agreement-toggle-container">
              <a-switch
                v-model="form.enableAgreementModule"
                size="small"
                :checked-text="'启用'"
                :unchecked-text="'禁用'"
                class="agreement-toggle"
                @change="handleAgreementToggleChange"
              />
              <span class="toggle-label">启用协议信息</span>
            </div>
          </div>
        </template>

        <!-- 协议信息表单区域 - 受开关控制 -->
        <div v-if="form.enableAgreementModule" class="agreement-form-content">
          <!-- 基本信息 -->
          <div class="field-group">
            <h4 class="group-title">基本信息</h4>
            <a-row :gutter="20">
              <a-col :span="12">
                <a-form-item label="标题" field="title" class="required-field">
                  <a-input v-model="form.title" placeholder="格式：平台-达人昵称-约稿日" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="合作形式" field="cooperationForm" :rules="[{ required: true, message: '请选择' }]">
                  <a-select v-model="form.cooperationForm" placeholder="请选择合作形式" :loading="loadingDictionaries">
                    <a-option
                      v-for="item in dictionaryOptions['cooperationForm'] || []"
                      :key="item.dictKey"
                      :value="item.dictKey"
                    >
                      {{ item.dictLabel }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="发布平台" field="publishPlatform" :rules="[{ required: true, message: '请选择' }]">
                  <!-- <a-input v-model="form.publishPlatform" placeholder="请输入发布平台" /> -->
                  <a-select v-model="form.publishPlatform" placeholder="请选择" :loading="loadingDictionaries">
                    <a-option
                      v-for="item in dictionaryOptions['publishPlatform'] || []"
                      :key="item.dictKey"
                      :value="item.dictKey"
                    >
                      {{ item.dictLabel }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 合作详情 -->
          <div class="field-group">
            <h4 class="group-title">合作详情</h4>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="合作品牌" field="cooperationBrand" :rules="[{ required: true, message: '请选择' }]">
                  <a-select v-model="form.cooperationBrand" placeholder="请选择合作品牌" :loading="loadingDictionaries">
                    <a-option
                      v-for="item in dictionaryOptions['cooperationBrand'] || []"
                      :key="item.dictKey"
                      :value="item.dictKey"
                    >
                      {{ item.dictLabel }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="合作产品"
                  field="cooperationProduct"
                  :rules="[{ required: true, message: '请选择' }]"
                >
                  <a-input v-model="form.cooperationProduct" placeholder="请输入合作产品" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="约定发布时间"
                  field="scheduledPublishTime"
                  :rules="[{ required: true, message: '请选择约定发布时间' }]"
                >
                  <a-date-picker
                    v-model="form.scheduledPublishTime"
                    placeholder="请选择约定发布时间"
                    style="width: 100%"
                    type="date"
                    value-format="YYYY/MM/DD"
                    format="YYYY/MM/DD"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item
                label="合作金额"
                field="cooperationAmount"
                :rules="[{ required: true, message: '请输入合作金额' }]"
              >
                <a-input-number v-model="form.cooperationAmount" placeholder="请输入合作金额" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="达人佣金比例" field="influencerCommissionRate">
                <a-input-number v-model="form.influencerCommissionRate" placeholder="请输入达人佣金比例（可选）">
                  <template #suffix>%</template>
                </a-input-number>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="如有返点是否完成"
                field="rebateCompleted"
                :rules="[{ required: true, message: '请选择' }]"
              >
                <a-select
                  v-model="form.rebateCompleted"
                  placeholder="请选择返点完成状态"
                  :loading="loadingDictionaries"
                >
                  <a-option
                    v-for="item in dictionaryOptions['rebateCompleted'] || []"
                    :key="item.dictKey"
                    :value="item.dictKey"
                  >
                    {{ item.dictLabel }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="收款人姓名" field="payeeName">
                <a-input v-model="form.payeeName" placeholder="请输入收款人姓名" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="银行账号" field="bankAccount">
                <a-input v-model="form.bankAccount" placeholder="请输入银行账号" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="开户行" field="bankName">
                <a-input v-model="form.bankName" placeholder="请输入开户行" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="备注" field="cooperationNotes">
            <a-textarea
              v-model="form.cooperationNotes"
              placeholder="请输入备注信息（如坑几、返点金额等）"
              :auto-size="{ minRows: 5, maxRows: 10 }"
            />
          </a-form-item>

          <!-- 第二部分：发布后登记 -->
          <a-divider orientation="left">发布后登记</a-divider>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="发布链接" field="publishLink">
                <a-input-group style="width: 100%">
                  <a-textarea
                    v-model="form.publishLink"
                    placeholder="请输入发布链接（要求长链接）"
                    style="width: calc(100% - 80px)"
                  />
                  <a-button type="primary" @click="parseNoteLink" :loading="parsing" style="width: 80px">
                    <template #icon>
                      <icon-link />
                    </template>
                    解析
                  </a-button>
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="实际发布日期" field="actualPublishDate">
                <a-date-picker
                  value-format="YYYY/MM/DD"
                  format="YYYY/MM/DD"
                  v-model="form.actualPublishDate"
                  placeholder="请选择实际发布日期"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="数据登记日期" field="dataRegistrationDate">
                <a-date-picker
                  value-format="YYYY/MM/DD"
                  format="YYYY/MM/DD"
                  v-model="form.dataRegistrationDate"
                  placeholder="发布十日内登记"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="观看量" field="viewCount">
                <a-input-number v-model="form.viewCount" placeholder="请输入观看量" style="width: 100%" :min="0" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="点赞数" field="likeCount">
                <a-input-number v-model="form.likeCount" placeholder="请输入点赞数" style="width: 100%" :min="0" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="收藏数" field="collectCount">
                <a-input-number v-model="form.collectCount" placeholder="请输入收藏数" style="width: 100%" :min="0" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="评论数" field="commentCount">
                <a-input-number v-model="form.commentCount" placeholder="请输入评论数" style="width: 100%" :min="0" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="内容植入系数" field="contentImplantCoefficient">
                <a-select
                  v-model="form.contentImplantCoefficient"
                  placeholder="请选择内容植入系数"
                  :loading="loadingDictionaries"
                >
                  <a-option
                    v-for="item in dictionaryOptions['contentImplantCoefficient'] || []"
                    :key="item.dictKey"
                    :value="item.dictKey"
                  >
                    {{ item.dictLabel }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="评论区维护系数" field="commentMaintenanceCoefficient">
                <a-select
                  v-model="form.commentMaintenanceCoefficient"
                  placeholder="请选择评论区维护系数"
                  :loading="loadingDictionaries"
                >
                  <a-option
                    v-for="item in dictionaryOptions['commentMaintenanceCoefficient'] || []"
                    :key="item.dictKey"
                    :value="item.dictKey"
                  >
                    {{ item.dictLabel }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="是否加入品牌话题" field="brandTopicIncluded">
                <a-select
                  v-model="form.brandTopicIncluded"
                  placeholder="请选择是否加入品牌话题"
                  :loading="loadingDictionaries"
                >
                  <a-option
                    v-for="item in dictionaryOptions['brandTopicIncluded'] || []"
                    :key="item.dictKey"
                    :value="item.dictKey"
                  >
                    {{ item.dictLabel }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="自评" field="selfEvaluation">
                <a-select v-model="form.selfEvaluation" placeholder="请选择自评" :loading="loadingDictionaries">
                  <a-option
                    v-for="item in dictionaryOptions['selfEvaluation'] || []"
                    :key="item.dictKey"
                    :value="item.dictKey"
                  >
                    {{ item.dictLabel }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 协议模块禁用时的提示信息 -->
        <div v-else class="agreement-disabled-notice">
          <a-empty description="协议信息模块已禁用">
            <template #image>
              <icon-file style="font-size: 48px; color: #c9cdd4" />
            </template>
            <div class="disabled-notice-text">
              <p>协议信息模块当前处于禁用状态</p>
              <p class="notice-hint">开启上方开关以启用协议信息录入功能</p>
            </div>
          </a-empty>
        </div>
      </a-card>

      <!-- ==================== CRM系统同步状态 ==================== -->
      <a-card class="form-section crm-sync-section" :bordered="false">
        <template #title>
          <div class="crm-sync-title">
            <icon-link />
            <span>CRM系统同步状态</span>
          </div>
        </template>

        <!-- 已同步状态显示 -->
        <div v-if="isCrmLinked" class="crm-linked-status">
          <a-alert :type="getCrmStatusType()" show-icon :closable="false" class="crm-status-alert">
            <template #icon>
              <icon-check-circle v-if="form.crmLinkStatus === 'fully_linked'" />
              <icon-user v-else-if="form.crmLinkStatus === 'customer_linked'" />
            </template>
            {{ getCrmStatusMessage() }}
          </a-alert>

          <!-- CRM关联信息展示 -->
          <div class="crm-info-display">
            <a-descriptions :column="2" size="medium" bordered>
              <a-descriptions-item
                v-if="form.externalCustomerId"
                label="CRM客户ID"
                :span="form.externalAgreementId ? 1 : 2"
              >
                <a-tag color="blue" class="crm-id-tag">
                  <icon-user />
                  {{ form.externalCustomerId }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item
                v-if="form.externalAgreementId"
                label="CRM协议ID"
                :span="form.externalCustomerId ? 1 : 2"
              >
                <a-tag color="green" class="crm-id-tag">
                  <icon-file />
                  {{ form.externalAgreementId }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="同步状态" :span="2">
                <a-tag :color="getCrmStatusTagColor()">
                  {{ getCrmStatusText() }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item v-if="form.dataSource" label="数据来源" :span="2">
                <a-tag color="purple">
                  {{ getDataSourceText() }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- 同步操作提示 -->
          <div class="crm-sync-tips">
            <a-alert type="info" show-icon :closable="false">
              此记录已与CRM系统同步，如需修改CRM数据请前往CRM系统操作
            </a-alert>
          </div>
        </div>

        <!-- 未同步状态 - 显示同步选项 -->
        <div v-else class="crm-sync-options">
          <a-alert type="warning" show-icon :closable="false" class="crm-status-alert">
            此记录尚未与CRM系统同步，您可以选择同步选项
          </a-alert>

          <div class="sync-checkbox-group">
            <a-row :gutter="24">
              <a-col :span="12">
                <div class="sync-option-card">
                  <a-checkbox v-model="form.syncCreateCustomer" :disabled="isEditMode" class="sync-checkbox">
                    <div class="sync-option-content">
                      <div class="sync-option-title">
                        <icon-user />
                        同步创建CRM客户
                      </div>
                      <div class="sync-option-desc">将客户信息同步到CRM系统</div>
                    </div>
                  </a-checkbox>
                </div>
              </a-col>
              <a-col :span="12">
                <div
                  class="sync-option-card"
                  :class="{ disabled: !form.syncCreateCustomer || !form.enableAgreementModule }"
                >
                  <a-checkbox
                    v-model="form.syncCreateAgreement"
                    :disabled="isEditMode || !form.syncCreateCustomer || !form.enableAgreementModule"
                    class="sync-checkbox"
                  >
                    <div class="sync-option-content">
                      <div class="sync-option-title">
                        <icon-file />
                        同步创建CRM协议
                      </div>
                      <div class="sync-option-desc">将协议信息同步到CRM系统</div>
                    </div>
                  </a-checkbox>
                </div>
              </a-col>
            </a-row>
          </div>

          <!-- 同步依赖提示 -->
          <a-alert
            v-if="form.syncCreateAgreement && !form.syncCreateCustomer"
            type="error"
            show-icon
            :closable="false"
            class="sync-dependency-alert"
          >
            创建协议必须先创建客户
          </a-alert>

          <!-- 协议模块禁用提示 -->
          <a-alert
            v-if="!form.enableAgreementModule"
            type="warning"
            show-icon
            :closable="false"
            class="sync-dependency-alert"
          >
            协议信息模块已禁用，CRM协议同步不可用
          </a-alert>
        </div>
      </a-card>

      <!-- 显示解析结果（保留原有功能） -->
      <div v-if="parsedNoteId || scheduledFetchTime" class="parse-info">
        <a-divider>解析信息</a-divider>
        <a-descriptions :column="2" size="small">
          <a-descriptions-item label="笔记ID" v-if="parsedNoteId">
            <a-tag color="blue">{{ parsedNoteId }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="定时拉取时间" v-if="scheduledFetchTime">
            <span>{{ formatDateTime(scheduledFetchTime) }}</span>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, nextTick, onMounted, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconLink, IconCheckCircle, IconUser, IconFile } from '@arco-design/web-vue/es/icon';
import { cooperationAPI, dictionaryAPI, crmIntegrationAPI } from '@/services/api';
import dictionaryService from '@/services/dictionaryService';
import dayjs from 'dayjs';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits(['cancel', 'success']);

// 响应式数据
const formRef = ref();
const submitting = ref(false);
const parsing = ref(false);
const parsedNoteId = ref('');
const scheduledFetchTime = ref('');
const loadingDictionaries = ref(false);

// CRM同步相关状态
const crmSyncing = ref(false);
const crmSyncStatus = ref('');
const originalFormData = ref({});

// 简化的字典数据存储
const dictionaryOptions = ref({});

// 需要的字典分类（简单数组）
const requiredCategories = [
  // 客户
  'customerPublicSea', // 所属公海
  'bloggerFansCount', // 博主粉丝量
  'seedingPlatform', // 种草平台

  // 协议
  'cooperationForm', // 合作形式
  'publishPlatform', // 发布平台
  'cooperationBrand', // 合作品牌
  'rebateCompleted', // 返点完成状态
  'contentImplantCoefficient', // 内容植入系数
  'commentMaintenanceCoefficient', // 评论维护系数
  'brandTopicIncluded', // 品牌话题包含
  'selfEvaluation' // 自我评价
];

// 移除所有复杂的工具函数，保持最简化

// 表单数据
const form = reactive({
  // 客户信息模块
  customerName: '',
  customerHomepage: '',
  receiverAddress: '',
  customerPublicSea: 'fd18e0d6b1164e7080f0fa91dc43b0d8',
  seedingPlatform: '',
  bloggerFansCount: '',
  influencerPlatformId: '',
  bloggerWechatAndNotes: '',

  // 协议信息模块 - 合作前信息
  title: '',
  cooperationForm: '',
  publishPlatform: '',
  cooperationBrand: '',
  cooperationProduct: '',
  cooperationNotes: '',
  scheduledPublishTime: null,
  cooperationAmount: '',
  influencerCommissionRate: '',
  payeeName: '',
  bankAccount: '',
  bankName: '',
  rebateCompleted: '',

  // 协议信息模块 - 发布后登记
  publishLink: '',
  actualPublishDate: null,
  dataRegistrationDate: null,
  viewCount: null,
  likeCount: null,
  collectCount: null,
  commentCount: null,
  contentImplantCoefficient: '',
  commentMaintenanceCoefficient: '',
  brandTopicIncluded: '',
  selfEvaluation: '',

  // 协议模块控制
  enableAgreementModule: false, // 默认关闭，编辑模式时会根据数据设置

  // CRM同步选项
  syncCreateCustomer: true,
  syncCreateAgreement: false,

  // CRM关联字段
  crmLinkStatus: 'unlinked',
  externalCustomerId: '',
  externalAgreementId: '',
  dataSource: '',

  // 保留原有字段（向后兼容）
  cooperationMonth: '',
  platform: '',
  bloggerName: '',
  responsiblePerson: '',
  influencerHomepage: '',
  cooperationNoteLink: '',
  cooperationPrice: '',
  contentDirection: '',
  workProgress: ''
});

// 表单验证规则
const rules = {
  customerName: [{ required: true, message: '请输入客户名称' }],
  title: [{ required: true, message: '请输入标题' }]
};

// 计算属性
const isCrmLinked = computed(() => {
  return form.crmLinkStatus && form.crmLinkStatus !== 'unlinked';
});

// CRM状态相关方法
const getCrmStatusMessage = () => {
  switch (form.crmLinkStatus) {
    case 'fully_linked':
      return '此记录已完全同步到CRM系统（客户和协议信息）';
    case 'customer_linked':
      return '此记录已同步客户信息到CRM系统';
    default:
      return '此记录尚未与CRM系统同步';
  }
};

const getCrmStatusType = () => {
  switch (form.crmLinkStatus) {
    case 'fully_linked':
      return 'success';
    case 'customer_linked':
      return 'info';
    default:
      return 'warning';
  }
};

const getCrmStatusText = () => {
  switch (form.crmLinkStatus) {
    case 'fully_linked':
      return '完全关联';
    case 'customer_linked':
      return '客户已关联';
    case 'unlinked':
    default:
      return '未关联';
  }
};

const getCrmStatusTagColor = () => {
  switch (form.crmLinkStatus) {
    case 'fully_linked':
      return 'green';
    case 'customer_linked':
      return 'blue';
    case 'unlinked':
    default:
      return 'gray';
  }
};

const getDataSourceText = () => {
  switch (form.dataSource) {
    case 'dingtalk_crm':
      return '钉钉CRM系统';
    case 'manual':
      return '手动创建';
    default:
      return form.dataSource || '未知来源';
  }
};

// 获取CRM同步状态类型
const getCrmSyncStatusType = () => {
  if (crmSyncing.value) {
    return 'info';
  }

  if (!crmSyncStatus.value) {
    return 'info';
  }

  const status = crmSyncStatus.value.toLowerCase();

  // 检查是否包含错误关键词
  if (status.includes('失败') || status.includes('错误') || status.includes('error') || status.includes('failed')) {
    return 'error';
  }

  // 检查是否包含警告关键词
  if (status.includes('部分错误') || status.includes('警告') || status.includes('warning')) {
    return 'warning';
  }

  // 检查是否包含成功关键词
  if (status.includes('成功') || status.includes('完成') || status.includes('success')) {
    return 'success';
  }

  // 默认为信息类型
  return 'info';
};

// 加载字典数据（简化版本）
const loadDictionaries = async () => {
  loadingDictionaries.value = true;
  try {
    // 使用批量获取接口，CRM数据优先
    const batchData = await dictionaryService.getBatchDictionaries(requiredCategories, {
      crmFirst: true,
      includeLocal: true,
      includeCrm: true
    });

    // 简化的数据转换
    dictionaryOptions.value = {};

    requiredCategories.forEach(category => {
      const data = batchData[category] || [];
      dictionaryOptions.value[category] = data.map(item => ({
        dictKey: item.value || item.dictKey,
        dictLabel: item.label || item.dictLabel
      }));
    });
  } catch (error) {
    console.error('加载字典数据失败:', error);
    Message.error(error.response.data.message || '字典数据加载失败');

    // 简单降级：清空数据
    dictionaryOptions.value = {};
    requiredCategories.forEach(category => {
      dictionaryOptions.value[category] = [];
    });
  } finally {
    loadingDictionaries.value = false;
  }
};

// 组件挂载时加载字典数据
onMounted(() => {
  loadDictionaries();
});

// 监听编辑数据变化
watch(
  () => props.editData,
  newData => {
    if (newData && Object.keys(newData).length > 0) {
      // 保存原始数据用于变化检测
      originalFormData.value = { ...newData };

      Object.keys(form).forEach(key => {
        if (newData[key] !== undefined) {
          form[key] = newData[key];
        }
      });

      // 设置解析结果
      if (newData.noteId) {
        parsedNoteId.value = newData.noteId;
      }
      if (newData.scheduledFetchTime) {
        scheduledFetchTime.value = newData.scheduledFetchTime;
      }
      if (newData.cooperationAmount) {
        form.cooperationAmount = Number(newData.cooperationAmount);
      }
      // 佣金比例
      if (newData.influencerCommissionRate) {
        form.influencerCommissionRate = Number(newData.influencerCommissionRate);
      }
      // 设置协议模块开关状态
      if (props.isEditMode) {
        // 编辑模式：如果有协议相关数据，则开启协议模块
        form.enableAgreementModule = true;
        console.log('编辑模式 - 协议模块开关状态:', form.enableAgreementModule);
      } else {
        // 创建模式：默认关闭
        form.enableAgreementModule = false;
        console.log('创建模式 - 协议模块开关状态:', form.enableAgreementModule);
      }

      // 如果是新增
      if (!props.isEditMode) {
        // 种草平台默认为空
        form.seedingPlatform = '';
        // 博主粉丝量级别
        form.bloggerFansCount = '';
        // 发布平台默认为空
        form.publishPlatform = '';
      }
    }
  },
  { immediate: true, deep: true }
);

// 监听模态框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible && !props.isEditMode && (!props.editData || Object.keys(props.editData).length === 0)) {
      // 只有在没有预填充数据时才重置表单
      resetForm();
    }
  }
);

// 监听实际发布日期变化，重新计算定时拉取时间和数据登记日期
watch(
  () => form.actualPublishDate,
  newDate => {
    if (parsedNoteId.value && newDate) {
      // 如果已经解析了笔记ID且有发布日期，重新计算定时拉取时间和数据登记日期
      const baseTime = new Date(newDate);

      // 重新计算定时拉取时间
      const fetchTime = new Date(baseTime);
      fetchTime.setDate(fetchTime.getDate() + 10);
      fetchTime.setHours(0, 0, 0, 0); // 设置为当天开始时间（00:00:00）
      scheduledFetchTime.value = fetchTime.toISOString();

      // 重新计算数据登记日期
      const registrationDate = new Date(baseTime);
      registrationDate.setDate(registrationDate.getDate() + 10);
      // 格式化为 YYYY/MM/DD 格式
      const year = registrationDate.getFullYear();
      const month = String(registrationDate.getMonth() + 1).padStart(2, '0');
      const day = String(registrationDate.getDate()).padStart(2, '0');
      form.dataRegistrationDate = `${year}/${month}/${day}`;
    }
  }
);

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach(key => {
    if (typeof form[key] === 'boolean') {
      form[key] = false;
    } else if (typeof form[key] === 'number') {
      form[key] = null;
    } else {
      form[key] = null;
    }
  });
  // 同步创建CRM客户默认开启
  form.syncCreateCustomer = true;
  parsedNoteId.value = '';
  scheduledFetchTime.value = '';
  // 协议模块重置
  Object.assign(form, {
    title: '',
    cooperationForm: '',
    publishPlatform: '',
    cooperationBrand: '',
    cooperationProduct: '',
    cooperationNotes: '',
    scheduledPublishTime: null,
    cooperationAmount: '',
    influencerCommissionRate: '',
    payeeName: '',
    bankAccount: '',
    bankName: '',
    rebateCompleted: '',

    // 协议信息模块 - 发布后登记
    publishLink: '',
    actualPublishDate: null,
    dataRegistrationDate: null,
    viewCount: null,
    likeCount: null,
    collectCount: null,
    commentCount: null,
    contentImplantCoefficient: '',
    commentMaintenanceCoefficient: '',
    brandTopicIncluded: '',
    selfEvaluation: ''
  });

  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 解析笔记链接
const parseNoteLink = async () => {
  if (!form.publishLink) {
    Message.warning('请先输入发布链接');
    return;
  }

  try {
    parsing.value = true;
    const response = await cooperationAPI.parseNoteLink(form.publishLink);
    const result = response.data;

    if (result.isValid) {
      parsedNoteId.value = result.noteId;

      // 计算基准时间（优先使用实际发布日期，否则使用当前时间）
      const baseTime = form.actualPublishDate ? new Date(form.actualPublishDate) : new Date();

      // 计算定时拉取时间（基准时间+10天的开始时间）
      const fetchTime = new Date(baseTime);
      fetchTime.setDate(fetchTime.getDate() + 10);
      fetchTime.setHours(0, 0, 0, 0); // 设置为当天开始时间（00:00:00）
      scheduledFetchTime.value = fetchTime.toISOString();

      // 计算数据登记日期（基准时间+10天，与定时拉取时间保持一致的日期部分）
      const registrationDate = new Date(baseTime);
      registrationDate.setDate(registrationDate.getDate() + 10);
      // 格式化为 YYYY/MM/DD 格式
      const year = registrationDate.getFullYear();
      const month = String(registrationDate.getMonth() + 1).padStart(2, '0');
      const day = String(registrationDate.getDate()).padStart(2, '0');
      form.dataRegistrationDate = `${year}/${month}/${day}`;

      Message.success('发布链接解析成功，已自动计算数据登记日期');
    } else {
      Message.error('发布链接格式不正确');
    }
  } catch (error) {
    console.error('解析发布链接失败:', error);
    Message.error(error.response.data.message || '解析发布链接失败');
  } finally {
    parsing.value = false;
  }
};

// 检测字段变化
const detectChangedFields = () => {
  const changedFields = [];

  // 客户相关字段
  const customerFields = [
    'customerName',
    'customerHomepage',
    'receiverAddress',
    'customerPublicSea',
    'seedingPlatform',
    'bloggerFansCount',
    'influencerPlatformId',
    'bloggerWechatAndNotes',
    'starMapId'
  ];

  // 协议相关字段
  const agreementFields = [
    'title',
    'cooperationForm',
    'cooperationBrand',
    'cooperationProduct',
    'cooperationAmount',
    'scheduledPublishTime',
    'cooperationNotes',
    'publishPlatform',
    'actualPublishDate',
    'publishLink',
    'responsiblePerson'
  ];

  const allFields = [...customerFields, ...agreementFields];

  for (const field of allFields) {
    if (form[field] !== originalFormData.value[field]) {
      changedFields.push(field);
    }
  }

  return changedFields;
};

// CRM智能同步
const performCrmSync = async (cooperationId, changedFields) => {
  try {
    crmSyncing.value = true;
    crmSyncStatus.value = '正在同步到CRM系统...';

    console.log('🔄 开始CRM智能同步:', { cooperationId, changedFields });

    const syncResult = await crmIntegrationAPI.smartSyncCooperationData({
      cooperationId: cooperationId,
      options: {
        changedFields: changedFields,
        enableAgreementModule: form.enableAgreementModule
      }
    });

    // 检查API调用是否成功
    if (!syncResult.success) {
      const errorMessage = syncResult.message || 'CRM同步失败';
      throw new Error(errorMessage);
    }

    console.log('✅ CRM同步API调用成功:', syncResult.data);

    // 如果有同步成功的数据，进行数据回写
    if (syncResult.data.customerSynced || syncResult.data.agreementSynced) {
      await performCrmDataFetch(form.customerName);
    }

    // 检查同步过程中是否有错误
    if (syncResult.data.errors && syncResult.data.errors.length > 0) {
      console.warn('⚠️ CRM同步部分失败:', syncResult.data.errors);
      const errorMessage = `CRM同步完成，但有部分错误: ${syncResult.data.errors.join(', ')}`;
      crmSyncStatus.value = errorMessage;
      // 如果有错误，抛出异常让调用方知道
      throw new Error(errorMessage);
    } else {
      crmSyncStatus.value = 'CRM同步成功';
    }
  } catch (error) {
    console.error('❌ CRM同步失败:', error);

    // 处理不同类型的错误
    let errorMessage = error.message;

    // 如果是axios错误，提取更详细的错误信息
    if (error.response) {
      // 服务器返回了错误响应
      const responseData = error.response.data;
      if (responseData && responseData.message) {
        errorMessage = responseData.message;
      } else {
        errorMessage = `CRM同步失败: HTTP ${error.response.status}`;
      }
    } else if (error.request) {
      // 请求发送了但没有收到响应
      errorMessage = 'CRM同步失败: 网络连接错误';
    }

    crmSyncStatus.value = `CRM同步失败: ${errorMessage}`;

    // 重新抛出错误，确保调用方能够捕获
    throw new Error(errorMessage);
  } finally {
    crmSyncing.value = false;
    // 延长状态显示时间，让用户能看到错误信息
    // setTimeout(() => {
    //   crmSyncStatus.value = '';
    // }, 5000);
  }
};

// CRM数据回写
const performCrmDataFetch = async customerName => {
  try {
    crmSyncStatus.value = '正在从CRM系统获取最新数据...';

    console.log('🔄 开始CRM数据回写:', customerName);

    // 调用CRM客户查询接口获取最新数据
    const customerResult = await crmIntegrationAPI.getCustomerList({
      page: 1,
      limit: 10,
      keyword: customerName
    });

    if (customerResult.success && customerResult.data.length > 0) {
      // 找到匹配的客户数据
      const matchedCustomer = customerResult.data.find(customer => customer.customerName === customerName);

      if (matchedCustomer) {
        console.log('✅ 找到匹配的CRM客户数据:', matchedCustomer);

        // 获取该客户的协议数据
        const agreementResult = await crmIntegrationAPI.getAgreementsByCustomer(customerName);

        if (agreementResult.success && agreementResult.data.length > 0) {
          console.log('✅ 找到匹配的CRM协议数据:', agreementResult.data);
        }

        crmSyncStatus.value = 'CRM数据同步完成，已获取最新数据';
      } else {
        console.log('⚠️ 未找到匹配的CRM客户数据');
        crmSyncStatus.value = 'CRM数据同步完成，但未找到匹配的客户数据';
      }
    } else {
      console.log('⚠️ CRM客户查询无结果');
      crmSyncStatus.value = 'CRM数据同步完成，但查询无结果';
    }

    console.log('✅ CRM数据回写完成');
  } catch (error) {
    console.error('❌ CRM数据回写失败:', error);
    crmSyncStatus.value = `CRM数据回写失败: ${error.message}`;
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate();
    if (valid) {
      Message.error('表单填写不完整，请检查必填项');
      return false;
    }

    submitting.value = true;

    const submitData = { ...form };

    // 根据协议模块开关状态设置CRM同步选项
    if (!form.enableAgreementModule) {
      // 协议模块关闭时，强制关闭协议同步
      submitData.syncCreateAgreement = false;
      console.log('协议模块已禁用，CRM协议同步已关闭');
    }

    // 检测字段变化（仅在编辑模式下）
    const changedFields = props.isEditMode ? detectChangedFields() : [];

    let result;
    if (props.isEditMode) {
      result = await cooperationAPI.update(props.editData.id, submitData);
      Message.success('更新成功');

      // 编辑模式下进行CRM智能同步 - 改为同步阻塞执行
      if (changedFields.length > 0) {
        console.log('🔍 检测到字段变化:', changedFields);
        try {
          // 同步等待CRM同步完成
          await performCrmSync(props.editData.id, changedFields);
          console.log('✅ CRM同步已完成');
          Message.success('CRM同步成功');
        } catch (error) {
          return false;
          console.error('❌ CRM同步失败:', error);
          // CRM同步失败时显示错误信息，但不阻止表单提交成功
          Message.error(`CRM同步失败: ${error}`);
          // 更新CRM同步状态显示错误
          crmSyncStatus.value = `CRM同步失败: ${error.message}`;
        }
      } else {
        console.log('📝 未检测到字段变化，跳过CRM同步');
      }
    } else {
      result = await cooperationAPI.create(submitData);
      Message.success('合作对接单创建成功！正在同步到CRM系统...');

      // 创建模式下，如果有客户名称，进行CRM同步 - 改为同步阻塞执行
      if (submitData.customerName) {
        console.log('🆕 新建记录，准备进行CRM同步');
        const newRecordId = result.data?.id || result.id;
        if (newRecordId) {
          try {
            // 同步等待CRM同步完成
            let res = await performCrmSync(newRecordId, ['customerName']);
            console.log('✅ CRM同步已完成');
          } catch (error) {
            return false;
            console.error('❌ CRM同步失败:', error);
            // CRM同步失败时显示错误信息，但不阻止表单提交成功
            Message.error(`CRM同步失败: ${error.response.data?.message}`);
            // 更新CRM同步状态显示错误
            crmSyncStatus.value = `CRM同步失败: ${error.response.data?.message}`;
          }
        }
      }
    }

    // 传递创建/更新的记录数据
    emit('success', result.data || result);
  } catch (error) {
    console.error('提交失败:', error);
    Message.error(error.response.data.message || '提交失败');
    return false;
  } finally {
    submitting.value = false;
  }
};

// 协议模块开关变化处理
const handleAgreementToggleChange = value => {
  console.log('协议模块开关状态变化:', value);

  // 当关闭协议模块时，同时关闭CRM协议同步
  if (!value) {
    form.syncCreateAgreement = false;
    Message.info('协议信息模块已禁用，CRM协议同步已自动关闭');
  } else {
    // 当开启协议模块时，如果客户同步已开启，则自动开启协议同步
    if (form.syncCreateCustomer) {
      form.syncCreateAgreement = true;
      Message.success('协议信息模块已启用，CRM协议同步已自动开启');
    } else {
      Message.success('协议信息模块已启用');
    }
  }
};

// 取消
const handleCancel = () => {
  emit('cancel');
};

// 格式化时间
const formatDateTime = dateTime => {
  if (!dateTime) return '';
  const date = new Date(dateTime);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};
</script>

<style scoped>
/* 表单整体样式 */
.form-section {
  margin-bottom: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e6eb;
}

.form-section :deep(.arco-card-header) {
  background: linear-gradient(135deg, #fafbfc 0%, #f5f6f7 100%);
  border-bottom: 2px solid #165dff;
  border-radius: 6px 6px 0 0;
  padding: 12px 20px;
  position: relative;
}

.form-section :deep(.arco-card-header)::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #165dff 0%, #246fff 100%);
}

.form-section :deep(.arco-card-header-title) {
  font-weight: 600;
  color: #1d2129;
  font-size: 15px;
}

.form-section :deep(.arco-card-body) {
  padding: 18px 20px;
}

/* 区块标题样式 */
.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-size: 15px;
  font-weight: 600;
  width: 100%;
}

.section-title > div:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title .arco-icon {
  font-size: 16px;
  color: #165dff;
}

/* 协议模块开关样式 */
.agreement-toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.agreement-toggle {
  flex-shrink: 0;
}

.toggle-label {
  font-size: 12px;
  color: #86909c;
  font-weight: 400;
  white-space: nowrap;
}

/* 协议模块禁用提示样式 */
.agreement-disabled-notice {
  padding: 0px 20px;
  text-align: center;
}

.disabled-notice-text {
  margin-top: 16px;
}

.disabled-notice-text p {
  margin: 0;
  color: #86909c;
  font-size: 14px;
  line-height: 1.5;
}

.notice-hint {
  font-size: 12px !important;
  color: #c9cdd4 !important;
  margin-top: 8px !important;
}

/* 协议表单内容区域 */
.agreement-form-content {
  transition: all 0.3s ease;
}

/* 字段分组样式 */
.field-group {
  margin-bottom: 18px;
}

.field-group:last-child {
  margin-bottom: 0;
}

.group-title {
  margin: 0 0 12px 0;
  font-size: 13px;
  font-weight: 600;
  color: #4e5969;
  padding: 0 0 4px 8px;
  border-left: 3px solid #165dff;
  position: relative;
  display: inline-block;
}

.group-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 8px;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, #165dff 0%, transparent 80%);
}

/* CRM同步区域样式 */
.crm-sync-section {
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
  position: relative;
}

.crm-sync-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid transparent;
  border-radius: 6px;
  background: linear-gradient(135deg, #165dff, #246fff) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.crm-sync-section:hover::before {
  opacity: 1;
}

.crm-sync-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 600;
}

.crm-sync-title .arco-icon {
  font-size: 16px;
  color: #165dff;
}

/* CRM已关联状态样式 */
.crm-linked-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.crm-status-alert {
  margin-bottom: 0;
}

.crm-info-display {
  background: #f9fafb;
  padding: 14px;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
}

.crm-id-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.crm-sync-tips {
  background: #e8f4ff;
  padding: 10px 12px;
  border-radius: 4px;
  border-left: 3px solid #165dff;
}

/* CRM同步选项样式 */
.crm-sync-options {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.sync-checkbox-group {
  margin-top: 12px;
}

.sync-option-card {
  background: #f9fafb;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
}

.sync-option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid #165dff;
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sync-option-card:hover::before {
  opacity: 1;
}

.sync-option-card:hover {
  background: #f0f7ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.15);
}

.sync-option-card.disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  opacity: 0.6;
}

.sync-checkbox {
  width: 100%;
}

.sync-option-content {
  margin-left: 6px;
}

.sync-option-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #1d2129;
  margin-bottom: 3px;
  font-size: 13px;
}

.sync-option-title .arco-icon {
  font-size: 14px;
  color: #165dff;
}

.sync-option-desc {
  font-size: 11px;
  color: #86909c;
  line-height: 1.3;
}

.sync-dependency-alert {
  margin-top: 12px;
}

/* 解析信息样式 */
.parse-result {
  margin-top: 6px;
}

.parse-info {
  margin-top: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
}

/* 表单项间距优化 */
:deep(.arco-form-item) {
  margin-bottom: 14px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: #4e5969;
  margin-bottom: 6px;
  font-size: 13px;
}

/* 输入框样式优化 */
:deep(.arco-input),
:deep(.arco-select-view),
:deep(.arco-textarea),
:deep(.arco-picker) {
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 13px;
}

:deep(.arco-input:focus),
:deep(.arco-select-view-focus),
:deep(.arco-textarea:focus),
:deep(.arco-picker:focus) {
  border-color: #165dff !important;
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.08) !important;
}

:deep(.arco-input:hover),
:deep(.arco-select-view:hover),
:deep(.arco-textarea:hover),
:deep(.arco-picker:hover) {
  border-color: #165dff !important;
}

/* 修复选择器样式 */
:deep(.arco-select-view-single) {
  border-radius: 4px;
  font-size: 13px;
}

:deep(.arco-select-view-single:hover) {
  border-color: #165dff !important;
}

:deep(.arco-select-view-single.arco-select-view-focus) {
  border-color: #165dff !important;
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.08) !important;
}

/* 修复日期选择器样式 */
:deep(.arco-picker-input) {
  border-radius: 4px;
  font-size: 13px;
}

:deep(.arco-picker:hover .arco-picker-input) {
  border-color: #165dff !important;
}

:deep(.arco-picker-focused .arco-picker-input) {
  border-color: #165dff !important;
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.08) !important;
}

/* 修复数字输入框样式 */
:deep(.arco-input-number) {
  border-radius: 4px;
  font-size: 13px;
}

:deep(.arco-input-number:hover) {
  border-color: #165dff !important;
}

:deep(.arco-input-number-focus) {
  border-color: #165dff !important;
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.08) !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-section :deep(.arco-card-body) {
    padding: 16px 18px;
  }

  .field-group {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  :deep(.arco-modal) {
    width: 95% !important;
    margin: 10px;
  }

  .form-section {
    margin-bottom: 12px;
  }

  .form-section :deep(.arco-card-header) {
    padding: 10px 16px;
  }

  .form-section :deep(.arco-card-body) {
    padding: 14px 16px;
  }

  :deep(.arco-col) {
    margin-bottom: 12px;
  }

  .field-group {
    margin-bottom: 14px;
  }

  .group-title {
    font-size: 12px;
    padding: 5px 10px;
    margin-bottom: 10px;
  }

  .sync-option-card {
    padding: 10px;
  }

  .crm-info-display {
    padding: 10px;
  }

  .crm-sync-status {
    margin-bottom: 16px;
  }

  .crm-sync-status .arco-alert {
    border-radius: 6px;
  }

  .crm-sync-status .arco-alert-info {
    background-color: #f0f9ff;
    border-color: #0ea5e9;
  }

  .crm-sync-status .arco-alert-success {
    background-color: #f0fdf4;
    border-color: #22c55e;
  }

  .crm-sync-status .arco-alert-error {
    background-color: #fef2f2;
    border-color: #ef4444;
  }

  :deep(.arco-form-item) {
    margin-bottom: 12px;
  }

  :deep(.arco-form-item-label) {
    font-size: 12px;
    margin-bottom: 4px;
  }
}

@media (max-width: 480px) {
  .form-section :deep(.arco-card-header) {
    padding: 8px 12px;
  }

  .form-section :deep(.arco-card-body) {
    padding: 12px;
  }

  .section-title {
    font-size: 13px;
  }

  .section-title .arco-icon {
    font-size: 14px;
  }

  .group-title {
    font-size: 11px;
    padding: 4px 8px;
  }

  .sync-option-title {
    font-size: 12px;
  }

  .sync-option-desc {
    font-size: 10px;
  }

  :deep(.arco-form-item) {
    margin-bottom: 10px;
  }

  :deep(.arco-form-item-label) {
    font-size: 11px;
  }

  :deep(.arco-input),
  :deep(.arco-select-view),
  :deep(.arco-textarea),
  :deep(.arco-picker) {
    font-size: 12px;
  }
}
</style>
