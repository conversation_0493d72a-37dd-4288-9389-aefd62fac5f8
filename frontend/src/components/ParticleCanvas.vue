<template>
  <canvas
    ref="canvasRef"
    class="particle-canvas"
    @mousemove="handleMouseMove"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
  />
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

// 组件属性
const props = defineProps({
  particleCount: {
    type: Number,
    default: 650
  },
  backgroundColor: {
    type: String,
    default: 'hsla(242, 30%, 5%, .55)'
  },
  particleColors: {
    type: Array,
    default: () => []
  },
  forceMultiplier: {
    type: Number,
    default: 0.09
  }
});

// Canvas引用
const canvasRef = ref(null);

// 动画相关变量
let ctx = null;
let animationId = null;
let resizeTimeout = null;
let resizeObserver = null;
let isInitialized = false;

// 粒子系统参数
const config = {
  particleCount: props.particleCount,
  beginFlow: 50,
  repeatFlow: 20,
  force: 0,
  mouseDown: false
};

// 鼠标位置
const mouse = {
  x: 0,
  y: 0
};

// 画布尺寸
const canvas = {
  width: 0,
  height: 0,
  midX: 0,
  midY: 0
};

// 粒子数组
let particles = [];
let flow = config.beginFlow;
let flowInterval = null;

// 粒子类
class Particle {
  constructor() {
    this.deg = 0;
    this.rad = 0;
    this.x = 0;
    this.y = 0;
    this.distX = 0;
    this.distY = 0;
    this.color = this.generateColor();
    this.size = 1;
  }

  generateColor() {
    if (props.particleColors.length > 0) {
      return props.particleColors[Math.floor(Math.random() * props.particleColors.length)];
    }
    return `rgb(${Math.floor(Math.random() * 130)},${Math.floor(Math.random() * 50)},${Math.floor(
      Math.random() * 100
    )})`;
  }
}

// 初始化画布
const initCanvas = () => {
  if (!canvasRef.value) return;

  ctx = canvasRef.value.getContext('2d');
  if (!ctx) return;

  updateCanvasSize();
  createParticles();
  startAnimation();
};

// 更新画布尺寸
const updateCanvasSize = () => {
  if (!canvasRef.value) return;

  const container = canvasRef.value.parentElement;
  if (!container) return;

  const rect = container.getBoundingClientRect();
  canvas.width = canvasRef.value.width = rect.width;
  canvas.height = canvasRef.value.height = rect.height;
  canvas.midX = canvas.width / 2;
  canvas.midY = canvas.height / 2;
  config.force = Math.max(canvas.width, canvas.height) * props.forceMultiplier;
};

// 创建粒子
const createParticles = () => {
  particles = [];
  for (let i = 0; i < config.particleCount; i++) {
    const particle = new Particle();
    particle.deg = Math.floor(Math.random() * 360);
    particle.rad = Math.floor(Math.random() * canvas.width * 0.5);
    particle.x = particle.distX = Math.floor(Math.random() * canvas.width);
    particle.y = particle.distY = Math.floor(Math.random() * canvas.height);
    particle.size = 1 + Math.floor(Math.random() * (particle.rad * 0.055));
    particles[i] = particle;
  }

  // 重置流动效果
  flow = config.beginFlow;
  
  // 清理之前的定时器
  if (flowInterval) {
    clearInterval(flowInterval);
  }
  
  // 创建新的流动效果
  flowInterval = setInterval(() => {
    flow--;
    if (flow <= config.repeatFlow) {
      flow = config.repeatFlow; // 保持最小值，避免动画停止
    }
  }, 20);
};

// 动画循环
const animate = () => {
  if (!ctx) return;

  // 清除画布并设置背景
  ctx.globalCompositeOperation = 'source-over';
  ctx.fillStyle = props.backgroundColor;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  ctx.globalCompositeOperation = 'lighter';

  const mx = mouse.x;
  const my = mouse.y;
  let bounds = config.force;

  if (config.mouseDown) {
    bounds = config.force * 2;
  }

  // 更新和绘制粒子
  for (let i = 0; i < particles.length; i++) {
    const p = particles[i];
    const radi = (Math.PI / 180) * p.deg;

    // 计算粒子的目标位置
    p.distX = canvas.midX + p.rad * Math.cos(radi);
    p.distY = canvas.midY + p.rad * Math.sin(radi) * 0.4;

    // 鼠标交互效果
    if (mx && my) {
      const react = Math.floor(bounds * 0.5 + Math.random() * bounds * 0.9);

      if (p.distX - mx > 0 && p.distX - mx < bounds && p.distY - my > 0 && p.distY - my < bounds) {
        p.distX += react;
        p.distY += react;
      } else if (p.distX - mx > 0 && p.distX - mx < bounds && p.distY - my < 0 && p.distY - my > -bounds) {
        p.distX += react;
        p.distY -= react;
      } else if (p.distX - mx < 0 && p.distX - mx > -bounds && p.distY - my > 0 && p.distY - my < bounds) {
        p.distX -= react;
        p.distY += react;
      } else if (p.distX - mx < 0 && p.distX - mx > -bounds && p.distY - my < 0 && p.distY - my > -bounds) {
        p.distX -= react;
        p.distY -= react;
      }
    }

    // 更新粒子位置 - 确保flow不会为0
    const currentFlow = Math.max(flow, 1);
    p.x += (p.distX - p.x) / currentFlow;
    p.y += (p.distY - p.y) / currentFlow;

    // 计算粒子大小
    let size = p.size * ((p.y * 1.5) / canvas.height);
    if (size < 0.1) {
      size = 0;
    }

    // 绘制粒子
    ctx.beginPath();
    ctx.fillStyle = p.color;
    ctx.arc(p.x, p.y, size, 0, Math.PI * 2, true);
    ctx.fill();
    ctx.closePath();

    // 更新粒子角度
    let vary;
    if (p.size < 2) {
      vary = 4;
    } else if (p.size < 3) {
      vary = 3;
    } else if (p.size < 4) {
      vary = 2;
    } else {
      vary = 1;
    }
    vary *= p.y / (canvas.height * 0.9);
    p.deg += vary;
    p.deg = p.deg % 360;
  }

  animationId = requestAnimationFrame(animate);
};

// 开始动画
const startAnimation = () => {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }
  animate();
};

// 停止动画
const stopAnimation = () => {
  if (animationId) {
    cancelAnimationFrame(animationId);
    animationId = null;
  }
  
  // 清理流动定时器
  if (flowInterval) {
    clearInterval(flowInterval);
    flowInterval = null;
  }
};

// 鼠标事件处理
const handleMouseMove = (event) => {
  const rect = canvasRef.value?.getBoundingClientRect();
  if (!rect) return;

  mouse.x = event.clientX - rect.left;
  mouse.y = event.clientY - rect.top;
};

const handleMouseDown = () => {
  config.mouseDown = true;
};

const handleMouseUp = () => {
  config.mouseDown = false;
};

// 容器大小变化处理
const handleResize = () => {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }

  resizeTimeout = window.setTimeout(() => {
    updateCanvasSize();
    createParticles();
  }, 100);
};

// 设置 ResizeObserver
const setupResizeObserver = () => {
  if (!canvasRef.value?.parentElement) return;

  resizeObserver = new ResizeObserver(entries => {
     
    for (const entry of entries) {
      handleResize();
    }
  });

  resizeObserver.observe(canvasRef.value.parentElement);
};

// 组件生命周期
onMounted(async () => {
  await nextTick();
  initCanvas();
  setupResizeObserver();
  // 保留 window resize 作为备用
  window.addEventListener('resize', handleResize);
  // 延迟设置初始化完成标志，避免初始 ResizeObserver 触发
  setTimeout(() => {
    isInitialized = true;
    console.log('Component initialization completed');
  }, 200);
});

onUnmounted(() => {
  stopAnimation();

  // 清理 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }

  // 清理事件监听器
  window.removeEventListener('resize', handleResize);

  // 清理定时器
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }
});
</script>

<style scoped>
.particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: auto;
  display: block;
  object-fit: cover;
}
</style>
