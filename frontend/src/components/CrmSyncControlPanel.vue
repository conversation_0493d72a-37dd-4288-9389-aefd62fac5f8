<template>
  <div class="crm-sync-control-panel">
    <!-- 同步模式选择 -->
    <div class="sync-mode-section">
      <h3>同步模式选择</h3>
      <a-radio-group v-model="syncConfig.mode" type="button" size="large" @change="onModeChange">
        <a-radio value="customers">
          <div class="mode-option">
            <icon-user-group />
            <span>客户数据同步</span>
          </div>
        </a-radio>
        <a-radio value="complete">
          <div class="mode-option">
            <icon-interaction />
            <span>完整数据同步</span>
          </div>
        </a-radio>
        <a-radio value="all">
          <div class="mode-option">
            <icon-apps />
            <span>全量数据同步</span>
          </div>
        </a-radio>
      </a-radio-group>

      <!-- 模式说明 -->
      <div class="mode-description">
        <a-alert :type="modeInfo.type" :title="modeInfo.title" :show-icon="false">
          {{ modeInfo.description }}
        </a-alert>
      </div>
    </div>

    <!-- 同步参数配置 -->
    <div class="sync-params-section">
      <h3>同步参数配置</h3>
      <a-form :model="syncConfig" layout="vertical">
        <a-row :gutter="16">
          <!-- 基础参数 -->
          <a-col :span="8">
            <a-form-item label="起始页码" field="page">
              <a-input-number v-model="syncConfig.page" :min="1" :max="1000" placeholder="从第几页开始" />
              <template #help> 指定从哪一页开始同步数据 </template>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="每页数量" field="limit">
              <a-input-number v-model="syncConfig.limit" :min="1" :max="100" placeholder="每页记录数" />
              <template #help> 每页处理的记录数量，建议10-50 </template>
            </a-form-item>
          </a-col>

          <!-- 全量同步特有参数 -->
          <a-col :span="8" v-if="syncConfig.mode === 'all'">
            <a-form-item label="最大页数" field="maxPages">
              <a-input-number v-model="syncConfig.maxPages" :min="1" :max="10000" placeholder="最大处理页数" />
              <template #help> 防止超时，限制最大处理页数 </template>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <!-- 过滤参数 -->
          <a-col :span="12">
            <a-form-item label="客户名称过滤" field="customerName">
              <a-input v-model="syncConfig.customerName" placeholder="可选，过滤特定客户" allow-clear />
              <template #help> 可选，只同步包含指定名称的客户 </template>
            </a-form-item>
          </a-col>

          <!-- 预览模式 -->
          <a-col :span="12" v-if="syncConfig.mode === 'all'">
            <a-form-item label="运行模式" field="dryRun">
              <a-switch v-model="syncConfig.dryRun" checked-text="预览模式" unchecked-text="实际同步" size="large" />
              <template #help> 预览模式只查看数据，不实际同步 </template>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 高级选项 -->
        <a-collapse v-model:active-key="advancedOptionsVisible">
          <a-collapse-item header="高级选项" key="advanced">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="请求延迟(ms)" field="requestDelay">
                  <a-input-number
                    v-model="syncConfig.requestDelay"
                    :min="100"
                    :max="5000"
                    :step="100"
                    placeholder="请求间隔"
                  />
                  <template #help> 请求之间的延迟时间，避免频率限制 </template>
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="重试次数" field="retryAttempts">
                  <a-input-number v-model="syncConfig.retryAttempts" :min="0" :max="5" placeholder="失败重试次数" />
                  <template #help> 请求失败时的重试次数 </template>
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="超时时间(s)" field="timeout">
                  <a-input-number
                    v-model="syncConfig.timeout"
                    :min="10"
                    :max="300"
                    :step="10"
                    placeholder="请求超时时间"
                  />
                  <template #help> 单个请求的超时时间 </template>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="数据验证" field="validateData">
                  <a-switch v-model="syncConfig.validateData" checked-text="启用验证" unchecked-text="跳过验证" />
                  <template #help> 是否对同步的数据进行完整性验证 </template>
                </a-form-item>
              </a-col>

              <a-col :span="12">
                <a-form-item label="错误处理" field="continueOnError">
                  <a-switch v-model="syncConfig.continueOnError" checked-text="继续执行" unchecked-text="遇错停止" />
                  <template #help> 遇到错误时是否继续处理后续数据 </template>
                </a-form-item>
              </a-col>
            </a-row>
          </a-collapse-item>
        </a-collapse>
      </a-form>
    </div>

    <!-- 同步控制按钮 -->
    <div class="sync-controls-section">
      <h3>同步控制</h3>
      <div class="control-buttons">
        <a-space size="large">
          <!-- 主要操作按钮 -->
          <a-button
            type="primary"
            size="large"
            :loading="syncStatus.isRunning"
            @click="startSync"
            :disabled="!canStartSync"
          >
            <template #icon>
              <icon-play-arrow />
            </template>
            {{ syncStatus.isRunning ? '同步中...' : '开始同步' }}
          </a-button>

          <a-button size="large" :disabled="!syncStatus.isRunning" @click="pauseSync">
            <template #icon>
              <icon-pause />
            </template>
            暂停同步
          </a-button>

          <a-button
            size="large"
            :disabled="!syncStatus.isRunning && !syncStatus.isPaused"
            @click="stopSync"
            status="danger"
          >
            <template #icon>
              <icon-stop />
            </template>
            停止同步
          </a-button>

          <!-- 辅助操作按钮 -->
          <a-button type="outline" size="large" @click="validateConfig" :loading="validating">
            <template #icon>
              <icon-check-circle />
            </template>
            验证配置
          </a-button>

          <a-button type="outline" size="large" @click="previewSync" :loading="previewing">
            <template #icon>
              <icon-eye />
            </template>
            预览数据
          </a-button>

          <a-button type="outline" size="large" @click="resetConfig">
            <template #icon>
              <icon-refresh />
            </template>
            重置配置
          </a-button>
        </a-space>
      </div>

      <!-- 同步状态指示器 -->
      <div class="sync-status-indicator" v-if="syncStatus.isRunning || syncStatus.isPaused">
        <a-alert :type="syncStatus.alertType" :title="syncStatus.statusText" :show-icon="true" :closable="false">
          <template #action>
            <a-button v-if="syncStatus.isPaused" type="primary" size="mini" @click="resumeSync"> 继续 </a-button>
          </template>
          <div class="status-details">
            <span>已处理: {{ syncStatus.processed }}</span>
            <span>成功: {{ syncStatus.success }}</span>
            <span>失败: {{ syncStatus.failed }}</span>
            <span>耗时: {{ syncStatus.duration }}s</span>
          </div>
        </a-alert>
      </div>
    </div>

    <!-- 快速配置模板 -->
    <div class="quick-templates-section">
      <h3>快速配置模板</h3>
      <div class="template-buttons">
        <a-space wrap>
          <a-button
            v-for="template in configTemplates"
            :key="template.name"
            type="outline"
            @click="applyTemplate(template)"
          >
            <template #icon>
              <component :is="template.icon" />
            </template>
            {{ template.name }}
          </a-button>
        </a-space>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import {
  IconUserGroup,
  IconInteraction,
  IconApps,
  IconPlayArrow,
  IconPause,
  IconStop,
  IconCheckCircle,
  IconEye,
  IconRefresh,
  IconThunderbolt,
  IconSafe
} from '@arco-design/web-vue/es/icon';

// Props
const props = defineProps({
  connectionStatus: {
    type: Object,
    default: () => ({ connected: false })
  }
});

// Emits
const emit = defineEmits(['sync-start', 'sync-pause', 'sync-stop', 'sync-resume', 'config-change']);

// 响应式数据
const advancedOptionsVisible = ref([]);
const validating = ref(false);
const previewing = ref(false);

// 同步配置
const syncConfig = reactive({
  mode: 'customers',
  page: 1,
  limit: 20,
  maxPages: 10,
  customerName: '',
  dryRun: false,
  requestDelay: 500,
  retryAttempts: 3,
  timeout: 30,
  validateData: true,
  continueOnError: true
});

// 同步状态
const syncStatus = reactive({
  isRunning: false,
  isPaused: false,
  processed: 0,
  success: 0,
  failed: 0,
  duration: 0,
  statusText: '',
  alertType: 'info'
});

// 计算属性
const canStartSync = computed(() => {
  return props.connectionStatus.connected && !syncStatus.isRunning;
});

const modeInfo = computed(() => {
  const modes = {
    customers: {
      type: 'info',
      title: '客户数据同步',
      description: '只同步客户基础信息，不包含协议数据。适用于快速更新客户信息。'
    },
    complete: {
      type: 'normal',
      title: '完整数据同步',
      description: '同步客户信息和对应的协议数据。推荐用于日常数据同步。'
    },
    all: {
      type: 'warning',
      title: '全量数据同步',
      description: '分页同步所有数据，支持大批量处理。建议在非业务高峰期使用。'
    }
  };
  return modes[syncConfig.mode] || modes.customers;
});

// 配置模板
const configTemplates = ref([
  {
    name: '快速同步',
    icon: 'IconThunderbolt',
    config: {
      mode: 'customers',
      page: 1,
      limit: 50,
      requestDelay: 300,
      retryAttempts: 2,
      timeout: 20
    }
  },
  {
    name: '安全同步',
    icon: 'IconSafe',
    config: {
      mode: 'complete',
      page: 1,
      limit: 10,
      requestDelay: 1000,
      retryAttempts: 5,
      timeout: 60,
      validateData: true
    }
  },
  {
    name: '批量同步',
    icon: 'IconApps',
    config: {
      mode: 'all',
      page: 1,
      limit: 20,
      maxPages: 50,
      requestDelay: 500,
      retryAttempts: 3,
      timeout: 30,
      dryRun: false
    }
  }
]);

// 方法
const onModeChange = () => {
  // 根据模式调整默认参数
  if (syncConfig.mode === 'all') {
    syncConfig.limit = Math.min(syncConfig.limit, 20);
  }
  emit('config-change', { ...syncConfig });
};

const startSync = () => {
  if (!canStartSync.value) return;

  syncStatus.isRunning = true;
  syncStatus.isPaused = false;
  syncStatus.processed = 0;
  syncStatus.success = 0;
  syncStatus.failed = 0;
  syncStatus.duration = 0;
  syncStatus.statusText = '同步进行中...';
  syncStatus.alertType = 'info';

  emit('sync-start', { ...syncConfig });
};

const pauseSync = () => {
  syncStatus.isPaused = true;
  syncStatus.statusText = '同步已暂停';
  syncStatus.alertType = 'warning';
  emit('sync-pause');
};

const resumeSync = () => {
  syncStatus.isPaused = false;
  syncStatus.statusText = '同步进行中...';
  syncStatus.alertType = 'info';
  emit('sync-resume');
};

const stopSync = () => {
  syncStatus.isRunning = false;
  syncStatus.isPaused = false;
  syncStatus.statusText = '同步已停止';
  syncStatus.alertType = 'error';
  emit('sync-stop');
};

const validateConfig = async () => {
  validating.value = true;
  try {
    // 这里可以添加配置验证逻辑
    await new Promise(resolve => setTimeout(resolve, 1000));
    Message.success('配置验证通过');
  } catch (error) {
    Message.error( error.response.data.message || '配置验证失败');
  } finally {
    validating.value = false;
  }
};

const previewSync = async () => {
  previewing.value = true;
  try {
    // 这里可以添加数据预览逻辑
    await new Promise(resolve => setTimeout(resolve, 1500));
    Message.success('数据预览完成');
  } catch (error) {
    Message.error('数据预览失败');
  } finally {
    previewing.value = false;
  }
};

const resetConfig = () => {
  Object.assign(syncConfig, {
    mode: 'customers',
    page: 1,
    limit: 20,
    maxPages: 10,
    customerName: '',
    dryRun: false,
    requestDelay: 500,
    retryAttempts: 3,
    timeout: 30,
    validateData: true,
    continueOnError: true
  });
  Message.success('配置已重置');
};

const applyTemplate = template => {
  Object.assign(syncConfig, template.config);
  Message.success(`已应用"${template.name}"模板`);
};

// 监听配置变化
watch(
  syncConfig,
  () => {
    emit('config-change', { ...syncConfig });
  },
  { deep: true }
);

// 暴露方法给父组件
defineExpose({
  updateSyncStatus: status => {
    Object.assign(syncStatus, status);
  },
  getSyncConfig: () => ({ ...syncConfig })
});
</script>

<style lang="less" scoped>
.crm-sync-control-panel {
  .sync-mode-section,
  .sync-params-section,
  .sync-controls-section,
  .quick-templates-section {
    margin-bottom: 32px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1d2129;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        width: 4px;
        height: 16px;
        background: #165dff;
        border-radius: 2px;
      }
    }
  }

  .sync-mode-section {
    :deep(.arco-radio-group) {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .arco-radio-button {
        flex: 1;
        height: auto;
        padding: 0;
        border-radius: 8px;
        overflow: hidden;

        .arco-radio-button-content {
          padding: 16px;
          height: auto;
        }

        .mode-option {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          text-align: center;

          .arco-icon {
            font-size: 24px;
            color: #86909c;
          }

          span {
            font-size: 14px;
            font-weight: 500;
          }
        }

        &.arco-radio-button-checked {
          .mode-option {
            .arco-icon {
              color: #165dff;
            }
          }
        }
      }
    }

    .mode-description {
      :deep(.arco-alert) {
        border-radius: 6px;

        .arco-alert-title {
          font-weight: 600;
          margin-bottom: 4px;
        }
      }
    }
  }

  .sync-params-section {
    :deep(.arco-form) {
      .arco-form-item {
        margin-bottom: 20px;

        .arco-form-item-label {
          font-weight: 500;
          color: #4e5969;
          margin-bottom: 8px;
        }

        .arco-form-item-help {
          font-size: 12px;
          color: #86909c;
          margin-top: 4px;
        }
      }

      .arco-input-number,
      .arco-input,
      .arco-switch {
        width: 100%;
      }

      .arco-switch {
        width: auto;
      }
    }

    :deep(.arco-collapse) {
      margin-top: 16px;
      border: 1px solid #f2f3f5;
      border-radius: 6px;

      .arco-collapse-item {
        border: none;

        .arco-collapse-item-header {
          padding: 12px 16px;
          background: #f7f8fa;
          font-weight: 500;
        }

        .arco-collapse-item-content {
          padding: 20px 16px;
        }
      }
    }
  }

  .sync-controls-section {
    .control-buttons {
      margin-bottom: 20px;
      text-align: center;
      padding: 20px;
      background: #f7f8fa;
      border-radius: 8px;

      :deep(.arco-space) {
        justify-content: center;
        flex-wrap: wrap;
      }

      .arco-btn {
        min-width: 120px;
        height: 44px;
        font-weight: 500;

        &.arco-btn-primary {
          background: linear-gradient(135deg, #165dff 0%, #246fff 100%);
          border: none;
          box-shadow: 0 4px 12px rgba(22, 93, 255, 0.3);

          &:hover {
            background: linear-gradient(135deg, #0e4fd1 0%, #1c5cff 100%);
            box-shadow: 0 6px 16px rgba(22, 93, 255, 0.4);
          }
        }

        &[status='danger'] {
          background: linear-gradient(135deg, #f53f3f 0%, #ff4d4f 100%);
          border: none;
          color: white;

          &:hover {
            background: linear-gradient(135deg, #d32f2f 0%, #f53f3f 100%);
          }
        }
      }
    }

    .sync-status-indicator {
      :deep(.arco-alert) {
        border-radius: 8px;

        .arco-alert-title {
          font-weight: 600;
          margin-bottom: 8px;
        }

        .status-details {
          display: flex;
          gap: 16px;
          font-size: 14px;
          color: #4e5969;

          span {
            padding: 2px 8px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .quick-templates-section {
    .template-buttons {
      :deep(.arco-space) {
        width: 100%;
      }

      .arco-btn {
        height: 40px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .arco-icon {
          font-size: 16px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .crm-sync-control-panel {
    .sync-mode-section {
      :deep(.arco-radio-group) {
        flex-direction: column;
        gap: 12px;

        .arco-radio-button {
          .mode-option {
            flex-direction: row;
            justify-content: flex-start;
            text-align: left;

            .arco-icon {
              font-size: 20px;
            }
          }
        }
      }
    }

    .sync-controls-section {
      .control-buttons {
        :deep(.arco-space) {
          .arco-btn {
            min-width: 100px;
            height: 40px;
          }
        }

        .status-details {
          flex-wrap: wrap;
          gap: 8px !important;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .crm-sync-control-panel {
    .sync-mode-section,
    .sync-params-section,
    .sync-controls-section,
    .quick-templates-section {
      padding: 16px;
      margin-bottom: 16px;
    }

    .sync-controls-section {
      .control-buttons {
        padding: 16px;

        :deep(.arco-space) {
          .arco-btn {
            min-width: 80px;
            height: 36px;
            font-size: 12px;
          }
        }
      }
    }

    .quick-templates-section {
      .template-buttons {
        .arco-btn {
          height: 36px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
