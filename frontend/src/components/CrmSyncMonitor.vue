<template>
  <div class="crm-sync-monitor">
    <!-- 实时状态概览 -->
    <div class="status-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="status-card">
            <a-statistic title="同步状态" :value="monitorData.status" :value-style="getStatusStyle(monitorData.status)">
              <template #prefix>
                <component :is="getStatusIcon(monitorData.status)" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>

        <a-col :span="6">
          <a-card class="status-card">
            <a-statistic
              title="总进度"
              :value="monitorData.totalProgress"
              suffix="%"
              :value-style="{ color: '#165dff' }"
            >
              <template #prefix>
                <icon-dashboard />
              </template>
            </a-statistic>
          </a-card>
        </a-col>

        <a-col :span="6">
          <a-card class="status-card">
            <a-statistic
              title="处理速度"
              :value="monitorData.processingRate"
              suffix="条/分钟"
              :value-style="{ color: '#00b42a' }"
            >
              <template #prefix>
                <icon-thunderbolt />
              </template>
            </a-statistic>
          </a-card>
        </a-col>

        <a-col :span="6">
          <a-card class="status-card">
            <a-statistic title="剩余时间" :value="monitorData.estimatedTime" :value-style="{ color: '#f77234' }">
              <template #prefix>
                <icon-clock-circle />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细进度信息 -->
    <div class="progress-details">
      <a-card title="同步进度详情">
        <template #extra>
          <a-space>
            <a-button type="text" size="mini" @click="toggleAutoRefresh">
              <icon-sync :spin="autoRefresh" />
              {{ autoRefresh ? '停止刷新' : '自动刷新' }}
            </a-button>
            <a-button type="text" size="mini" @click="refreshData">
              <icon-refresh />
              手动刷新
            </a-button>
          </a-space>
        </template>

        <!-- 进度条组 -->
        <div class="progress-group">
          <div class="progress-item">
            <div class="progress-label">
              <span>客户数据同步</span>
              <span class="progress-text"
                >{{ monitorData.customerSync.current }}/{{ monitorData.customerSync.total }}</span
              >
            </div>
            <a-progress
              :percent="monitorData.customerSync.percentage"
              :status="monitorData.customerSync.status"
              :stroke-width="8"
              show-text
            />
          </div>

          <div class="progress-item">
            <div class="progress-label">
              <span>协议数据同步</span>
              <span class="progress-text"
                >{{ monitorData.agreementSync.current }}/{{ monitorData.agreementSync.total }}</span
              >
            </div>
            <a-progress
              :percent="monitorData.agreementSync.percentage"
              :status="monitorData.agreementSync.status"
              :stroke-width="8"
              show-text
            />
          </div>

          <div class="progress-item">
            <div class="progress-label">
              <span>数据验证</span>
              <span class="progress-text">{{ monitorData.validation.current }}/{{ monitorData.validation.total }}</span>
            </div>
            <a-progress
              :percent="monitorData.validation.percentage"
              :status="monitorData.validation.status"
              :stroke-width="8"
              show-text
            />
          </div>
        </div>

        <!-- 统计信息表格 -->
        <div class="stats-table">
          <h4>统计信息</h4>
          <a-table :data="statsTableData" :pagination="false" :bordered="false" size="small">
            <template #columns>
              <a-table-column title="项目" data-index="label" :width="120" />
              <a-table-column title="成功" data-index="success" :width="80">
                <template #cell="{ record }">
                  <span class="success-text">{{ record.success }}</span>
                </template>
              </a-table-column>
              <a-table-column title="失败" data-index="failed" :width="80">
                <template #cell="{ record }">
                  <span class="error-text">{{ record.failed }}</span>
                </template>
              </a-table-column>
              <a-table-column title="跳过" data-index="skipped" :width="80">
                <template #cell="{ record }">
                  <span class="warning-text">{{ record.skipped }}</span>
                </template>
              </a-table-column>
              <a-table-column title="总计" data-index="total" :width="80" />
              <a-table-column title="成功率" data-index="successRate">
                <template #cell="{ record }">
                  <a-tag :color="getSuccessRateColor(record.successRate)"> {{ record.successRate }}% </a-tag>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </a-card>
    </div>

    <!-- 实时日志监控 -->
    <div class="log-monitor">
      <a-card title="实时日志监控">
        <template #extra>
          <a-space>
            <a-select v-model="logFilter" size="mini" style="width: 120px">
              <a-option value="all">全部日志</a-option>
              <a-option value="error">错误日志</a-option>
              <a-option value="warning">警告日志</a-option>
              <a-option value="info">信息日志</a-option>
              <a-option value="success">成功日志</a-option>
            </a-select>
            <a-button type="text" size="mini" @click="clearLogs">
              <icon-delete />
              清空
            </a-button>
            <a-button type="text" size="mini" @click="exportLogs">
              <icon-download />
              导出
            </a-button>
          </a-space>
        </template>

        <div class="log-container">
          <div class="log-stats">
            <a-space>
              <a-tag color="red">错误: {{ logStats.error }}</a-tag>
              <a-tag color="orange">警告: {{ logStats.warning }}</a-tag>
              <a-tag color="blue">信息: {{ logStats.info }}</a-tag>
              <a-tag color="green">成功: {{ logStats.success }}</a-tag>
            </a-space>
          </div>

          <div class="log-list" ref="logListRef">
            <div v-for="(log, index) in filteredLogs" :key="index" class="log-entry" :class="log.level">
              <div class="log-time">{{ formatLogTime(log.timestamp) }}</div>
              <div class="log-level">
                <a-tag :color="getLogLevelColor(log.level)" size="small">
                  {{ log.level.toUpperCase() }}
                </a-tag>
              </div>
              <div class="log-message">{{ log.message }}</div>
              <div class="log-details" v-if="log.details">
                <a-button type="text" size="mini" @click="showLogDetails(log)">
                  <icon-eye />
                  详情
                </a-button>
              </div>
            </div>

            <div v-if="filteredLogs.length === 0" class="log-empty">
              <a-empty description="暂无日志记录" />
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 性能监控图表 -->
    <div class="performance-charts">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="处理速度趋势" class="chart-card">
            <div class="chart-container" ref="speedChartRef">
              <!-- 这里可以集成图表库，如 ECharts -->
              <div class="chart-placeholder">
                <icon-bar-chart />
                <p>处理速度趋势图</p>
                <p class="chart-desc">显示每分钟处理的记录数量变化</p>
              </div>
            </div>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="错误率统计" class="chart-card">
            <div class="chart-container" ref="errorChartRef">
              <div class="chart-placeholder">
                <icon-dashboard />
                <p>错误率统计图</p>
                <p class="chart-desc">显示成功、失败、跳过的比例分布</p>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 日志详情模态框 -->
    <a-modal v-model:visible="logDetailVisible" title="日志详情" width="800px" :footer="false">
      <div v-if="selectedLog" class="log-detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="时间">
            {{ formatDateTime(selectedLog.timestamp) }}
          </a-descriptions-item>
          <a-descriptions-item label="级别">
            <a-tag :color="getLogLevelColor(selectedLog.level)">
              {{ selectedLog.level.toUpperCase() }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="消息" :span="2">
            {{ selectedLog.message }}
          </a-descriptions-item>
          <a-descriptions-item label="详细信息" :span="2" v-if="selectedLog.details">
            <pre class="log-details-pre">{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';
import {
  IconDashboard,
  IconThunderbolt,
  IconClockCircle,
  IconSync,
  IconRefresh,
  IconDelete,
  IconDownload,
  IconEye,
  IconBarChart,
  IconPlayArrow,
  IconPause,
  IconStop,
  IconCheckCircle,
  IconExclamationCircle
} from '@arco-design/web-vue/es/icon';

// Props
const props = defineProps({
  syncData: {
    type: Object,
    default: () => ({})
  },
  isActive: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['log-clear', 'log-export', 'data-refresh']);

// 响应式数据
const autoRefresh = ref(true);
const logFilter = ref('all');
const logDetailVisible = ref(false);
const selectedLog = ref(null);
const logListRef = ref(null);

// 监控数据
const monitorData = reactive({
  status: 'idle',
  totalProgress: 0,
  processingRate: 0,
  estimatedTime: '--',
  customerSync: {
    current: 0,
    total: 0,
    percentage: 0,
    status: 'normal'
  },
  agreementSync: {
    current: 0,
    total: 0,
    percentage: 0,
    status: 'normal'
  },
  validation: {
    current: 0,
    total: 0,
    percentage: 0,
    status: 'normal'
  }
});

// 日志数据
const logs = ref([]);
const logStats = reactive({
  error: 0,
  warning: 0,
  info: 0,
  success: 0
});

// 计算属性
const filteredLogs = computed(() => {
  if (logFilter.value === 'all') {
    return logs.value;
  }
  return logs.value.filter(log => log.level === logFilter.value);
});

const statsTableData = computed(() => [
  {
    label: '客户数据',
    success: monitorData.customerSync.current,
    failed: 0,
    skipped: 0,
    total: monitorData.customerSync.total,
    successRate:
      monitorData.customerSync.total > 0
        ? Math.round((monitorData.customerSync.current / monitorData.customerSync.total) * 100)
        : 0
  },
  {
    label: '协议数据',
    success: monitorData.agreementSync.current,
    failed: 0,
    skipped: 0,
    total: monitorData.agreementSync.total,
    successRate:
      monitorData.agreementSync.total > 0
        ? Math.round((monitorData.agreementSync.current / monitorData.agreementSync.total) * 100)
        : 0
  },
  {
    label: '数据验证',
    success: monitorData.validation.current,
    failed: 0,
    skipped: 0,
    total: monitorData.validation.total,
    successRate:
      monitorData.validation.total > 0
        ? Math.round((monitorData.validation.current / monitorData.validation.total) * 100)
        : 0
  }
]);

// 方法
const getStatusIcon = status => {
  const icons = {
    idle: 'IconPause',
    running: 'IconPlayArrow',
    completed: 'IconCheckCircle',
    error: 'IconExclamationCircle',
    paused: 'IconPause'
  };
  return icons[status] || 'IconPause';
};

const getStatusStyle = status => {
  const styles = {
    idle: { color: '#86909c' },
    running: { color: '#165dff' },
    completed: { color: '#00b42a' },
    error: { color: '#f53f3f' },
    paused: { color: '#ff7d00' }
  };
  return styles[status] || { color: '#86909c' };
};

const getSuccessRateColor = rate => {
  if (rate >= 95) return 'green';
  if (rate >= 80) return 'blue';
  if (rate >= 60) return 'orange';
  return 'red';
};

const getLogLevelColor = level => {
  const colors = {
    error: 'red',
    warning: 'orange',
    info: 'blue',
    success: 'green'
  };
  return colors[level] || 'gray';
};

const formatLogTime = timestamp => {
  return new Date(timestamp).toLocaleTimeString();
};

const formatDateTime = timestamp => {
  return new Date(timestamp).toLocaleString();
};

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value;
  if (autoRefresh.value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

const refreshData = () => {
  emit('data-refresh');
  Message.success('数据已刷新');
};

const clearLogs = () => {
  logs.value = [];
  Object.keys(logStats).forEach(key => {
    logStats[key] = 0;
  });
  emit('log-clear');
  Message.success('日志已清空');
};

const exportLogs = () => {
  const logData = filteredLogs.value
    .map(log => `[${formatDateTime(log.timestamp)}] ${log.level.toUpperCase()}: ${log.message}`)
    .join('\n');

  const blob = new Blob([logData], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `crm-sync-logs-${new Date().toISOString().split('T')[0]}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  emit('log-export');
  Message.success('日志导出成功');
};

const showLogDetails = log => {
  selectedLog.value = log;
  logDetailVisible.value = true;
};

const addLog = (level, message, details = null) => {
  const log = {
    timestamp: new Date(),
    level,
    message,
    details
  };

  logs.value.unshift(log);
  logStats[level]++;

  // 限制日志数量
  if (logs.value.length > 1000) {
    logs.value = logs.value.slice(0, 1000);
  }

  // 自动滚动到最新日志
  nextTick(() => {
    if (logListRef.value) {
      logListRef.value.scrollTop = 0;
    }
  });
};

const updateMonitorData = data => {
  Object.assign(monitorData, data);
};

// 自动刷新相关
let refreshTimer = null;

const startAutoRefresh = () => {
  if (refreshTimer) return;

  refreshTimer = setInterval(() => {
    if (props.isActive) {
      refreshData();
    }
  }, 5000); // 每5秒刷新一次
};

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 生命周期
onMounted(() => {
  if (autoRefresh.value && props.isActive) {
    startAutoRefresh();
  }
});

onUnmounted(() => {
  stopAutoRefresh();
});

// 暴露方法给父组件
defineExpose({
  addLog,
  updateMonitorData,
  clearLogs
});
</script>

<style lang="less" scoped>
.crm-sync-monitor {
  .status-overview,
  .progress-details,
  .log-monitor,
  .performance-charts {
    margin-bottom: 24px;
  }

  .status-card {
    height: 100%;

    :deep(.arco-card-body) {
      padding: 20px;
    }

    :deep(.arco-statistic) {
      .arco-statistic-title {
        font-size: 14px;
        color: #86909c;
        margin-bottom: 8px;
      }

      .arco-statistic-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .arco-statistic-content-prefix {
          font-size: 20px;
        }

        .arco-statistic-content-value {
          font-size: 24px;
          font-weight: 600;
        }

        .arco-statistic-content-suffix {
          font-size: 14px;
          color: #86909c;
        }
      }
    }
  }

  .progress-details {
    :deep(.arco-card-header) {
      border-bottom: 1px solid #f2f3f5;
      padding: 20px 24px;

      .arco-card-header-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    :deep(.arco-card-body) {
      padding: 24px;
    }

    .progress-group {
      margin-bottom: 32px;

      .progress-item {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .progress-label {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          font-size: 14px;

          span:first-child {
            font-weight: 500;
            color: #1d2129;
          }

          .progress-text {
            color: #86909c;
            font-size: 12px;
          }
        }

        :deep(.arco-progress) {
          .arco-progress-line-text {
            font-weight: 600;
          }
        }
      }
    }

    .stats-table {
      h4 {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
      }

      :deep(.arco-table) {
        .arco-table-th {
          background: #f7f8fa;
          font-weight: 600;
        }

        .success-text {
          color: #00b42a;
          font-weight: 500;
        }

        .error-text {
          color: #f53f3f;
          font-weight: 500;
        }

        .warning-text {
          color: #ff7d00;
          font-weight: 500;
        }
      }
    }
  }

  .log-monitor {
    :deep(.arco-card-header) {
      border-bottom: 1px solid #f2f3f5;
      padding: 20px 24px;

      .arco-card-header-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    :deep(.arco-card-body) {
      padding: 24px;
    }

    .log-container {
      .log-stats {
        margin-bottom: 16px;
        padding: 12px;
        background: #f7f8fa;
        border-radius: 6px;
      }

      .log-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #f2f3f5;
        border-radius: 6px;
        background: white;

        .log-entry {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 8px 12px;
          border-bottom: 1px solid #f7f8fa;
          font-size: 12px;
          transition: background-color 0.2s;

          &:hover {
            background: #f7f8fa;
          }

          &:last-child {
            border-bottom: none;
          }

          .log-time {
            color: #86909c;
            white-space: nowrap;
            width: 80px;
          }

          .log-level {
            width: 60px;
          }

          .log-message {
            flex: 1;
            color: #1d2129;
            line-height: 1.4;
          }

          .log-details {
            width: 60px;
            text-align: right;
          }

          &.error {
            border-left: 3px solid #f53f3f;
          }

          &.warning {
            border-left: 3px solid #ff7d00;
          }

          &.success {
            border-left: 3px solid #00b42a;
          }

          &.info {
            border-left: 3px solid #165dff;
          }
        }

        .log-empty {
          padding: 40px;
          text-align: center;
        }
      }
    }
  }

  .performance-charts {
    .chart-card {
      height: 300px;

      :deep(.arco-card-header) {
        border-bottom: 1px solid #f2f3f5;
        padding: 16px 20px;

        .arco-card-header-title {
          font-size: 14px;
          font-weight: 600;
        }
      }

      :deep(.arco-card-body) {
        padding: 20px;
        height: calc(100% - 57px);
      }

      .chart-container {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .chart-placeholder {
          text-align: center;
          color: #86909c;

          .arco-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #c9cdd4;
          }

          p {
            margin: 8px 0;
            font-size: 16px;
            font-weight: 500;
          }

          .chart-desc {
            font-size: 12px;
            color: #86909c;
          }
        }
      }
    }
  }

  .log-detail-content {
    .log-details-pre {
      background: #f7f8fa;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 1.4;
      max-height: 300px;
      overflow-y: auto;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .crm-sync-monitor {
    .status-overview {
      .arco-col {
        margin-bottom: 16px;
      }
    }

    .performance-charts {
      .arco-col {
        margin-bottom: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .crm-sync-monitor {
    .status-overview,
    .progress-details,
    .log-monitor,
    .performance-charts {
      margin-bottom: 16px;
    }

    .progress-details,
    .log-monitor {
      :deep(.arco-card-header),
      :deep(.arco-card-body) {
        padding: 16px;
      }
    }

    .log-monitor {
      .log-container {
        .log-list {
          max-height: 300px;

          .log-entry {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
            padding: 12px;

            .log-time,
            .log-level,
            .log-details {
              width: auto;
            }

            .log-message {
              width: 100%;
            }
          }
        }
      }
    }

    .performance-charts {
      .chart-card {
        height: 250px;
      }
    }
  }
}
</style>
