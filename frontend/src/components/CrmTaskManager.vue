<template>
  <div class="crm-task-manager">
    <!-- 任务调度配置 -->
    <div class="task-scheduler">
      <a-card title="任务调度配置">
        <template #extra>
          <a-space>
            <a-switch
              v-model="schedulerEnabled"
              checked-text="启用调度"
              unchecked-text="禁用调度"
              @change="toggleScheduler"
            />
            <a-button type="primary" @click="showCreateTaskModal">
              <template #icon>
                <icon-plus />
              </template>
              创建任务
            </a-button>
          </a-space>
        </template>

        <!-- 快速调度选项 -->
        <div class="quick-schedule">
          <h4>快速调度</h4>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card class="schedule-card" @click="createQuickTask('hourly')">
                <div class="schedule-option">
                  <icon-clock-circle />
                  <span>每小时同步</span>
                  <p>适用于高频数据更新</p>
                </div>
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card class="schedule-card" @click="createQuickTask('daily')">
                <div class="schedule-option">
                  <icon-calendar />
                  <span>每日同步</span>
                  <p>适用于日常数据维护</p>
                </div>
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card class="schedule-card" @click="createQuickTask('weekly')">
                <div class="schedule-option">
                  <icon-calendar-clock />
                  <span>每周同步</span>
                  <p>适用于定期数据整理</p>
                </div>
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card class="schedule-card" @click="createQuickTask('custom')">
                <div class="schedule-option">
                  <icon-settings />
                  <span>自定义调度</span>
                  <p>灵活配置同步时间</p>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 任务队列管理 -->
    <div class="task-queue">
      <a-card title="任务队列">
        <template #extra>
          <a-space>
            <a-button type="text" @click="refreshQueue">
              <icon-refresh />
              刷新队列
            </a-button>
            <a-button type="text" @click="clearCompletedTasks">
              <icon-delete />
              清理已完成
            </a-button>
          </a-space>
        </template>

        <!-- 队列统计 -->
        <div class="queue-stats">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="等待中" :value="queueStats.pending" :value-style="{ color: '#165dff' }" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="执行中" :value="queueStats.running" :value-style="{ color: '#00b42a' }" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="已完成" :value="queueStats.completed" :value-style="{ color: '#86909c' }" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="失败" :value="queueStats.failed" :value-style="{ color: '#f53f3f' }" />
            </a-col>
          </a-row>
        </div>

        <!-- 任务列表 -->
        <div class="task-list">
          <a-table :data="taskQueue" :pagination="{ pageSize: 10 }" :loading="queueLoading">
            <template #columns>
              <a-table-column title="任务名称" data-index="name" :width="200">
                <template #cell="{ record }">
                  <div class="task-name">
                    <component :is="getTaskIcon(record.type)" />
                    <span>{{ record.name }}</span>
                  </div>
                </template>
              </a-table-column>

              <a-table-column title="类型" data-index="type" :width="120">
                <template #cell="{ record }">
                  <a-tag :color="getTaskTypeColor(record.type)">
                    {{ getTaskTypeText(record.type) }}
                  </a-tag>
                </template>
              </a-table-column>

              <a-table-column title="状态" data-index="status" :width="100">
                <template #cell="{ record }">
                  <a-tag :color="getTaskStatusColor(record.status)">
                    {{ getTaskStatusText(record.status) }}
                  </a-tag>
                </template>
              </a-table-column>

              <a-table-column title="进度" data-index="progress" :width="120">
                <template #cell="{ record }">
                  <a-progress :percent="record.progress" :status="getProgressStatus(record.status)" size="small" />
                </template>
              </a-table-column>

              <a-table-column title="创建时间" data-index="createdAt" :width="150">
                <template #cell="{ record }">
                  {{ formatDateTime(record.createdAt) }}
                </template>
              </a-table-column>

              <a-table-column title="下次执行" data-index="nextRun" :width="150">
                <template #cell="{ record }">
                  {{ record.nextRun ? formatDateTime(record.nextRun) : '-' }}
                </template>
              </a-table-column>

              <a-table-column title="操作" :width="200" fixed="right">
                <template #cell="{ record }">
                  <a-space>
                    <a-button type="text" size="mini" @click="viewTaskDetails(record)">
                      <icon-eye />
                      详情
                    </a-button>

                    <a-button v-if="record.status === 'pending'" type="text" size="mini" @click="runTaskNow(record)">
                      <icon-play-arrow />
                      立即执行
                    </a-button>

                    <a-button v-if="record.status === 'running'" type="text" size="mini" @click="pauseTask(record)">
                      <icon-pause />
                      暂停
                    </a-button>

                    <a-button type="text" size="mini" @click="editTask(record)" :disabled="record.status === 'running'">
                      <icon-edit />
                      编辑
                    </a-button>

                    <a-popconfirm content="确定要删除这个任务吗？" @ok="deleteTask(record)">
                      <a-button type="text" size="mini" status="danger" :disabled="record.status === 'running'">
                        <icon-delete />
                        删除
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </a-card>
    </div>

    <!-- 执行历史 -->
    <div class="execution-history">
      <a-card title="执行历史">
        <template #extra>
          <a-space>
            <a-range-picker v-model="historyDateRange" @change="filterHistory" size="small" />
            <a-button type="text" @click="exportHistory">
              <icon-download />
              导出历史
            </a-button>
          </a-space>
        </template>

        <a-table :data="executionHistory" :pagination="{ pageSize: 15 }" :loading="historyLoading">
          <template #columns>
            <a-table-column title="任务名称" data-index="taskName" :width="200" />

            <a-table-column title="执行时间" data-index="executedAt" :width="150">
              <template #cell="{ record }">
                {{ formatDateTime(record.executedAt) }}
              </template>
            </a-table-column>

            <a-table-column title="执行结果" data-index="result" :width="120">
              <template #cell="{ record }">
                <a-tag :color="record.result === 'success' ? 'green' : 'red'">
                  {{ record.result === 'success' ? '成功' : '失败' }}
                </a-tag>
              </template>
            </a-table-column>

            <a-table-column title="处理数量" data-index="processedCount" :width="100" />

            <a-table-column title="耗时" data-index="duration" :width="100">
              <template #cell="{ record }"> {{ record.duration }}秒 </template>
            </a-table-column>

            <a-table-column title="错误信息" data-index="errorMessage" :width="200">
              <template #cell="{ record }">
                <span v-if="record.errorMessage" class="error-message">
                  {{ record.errorMessage }}
                </span>
                <span v-else class="success-message">-</span>
              </template>
            </a-table-column>

            <a-table-column title="操作" :width="120">
              <template #cell="{ record }">
                <a-space>
                  <a-button type="text" size="mini" @click="viewExecutionDetails(record)">
                    <icon-eye />
                    详情
                  </a-button>

                  <a-button v-if="record.result === 'success'" type="text" size="mini" @click="rerunTask(record)">
                    <icon-refresh />
                    重新执行
                  </a-button>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建任务模态框 -->
    <a-modal
      v-model:visible="createTaskVisible"
      title="创建同步任务"
      width="600px"
      @ok="handleCreateTask"
      @cancel="resetCreateForm"
    >
      <a-form :model="newTask" layout="vertical">
        <a-form-item label="任务名称" required>
          <a-input v-model="newTask.name" placeholder="请输入任务名称" />
        </a-form-item>

        <a-form-item label="同步类型" required>
          <a-select v-model="newTask.type" placeholder="选择同步类型">
            <a-option value="customers">客户数据同步</a-option>
            <a-option value="complete">完整数据同步</a-option>
            <a-option value="all">全量数据同步</a-option>
          </a-select>
        </a-form-item>

        <a-form-item label="调度类型" required>
          <a-radio-group v-model="newTask.scheduleType">
            <a-radio value="once">立即执行</a-radio>
            <a-radio value="recurring">定期执行</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="newTask.scheduleType === 'recurring'" label="执行频率">
          <a-select v-model="newTask.frequency" placeholder="选择执行频率">
            <a-option value="hourly">每小时</a-option>
            <a-option value="daily">每天</a-option>
            <a-option value="weekly">每周</a-option>
            <a-option value="monthly">每月</a-option>
          </a-select>
        </a-form-item>

        <a-form-item v-if="newTask.scheduleType === 'recurring'" label="开始时间">
          <a-date-picker
            v-model="newTask.startTime"
            show-time
            placeholder="选择开始时间"
            style="width: 100%"
            format="YYYY/MM/DD HH:mm:ss"
            value-format="YYYY/MM/DD HH:mm:ss"
          />
        </a-form-item>

        <a-form-item label="任务描述">
          <a-textarea v-model="newTask.description" placeholder="请输入任务描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 任务详情模态框 -->
    <a-modal v-model:visible="taskDetailVisible" title="任务详情" width="800px" :footer="false">
      <div v-if="selectedTask" class="task-detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="任务名称">
            {{ selectedTask.name }}
          </a-descriptions-item>
          <a-descriptions-item label="任务类型">
            <a-tag :color="getTaskTypeColor(selectedTask.type)">
              {{ getTaskTypeText(selectedTask.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getTaskStatusColor(selectedTask.status)">
              {{ getTaskStatusText(selectedTask.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="执行进度">
            <a-progress :percent="selectedTask.progress" />
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(selectedTask.createdAt) }}
          </a-descriptions-item>
          <a-descriptions-item label="下次执行">
            {{ selectedTask.nextRun ? formatDateTime(selectedTask.nextRun) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="任务描述" :span="2">
            {{ selectedTask.description || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import {
  IconPlus,
  IconClockCircle,
  IconCalendar,
  IconCalendarClock,
  IconSettings,
  IconRefresh,
  IconDelete,
  IconEye,
  IconPlayArrow,
  IconPause,
  IconEdit,
  IconDownload,
  IconUserGroup,
  IconInteraction,
  IconApps
} from '@arco-design/web-vue/es/icon';

// 响应式数据
const schedulerEnabled = ref(true);
const queueLoading = ref(false);
const historyLoading = ref(false);
const createTaskVisible = ref(false);
const taskDetailVisible = ref(false);
const selectedTask = ref(null);
const historyDateRange = ref([]);

// 队列统计
const queueStats = reactive({
  pending: 3,
  running: 1,
  completed: 15,
  failed: 2
});

// 新任务表单
const newTask = reactive({
  name: '',
  type: 'customers',
  scheduleType: 'once',
  frequency: 'daily',
  startTime: null,
  description: ''
});

// 任务队列数据
const taskQueue = ref([
  {
    id: 1,
    name: '每日客户数据同步',
    type: 'customers',
    status: 'running',
    progress: 65,
    createdAt: new Date(Date.now() - 86400000),
    nextRun: new Date(Date.now() + 3600000),
    description: '每日定时同步客户数据'
  },
  {
    id: 2,
    name: '完整数据同步',
    type: 'complete',
    status: 'pending',
    progress: 0,
    createdAt: new Date(Date.now() - 3600000),
    nextRun: new Date(Date.now() + 1800000),
    description: '同步客户和协议数据'
  },
  {
    id: 3,
    name: '全量数据同步',
    type: 'all',
    status: 'completed',
    progress: 100,
    createdAt: new Date(Date.now() - 7200000),
    nextRun: null,
    description: '一次性全量数据同步'
  }
]);

// 执行历史数据
const executionHistory = ref([
  {
    id: 1,
    taskName: '每日客户数据同步',
    executedAt: new Date(Date.now() - 86400000),
    result: 'success',
    processedCount: 150,
    duration: 45,
    errorMessage: null
  },
  {
    id: 2,
    taskName: '完整数据同步',
    executedAt: new Date(Date.now() - 172800000),
    result: 'failed',
    processedCount: 0,
    duration: 10,
    errorMessage: 'CRM连接超时'
  }
]);

// 方法
const toggleScheduler = enabled => {
  Message.info(enabled ? '任务调度已启用' : '任务调度已禁用');
};

const createQuickTask = type => {
  const taskTemplates = {
    hourly: { name: '每小时同步', frequency: 'hourly', type: 'customers' },
    daily: { name: '每日同步', frequency: 'daily', type: 'complete' },
    weekly: { name: '每周同步', frequency: 'weekly', type: 'all' },
    custom: { name: '自定义任务', frequency: 'daily', type: 'customers' }
  };

  const template = taskTemplates[type];
  Object.assign(newTask, {
    ...template,
    scheduleType: 'recurring',
    description: `${template.name}任务`
  });

  createTaskVisible.value = true;
};

const showCreateTaskModal = () => {
  createTaskVisible.value = true;
};

const handleCreateTask = () => {
  // 验证表单
  if (!newTask.name || !newTask.type) {
    Message.error('请填写必填字段');
    return;
  }

  // 创建新任务
  const task = {
    id: Date.now(),
    ...newTask,
    status: 'pending',
    progress: 0,
    createdAt: new Date(),
    nextRun: newTask.scheduleType === 'recurring' ? newTask.startTime || new Date() : null
  };

  taskQueue.value.unshift(task);
  queueStats.pending++;

  Message.success('任务创建成功');
  createTaskVisible.value = false;
  resetCreateForm();
};

const resetCreateForm = () => {
  Object.assign(newTask, {
    name: '',
    type: 'customers',
    scheduleType: 'once',
    frequency: 'daily',
    startTime: null,
    description: ''
  });
};

// 工具方法
const getTaskIcon = type => {
  const icons = {
    customers: 'IconUserGroup',
    complete: 'IconInteraction',
    all: 'IconApps'
  };
  return icons[type] || 'IconUserGroup';
};

const getTaskTypeColor = type => {
  const colors = {
    customers: 'blue',
    complete: 'green',
    all: 'orange'
  };
  return colors[type] || 'blue';
};

const getTaskTypeText = type => {
  const texts = {
    customers: '客户同步',
    complete: '完整同步',
    all: '全量同步'
  };
  return texts[type] || '未知';
};

const getTaskStatusColor = status => {
  const colors = {
    pending: 'blue',
    running: 'green',
    completed: 'gray',
    failed: 'red',
    paused: 'orange'
  };
  return colors[status] || 'gray';
};

const getTaskStatusText = status => {
  const texts = {
    pending: '等待中',
    running: '执行中',
    completed: '已完成',
    failed: '失败',
    paused: '已暂停'
  };
  return texts[status] || '未知';
};

const getProgressStatus = status => {
  if (status === 'failed') return 'exception';
  if (status === 'completed') return 'success';
  return 'normal';
};

const formatDateTime = date => {
  if (!date) return '-';
  return new Date(date).toLocaleString();
};

const refreshQueue = () => {
  queueLoading.value = true;
  setTimeout(() => {
    queueLoading.value = false;
    Message.success('队列已刷新');
  }, 1000);
};

const clearCompletedTasks = () => {
  const completedCount = taskQueue.value.filter(task => task.status === 'completed').length;
  taskQueue.value = taskQueue.value.filter(task => task.status !== 'completed');
  queueStats.completed = 0;
  Message.success(`已清理 ${completedCount} 个已完成任务`);
};

const viewTaskDetails = task => {
  selectedTask.value = task;
  taskDetailVisible.value = true;
};

const runTaskNow = task => {
  task.status = 'running';
  task.progress = 0;
  queueStats.pending--;
  queueStats.running++;
  Message.success('任务已开始执行');

  // 模拟任务执行
  const interval = setInterval(() => {
    task.progress += 10;
    if (task.progress >= 100) {
      task.status = 'completed';
      task.progress = 100;
      queueStats.running--;
      queueStats.completed++;
      clearInterval(interval);
      Message.success('任务执行完成');
    }
  }, 1000);
};

const pauseTask = task => {
  task.status = 'paused';
  queueStats.running--;
  queueStats.pending++;
  Message.info('任务已暂停');
};

const editTask = task => {
  Object.assign(newTask, task);
  createTaskVisible.value = true;
};

const deleteTask = task => {
  const index = taskQueue.value.findIndex(t => t.id === task.id);
  if (index > -1) {
    taskQueue.value.splice(index, 1);
    queueStats[task.status]--;
    Message.success('任务已删除');
  }
};

const filterHistory = () => {
  historyLoading.value = true;
  setTimeout(() => {
    historyLoading.value = false;
    Message.success('历史记录已筛选');
  }, 500);
};

const exportHistory = () => {
  Message.success('历史记录导出成功');
};

const viewExecutionDetails = record => {
  Modal.info({
    title: '执行详情',
    content: `任务: ${record.taskName}\n执行时间: ${formatDateTime(record.executedAt)}\n处理数量: ${
      record.processedCount
    }\n耗时: ${record.duration}秒`,
    width: 500
  });
};

const rerunTask = record => {
  Message.success('任务已重新加入队列');
};

// 生命周期
onMounted(() => {
  // 初始化数据
});
</script>

<style lang="less" scoped>
.crm-task-manager {
  .task-scheduler,
  .task-queue,
  .execution-history {
    margin-bottom: 24px;
  }

  .task-scheduler {
    :deep(.arco-card-header) {
      border-bottom: 1px solid #f2f3f5;
      padding: 20px 24px;

      .arco-card-header-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    :deep(.arco-card-body) {
      padding: 24px;
    }

    .quick-schedule {
      h4 {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
      }

      .schedule-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;

        &:hover {
          border-color: #165dff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(22, 93, 255, 0.15);
        }

        :deep(.arco-card-body) {
          padding: 20px;
          text-align: center;
        }

        .schedule-option {
          .arco-icon {
            font-size: 32px;
            color: #165dff;
            margin-bottom: 12px;
          }

          span {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 8px;
          }

          p {
            margin: 0;
            font-size: 12px;
            color: #86909c;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .task-queue {
    :deep(.arco-card-header) {
      border-bottom: 1px solid #f2f3f5;
      padding: 20px 24px;

      .arco-card-header-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    :deep(.arco-card-body) {
      padding: 24px;
    }

    .queue-stats {
      margin-bottom: 24px;
      padding: 20px;
      background: #f7f8fa;
      border-radius: 8px;

      :deep(.arco-statistic) {
        text-align: center;

        .arco-statistic-title {
          font-size: 14px;
          color: #86909c;
          margin-bottom: 8px;
        }

        .arco-statistic-content-value {
          font-size: 24px;
          font-weight: 600;
        }
      }
    }

    .task-list {
      :deep(.arco-table) {
        .task-name {
          display: flex;
          align-items: center;
          gap: 8px;

          .arco-icon {
            font-size: 16px;
            color: #165dff;
          }

          span {
            font-weight: 500;
          }
        }

        .arco-btn {
          &[status='danger'] {
            color: #f53f3f;

            &:hover {
              background: rgba(245, 63, 63, 0.1);
            }
          }
        }
      }
    }
  }

  .execution-history {
    :deep(.arco-card-header) {
      border-bottom: 1px solid #f2f3f5;
      padding: 20px 24px;

      .arco-card-header-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    :deep(.arco-card-body) {
      padding: 24px;
    }

    :deep(.arco-table) {
      .error-message {
        color: #f53f3f;
        font-size: 12px;
      }

      .success-message {
        color: #86909c;
      }
    }
  }

  .task-detail-content {
    :deep(.arco-descriptions) {
      .arco-descriptions-item-label {
        font-weight: 600;
        color: #4e5969;
      }

      .arco-descriptions-item-value {
        color: #1d2129;
      }
    }
  }
}

// 模态框样式
:deep(.arco-modal) {
  .arco-modal-header {
    border-bottom: 1px solid #f2f3f5;
    padding: 20px 24px;

    .arco-modal-title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .arco-modal-body {
    padding: 24px;
  }

  .arco-form {
    .arco-form-item {
      margin-bottom: 20px;

      .arco-form-item-label {
        font-weight: 500;
        color: #4e5969;
        margin-bottom: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .crm-task-manager {
    .task-scheduler {
      .quick-schedule {
        .arco-col {
          margin-bottom: 16px;
        }
      }
    }

    .task-queue {
      .queue-stats {
        .arco-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .crm-task-manager {
    .task-scheduler,
    .task-queue,
    .execution-history {
      margin-bottom: 16px;

      :deep(.arco-card-header),
      :deep(.arco-card-body) {
        padding: 16px;
      }
    }

    .task-scheduler {
      .quick-schedule {
        .schedule-card {
          margin-bottom: 12px;

          .schedule-option {
            .arco-icon {
              font-size: 24px;
            }

            span {
              font-size: 14px;
            }

            p {
              font-size: 11px;
            }
          }
        }
      }
    }

    .task-queue {
      .queue-stats {
        padding: 16px;

        :deep(.arco-statistic) {
          .arco-statistic-content-value {
            font-size: 20px;
          }
        }
      }

      .task-list {
        :deep(.arco-table) {
          .arco-table-cell {
            padding: 8px 4px;
          }

          .task-name {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }
        }
      }
    }
  }

  :deep(.arco-modal) {
    .arco-modal-header,
    .arco-modal-body {
      padding: 16px;
    }
  }
}
</style>
