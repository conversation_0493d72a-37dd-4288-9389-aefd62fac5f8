/**
 * 合作对接平台工具函数
 * 提供合作对接数据的平台字段显示逻辑
 * 
 * 功能说明：
 * - 优先使用 publishPlatform（发布平台）字段
 * - 备选使用 seedingPlatform（种草平台）字段
 * - 支持字典转换和降级处理
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { getPlatformColor, getPlatformName } from './platformUtils';

/**
 * 获取合作对接数据的平台显示文本
 * @param {Object} cooperationData 合作对接数据
 * @param {Object} dictionaryOptions 字典选项数据
 * @returns {string} 平台显示文本
 */
export const getCooperationPlatformText = (cooperationData, dictionaryOptions) => {
  if (!cooperationData) return '未知平台';
  
  // 优先使用 publishPlatform（发布平台）
  if (cooperationData.publishPlatform) {
    const publishPlatformDict = dictionaryOptions?.publishPlatform || [];
    const publishPlatformItem = publishPlatformDict.find(item => 
      item.dictKey === cooperationData.publishPlatform
    );
    return publishPlatformItem ? publishPlatformItem.dictLabel : cooperationData.publishPlatform;
  }
  
  // 备选使用 seedingPlatform（种草平台）
  if (cooperationData.seedingPlatform) {
    const seedingPlatformDict = dictionaryOptions?.seedingPlatform || [];
    const seedingPlatformItem = seedingPlatformDict.find(item => 
      item.dictKey === cooperationData.seedingPlatform
    );
    return seedingPlatformItem ? seedingPlatformItem.dictLabel : cooperationData.seedingPlatform;
  }
  
  // 降级处理：使用原有的platform字段
  if (cooperationData.platform) {
    return getPlatformName(cooperationData.platform);
  }
  
  // 都不存在时的降级处理
  return '未知平台';
};

/**
 * 获取合作对接数据的平台颜色
 * @param {Object} cooperationData 合作对接数据
 * @returns {string} 平台颜色
 */
export const getCooperationPlatformColor = (cooperationData) => {
  if (!cooperationData) return 'gray';
  
  // 获取平台值（优先publishPlatform，备选seedingPlatform）
  const platformValue = cooperationData.publishPlatform || cooperationData.seedingPlatform;
  
  if (!platformValue) {
    // 降级处理：使用原有的platform字段
    return getPlatformColor(cooperationData.platform);
  }
  
  // 根据平台值返回颜色（可以根据实际需要调整）
  const platformColorMap = {
    '小红书': 'red',
    '抖音': 'blue',
    '快手': 'orange',
    '微博': 'purple',
    'B站': 'cyan',
    '知乎': 'green',
    '淘宝': 'orange',
    '微信&视频号': 'green',
    '其他': 'gray',
    '置换': 'purple'
  };
  
  return platformColorMap[platformValue] || 'blue';
};

/**
 * 获取合作对接数据的平台原始值
 * @param {Object} cooperationData 合作对接数据
 * @returns {string} 平台原始值
 */
export const getCooperationPlatformValue = (cooperationData) => {
  if (!cooperationData) return '';
  
  // 优先使用 publishPlatform（发布平台）
  if (cooperationData.publishPlatform) {
    return cooperationData.publishPlatform;
  }
  
  // 备选使用 seedingPlatform（种草平台）
  if (cooperationData.seedingPlatform) {
    return cooperationData.seedingPlatform;
  }
  
  // 降级处理：使用原有的platform字段
  if (cooperationData.platform) {
    return cooperationData.platform;
  }
  
  return '';
};

/**
 * 检查合作对接数据是否有平台信息
 * @param {Object} cooperationData 合作对接数据
 * @returns {boolean} 是否有平台信息
 */
export const hasCooperationPlatformInfo = (cooperationData) => {
  if (!cooperationData) return false;
  
  return !!(
    cooperationData.publishPlatform || 
    cooperationData.seedingPlatform || 
    cooperationData.platform
  );
};

/**
 * 获取平台字段的优先级信息
 * @param {Object} cooperationData 合作对接数据
 * @returns {Object} 平台字段优先级信息
 */
export const getCooperationPlatformPriority = (cooperationData) => {
  if (!cooperationData) {
    return { field: null, value: null, priority: 0 };
  }
  
  if (cooperationData.publishPlatform) {
    return { 
      field: 'publishPlatform', 
      value: cooperationData.publishPlatform, 
      priority: 1 
    };
  }
  
  if (cooperationData.seedingPlatform) {
    return { 
      field: 'seedingPlatform', 
      value: cooperationData.seedingPlatform, 
      priority: 2 
    };
  }
  
  if (cooperationData.platform) {
    return { 
      field: 'platform', 
      value: cooperationData.platform, 
      priority: 3 
    };
  }
  
  return { field: null, value: null, priority: 0 };
};

/**
 * 需要的字典分类常量
 */
export const COOPERATION_PLATFORM_DICTIONARY_CATEGORIES = [
  'seedingPlatform', // 种草平台
  'publishPlatform'  // 发布平台
];

/**
 * 创建字典数据加载函数
 * @param {Function} dictionaryService 字典服务实例
 * @returns {Function} 加载字典数据的函数
 */
export const createDictionaryLoader = (dictionaryService) => {
  return async () => {
    try {
      // 使用批量获取接口，CRM数据优先
      const batchData = await dictionaryService.getBatchDictionaries(
        COOPERATION_PLATFORM_DICTIONARY_CATEGORIES, 
        {
          crmFirst: true,
          includeLocal: true,
          includeCrm: true
        }
      );

      // 简化的数据转换
      const dictionaryOptions = {};

      COOPERATION_PLATFORM_DICTIONARY_CATEGORIES.forEach(category => {
        const data = batchData[category] || [];
        dictionaryOptions[category] = data.map(item => ({
          dictKey: item.value || item.dictKey,
          dictLabel: item.label || item.dictLabel
        }));
      });

      return dictionaryOptions;
    } catch (error) {
      console.error('加载合作对接平台字典数据失败:', error);
      
      // 简单降级：返回空数据
      const dictionaryOptions = {};
      COOPERATION_PLATFORM_DICTIONARY_CATEGORIES.forEach(category => {
        dictionaryOptions[category] = [];
      });
      
      return dictionaryOptions;
    }
  };
};
