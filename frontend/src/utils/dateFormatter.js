/**
 * 前端日期格式化工具
 * 统一使用 YYYY/MM/DD 格式
 */

/**
 * 格式化日期为 YYYY/MM/DD 格式
 * @param {string|Date} date 日期
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date) {
  if (!date) return '-';
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '-';
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
}

/**
 * 格式化日期时间为 YYYY/MM/DD HH:mm:ss 格式
 * @param {string|Date} dateTime 日期时间
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(dateTime) {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  if (isNaN(date.getTime())) return '-';
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 格式化时间为 HH:mm:ss 格式
 * @param {string|Date} time 时间
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time) {
  if (!time) return '-';
  const date = new Date(time);
  if (isNaN(date.getTime())) return '-';
  
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
}

/**
 * 获取当前日期的 YYYY/MM/DD 格式
 * @returns {string} 当前日期
 */
export function getCurrentDate() {
  return formatDate(new Date());
}

/**
 * 获取当前日期时间的 YYYY/MM/DD HH:mm:ss 格式
 * @returns {string} 当前日期时间
 */
export function getCurrentDateTime() {
  return formatDateTime(new Date());
}

/**
 * 解析日期字符串，支持多种格式
 * @param {string} dateStr 日期字符串
 * @returns {Date|null} 解析后的日期对象
 */
export function parseDate(dateStr) {
  if (!dateStr) return null;
  
  // 支持的格式：YYYY/MM/DD, YYYY-MM-DD, YYYY.MM.DD
  const normalizedStr = dateStr.replace(/[.\-]/g, '/');
  const date = new Date(normalizedStr);
  
  return isNaN(date.getTime()) ? null : date;
}

/**
 * 计算两个日期之间的天数差
 * @param {string|Date} startDate 开始日期
 * @param {string|Date} endDate 结束日期
 * @returns {number} 天数差
 */
export function daysBetween(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0;
  
  const diffTime = Math.abs(end - start);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * 检查日期是否为今天
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  const today = new Date();
  const checkDate = new Date(date);
  
  return today.getFullYear() === checkDate.getFullYear() &&
         today.getMonth() === checkDate.getMonth() &&
         today.getDate() === checkDate.getDate();
}

/**
 * 获取相对时间描述（如：3天前、1小时前）
 * @param {string|Date} date 日期
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date) {
  if (!date) return '-';
  
  const now = new Date();
  const targetDate = new Date(date);
  const diffMs = now - targetDate;
  
  if (diffMs < 0) return '未来时间';
  
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffDays > 0) return `${diffDays}天前`;
  if (diffHours > 0) return `${diffHours}小时前`;
  if (diffMinutes > 0) return `${diffMinutes}分钟前`;
  return '刚刚';
}

// 默认导出所有函数
export default {
  formatDate,
  formatDateTime,
  formatTime,
  getCurrentDate,
  getCurrentDateTime,
  parseDate,
  daysBetween,
  isToday,
  getRelativeTime
};
