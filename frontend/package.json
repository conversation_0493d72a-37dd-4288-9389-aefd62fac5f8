{"name": "agentx-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix"}, "dependencies": {"@arco-design/web-vue": "^2.57.0", "axios": "^1.10.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "less": "^4.3.0", "npm-run-all2": "^8.0.4", "vite": "^5.4.19", "vite-plugin-vue-devtools": "^7.7.7"}}