# Agent智能助手模块测试指南

## 测试环境

- **开发服务器**: http://localhost:5174
- **Agent URL**: http://dify.epochy.cn/chatbot/vwx203kISiUq1Nkl
- **浏览器要求**: 现代浏览器，支持iframe和麦克风权限

## 功能测试清单

### 1. 路由和导航测试

#### 测试步骤：
1. 启动开发服务器：`cd frontend && npm run dev`
2. 打开浏览器访问：http://localhost:5174
3. 登录系统（如果需要）
4. 在左侧导航菜单中查找"AI智能助手"菜单项
5. 点击"AI智能助手"菜单项

#### 预期结果：
- ✅ 菜单项显示正确的图标（消息图标）
- ✅ 菜单项文字为"AI智能助手"
- ✅ 点击后正确跳转到 `/agent` 路径
- ✅ 页面标题显示为"AI智能助手"
- ✅ 面包屑导航正确显示

### 2. 页面布局测试

#### 测试步骤：
1. 访问Agent页面
2. 检查页面整体布局
3. 测试响应式设计

#### 预期结果：
- ✅ 页面标题和描述正确显示
- ✅ "重新加载"按钮位于右上角
- ✅ Agent聊天界面卡片正确显示
- ✅ 使用提示卡片在底部显示
- ✅ 响应式设计在不同屏幕尺寸下正常工作

### 3. iframe加载测试

#### 测试步骤：
1. 访问Agent页面
2. 观察iframe加载过程
3. 等待加载完成

#### 预期结果：
- ✅ 初始显示加载动画和提示文字
- ✅ 加载动画有脉冲效果
- ✅ 15秒内iframe成功加载
- ✅ 加载完成后显示Dify聊天界面
- ✅ iframe尺寸正确（宽度100%，最小高度700px）

### 4. 错误处理测试

#### 测试步骤：
1. 断开网络连接
2. 访问Agent页面或点击重新加载
3. 观察错误处理

#### 预期结果：
- ✅ 显示错误图标和错误信息
- ✅ 错误信息友好易懂
- ✅ 显示"重试"按钮
- ✅ 点击重试按钮重新加载iframe

### 5. 交互功能测试

#### 测试步骤：
1. 确保iframe成功加载
2. 测试聊天功能
3. 测试语音功能（如果支持）

#### 预期结果：
- ✅ 可以在聊天框中输入文字
- ✅ AI助手能正常响应
- ✅ 麦克风权限请求正常（如果支持）
- ✅ 语音功能正常工作（如果支持）

### 6. 重新加载功能测试

#### 测试步骤：
1. 在Agent页面点击"重新加载"按钮
2. 观察重新加载过程

#### 预期结果：
- ✅ 点击按钮后显示加载状态
- ✅ iframe重新加载
- ✅ 加载完成后恢复正常状态

### 7. 响应式设计测试

#### 测试步骤：
1. 在不同设备尺寸下测试
2. 调整浏览器窗口大小

#### 预期结果：
- ✅ 桌面端（>768px）：iframe高度700px
- ✅ 平板端（≤768px）：iframe高度600px
- ✅ 手机端（≤480px）：iframe高度500px
- ✅ 页面布局在所有尺寸下都正常

### 8. 样式一致性测试

#### 测试步骤：
1. 检查Agent页面样式
2. 与其他页面对比

#### 预期结果：
- ✅ 页面标题样式与其他页面一致
- ✅ 卡片样式与项目风格一致
- ✅ 按钮样式与项目风格一致
- ✅ 颜色和字体与项目规范一致

## 浏览器兼容性测试

### 桌面浏览器
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (最新版本)
- [ ] Edge (最新版本)

### 移动浏览器
- [ ] iOS Safari
- [ ] Android Chrome
- [ ] 微信内置浏览器

## 性能测试

### 加载性能
- [ ] 首次加载时间 < 3秒
- [ ] iframe加载时间 < 15秒
- [ ] 页面切换流畅

### 内存使用
- [ ] 长时间使用无内存泄漏
- [ ] iframe资源正常释放

## 安全测试

### iframe安全
- [ ] iframe沙箱机制正常
- [ ] 跨域策略正确配置
- [ ] 麦克风权限请求安全

## 故障排除

### 常见问题及解决方案

1. **iframe加载失败**
   - 检查网络连接
   - 确认Dify服务可访问
   - 检查浏览器控制台错误

2. **语音功能不工作**
   - 确认浏览器支持麦克风
   - 检查麦克风权限设置
   - 确认HTTPS环境（生产环境）

3. **样式显示异常**
   - 检查CSS文件加载
   - 确认Less编译正常
   - 检查浏览器兼容性

4. **响应式布局问题**
   - 检查CSS媒体查询
   - 测试不同屏幕尺寸
   - 确认viewport设置

## 测试报告模板

```
测试日期：____
测试人员：____
浏览器版本：____
设备信息：____

功能测试结果：
- 路由导航：✅/❌
- 页面布局：✅/❌
- iframe加载：✅/❌
- 错误处理：✅/❌
- 交互功能：✅/❌
- 重新加载：✅/❌
- 响应式设计：✅/❌
- 样式一致性：✅/❌

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```

## 自动化测试建议

### 单元测试
- 组件渲染测试
- 事件处理测试
- 状态管理测试

### 集成测试
- 路由跳转测试
- iframe加载测试
- 错误处理测试

### E2E测试
- 完整用户流程测试
- 跨浏览器兼容性测试
- 性能基准测试
