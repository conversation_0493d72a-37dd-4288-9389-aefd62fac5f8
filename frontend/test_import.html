<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试导入功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>达人公海导入功能测试</h1>
    
    <div class="test-section">
        <h3>1. 获取达人公海列表</h3>
        <button class="btn-primary" onclick="getPublicInfluencers()">获取列表</button>
        <div id="list-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>2. 单个导入测试</h3>
        <input type="number" id="single-id" placeholder="输入达人ID" value="41">
        <button class="btn-success" onclick="testSingleImport()">单个导入</button>
        <div id="single-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>3. 批量导入测试</h3>
        <input type="text" id="batch-ids" placeholder="输入多个ID，用逗号分隔" value="41,42,43">
        <button class="btn-success" onclick="testBatchImport()">批量导入</button>
        <div id="batch-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>4. 错误测试</h3>
        <button class="btn-warning" onclick="testInvalidId()">测试无效ID</button>
        <button class="btn-warning" onclick="testEmptyBatch()">测试空批量</button>
        <div id="error-result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        // 获取token（从localStorage或手动设置）
        function getToken() {
            return localStorage.getItem('token') || 'test-token';
        }

        // 通用API调用函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(`${API_BASE}${url}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${getToken()}`,
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, status: response.status, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
        }

        // 获取达人公海列表
        async function getPublicInfluencers() {
            const result = await apiCall('/public-influencers?page=1&limit=5');
            showResult('list-result', result, result.success ? 'success' : 'error');
        }

        // 测试单个导入
        async function testSingleImport() {
            const id = document.getElementById('single-id').value;
            if (!id) {
                showResult('single-result', { error: '请输入达人ID' }, 'error');
                return;
            }

            const result = await apiCall(`/public-influencers/${id}/import`, {
                method: 'POST'
            });
            showResult('single-result', result, result.success ? 'success' : 'error');
        }

        // 测试批量导入
        async function testBatchImport() {
            const idsStr = document.getElementById('batch-ids').value;
            if (!idsStr) {
                showResult('batch-result', { error: '请输入达人ID列表' }, 'error');
                return;
            }

            const ids = idsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            
            const result = await apiCall('/public-influencers/batch-import', {
                method: 'POST',
                body: JSON.stringify({ ids })
            });
            showResult('batch-result', result, result.success ? 'success' : 'error');
        }

        // 测试无效ID
        async function testInvalidId() {
            const result = await apiCall('/public-influencers/99999/import', {
                method: 'POST'
            });
            showResult('error-result', result, 'info');
        }

        // 测试空批量导入
        async function testEmptyBatch() {
            const result = await apiCall('/public-influencers/batch-import', {
                method: 'POST',
                body: JSON.stringify({ ids: [] })
            });
            showResult('error-result', result, 'info');
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('测试页面已加载');
            console.log('当前token:', getToken());
            
            // 如果没有token，提示用户
            if (!localStorage.getItem('token')) {
                alert('请先在主页面登录，或者手动设置token到localStorage');
            }
        };
    </script>
</body>
</html>
