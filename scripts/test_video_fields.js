/**
 * 测试脚本：验证视频字段迁移是否成功
 */

const { sequelize } = require('../src/config/database');
const { Influencer } = require('../src/models');

async function testVideoFields() {
  try {
    console.log('🔄 开始测试视频字段...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 测试数据
    const testData = {
      nickname: '测试达人_视频字段',
      platform: 'xiaohongshu',
      platformId: 'test_video_fields_' + Date.now(),
      avatarUrl: 'https://example.com/avatar.jpg',
      followersCount: 10000,
      contactInfo: {
        wechat: 'test_wechat',
        phone: '138****0000'
      },
      videoStats: {
        videoCount: 50,
        averagePlay: 25000,
        totalPlay: 1250000,
        totalLike: 125000,
        totalComment: 12500,
        totalShare: 2500
      },
      videoDetails: [
        {
          videoId: 'video_001',
          title: '测试视频1',
          playCount: 30000,
          likeCount: 3000,
          commentCount: 300,
          shareCount: 150,
          publishTime: '2025-01-10T10:00:00Z',
          duration: 120
        },
        {
          videoId: 'video_002',
          title: '测试视频2',
          playCount: 20000,
          likeCount: 2000,
          commentCount: 200,
          shareCount: 100,
          publishTime: '2025-01-09T15:30:00Z',
          duration: 90
        }
      ],
      status: 'active'
    };
    
    // 创建测试记录
    console.log('📝 创建测试达人记录...');
    const influencer = await Influencer.create(testData);
    console.log('✅ 测试记录创建成功，ID:', influencer.id);
    
    // 读取并验证数据
    console.log('🔍 验证数据完整性...');
    const savedInfluencer = await Influencer.findByPk(influencer.id);
    
    if (!savedInfluencer) {
      throw new Error('无法找到保存的记录');
    }
    
    // 验证 videoStats 字段
    if (savedInfluencer.videoStats) {
      console.log('✅ videoStats 字段保存成功:');
      console.log('  - 视频数量:', savedInfluencer.videoStats.videoCount);
      console.log('  - 平均播放量:', savedInfluencer.videoStats.averagePlay);
      console.log('  - 总播放量:', savedInfluencer.videoStats.totalPlay);
    } else {
      console.log('❌ videoStats 字段保存失败');
    }
    
    // 验证 videoDetails 字段
    if (savedInfluencer.videoDetails && Array.isArray(savedInfluencer.videoDetails)) {
      console.log('✅ videoDetails 字段保存成功:');
      console.log('  - 视频详情数量:', savedInfluencer.videoDetails.length);
      savedInfluencer.videoDetails.forEach((video, index) => {
        console.log(`  - 视频${index + 1}: ${video.title} (播放量: ${video.playCount})`);
      });
    } else {
      console.log('❌ videoDetails 字段保存失败');
    }
    
    // 清理测试数据
    console.log('🧹 清理测试数据...');
    await savedInfluencer.destroy();
    console.log('✅ 测试数据清理完成');
    
    console.log('🎉 视频字段测试完成，所有功能正常！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行测试
if (require.main === module) {
  testVideoFields()
    .then(() => {
      console.log('✅ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = testVideoFields;
