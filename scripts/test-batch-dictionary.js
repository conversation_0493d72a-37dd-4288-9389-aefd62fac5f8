/**
 * 批量字典获取功能测试脚本
 * 
 * 功能说明：
 * - 测试批量字典获取API接口
 * - 验证数据格式和性能
 * - 测试前端字典服务的批量获取功能
 * 
 * 使用方法：
 * node scripts/test-batch-dictionary.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize, Dictionary } = require('../src/models');
const DictionaryController = require('../src/controllers/DictionaryController');
const dictionaryService = require('../src/services/DictionaryService');

class BatchDictionaryTester {
  constructor() {
    this.testResults = {
      api: { passed: 0, failed: 0, tests: [] },
      performance: { passed: 0, failed: 0, tests: [] },
      dataFormat: { passed: 0, failed: 0, tests: [] }
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始测试批量字典获取功能...\n');

    try {
      await this.setupTestData();
      await this.testAPIInterface();
      await this.testPerformance();
      await this.testDataFormat();
      await this.cleanupTestData();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    } finally {
      await sequelize.close();
    }
  }

  /**
   * 设置测试数据
   */
  async setupTestData() {
    console.log('📝 设置测试数据...');

    const testCategories = [
      'cooperation_form',
      'cooperation_brand',
      'rebate_status'
    ];

    const testData = [];
    
    testCategories.forEach((category, categoryIndex) => {
      for (let i = 1; i <= 3; i++) {
        testData.push({
          category: category,
          dictKey: `test_${category}_${i}`,
          dictLabel: `测试${category}选项${i}`,
          dictValue: `value_${i}`,
          sortOrder: i,
          status: 'active',
          description: `测试数据 - ${category}`,
          createdBy: 1,
          updatedBy: 1
        });
      }
    });

    for (const data of testData) {
      await Dictionary.create(data);
    }

    console.log('✅ 测试数据设置完成\n');
  }

  /**
   * 测试API接口
   */
  async testAPIInterface() {
    console.log('🔧 测试API接口...');

    // 使用导入的dictionaryService实例

    // 测试单个分类获取
    await this.runTest('api', '单个分类获取', async () => {
      const result = await dictionaryService.getDictionariesByCategory('cooperation_form', true);
      return Array.isArray(result) && result.length >= 3;
    });

    // 测试多个分类获取
    await this.runTest('api', '多个分类获取', async () => {
      const categories = ['cooperation_form', 'cooperation_brand'];
      const promises = categories.map(cat => 
        dictionaryService.getDictionariesByCategory(cat, true)
      );
      const results = await Promise.all(promises);
      return results.every(result => Array.isArray(result) && result.length >= 3);
    });

    // 测试空分类处理
    await this.runTest('api', '空分类处理', async () => {
      const result = await dictionaryService.getDictionariesByCategory('non_existent_category', true);
      return Array.isArray(result) && result.length === 0;
    });

    console.log('✅ API接口测试完成\n');
  }

  /**
   * 测试性能
   */
  async testPerformance() {
    console.log('⚡ 测试性能...');

    // 使用导入的dictionaryService实例
    const categories = ['cooperation_form', 'cooperation_brand', 'rebate_status'];

    // 测试串行获取性能
    await this.runTest('performance', '串行获取性能', async () => {
      const startTime = Date.now();
      
      for (const category of categories) {
        await dictionaryService.getDictionariesByCategory(category, true);
      }
      
      const serialTime = Date.now() - startTime;
      console.log(`  串行获取耗时: ${serialTime}ms`);
      return serialTime < 5000; // 应该在5秒内完成
    });

    // 测试并行获取性能
    await this.runTest('performance', '并行获取性能', async () => {
      const startTime = Date.now();
      
      const promises = categories.map(category => 
        dictionaryService.getDictionariesByCategory(category, true)
      );
      await Promise.all(promises);
      
      const parallelTime = Date.now() - startTime;
      console.log(`  并行获取耗时: ${parallelTime}ms`);
      return parallelTime < 3000; // 并行应该更快
    });

    // 测试大量分类获取
    await this.runTest('performance', '大量分类获取', async () => {
      const startTime = Date.now();
      
      const manyCategories = Array(10).fill().map((_, i) => 
        categories[i % categories.length]
      );
      
      const promises = manyCategories.map(category => 
        dictionaryService.getDictionariesByCategory(category, true)
      );
      await Promise.all(promises);
      
      const batchTime = Date.now() - startTime;
      console.log(`  大量分类获取耗时: ${batchTime}ms`);
      return batchTime < 10000; // 应该在10秒内完成
    });

    console.log('✅ 性能测试完成\n');
  }

  /**
   * 测试数据格式
   */
  async testDataFormat() {
    console.log('📋 测试数据格式...');

    // 使用导入的dictionaryService实例

    // 测试数据结构
    await this.runTest('dataFormat', '数据结构验证', async () => {
      const result = await dictionaryService.getDictionariesByCategory('cooperation_form', true);
      
      if (!Array.isArray(result) || result.length === 0) {
        return false;
      }
      
      const item = result[0];
      const requiredFields = ['id', 'category', 'dictKey', 'dictLabel', 'status'];
      
      return requiredFields.every(field => item.hasOwnProperty(field));
    });

    // 测试数据排序
    await this.runTest('dataFormat', '数据排序验证', async () => {
      const result = await dictionaryService.getDictionariesByCategory('cooperation_form', true);
      
      if (result.length < 2) {
        return true; // 少于2项无需验证排序
      }
      
      // 验证按sortOrder排序
      for (let i = 1; i < result.length; i++) {
        if (result[i].sortOrder < result[i-1].sortOrder) {
          return false;
        }
      }
      
      return true;
    });

    // 测试数据过滤
    await this.runTest('dataFormat', '数据过滤验证', async () => {
      const activeResult = await dictionaryService.getDictionariesByCategory('cooperation_form', true);
      const allResult = await dictionaryService.getDictionariesByCategory('cooperation_form', false);
      
      // 活跃数据应该少于或等于全部数据
      return activeResult.length <= allResult.length;
    });

    console.log('✅ 数据格式测试完成\n');
  }

  /**
   * 清理测试数据
   */
  async cleanupTestData() {
    console.log('🧹 清理测试数据...');

    await Dictionary.destroy({
      where: {
        dictKey: {
          [require('sequelize').Op.like]: 'test_%'
        }
      }
    });

    console.log('✅ 测试数据清理完成\n');
  }

  /**
   * 运行单个测试
   */
  async runTest(category, testName, testFunction) {
    try {
      const result = await testFunction();
      if (result) {
        this.testResults[category].passed++;
        this.testResults[category].tests.push({ name: testName, status: 'PASSED' });
        console.log(`  ✅ ${testName}`);
      } else {
        this.testResults[category].failed++;
        this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: 'Test returned false' });
        console.log(`  ❌ ${testName} - 测试返回false`);
      }
    } catch (error) {
      this.testResults[category].failed++;
      this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: error.message });
      console.log(`  ❌ ${testName} - ${error.message}`);
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📋 批量字典获取功能测试结果汇总:');
    console.log('=' * 60);

    let totalPassed = 0;
    let totalFailed = 0;

    for (const [category, results] of Object.entries(this.testResults)) {
      const categoryName = {
        api: 'API接口',
        performance: '性能测试',
        dataFormat: '数据格式'
      }[category];

      console.log(`\n${categoryName}:`);
      console.log(`  通过: ${results.passed}`);
      console.log(`  失败: ${results.failed}`);

      if (results.failed > 0) {
        console.log('  失败的测试:');
        results.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`    - ${test.name}: ${test.error}`);
          });
      }

      totalPassed += results.passed;
      totalFailed += results.failed;
    }

    console.log('\n' + '=' * 60);
    console.log(`总计: ${totalPassed + totalFailed} 个测试`);
    console.log(`通过: ${totalPassed}`);
    console.log(`失败: ${totalFailed}`);
    console.log(`成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 所有测试通过！批量字典获取功能正常。');
      console.log('\n📌 功能验证要点:');
      console.log('✅ API接口正确响应');
      console.log('✅ 性能满足要求');
      console.log('✅ 数据格式正确');
      console.log('✅ 错误处理完善');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关功能。');
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new BatchDictionaryTester();
  tester.runAllTests().catch(console.error);
}

module.exports = BatchDictionaryTester;
