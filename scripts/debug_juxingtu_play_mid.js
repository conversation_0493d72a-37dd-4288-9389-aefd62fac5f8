/**
 * 调试巨量星图播放量中位数API
 * 专门测试巨量星图爬虫的播放量中位数获取功能
 */

const { sequelize, PublicInfluencer, CrawlTask } = require('../src/models');
const XingtuCrawler = require('../src/services/crawler/crawlers/XingtuCrawler');

async function debugJuxingtuPlayMid() {
  try {
    console.log('🔍 开始调试巨量星图播放量中位数API...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 查找一些巨量星图的达人ID用于测试
    console.log('\n📋 步骤1: 查找巨量星图达人ID');
    
    const juxingtuRecords = await PublicInfluencer.findAll({
      where: { platform: 'juxingtu' },
      attributes: ['id', 'platformUserId', 'nickname', 'playMid'],
      limit: 5,
      order: [['createdAt', 'DESC']]
    });

    if (juxingtuRecords.length === 0) {
      console.log('❌ 没有找到巨量星图的达人记录');
      return;
    }

    console.log(`找到 ${juxingtuRecords.length} 个巨量星图达人记录:`);
    juxingtuRecords.forEach(record => {
      console.log(`   ID${record.id}: ${record.nickname} (${record.platformUserId}) - playMid: ${record.playMid || 'null'}`);
    });

    // 2. 初始化爬虫
    console.log('\n📋 步骤2: 初始化巨量星图爬虫');
    
    const crawler = new XingtuCrawler();
    await crawler.initialize();
    console.log('✅ 爬虫初始化成功');

    // 3. 测试播放量中位数API
    console.log('\n📋 步骤3: 测试播放量中位数API');
    
    for (const record of juxingtuRecords) {
      console.log(`\n🔍 测试达人: ${record.nickname} (${record.platformUserId})`);
      
      try {
        // 直接调用播放量中位数API
        const playMid = await crawler.getAuthorMedianPlay(record.platformUserId);
        
        console.log(`   API返回结果: ${playMid}`);
        console.log(`   数据库中的值: ${record.playMid || 'null'}`);
        
        if (playMid !== null && playMid !== undefined) {
          console.log(`   ✅ API调用成功，获取到播放量中位数: ${playMid}`);
        } else {
          console.log(`   ❌ API调用失败或返回空值`);
        }
        
      } catch (error) {
        console.log(`   ❌ API调用异常: ${error.message}`);
      }
      
      // 添加延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 4. 测试API响应结构
    console.log('\n📋 步骤4: 详细分析API响应结构');
    
    const testAuthorId = juxingtuRecords[0].platformUserId;
    console.log(`\n🔍 详细测试达人: ${juxingtuRecords[0].nickname} (${testAuthorId})`);
    
    try {
      // 手动构建请求来查看完整响应
      const url = `${crawler.baseUrl}/gw/api/data_sp/get_author_spread_info?o_author_id=${testAuthorId}&platform_source=1&platform_channel=1&type=2&flow_type=0&only_assign=true&range=3`;
      console.log(`   请求URL: ${url}`);
      
      const config = crawler.createRequestConfig('get', url);
      const response = await crawler.fetchWithRetry(url, config);
      
      console.log(`   完整响应:`, JSON.stringify(response, null, 2));
      
      if (response) {
        console.log(`   响应字段分析:`);
        console.log(`     - 是否有play_mid字段: ${'play_mid' in response}`);
        console.log(`     - play_mid值: ${response.play_mid}`);
        console.log(`     - play_mid类型: ${typeof response.play_mid}`);
        console.log(`     - 响应的所有字段: ${Object.keys(response).join(', ')}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 详细API测试失败: ${error.message}`);
    }

    // 5. 检查Cookie状态
    console.log('\n📋 步骤5: 检查Cookie状态');
    
    if (crawler.currentCookie) {
      console.log(`   当前Cookie: ${crawler.currentCookie.platform}`);
      console.log(`   Cookie状态: ${crawler.currentCookie.isValid ? '有效' : '无效'}`);
      console.log(`   Cookie创建时间: ${crawler.currentCookie.createdAt}`);
    } else {
      console.log(`   ❌ 没有可用的Cookie`);
    }

    // 6. 测试不同的API参数
    console.log('\n📋 步骤6: 测试不同的API参数');
    
    const testParams = [
      { range: 3, type: 2, description: '默认参数' },
      { range: 1, type: 2, description: '范围=1' },
      { range: 7, type: 2, description: '范围=7' },
      { range: 3, type: 1, description: '类型=1' },
      { range: 3, type: 3, description: '类型=3' }
    ];
    
    for (const params of testParams) {
      console.log(`\n   测试参数: ${params.description}`);
      
      try {
        const url = `${crawler.baseUrl}/gw/api/data_sp/get_author_spread_info?o_author_id=${testAuthorId}&platform_source=1&platform_channel=1&type=${params.type}&flow_type=0&only_assign=true&range=${params.range}`;
        const config = crawler.createRequestConfig('get', url);
        const response = await crawler.fetchWithRetry(url, config);
        
        if (response && response.play_mid) {
          console.log(`     ✅ 成功获取播放量中位数: ${response.play_mid}`);
        } else {
          console.log(`     ❌ 未获取到播放量中位数`);
          console.log(`     响应: ${JSON.stringify(response)}`);
        }
        
      } catch (error) {
        console.log(`     ❌ 请求失败: ${error.message}`);
      }
      
      // 添加延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n✅ 巨量星图播放量中位数API调试完成');

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  debugJuxingtuPlayMid()
    .then(() => {
      console.log('\n📊 调试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = debugJuxingtuPlayMid;
