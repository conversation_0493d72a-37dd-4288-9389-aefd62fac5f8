/**
 * 测试巨量星图完整爬取流程
 * 模拟真实的爬取过程，检查播放量中位数是否正确保存
 */

const { sequelize, PublicInfluencer, CrawlTask } = require('../src/models');
const XingtuCrawler = require('../src/services/crawler/crawlers/XingtuCrawler');
const CrawlerManager = require('../src/services/crawler/CrawlerManager');

async function testJuxingtuFullCrawl() {
  try {
    console.log('🧪 开始测试巨量星图完整爬取流程...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 创建测试任务
    console.log('\n📋 步骤1: 创建测试任务');
    
    const testTask = await CrawlTask.create({
      taskName: '播放量中位数测试任务',
      platform: 'juxingtu',
      keywords: '测试',
      config: { maxPages: 1, pageSize: 3 },
      status: 'pending',
      maxPages: 1,
      createdBy: 1
    });

    console.log(`✅ 测试任务创建成功: ID${testTask.id}`);

    // 2. 初始化爬虫和管理器
    console.log('\n📋 步骤2: 初始化爬虫');
    
    const crawler = new XingtuCrawler();
    await crawler.initialize();
    console.log('✅ 爬虫初始化成功');

    const crawlerManager = new CrawlerManager();
    await crawlerManager.initialize();
    console.log('✅ 爬虫管理器初始化成功');

    // 3. 手动测试单个达人的完整处理流程
    console.log('\n📋 步骤3: 测试单个达人的完整处理流程');
    
    // 使用一个我们知道有播放量中位数的达人ID
    const testAuthorId = '6870164318311153677'; // 从调试中知道这个有数据
    
    console.log(`🔍 测试达人ID: ${testAuthorId}`);
    
    // 模拟从列表接口获取的达人数据
    const mockAuthorFromList = {
      star_id: testAuthorId,
      nick_name: '测试达人',
      attribute_datas: {}
    };

    try {
      // 调用完整的达人详情获取方法
      console.log('📥 调用 getAuthorDetail 方法...');
      const authorDetail = await crawler.getAuthorDetail(testAuthorId, mockAuthorFromList, {
        saveVideos: false,
        crawlTaskId: testTask.id
      });

      if (authorDetail) {
        console.log('✅ 达人详情获取成功:');
        console.log(`   昵称: ${authorDetail.nickname}`);
        console.log(`   平台用户ID: ${authorDetail.platformUserId}`);
        console.log(`   播放量中位数: ${authorDetail.playMid}`);
        console.log(`   包含playMid字段: ${'playMid' in authorDetail}`);
        console.log(`   playMid类型: ${typeof authorDetail.playMid}`);

        // 4. 测试数据保存流程
        console.log('\n📋 步骤4: 测试数据保存流程');
        
        console.log('💾 调用 CrawlerManager.handleResult...');
        await crawlerManager.handleResult(testTask.id, authorDetail);
        
        // 5. 验证数据库保存结果
        console.log('\n📋 步骤5: 验证数据库保存结果');
        
        // 先查找任何匹配的记录（不限制taskId）
        const anyRecord = await PublicInfluencer.findOne({
          where: {
            platform: 'juxingtu',
            platformUserId: testAuthorId
          },
          order: [['createdAt', 'DESC']]
        });

        console.log(`🔍 查找任何匹配的记录: ${anyRecord ? '找到' : '未找到'}`);
        if (anyRecord) {
          console.log(`   记录ID: ${anyRecord.id}, 任务ID: ${anyRecord.taskId}, 播放量中位数: ${anyRecord.playMid}`);
        }

        // 再查找特定任务的记录
        const savedRecord = await PublicInfluencer.findOne({
          where: {
            taskId: testTask.id,
            platformUserId: testAuthorId
          },
          order: [['createdAt', 'DESC']]
        });

        if (savedRecord) {
          console.log('✅ 数据库记录保存成功:');
          console.log(`   记录ID: ${savedRecord.id}`);
          console.log(`   昵称: ${savedRecord.nickname}`);
          console.log(`   播放量中位数: ${savedRecord.playMid}`);
          console.log(`   数据库中playMid类型: ${typeof savedRecord.playMid}`);
          
          if (savedRecord.playMid !== null && savedRecord.playMid !== undefined) {
            console.log('🎉 播放量中位数保存成功！');
          } else {
            console.log('❌ 播放量中位数保存失败，数据库中为null');
          }
        } else {
          console.log('❌ 数据库中没有找到保存的记录');
        }

      } else {
        console.log('❌ 达人详情获取失败');
      }

    } catch (error) {
      console.error('❌ 达人处理过程中发生错误:', error.message);
    }

    // 6. 测试批量处理流程
    console.log('\n📋 步骤6: 测试批量处理流程');
    
    // 模拟一个小的达人列表
    const mockAuthorList = [
      { star_id: '6870164318311153677', nick_name: '测试达人1', attribute_datas: {} },
      { star_id: '7089360510797217832', nick_name: '测试达人2', attribute_datas: {} }
    ];

    const results = {
      totalCount: 0,
      successCount: 0,
      failedCount: 0,
      data: []
    };

    const callbacks = {
      onResult: async (result) => {
        console.log(`📥 批量处理回调接收到结果:`);
        console.log(`   达人: ${result.nickname}`);
        console.log(`   播放量中位数: ${result.playMid}`);
        
        // 调用数据保存
        await crawlerManager.handleResult(testTask.id, result);
      },
      onError: async (error) => {
        console.log(`❌ 批量处理错误: ${error.message}`);
      }
    };

    console.log('🔄 开始批量处理...');
    await crawler.processAuthorsInBatches(mockAuthorList, results, callbacks, {
      saveVideos: false,
      crawlTaskId: testTask.id
    });

    // 7. 最终验证
    console.log('\n📋 步骤7: 最终验证数据库结果');
    
    const finalRecords = await PublicInfluencer.findAll({
      where: { taskId: testTask.id },
      attributes: ['id', 'nickname', 'platformUserId', 'playMid', 'createdAt'],
      order: [['createdAt', 'ASC']]
    });

    console.log(`📊 任务 ${testTask.id} 的最终结果:`);
    console.log(`   总记录数: ${finalRecords.length}`);
    
    let withPlayMidCount = 0;
    finalRecords.forEach(record => {
      const hasPlayMid = record.playMid !== null && record.playMid !== undefined;
      if (hasPlayMid) withPlayMidCount++;
      
      console.log(`   ID${record.id}: ${record.nickname} - playMid: ${record.playMid || 'null'} ${hasPlayMid ? '✅' : '❌'}`);
    });

    console.log(`   有播放量中位数的记录: ${withPlayMidCount}/${finalRecords.length}`);
    
    if (withPlayMidCount > 0) {
      console.log('🎉 测试成功！播放量中位数功能正常工作');
    } else {
      console.log('❌ 测试失败！播放量中位数没有正确保存');
    }

    console.log('\n✅ 巨量星图完整爬取流程测试完成');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testJuxingtuFullCrawl()
    .then(() => {
      console.log('\n📊 测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = testJuxingtuFullCrawl;
