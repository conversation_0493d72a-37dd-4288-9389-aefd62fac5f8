/**
 * 星图爬虫视频保存功能使用示例
 * 演示如何在爬虫任务中启用视频数据保存功能
 */

const { initializeAndSyncDatabase, CrawlTask } = require('../src/models');
const XingtuCrawler = require('../src/services/crawler/crawlers/XingtuCrawler');

async function exampleCrawlWithVideoSave() {
  try {
    console.log('🚀 星图爬虫视频保存功能使用示例\n');

    // 1. 初始化数据库
    console.log('📋 步骤1: 初始化数据库...');
    await initializeAndSyncDatabase();
    console.log('✅ 数据库初始化完成\n');

    // 2. 创建爬虫任务
    console.log('📋 步骤2: 创建爬虫任务...');
    const crawlTask = await CrawlTask.create({
      taskName: '美食达人爬取_视频保存示例',
      platform: 'juxingtu',
      keywords: '美食',
      maxPages: 1, // 示例只爬取1页
      status: 'pending',
      createdBy: 1,
      config: {
        saveVideos: true // 在任务配置中标记启用视频保存
      }
    });
    console.log(`✅ 爬虫任务创建成功，ID: ${crawlTask.id}\n`);

    // 3. 配置爬虫参数（默认启用视频保存）
    console.log('📋 步骤3: 配置爬虫参数...');
    const crawlConfig = {
      keywords: '美食',
      maxPages: 1
      // 🎯 注意：无需设置 saveVideos 和 crawlTaskId
      // 系统会自动启用视频保存功能并关联任务ID
    };
    console.log('✅ 爬虫配置完成，视频保存功能已默认启用\n');

    // 4. 设置回调函数
    console.log('📋 步骤4: 设置回调函数...');
    const callbacks = {
      onProgress: async (progress) => {
        console.log(`📊 进度更新: 第${progress.currentPage}页/${progress.totalPages}页 (${progress.percentage}%)`);
        console.log(`   成功: ${progress.successCount}, 失败: ${progress.failedCount}`);
      },
      
      onResult: async (result) => {
        console.log(`✅ 达人数据获取成功: ${result.nickname} (${result.platformUserId})`);
        console.log(`   粉丝数: ${result.followersCount}, 视频数: ${result.videoStats.videoCount}`);
        
        // 检查是否有视频保存结果
        if (result.videoSaveResult) {
          console.log(`   📹 视频保存结果: 成功${result.videoSaveResult.success}, 新增${result.videoSaveResult.created}, 更新${result.videoSaveResult.updated}`);
        }
        
        // 检查是否有视频保存错误
        if (result.videoSaveError) {
          console.log(`   ⚠️ 视频保存错误: ${result.videoSaveError}`);
        }
      },
      
      onError: async (error) => {
        console.error(`❌ 爬取错误: ${error.message}`);
      }
    };
    console.log('✅ 回调函数设置完成\n');

    // 5. 执行爬虫任务
    console.log('📋 步骤5: 执行爬虫任务...');
    console.log('⚠️ 注意：此示例需要有效的巨量星图Cookie才能正常运行');
    console.log('如果没有Cookie，爬虫将会失败，但代码逻辑是正确的\n');

    // 更新任务状态为运行中
    await crawlTask.update({ status: 'running', startedAt: new Date() });

    try {
      // 创建爬虫实例并执行
      const crawler = new XingtuCrawler();
      const results = await crawler.crawl(crawlConfig, callbacks);

      // 更新任务状态为完成
      await crawlTask.update({ 
        status: 'completed', 
        completedAt: new Date(),
        resultSummary: {
          totalCount: results.totalCount,
          successCount: results.successCount,
          failedCount: results.failedCount
        }
      });

      console.log('\n✅ 爬虫任务执行完成');
      console.log('📊 执行结果:', {
        总数量: results.totalCount,
        成功数量: results.successCount,
        失败数量: results.failedCount
      });

      // 统计视频保存情况
      let totalVideosSaved = 0;
      let totalVideosCreated = 0;
      let totalVideosUpdated = 0;

      results.data.forEach(result => {
        if (result.videoSaveResult) {
          totalVideosSaved += result.videoSaveResult.success;
          totalVideosCreated += result.videoSaveResult.created;
          totalVideosUpdated += result.videoSaveResult.updated;
        }
      });

      console.log('📹 视频保存统计:', {
        总保存视频数: totalVideosSaved,
        新增视频数: totalVideosCreated,
        更新视频数: totalVideosUpdated
      });

    } catch (crawlError) {
      // 更新任务状态为失败
      await crawlTask.update({ 
        status: 'failed', 
        completedAt: new Date(),
        errorMessage: crawlError.message
      });

      if (crawlError.message.includes('Cookie')) {
        console.log('\n⚠️ 爬虫执行失败：缺少有效的Cookie');
        console.log('💡 解决方案：');
        console.log('   1. 在系统中添加有效的巨量星图Cookie');
        console.log('   2. 确保Cookie未过期且有访问权限');
        console.log('   3. 参考Cookie管理文档进行配置');
      } else {
        throw crawlError;
      }
    }

    console.log('\n📋 步骤6: 功能说明总结...');
    console.log('🎯 视频保存功能特性：');
    console.log('   ✅ 自动保存达人视频数据到数据库');
    console.log('   ✅ 建立达人与视频的关联关系');
    console.log('   ✅ 处理重复视频数据（更新机制）');
    console.log('   ✅ 事务安全保证数据一致性');
    console.log('   ✅ 完整的错误处理和日志记录');
    console.log('   ✅ 如果达人不存在会自动创建');

    console.log('\n🔧 配置要点：');
    console.log('   1. 视频保存功能默认启用，无需手动配置');
    console.log('   2. 任务ID自动关联，无需手动设置');
    console.log('   3. 如需禁用，请设置 config.saveVideos: false');
    console.log('   4. 监听回调函数获取保存结果');

    console.log('\n📊 数据存储：');
    console.log('   - 达人数据存储在 influencers 表');
    console.log('   - 视频数据存储在 author_videos 表');
    console.log('   - 通过 author_id 字段建立关联');
    console.log('   - 支持多平台数据（platform 字段区分）');

    console.log('\n🎉 示例演示完成！');

  } catch (error) {
    console.error('\n❌ 示例执行失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行示例
if (require.main === module) {
  exampleCrawlWithVideoSave()
    .then(() => {
      console.log('\n✅ 示例完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 示例异常:', error);
      process.exit(1);
    });
}

module.exports = { exampleCrawlWithVideoSave };
