/**
 * 修复重复数据脚本
 * 清理重复的达人数据，保留最新的记录
 */

const { sequelize, PublicInfluencer } = require('../src/models');

async function fixDuplicateData(dryRun = true) {
  try {
    console.log('🔧 开始修复重复数据...\n');
    console.log(`模式: ${dryRun ? '预览模式（不会实际删除数据）' : '执行模式（会实际删除重复数据）'}`);

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 查找所有重复数据
    console.log('\n🔍 步骤1: 查找重复数据');
    
    const duplicateGroups = await sequelize.query(`
      SELECT platform, platform_user_id, COUNT(*) as count
      FROM public_influencers 
      GROUP BY platform, platform_user_id 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    console.log(`   发现 ${duplicateGroups.length} 组重复数据`);

    if (duplicateGroups.length === 0) {
      console.log('✅ 没有发现重复数据，无需修复');
      return { duplicateGroups: 0, recordsToDelete: 0, recordsDeleted: 0 };
    }

    // 2. 分析每组重复数据
    console.log('\n📊 步骤2: 分析重复数据详情');
    
    let totalRecordsToDelete = 0;
    const deletionPlan = [];

    for (const group of duplicateGroups) {
      // 获取该组的所有记录
      const records = await PublicInfluencer.findAll({
        where: {
          platform: group.platform,
          platformUserId: group.platform_user_id
        },
        order: [['createdAt', 'DESC']] // 按创建时间降序，最新的在前
      });

      console.log(`\n   ${group.platform} - ${group.platform_user_id} (${group.count}条记录):`);
      
      // 保留最新的记录，标记其他记录为待删除
      const recordsToKeep = records.slice(0, 1); // 保留第一条（最新的）
      const recordsToDelete = records.slice(1);  // 删除其他的

      recordsToKeep.forEach(record => {
        console.log(`     ✅ 保留: ID${record.id} (任务${record.taskId}) - ${record.nickname} - ${record.createdAt}`);
      });

      recordsToDelete.forEach(record => {
        console.log(`     ❌ 删除: ID${record.id} (任务${record.taskId}) - ${record.nickname} - ${record.createdAt}`);
        deletionPlan.push({
          id: record.id,
          platform: record.platform,
          platformUserId: record.platformUserId,
          nickname: record.nickname,
          taskId: record.taskId,
          createdAt: record.createdAt
        });
      });

      totalRecordsToDelete += recordsToDelete.length;
    }

    console.log(`\n📋 删除计划摘要:`);
    console.log(`   重复数据组数: ${duplicateGroups.length}`);
    console.log(`   计划删除记录数: ${totalRecordsToDelete}`);

    // 3. 执行删除操作
    let actualDeletedCount = 0;
    
    if (!dryRun && totalRecordsToDelete > 0) {
      console.log('\n🗑️ 步骤3: 执行删除操作');
      
      // 使用事务确保数据一致性
      const transaction = await sequelize.transaction();
      
      try {
        for (const record of deletionPlan) {
          await PublicInfluencer.destroy({
            where: { id: record.id },
            transaction
          });
          
          actualDeletedCount++;
          console.log(`   已删除: ID${record.id} - ${record.nickname}`);
        }
        
        await transaction.commit();
        console.log(`✅ 成功删除 ${actualDeletedCount} 条重复记录`);
        
      } catch (error) {
        await transaction.rollback();
        console.error('❌ 删除操作失败，已回滚事务:', error);
        throw error;
      }
    } else if (dryRun) {
      console.log('\n💡 预览模式: 如需实际执行删除，请使用参数 --execute');
    }

    // 4. 验证修复结果
    if (!dryRun && actualDeletedCount > 0) {
      console.log('\n🔍 步骤4: 验证修复结果');
      
      const remainingDuplicates = await sequelize.query(`
        SELECT platform, platform_user_id, COUNT(*) as count
        FROM public_influencers 
        GROUP BY platform, platform_user_id 
        HAVING COUNT(*) > 1
      `, {
        type: sequelize.QueryTypes.SELECT
      });

      if (remainingDuplicates.length === 0) {
        console.log('✅ 修复成功，已无重复数据');
      } else {
        console.log(`⚠️ 仍有 ${remainingDuplicates.length} 组重复数据，可能需要进一步处理`);
      }
    }

    console.log('\n✅ 重复数据修复完成');
    
    return {
      duplicateGroups: duplicateGroups.length,
      recordsToDelete: totalRecordsToDelete,
      recordsDeleted: actualDeletedCount,
      deletionPlan: dryRun ? deletionPlan : []
    };

  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const args = process.argv.slice(2);
  const executeMode = args.includes('--execute');
  
  fixDuplicateData(!executeMode)
    .then(result => {
      console.log('\n📊 修复结果摘要:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = fixDuplicateData;
