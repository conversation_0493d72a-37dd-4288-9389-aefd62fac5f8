/**
 * 测试MCP接口的新平台参数功能
 * 验证jlxt和xhs参数是否正确映射到对应的平台
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://127.0.0.1:3001';
const API_ENDPOINT = '/api/simple-mcp/execute';

/**
 * 测试用例配置
 */
const testCases = [
  {
    name: '测试新格式 - 小红书(xhs)',
    data: {
      tool: 'create_crawler_task',
      arguments: {
        keywords: '美妆博主测试',
        platform: 'xhs',
        taskName: '小红书美妆达人测试(新格式)',
        maxPages: 1
      }
    },
    expectedPlatform: 'xiaohongshu'
  },
  {
    name: '测试新格式 - 巨量星图(jlxt)',
    data: {
      tool: 'create_crawler_task',
      arguments: {
        keywords: '科技达人测试',
        platform: 'jlxt',
        taskName: '巨量星图科技达人测试(新格式)',
        maxPages: 1
      }
    },
    expectedPlatform: 'juxingtu'
  },
  {
    name: '测试兼容格式 - 小红书(xiaohongshu)',
    data: {
      tool: 'create_crawler_task',
      arguments: {
        keywords: '美妆博主测试',
        platform: 'xiaohongshu',
        taskName: '小红书美妆达人测试(兼容格式)',
        maxPages: 1
      }
    },
    expectedPlatform: 'xiaohongshu'
  },
  {
    name: '测试兼容格式 - 巨量星图(juxingtu)',
    data: {
      tool: 'create_crawler_task',
      arguments: {
        keywords: '科技达人测试',
        platform: 'juxingtu',
        taskName: '巨量星图科技达人测试(兼容格式)',
        maxPages: 1
      }
    },
    expectedPlatform: 'juxingtu'
  },
  {
    name: '测试无效平台参数',
    data: {
      tool: 'create_crawler_task',
      arguments: {
        keywords: '测试关键词',
        platform: 'invalid_platform',
        taskName: '无效平台测试',
        maxPages: 1
      }
    },
    shouldFail: true
  }
];

/**
 * 执行单个测试用例
 */
async function runTestCase(testCase) {
  console.log(`\n🧪 ${testCase.name}`);
  console.log('📋 请求数据:', JSON.stringify(testCase.data, null, 2));

  try {
    const response = await axios.post(`${BASE_URL}${API_ENDPOINT}`, testCase.data, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    if (testCase.shouldFail) {
      console.log('❌ 测试失败: 期望请求失败，但实际成功了');
      return false;
    }

    if (response.data.success) {
      console.log('✅ 请求成功');
      console.log('📊 响应数据:', JSON.stringify(response.data.data, null, 2));

      // 验证平台映射是否正确
      if (testCase.expectedPlatform) {
        const actualPlatform = response.data.data.platform;
        if (actualPlatform === testCase.expectedPlatform) {
          console.log(`✅ 平台映射正确: ${testCase.data.arguments.platform} -> ${actualPlatform}`);
          return true;
        } else {
          console.log(`❌ 平台映射错误: 期望 ${testCase.expectedPlatform}, 实际 ${actualPlatform}`);
          return false;
        }
      }
      return true;
    } else {
      console.log('❌ 请求失败:', response.data.message);
      return false;
    }

  } catch (error) {
    if (testCase.shouldFail) {
      console.log('✅ 测试通过: 请求按预期失败');
      console.log('📝 错误信息:', error.response?.data?.message || error.message);
      return true;
    } else {
      console.log('❌ 请求异常:', error.response?.data?.message || error.message);
      return false;
    }
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始测试MCP平台参数功能');
  console.log('=' * 50);

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    const result = await runTestCase(testCase);
    if (result) {
      passedTests++;
    }
    
    // 测试间延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n' + '=' * 50);
  console.log('📊 测试结果汇总:');
  console.log(`✅ 通过: ${passedTests}/${totalTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！');
  } else {
    console.log('⚠️ 部分测试失败，请检查实现');
  }
}

/**
 * 测试工具列表接口
 */
async function testToolsList() {
  console.log('\n🔧 测试工具列表接口');
  try {
    const response = await axios.get(`${BASE_URL}/api/simple-mcp/tools`);
    console.log('✅ 工具列表获取成功');
    console.log('📋 可用工具:', response.data.data.tools.map(t => t.name).join(', '));
  } catch (error) {
    console.log('❌ 工具列表获取失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    // 首先测试工具列表
    await testToolsList();
    
    // 然后运行平台参数测试
    await runAllTests();
    
  } catch (error) {
    console.error('💥 测试执行异常:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  runAllTests,
  runTestCase,
  testCases
};
