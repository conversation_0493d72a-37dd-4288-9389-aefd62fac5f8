/**
 * 检查合作对接管理表结构脚本
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4'
};

async function checkCooperationTable() {
  let connection;
  
  try {
    console.log('🔄 检查合作对接管理表结构...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查表是否存在
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'cooperation_management'"
    );

    if (tables.length === 0) {
      console.log('❌ cooperation_management 表不存在');
      return;
    }

    console.log('✅ cooperation_management 表存在');

    // 获取表结构
    const [columns] = await connection.execute(
      'DESCRIBE cooperation_management'
    );
    
    console.log('📋 当前表结构:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''} ${col.Extra || ''}`);
    });

    // 检查新字段是否存在
    const newFields = [
      'customer_name',
      'customer_homepage', 
      'customer_public_sea',
      'seeding_platform',
      'blogger_fans_count',
      'influencer_platform_id',
      'blogger_wechat_and_notes',
      'title',
      'cooperation_form',
      'publish_platform',
      'cooperation_brand',
      'cooperation_notes',
      'cooperation_amount',
      'influencer_commission_rate',
      'payee_name',
      'bank_account',
      'bank_name',
      'rebate_completed',
      'publish_link',
      'actual_publish_date',
      'data_registration_date',
      'view_count',
      'like_count',
      'collect_count',
      'comment_count',
      'content_implant_coefficient',
      'comment_maintenance_coefficient',
      'brand_topic_included',
      'self_evaluation',
      'external_customer_id',
      'external_agreement_id',
      'crm_link_status',
      'influencer_report_id'
    ];

    const existingFields = columns.map(col => col.Field);
    const missingFields = newFields.filter(field => !existingFields.includes(field));
    
    if (missingFields.length > 0) {
      console.log('⚠️ 缺少以下新字段:');
      missingFields.forEach(field => {
        console.log(`  - ${field}`);
      });
    } else {
      console.log('✅ 所有新字段都已存在');
    }

    // 检查数据量
    const [count] = await connection.execute(
      'SELECT COUNT(*) as count FROM cooperation_management'
    );
    console.log(`📊 当前数据量: ${count[0].count} 条`);

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

checkCooperationTable();
