/**
 * 中文名称功能测试脚本
 * 
 * 功能说明：
 * 1. 测试用户创建时添加中文名称
 * 2. 测试用户更新中文名称
 * 3. 测试中文名称搜索功能
 * 4. 测试数据完整性和兼容性
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const axios = require('axios');
const { User } = require('../src/models');

// API基础配置
const API_BASE_URL = 'http://localhost:3001/api';
let authToken = '';

// 测试用户数据
const testUsers = [
  {
    username: 'testuser1',
    email: '<EMAIL>',
    chineseName: '张三',
    password: 'test123456',
    role: 'user',
    status: 'active'
  },
  {
    username: 'testuser2',
    email: '<EMAIL>',
    chineseName: '李四',
    password: 'test123456',
    role: 'user',
    status: 'active'
  },
  {
    username: 'testuser3',
    email: '<EMAIL>',
    chineseName: '',  // 空中文名称测试
    password: 'test123456',
    role: 'user',
    status: 'active'
  }
];

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(config => {
  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

// 响应拦截器
api.interceptors.response.use(
  response => response.data,
  error => {
    console.error('API请求失败:', error.response?.data || error.message);
    throw error;
  }
);

/**
 * 管理员登录获取token
 */
async function adminLogin() {
  try {
    console.log('🔐 正在进行管理员登录...');
    const response = await api.post('/auth/login', {
      username: 'admin',
      password: 'admin123456'
    });
    
    if (response.success && response.data.token) {
      authToken = response.data.token;
      console.log('✅ 管理员登录成功');
      return true;
    } else {
      console.error('❌ 管理员登录失败:', response.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 管理员登录异常:', error.message);
    return false;
  }
}

/**
 * 测试用户创建功能
 */
async function testUserCreation() {
  console.log('\n📝 测试用户创建功能...');
  const createdUsers = [];
  
  for (const userData of testUsers) {
    try {
      console.log(`  创建用户: ${userData.username} (${userData.chineseName || '无中文名称'})`);
      const response = await api.post('/users', userData);
      
      if (response.success) {
        console.log(`  ✅ 用户 ${userData.username} 创建成功`);
        createdUsers.push(response.data);
      } else {
        console.log(`  ❌ 用户 ${userData.username} 创建失败: ${response.message}`);
      }
    } catch (error) {
      console.log(`  ❌ 用户 ${userData.username} 创建异常: ${error.message}`);
    }
  }
  
  return createdUsers;
}

/**
 * 测试用户列表查询功能
 */
async function testUserList() {
  console.log('\n📋 测试用户列表查询功能...');
  
  try {
    const response = await api.get('/users', {
      params: { page: 1, limit: 20 }
    });
    
    if (response.success) {
      console.log(`  ✅ 获取用户列表成功，共 ${response.data.length} 个用户`);
      
      // 检查中文名称字段
      const usersWithChineseName = response.data.filter(user => user.chineseName);
      console.log(`  📊 其中 ${usersWithChineseName.length} 个用户有中文名称`);
      
      // 显示部分用户信息
      response.data.slice(0, 5).forEach(user => {
        console.log(`    - ${user.username} (${user.chineseName || '无中文名称'}) - ${user.email}`);
      });
      
      return response.data;
    } else {
      console.log(`  ❌ 获取用户列表失败: ${response.message}`);
      return [];
    }
  } catch (error) {
    console.log(`  ❌ 获取用户列表异常: ${error.message}`);
    return [];
  }
}

/**
 * 测试中文名称搜索功能
 */
async function testChineseNameSearch() {
  console.log('\n🔍 测试中文名称搜索功能...');
  
  const searchTerms = ['张三', '李四', 'testuser', '@example.com'];
  
  for (const keyword of searchTerms) {
    try {
      console.log(`  搜索关键词: "${keyword}"`);
      const response = await api.get('/users', {
        params: { keyword, page: 1, limit: 10 }
      });
      
      if (response.success) {
        console.log(`    ✅ 搜索成功，找到 ${response.data.length} 个结果`);
        response.data.forEach(user => {
          console.log(`      - ${user.username} (${user.chineseName || '无中文名称'}) - ${user.email}`);
        });
      } else {
        console.log(`    ❌ 搜索失败: ${response.message}`);
      }
    } catch (error) {
      console.log(`    ❌ 搜索异常: ${error.message}`);
    }
  }
}

/**
 * 测试用户更新功能
 */
async function testUserUpdate(users) {
  console.log('\n✏️ 测试用户更新功能...');
  
  if (users.length === 0) {
    console.log('  ⚠️ 没有可用的测试用户，跳过更新测试');
    return;
  }
  
  const testUser = users[0];
  const newChineseName = '王五';
  
  try {
    console.log(`  更新用户 ${testUser.username} 的中文名称为: ${newChineseName}`);
    const response = await api.put(`/users/${testUser.id}`, {
      username: testUser.username,
      email: testUser.email,
      chineseName: newChineseName,
      role: testUser.role,
      status: testUser.status
    });
    
    if (response.success) {
      console.log(`  ✅ 用户更新成功`);
      console.log(`    - 新中文名称: ${response.data.chineseName}`);
    } else {
      console.log(`  ❌ 用户更新失败: ${response.message}`);
    }
  } catch (error) {
    console.log(`  ❌ 用户更新异常: ${error.message}`);
  }
}

/**
 * 清理测试数据
 */
async function cleanupTestData() {
  console.log('\n🧹 清理测试数据...');
  
  try {
    // 查找测试用户
    const testUsernames = testUsers.map(u => u.username);
    const users = await User.findAll({
      where: {
        username: testUsernames
      }
    });
    
    if (users.length > 0) {
      console.log(`  找到 ${users.length} 个测试用户，正在删除...`);
      
      for (const user of users) {
        try {
          await api.delete(`/users/${user.id}`);
          console.log(`    ✅ 删除用户: ${user.username}`);
        } catch (error) {
          console.log(`    ❌ 删除用户 ${user.username} 失败: ${error.message}`);
        }
      }
    } else {
      console.log('  ℹ️ 没有找到需要清理的测试用户');
    }
  } catch (error) {
    console.log(`  ❌ 清理测试数据异常: ${error.message}`);
  }
}

/**
 * 数据库直接验证
 */
async function validateDatabase() {
  console.log('\n🗄️ 验证数据库字段...');
  
  try {
    // 检查字段是否存在
    const [results] = await User.sequelize.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'chinese_name'
    `);
    
    if (results.length > 0) {
      console.log('  ✅ chinese_name 字段存在');
      console.log('    字段信息:', results[0]);
    } else {
      console.log('  ❌ chinese_name 字段不存在');
    }
    
    // 检查现有用户数据
    const userCount = await User.count();
    const usersWithChineseName = await User.count({
      where: {
        chineseName: { [User.sequelize.Op.ne]: null }
      }
    });
    
    console.log(`  📊 数据库统计:`);
    console.log(`    - 总用户数: ${userCount}`);
    console.log(`    - 有中文名称的用户: ${usersWithChineseName}`);
    
  } catch (error) {
    console.log(`  ❌ 数据库验证异常: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始中文名称功能测试\n');
  
  try {
    // 1. 数据库验证
    await validateDatabase();
    
    // 2. 管理员登录
    const loginSuccess = await adminLogin();
    if (!loginSuccess) {
      console.log('❌ 无法获取管理员权限，测试终止');
      return;
    }
    
    // 3. 清理可能存在的测试数据
    await cleanupTestData();
    
    // 4. 测试用户创建
    const createdUsers = await testUserCreation();
    
    // 5. 测试用户列表查询
    const allUsers = await testUserList();
    
    // 6. 测试中文名称搜索
    await testChineseNameSearch();
    
    // 7. 测试用户更新
    await testUserUpdate(createdUsers);
    
    // 8. 再次查询验证更新结果
    await testUserList();
    
    // 9. 清理测试数据
    await cleanupTestData();
    
    console.log('\n🎉 中文名称功能测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生异常:', error);
  } finally {
    // 确保数据库连接关闭
    try {
      await User.sequelize.close();
      console.log('🔌 数据库连接已关闭');
    } catch (error) {
      console.error('❌ 关闭数据库连接失败:', error.message);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testUserCreation,
  testUserList,
  testChineseNameSearch,
  testUserUpdate,
  validateDatabase
};
