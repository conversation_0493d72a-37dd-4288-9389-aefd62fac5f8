/**
 * 生产环境数据库检查脚本
 * 用于检查生产环境数据库的 play_mid 字段状态
 */

const mysql = require('mysql2/promise');

// 生产环境数据库配置
const productionConfig = {
  host: '*************',
  port: 3306,
  user: 'daren_db',
  password: 'daren_db_2025!',
  database: 'daren_db',
  charset: 'utf8mb4'
};

async function checkProductionDatabase() {
  let connection;
  
  try {
    console.log('🔍 检查生产环境数据库 play_mid 字段状态...\n');

    // 连接生产环境数据库
    console.log('📡 连接生产环境数据库...');
    connection = await mysql.createConnection(productionConfig);
    console.log('✅ 生产环境数据库连接成功\n');

    // 检查数据库基本信息
    const [dbInfo] = await connection.execute('SELECT DATABASE() as current_db');
    const [timeInfo] = await connection.execute('SELECT NOW() as current_datetime');
    const [versionInfo] = await connection.execute('SELECT VERSION() as db_version');

    console.log('📋 数据库信息:');
    console.log(`   数据库: ${dbInfo[0].current_db}`);
    console.log(`   时间: ${timeInfo[0].current_datetime}`);
    console.log(`   版本: ${versionInfo[0].db_version}\n`);

    // 检查表是否存在
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME, TABLE_ROWS, CREATE_TIME
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'daren_db' 
        AND TABLE_NAME IN ('my_influencers', 'public_influencers')
    `);
    
    console.log('📊 表信息:');
    tables.forEach(table => {
      console.log(`   ${table.TABLE_NAME}: ${table.TABLE_ROWS} 条记录, 创建时间: ${table.CREATE_TIME}`);
    });
    console.log();

    // 检查 play_mid 字段是否存在
    const [playMidFields] = await connection.execute(`
      SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'daren_db' 
        AND TABLE_NAME IN ('my_influencers', 'public_influencers')
        AND COLUMN_NAME = 'play_mid'
    `);

    if (playMidFields.length === 0) {
      console.log('❌ play_mid 字段不存在');
      console.log('   需要执行迁移脚本添加字段\n');
      
      console.log('🚀 执行迁移命令:');
      console.log('   node scripts/migrate_production_play_mid.js');
    } else {
      console.log('✅ play_mid 字段存在:');
      playMidFields.forEach(field => {
        console.log(`   ${field.TABLE_NAME}.${field.COLUMN_NAME}:`);
        console.log(`     类型: ${field.DATA_TYPE}`);
        console.log(`     允许NULL: ${field.IS_NULLABLE}`);
        console.log(`     默认值: ${field.COLUMN_DEFAULT || 'NULL'}`);
        console.log(`     注释: ${field.COLUMN_COMMENT}`);
      });
      console.log();

      // 统计 play_mid 字段的数据情况
      console.log('📈 play_mid 字段数据统计:');
      
      const [myInfluencerStats] = await connection.execute(`
        SELECT 
          COUNT(*) as total_records,
          COUNT(play_mid) as records_with_play_mid,
          (COUNT(*) - COUNT(play_mid)) as records_without_play_mid,
          MIN(play_mid) as min_play_mid,
          MAX(play_mid) as max_play_mid
        FROM my_influencers
      `);

      const [publicInfluencerStats] = await connection.execute(`
        SELECT 
          COUNT(*) as total_records,
          COUNT(play_mid) as records_with_play_mid,
          (COUNT(*) - COUNT(play_mid)) as records_without_play_mid,
          MIN(play_mid) as min_play_mid,
          MAX(play_mid) as max_play_mid
        FROM public_influencers
      `);

      console.log('   my_influencers:');
      console.log(`     总记录: ${myInfluencerStats[0].total_records}`);
      console.log(`     有播放量中位数: ${myInfluencerStats[0].records_with_play_mid}`);
      console.log(`     无播放量中位数: ${myInfluencerStats[0].records_without_play_mid}`);
      if (myInfluencerStats[0].records_with_play_mid > 0) {
        console.log(`     最小值: ${myInfluencerStats[0].min_play_mid}`);
        console.log(`     最大值: ${myInfluencerStats[0].max_play_mid}`);
      }

      console.log('   public_influencers:');
      console.log(`     总记录: ${publicInfluencerStats[0].total_records}`);
      console.log(`     有播放量中位数: ${publicInfluencerStats[0].records_with_play_mid}`);
      console.log(`     无播放量中位数: ${publicInfluencerStats[0].records_without_play_mid}`);
      if (publicInfluencerStats[0].records_with_play_mid > 0) {
        console.log(`     最小值: ${publicInfluencerStats[0].min_play_mid}`);
        console.log(`     最大值: ${publicInfluencerStats[0].max_play_mid}`);
      }
    }

    console.log('\n🎉 生产环境数据库检查完成！');

  } catch (error) {
    console.error('\n❌ 检查失败:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('   无法连接到生产环境数据库，请检查网络连接和数据库服务状态');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('   数据库访问被拒绝，请检查用户名和密码');
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔒 数据库连接已关闭');
    }
  }
}

// 运行检查
if (require.main === module) {
  checkProductionDatabase();
}

module.exports = checkProductionDatabase;
