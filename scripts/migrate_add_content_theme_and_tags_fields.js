/**
 * 数据库迁移脚本：为达人表添加内容主题和达人标签字段
 * 
 * 功能说明：
 * 1. 为 public_influencers 表添加 content_theme 和 influencer_tags 字段
 * 2. 为 my_influencers 表添加 content_theme 和 influencer_tags 字段
 * 3. 从现有的 authorExtInfo 字段中提取数据并迁移到新字段
 * 4. 提供完整的迁移和验证流程
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize, PublicInfluencer, MyInfluencer, initializeAndSyncDatabase } = require('../src/models');
const { Op } = require('sequelize');

class ContentThemeAndTagsMigrator {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.batchSize = options.batchSize || 50;
    this.migrationReport = {
      startTime: new Date(),
      endTime: null,
      publicInfluencers: {
        total: 0,
        processed: 0,
        success: 0,
        failed: 0,
        errors: []
      },
      myInfluencers: {
        total: 0,
        processed: 0,
        success: 0,
        failed: 0,
        errors: []
      },
      fieldsAdded: false,
      errors: []
    };
  }

  /**
   * 执行完整的迁移流程
   */
  async executeMigration() {
    try {
      console.log('🚀 开始内容主题和达人标签字段迁移流程...\n');

      // 1. 初始化数据库
      await this.initializeDatabase();

      // 2. 添加新字段
      await this.addNewFields();

      // 3. 检测和分析数据
      await this.analyzeData();

      // 4. 迁移达人公海数据
      await this.migratePublicInfluencersData();

      // 5. 迁移我的达人数据
      await this.migrateMyInfluencersData();

      // 6. 验证迁移结果
      await this.validateMigration();

      // 7. 生成最终报告
      this.generateFinalReport();

      console.log('🎉 内容主题和达人标签字段迁移流程完成！\n');
      return this.migrationReport;

    } catch (error) {
      console.error('❌ 迁移流程失败:', error.message);
      this.migrationReport.errors.push({
        stage: 'migration_process',
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    } finally {
      this.migrationReport.endTime = new Date();
    }
  }

  /**
   * 初始化数据库
   */
  async initializeDatabase() {
    console.log('📋 步骤1: 初始化数据库...');
    await initializeAndSyncDatabase();
    console.log('✅ 数据库初始化完成\n');
  }

  /**
   * 添加新字段到数据库表
   */
  async addNewFields() {
    console.log('📋 步骤2: 添加新字段...');
    
    const transaction = await sequelize.transaction();
    
    try {
      // 为 public_influencers 表添加字段
      await this.addFieldsToTable('public_influencers', transaction);
      
      // 为 my_influencers 表添加字段
      await this.addFieldsToTable('my_influencers', transaction);
      
      await transaction.commit();
      this.migrationReport.fieldsAdded = true;
      console.log('✅ 新字段添加完成\n');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 添加字段失败:', error.message);
      throw error;
    }
  }

  /**
   * 为指定表添加字段
   */
  async addFieldsToTable(tableName, transaction) {
    try {
      // 添加 content_theme 字段
      await sequelize.query(`
        ALTER TABLE ${tableName} 
        ADD COLUMN content_theme JSON COMMENT '内容主题（从authorExtInfo.content_theme_labels_180d提取）'
      `, { transaction });
      console.log(`✅ ${tableName} 表添加 content_theme 字段成功`);
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log(`ℹ️ ${tableName} 表的 content_theme 字段已存在，跳过`);
      } else {
        throw error;
      }
    }

    try {
      // 添加 influencer_tags 字段
      await sequelize.query(`
        ALTER TABLE ${tableName} 
        ADD COLUMN influencer_tags JSON COMMENT '达人标签（从authorExtInfo.tags_relation提取）'
      `, { transaction });
      console.log(`✅ ${tableName} 表添加 influencer_tags 字段成功`);
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log(`ℹ️ ${tableName} 表的 influencer_tags 字段已存在，跳过`);
      } else {
        throw error;
      }
    }
  }

  /**
   * 分析需要迁移的数据
   */
  async analyzeData() {
    console.log('📋 步骤3: 检测和分析数据...');

    // 分析达人公海数据
    const publicInfluencersCount = await PublicInfluencer.count();
    const publicInfluencersWithExtInfo = await PublicInfluencer.count({
      where: {
        author_ext_info: {
          [Op.ne]: null
        }
      }
    });

    // 分析我的达人数据
    const myInfluencersCount = await MyInfluencer.count();
    const myInfluencersWithExtInfo = await MyInfluencer.count({
      where: {
        author_ext_info: {
          [Op.ne]: null
        }
      }
    });

    this.migrationReport.publicInfluencers.total = publicInfluencersCount;
    this.migrationReport.myInfluencers.total = myInfluencersCount;

    console.log(`📊 达人公海总数: ${publicInfluencersCount}`);
    console.log(`📊 有扩展信息的达人公海数: ${publicInfluencersWithExtInfo}`);
    console.log(`📊 我的达人总数: ${myInfluencersCount}`);
    console.log(`📊 有扩展信息的我的达人数: ${myInfluencersWithExtInfo}\n`);
  }

  /**
   * 迁移达人公海数据
   */
  async migratePublicInfluencersData() {
    console.log('📋 步骤4: 迁移达人公海数据...');
    await this.migrateTableData(PublicInfluencer, 'publicInfluencers', '达人公海');
  }

  /**
   * 迁移我的达人数据
   */
  async migrateMyInfluencersData() {
    console.log('📋 步骤5: 迁移我的达人数据...');
    await this.migrateTableData(MyInfluencer, 'myInfluencers', '我的达人');
  }

  /**
   * 迁移指定表的数据
   */
  async migrateTableData(Model, reportKey, tableName) {
    const totalCount = await Model.count({
      where: {
        author_ext_info: {
          [Op.ne]: null
        }
      }
    });

    if (totalCount === 0) {
      console.log(`ℹ️ ${tableName}表中没有需要迁移的数据\n`);
      return;
    }

    console.log(`🔄 开始迁移${tableName}数据，总计 ${totalCount} 条记录`);

    let offset = 0;
    let batchNumber = 1;

    while (offset < totalCount) {
      console.log(`🔄 处理第 ${batchNumber} 批数据 (${offset + 1}-${Math.min(offset + this.batchSize, totalCount)}/${totalCount})`);

      const records = await Model.findAll({
        where: {
          author_ext_info: {
            [Op.ne]: null
          }
        },
        limit: this.batchSize,
        offset: offset,
        order: [['id', 'ASC']]
      });

      for (const record of records) {
        try {
          await this.migrateRecord(record, Model);
          this.migrationReport[reportKey].success++;
        } catch (error) {
          console.error(`❌ 迁移记录 ${record.id} 失败:`, error.message);
          this.migrationReport[reportKey].failed++;
          this.migrationReport[reportKey].errors.push({
            recordId: record.id,
            error: error.message,
            timestamp: new Date()
          });
        }
        this.migrationReport[reportKey].processed++;
      }

      offset += this.batchSize;
      batchNumber++;

      // 批次间短暂延迟
      if (offset < totalCount) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    console.log(`✅ ${tableName}数据迁移完成\n`);
  }

  /**
   * 迁移单条记录
   */
  async migrateRecord(record, Model) {
    const authorExtInfo = record.authorExtInfo || record.author_ext_info;

    if (!authorExtInfo || typeof authorExtInfo !== 'object') {
      return;
    }

    const updateData = {};

    // 提取内容主题数据
    if (authorExtInfo.content_theme_labels_180d) {
      updateData.content_theme = authorExtInfo.content_theme_labels_180d;
    }

    // 提取达人标签数据
    if (authorExtInfo.tags_relation) {
      updateData.influencer_tags = authorExtInfo.tags_relation;
    }

    // 如果有数据需要更新
    if (Object.keys(updateData).length > 0) {
      if (!this.dryRun) {
        await record.update(updateData);
      }
      console.log(`📝 记录 ${record.id} 迁移完成: ${Object.keys(updateData).join(', ')}`);
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    console.log('📋 步骤6: 验证迁移结果...');

    // 验证达人公海数据
    const publicWithContentTheme = await PublicInfluencer.count({
      where: {
        content_theme: {
          [Op.ne]: null
        }
      }
    });

    const publicWithInfluencerTags = await PublicInfluencer.count({
      where: {
        influencer_tags: {
          [Op.ne]: null
        }
      }
    });

    // 验证我的达人数据
    const myWithContentTheme = await MyInfluencer.count({
      where: {
        content_theme: {
          [Op.ne]: null
        }
      }
    });

    const myWithInfluencerTags = await MyInfluencer.count({
      where: {
        influencer_tags: {
          [Op.ne]: null
        }
      }
    });

    console.log(`📊 达人公海 - 有内容主题数据: ${publicWithContentTheme}`);
    console.log(`📊 达人公海 - 有达人标签数据: ${publicWithInfluencerTags}`);
    console.log(`📊 我的达人 - 有内容主题数据: ${myWithContentTheme}`);
    console.log(`📊 我的达人 - 有达人标签数据: ${myWithInfluencerTags}`);
    console.log('✅ 迁移结果验证完成\n');
  }

  /**
   * 生成最终报告
   */
  generateFinalReport() {
    console.log('📋 步骤7: 生成迁移报告...');

    const duration = this.migrationReport.endTime - this.migrationReport.startTime;
    const durationMinutes = (duration / 1000 / 60).toFixed(2);

    console.log('\n📊 ===== 内容主题和达人标签字段迁移报告 =====');
    console.log(`⏱️ 迁移耗时: ${durationMinutes} 分钟`);
    console.log(`🔧 字段添加: ${this.migrationReport.fieldsAdded ? '✅ 成功' : '❌ 失败'}`);

    console.log('\n📈 达人公海数据迁移:');
    console.log(`📊 总记录数: ${this.migrationReport.publicInfluencers.total}`);
    console.log(`🔄 已处理: ${this.migrationReport.publicInfluencers.processed}`);
    console.log(`✅ 成功: ${this.migrationReport.publicInfluencers.success}`);
    console.log(`❌ 失败: ${this.migrationReport.publicInfluencers.failed}`);

    console.log('\n📈 我的达人数据迁移:');
    console.log(`📊 总记录数: ${this.migrationReport.myInfluencers.total}`);
    console.log(`🔄 已处理: ${this.migrationReport.myInfluencers.processed}`);
    console.log(`✅ 成功: ${this.migrationReport.myInfluencers.success}`);
    console.log(`❌ 失败: ${this.migrationReport.myInfluencers.failed}`);

    const totalSuccess = this.migrationReport.publicInfluencers.success + this.migrationReport.myInfluencers.success;
    const totalProcessed = this.migrationReport.publicInfluencers.processed + this.migrationReport.myInfluencers.processed;
    const successRate = totalProcessed > 0 ? ((totalSuccess / totalProcessed) * 100).toFixed(2) : 0;

    console.log(`\n🎯 总体成功率: ${successRate}%`);

    if (this.migrationReport.errors.length > 0) {
      console.log('\n❌ 迁移过程中的错误:');
      this.migrationReport.errors.forEach((error, index) => {
        console.log(`${index + 1}. [${error.stage}] ${error.error}`);
      });
    }

    console.log('\n🎉 迁移报告生成完成！');
  }
}

// 解析命令行参数
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    batchSize: 50
  };

  args.forEach(arg => {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--batch-size=')) {
      options.batchSize = parseInt(arg.split('=')[1]) || 50;
    }
  });

  return options;
}

// 主执行函数
async function runMigration() {
  const options = parseArguments();
  const migrator = new ContentThemeAndTagsMigrator(options);

  try {
    if (options.dryRun) {
      console.log('🔍 运行模式: 预演模式（不会实际修改数据）\n');
    }

    const report = await migrator.executeMigration();

    console.log('\n✅ 迁移流程完成');
    process.exit(0);

  } catch (error) {
    console.error('\n❌ 迁移流程失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runMigration();
}

module.exports = ContentThemeAndTagsMigrator;
