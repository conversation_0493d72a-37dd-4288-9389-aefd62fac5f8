/**
 * 简单的字段添加脚本
 */

const { sequelize } = require('../src/config/database');

async function addFields() {
  console.log('🚀 开始添加字段...');

  try {
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 添加 my_influencers 表字段
    console.log('\n📋 为 my_influencers 表添加字段...');
    
    try {
      await sequelize.query(`
        ALTER TABLE my_influencers 
        ADD COLUMN content_theme JSON COMMENT '内容主题'
      `);
      console.log('✅ content_theme 字段添加成功');
    } catch (error) {
      if (error.message.includes('Duplicate column')) {
        console.log('ℹ️ content_theme 字段已存在');
      } else {
        throw error;
      }
    }

    try {
      await sequelize.query(`
        ALTER TABLE my_influencers 
        ADD COLUMN influencer_tags JSON COMMENT '达人标签'
      `);
      console.log('✅ influencer_tags 字段添加成功');
    } catch (error) {
      if (error.message.includes('Duplicate column')) {
        console.log('ℹ️ influencer_tags 字段已存在');
      } else {
        throw error;
      }
    }

    // 添加 public_influencers 表字段
    console.log('\n📋 为 public_influencers 表添加字段...');
    
    try {
      await sequelize.query(`
        ALTER TABLE public_influencers 
        ADD COLUMN content_theme JSON COMMENT '内容主题'
      `);
      console.log('✅ content_theme 字段添加成功');
    } catch (error) {
      if (error.message.includes('Duplicate column')) {
        console.log('ℹ️ content_theme 字段已存在');
      } else {
        throw error;
      }
    }

    try {
      await sequelize.query(`
        ALTER TABLE public_influencers 
        ADD COLUMN influencer_tags JSON COMMENT '达人标签'
      `);
      console.log('✅ influencer_tags 字段添加成功');
    } catch (error) {
      if (error.message.includes('Duplicate column')) {
        console.log('ℹ️ influencer_tags 字段已存在');
      } else {
        throw error;
      }
    }

    // 验证字段
    console.log('\n📋 验证字段添加结果...');
    
    const [myColumns] = await sequelize.query(`
      SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'my_influencers' 
      AND COLUMN_NAME IN ('content_theme', 'influencer_tags')
    `);
    
    const [publicColumns] = await sequelize.query(`
      SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'public_influencers' 
      AND COLUMN_NAME IN ('content_theme', 'influencer_tags')
    `);

    console.log(`✅ my_influencers 新字段: ${myColumns.map(col => col.COLUMN_NAME).join(', ')}`);
    console.log(`✅ public_influencers 新字段: ${publicColumns.map(col => col.COLUMN_NAME).join(', ')}`);

    await sequelize.close();
    console.log('\n🎉 字段添加完成！');

  } catch (error) {
    console.error('❌ 错误:', error.message);
    process.exit(1);
  }
}

addFields();
