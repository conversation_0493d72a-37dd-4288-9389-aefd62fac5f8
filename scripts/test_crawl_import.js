/**
 * 测试脚本：验证爬虫结果导入到达人表的视频字段功能
 */

const { sequelize } = require('../src/config/database');
const { CrawlResult, Influencer } = require('../src/models');

async function testCrawlImport() {
  try {
    console.log('🔄 开始测试爬虫结果导入...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 查找现有的爬取结果
    console.log('🔍 查找现有爬取结果...');
    const crawlResults = await CrawlResult.findAll({
      where: {
        status: 'pending'
      },
      limit: 1
    });
    
    if (crawlResults.length === 0) {
      console.log('ℹ️ 没有找到待处理的爬取结果，创建测试数据...');
      
      // 创建测试爬取结果
      const testCrawlResult = await CrawlResult.create({
        taskId: 1,
        platform: 'xiaohongshu',
        platformUserId: 'test_import_' + Date.now(),
        nickname: '测试导入达人',
        avatarUrl: 'https://example.com/avatar.jpg',
        followersCount: 50000,
        city: '北京',
        uniqueId: 'test_unique_id',
        contactInfo: {
          wechat: 'test_wechat_import',
          phone: '139****0000'
        },
        videoStats: {
          videoCount: 30,
          averagePlay: 15000,
          totalPlay: 450000,
          totalLike: 45000,
          totalComment: 4500,
          totalShare: 900
        },
        videoDetails: [
          {
            videoId: 'import_video_001',
            title: '导入测试视频1',
            playCount: 18000,
            likeCount: 1800,
            commentCount: 180,
            shareCount: 90,
            publishTime: '2025-01-08T14:20:00Z',
            duration: 150
          },
          {
            videoId: 'import_video_002',
            title: '导入测试视频2',
            playCount: 12000,
            likeCount: 1200,
            commentCount: 120,
            shareCount: 60,
            publishTime: '2025-01-07T09:15:00Z',
            duration: 180
          }
        ],
        rawData: {
          source: 'test_import',
          timestamp: new Date().toISOString()
        },
        status: 'pending'
      });
      
      console.log('✅ 测试爬取结果创建成功，ID:', testCrawlResult.id);
      crawlResults.push(testCrawlResult);
    }
    
    const result = crawlResults[0];
    console.log('📋 使用爬取结果:', result.id, '-', result.nickname);
    
    // 检查是否已存在相同的达人
    const existingInfluencer = await Influencer.findOne({
      where: {
        platform: result.platform,
        platformId: result.platformUserId
      }
    });
    
    if (existingInfluencer) {
      console.log('ℹ️ 达人已存在，先删除旧记录...');
      await existingInfluencer.destroy();
    }
    
    // 模拟导入过程
    console.log('📥 开始导入到达人表...');
    const influencerData = {
      platform: result.platform,
      platformId: result.platformUserId,
      nickname: result.nickname,
      avatarUrl: result.avatarUrl,
      followersCount: result.followersCount,
      contactInfo: result.contactInfo,
      videoStats: result.videoStats,
      videoDetails: result.videoDetails,
      status: 'active',
      notes: '从爬虫结果导入测试'
    };
    
    const influencer = await Influencer.create(influencerData);
    console.log('✅ 导入成功，达人ID:', influencer.id);
    
    // 验证导入的数据
    console.log('🔍 验证导入的视频数据...');
    const savedInfluencer = await Influencer.findByPk(influencer.id);
    
    if (savedInfluencer.videoStats) {
      console.log('✅ videoStats 导入成功:');
      console.log('  - 视频数量:', savedInfluencer.videoStats.videoCount);
      console.log('  - 平均播放量:', savedInfluencer.videoStats.averagePlay);
      console.log('  - 总播放量:', savedInfluencer.videoStats.totalPlay);
      console.log('  - 总点赞数:', savedInfluencer.videoStats.totalLike);
    } else {
      console.log('❌ videoStats 导入失败');
    }
    
    if (savedInfluencer.videoDetails && Array.isArray(savedInfluencer.videoDetails)) {
      console.log('✅ videoDetails 导入成功:');
      console.log('  - 视频详情数量:', savedInfluencer.videoDetails.length);
      savedInfluencer.videoDetails.forEach((video, index) => {
        console.log(`  - 视频${index + 1}: ${video.title} (播放量: ${video.playCount})`);
      });
    } else {
      console.log('❌ videoDetails 导入失败');
    }
    
    // 更新爬取结果状态
    await result.update({
      status: 'imported',
      importedInfluencerId: influencer.id
    });
    console.log('✅ 爬取结果状态更新为已导入');
    
    console.log('🎉 爬虫结果导入测试完成，所有功能正常！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行测试
if (require.main === module) {
  testCrawlImport()
    .then(() => {
      console.log('✅ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = testCrawlImport;
