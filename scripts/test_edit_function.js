/**
 * 编辑功能测试脚本
 * 测试达人提报的编辑功能
 */

const axios = require('axios');

async function testEditFunction() {
  try {
    console.log('🚀 开始测试编辑功能...\n');

    // 1. 登录获取token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'testadmin',
      password: '123456'
    });

    if (!loginResponse.data.success) {
      throw new Error('登录失败');
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    // 2. 获取一个提报记录用于编辑测试
    const listResponse = await axios.get('http://localhost:3001/api/influencer-reports?limit=1', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (!listResponse.data.success || listResponse.data.data.length === 0) {
      throw new Error('没有找到可编辑的提报记录');
    }

    const originalReport = listResponse.data.data[0];
    console.log('✅ 获取到测试提报记录:', {
      id: originalReport.id,
      influencerName: originalReport.influencerName,
      platform: originalReport.platform,
      status: originalReport.status
    });

    // 3. 测试编辑功能
    const editData = {
      platform: originalReport.platform,
      influencerName: originalReport.influencerName + '_已编辑',
      operationManager: '编辑测试运营',
      influencerUrl: originalReport.influencerUrl,
      followersCount: (originalReport.followersCount || 0) + 1000,
      playMid: '编辑后的播放量',
      selectionReason: '编辑测试：' + (originalReport.selectionReason || '原选择理由'),
      platformPrice: '编辑后的平台报价',
      cooperationPrice: '编辑后的合作价格',
      notes: '编辑测试备注：' + new Date().toLocaleString(),
      platformUserId: originalReport.platformUserId
    };

    console.log('📝 发送编辑请求...');
    const editResponse = await axios.put(
      `http://localhost:3001/api/influencer-reports/${originalReport.id}`,
      editData,
      {
        headers: { 'Authorization': `Bearer ${token}` }
      }
    );

    if (editResponse.data.success) {
      console.log('✅ 编辑成功');
      
      // 4. 验证编辑结果
      const verifyResponse = await axios.get(
        `http://localhost:3001/api/influencer-reports/${originalReport.id}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (verifyResponse.data.success) {
        const updatedReport = verifyResponse.data.data;
        console.log('✅ 编辑验证成功');
        
        console.log('\n📊 编辑前后对比:');
        console.log('达人名称:', originalReport.influencerName, '->', updatedReport.influencerName);
        console.log('运营负责人:', originalReport.operationManager, '->', updatedReport.operationManager);
        console.log('粉丝量:', originalReport.followersCount, '->', updatedReport.followersCount);
        console.log('播放量中位数:', originalReport.playMid, '->', updatedReport.playMid);
        console.log('备注:', originalReport.notes, '->', updatedReport.notes);
        
        // 验证审核相关字段是否保持不变
        console.log('\n🔍 审核信息验证:');
        console.log('状态:', updatedReport.status, '(应保持不变)');
        console.log('审核意见:', updatedReport.reviewComment || '无');
        console.log('重新提报次数:', updatedReport.resubmitCount || 0);
        
      } else {
        console.error('❌ 编辑验证失败:', verifyResponse.data.message);
      }
    } else {
      console.error('❌ 编辑失败:', editResponse.data.message);
    }

    console.log('\n🎉 编辑功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testEditFunction();
