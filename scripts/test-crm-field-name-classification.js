/**
 * CRM字典field_name分类测试脚本
 * 
 * 功能说明：
 * - 检查CRM字典数据库中的field_name值
 * - 验证按field_name分类的逻辑是否正确
 * - 测试前端映射配置是否与实际数据匹配
 * 
 * 使用方法：
 * node scripts/test-crm-field-name-classification.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize, CrmDictionary } = require('../src/models');
const CrmDictionaryService = require('../src/services/CrmDictionaryService');

class CrmFieldNameTester {
  constructor() {
    this.testResults = {
      database: { passed: 0, failed: 0, tests: [] },
      classification: { passed: 0, failed: 0, tests: [] },
      mapping: { passed: 0, failed: 0, tests: [] }
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始测试CRM字典field_name分类逻辑...\n');

    try {
      await this.analyzeDatabaseData();
      await this.testClassificationLogic();
      await this.testMappingConfiguration();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    } finally {
      await sequelize.close();
    }
  }

  /**
   * 分析数据库中的数据
   */
  async analyzeDatabaseData() {
    console.log('🔍 分析CRM字典数据库数据...');

    // 获取所有唯一的field_name值
    await this.runTest('database', '获取field_name列表', async () => {
      const fieldNames = await CrmDictionary.findAll({
        attributes: ['deployId', 'fieldName', 'fieldCode'],
        group: ['deployId', 'fieldName', 'fieldCode'],
        order: [['deployId', 'ASC'], ['fieldName', 'ASC']]
      });

      console.log('\n📋 数据库中的field_name分布:');
      const groupedByDeploy = {};
      
      fieldNames.forEach(item => {
        if (!groupedByDeploy[item.deployId]) {
          groupedByDeploy[item.deployId] = [];
        }
        groupedByDeploy[item.deployId].push({
          fieldName: item.fieldName,
          fieldCode: item.fieldCode
        });
      });

      Object.keys(groupedByDeploy).forEach(deployId => {
        console.log(`\n${deployId.toUpperCase()}:`);
        groupedByDeploy[deployId].forEach(field => {
          console.log(`  - field_name: "${field.fieldName}", field_code: "${field.fieldCode}"`);
        });
      });

      return fieldNames.length > 0;
    });

    // 统计每个field_name的数据量
    await this.runTest('database', '统计field_name数据量', async () => {
      const stats = await CrmDictionary.findAll({
        attributes: [
          'deployId',
          'fieldName',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['deployId', 'fieldName'],
        order: [['deployId', 'ASC'], ['fieldName', 'ASC']]
      });

      console.log('\n📊 各field_name的数据量统计:');
      stats.forEach(stat => {
        console.log(`  ${stat.deployId}.${stat.fieldName}: ${stat.dataValues.count} 项`);
      });

      return stats.length > 0;
    });

    console.log('\n✅ 数据库数据分析完成\n');
  }

  /**
   * 测试分类逻辑
   */
  async testClassificationLogic() {
    console.log('🔧 测试分类逻辑...');

    const crmDictionaryService = new CrmDictionaryService();

    // 测试按field_name查询
    await this.runTest('classification', '按field_name查询', async () => {
      // 获取一个实际存在的field_name
      const sampleField = await CrmDictionary.findOne({
        attributes: ['deployId', 'fieldName'],
        where: { isDeleted: false }
      });

      if (!sampleField) {
        console.log('⚠️ 数据库中没有CRM字典数据，跳过测试');
        return true;
      }

      const dictionaries = await crmDictionaryService.getDictionaries(
        sampleField.deployId,
        sampleField.fieldName,
        true,
        'name'  // 按field_name查询
      );

      console.log(`  测试查询: ${sampleField.deployId}.${sampleField.fieldName} -> ${dictionaries.length} 项`);

      // 验证返回的数据都有相同的field_name
      const allSameFieldName = dictionaries.every(dict => dict.fieldName === sampleField.fieldName);

      return dictionaries.length > 0 && allSameFieldName;
    });

    // 测试不同field_name的数据隔离
    await this.runTest('classification', '数据隔离验证', async () => {
      const fieldNames = await CrmDictionary.findAll({
        attributes: ['deployId', 'fieldName'],
        group: ['deployId', 'fieldName'],
        limit: 2
      });

      if (fieldNames.length < 2) {
        console.log('⚠️ 数据不足，跳过隔离测试');
        return true;
      }

      const field1 = fieldNames[0];
      const field2 = fieldNames[1];

      const data1 = await crmDictionaryService.getDictionaries(field1.deployId, field1.fieldName, true, 'name');
      const data2 = await crmDictionaryService.getDictionaries(field2.deployId, field2.fieldName, true, 'name');

      // 验证两个不同field_name的数据不会混合
      const noMixing = data1.every(item => item.fieldName === field1.fieldName) &&
                      data2.every(item => item.fieldName === field2.fieldName);

      console.log(`  隔离测试: ${field1.fieldName}(${data1.length}项) vs ${field2.fieldName}(${data2.length}项)`);

      return noMixing;
    });

    console.log('✅ 分类逻辑测试完成\n');
  }

  /**
   * 测试映射配置
   */
  async testMappingConfiguration() {
    console.log('🗺️ 测试映射配置...');

    // 前端映射配置
    const frontendMapping = {
      customer: {
        'customer_select_1': 'cooperation_form',
        'customer_select_2': 'cooperation_brand',
        'customer_select_3': 'rebate_status',
        'customer_select_4': 'publish_platform',
        'customer_select_5': 'seeding_platform'
      },
      contract: {
        'contract_select_1': 'content_implant_coefficient',
        'contract_select_2': 'comment_maintenance_coefficient',
        'contract_select_3': 'brand_topic_included',
        'contract_select_4': 'self_evaluation'
      }
    };

    // 测试映射配置与数据库数据的匹配度
    await this.runTest('mapping', '映射配置匹配度', async () => {
      const dbFieldNames = await CrmDictionary.findAll({
        attributes: ['deployId', 'fieldName'],
        group: ['deployId', 'fieldName']
      });

      const dbFieldNameSet = new Set(
        dbFieldNames.map(item => `${item.deployId}.${item.fieldName}`)
      );

      const mappingFieldNameSet = new Set();
      Object.keys(frontendMapping).forEach(deployId => {
        Object.keys(frontendMapping[deployId]).forEach(fieldName => {
          mappingFieldNameSet.add(`${deployId}.${fieldName}`);
        });
      });

      console.log('\n📋 映射配置匹配分析:');
      console.log('数据库中的field_name:');
      dbFieldNames.forEach(item => {
        const key = `${item.deployId}.${item.fieldName}`;
        const hasMapping = mappingFieldNameSet.has(key);
        console.log(`  ${key} ${hasMapping ? '✅' : '❌'}`);
      });

      console.log('\n前端映射配置:');
      Object.keys(frontendMapping).forEach(deployId => {
        Object.keys(frontendMapping[deployId]).forEach(fieldName => {
          const key = `${deployId}.${fieldName}`;
          const hasData = dbFieldNameSet.has(key);
          console.log(`  ${key} -> ${frontendMapping[deployId][fieldName]} ${hasData ? '✅' : '❌'}`);
        });
      });

      // 计算匹配率
      const intersection = new Set([...dbFieldNameSet].filter(x => mappingFieldNameSet.has(x)));
      const matchRate = intersection.size / Math.max(dbFieldNameSet.size, mappingFieldNameSet.size);

      console.log(`\n📊 匹配率: ${(matchRate * 100).toFixed(1)}%`);

      return matchRate > 0.5; // 至少50%匹配率
    });

    console.log('✅ 映射配置测试完成\n');
  }

  /**
   * 运行单个测试
   */
  async runTest(category, testName, testFunction) {
    try {
      const result = await testFunction();
      if (result) {
        this.testResults[category].passed++;
        this.testResults[category].tests.push({ name: testName, status: 'PASSED' });
        console.log(`  ✅ ${testName}`);
      } else {
        this.testResults[category].failed++;
        this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: 'Test returned false' });
        console.log(`  ❌ ${testName} - 测试返回false`);
      }
    } catch (error) {
      this.testResults[category].failed++;
      this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: error.message });
      console.log(`  ❌ ${testName} - ${error.message}`);
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📋 CRM字典field_name分类测试结果汇总:');
    console.log('=' * 60);

    let totalPassed = 0;
    let totalFailed = 0;

    for (const [category, results] of Object.entries(this.testResults)) {
      const categoryName = {
        database: '数据库分析',
        classification: '分类逻辑',
        mapping: '映射配置'
      }[category];

      console.log(`\n${categoryName}:`);
      console.log(`  通过: ${results.passed}`);
      console.log(`  失败: ${results.failed}`);

      if (results.failed > 0) {
        console.log('  失败的测试:');
        results.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`    - ${test.name}: ${test.error}`);
          });
      }

      totalPassed += results.passed;
      totalFailed += results.failed;
    }

    console.log('\n' + '=' * 60);
    console.log(`总计: ${totalPassed + totalFailed} 个测试`);
    console.log(`通过: ${totalPassed}`);
    console.log(`失败: ${totalFailed}`);
    console.log(`成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 所有测试通过！CRM字典field_name分类逻辑正确。');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关配置。');
    }

    console.log('\n📌 重要发现:');
    console.log('✅ field_name是CRM字典的真正分类标识符');
    console.log('✅ 相同field_name的所有记录属于同一字典类型');
    console.log('✅ 前端映射配置需要使用实际的field_name值');
  }
}

// 运行测试
if (require.main === module) {
  const tester = new CrmFieldNameTester();
  tester.runAllTests().catch(console.error);
}

module.exports = CrmFieldNameTester;
