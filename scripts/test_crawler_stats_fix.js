/**
 * 测试脚本：验证爬虫统计接口修复
 * 测试 /api/crawler/stats 接口返回的数据格式是否正确
 */

const { sequelize } = require('../src/config/database');
const { CrawlTask } = require('../src/models');
const CrawlerController = require('../src/controllers/CrawlerController');

async function testCrawlerStatsfix() {
  try {
    console.log('🔄 开始测试爬虫统计接口修复...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 1. 查看当前数据库中的任务状态分布
    console.log('\n📋 步骤1: 查看数据库中的任务状态分布...');
    const taskStats = await CrawlTask.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', '*'), 'count']
      ],
      group: ['status'],
      raw: true
    });
    
    console.log('数据库中的任务状态分布:');
    let totalTasks = 0;
    const statusCounts = {};
    
    taskStats.forEach(stat => {
      const count = parseInt(stat.count);
      statusCounts[stat.status] = count;
      totalTasks += count;
      console.log(`  - ${stat.status}: ${count}`);
    });
    console.log(`  - 总计: ${totalTasks}`);
    
    // 2. 测试修复后的统计接口
    console.log('\n📋 步骤2: 测试修复后的统计接口...');
    
    const mockCtx = {
      query: {},
      body: null
    };
    
    // 调用控制器方法
    await CrawlerController.getStats(mockCtx);
    
    const responseData = mockCtx.body;
    
    console.log('接口返回的数据结构:');
    console.log(`  - success: ${responseData.success}`);
    console.log(`  - message: ${responseData.message}`);
    
    if (responseData.data) {
      console.log('统计数据:');
      console.log(`  - total: ${responseData.data.total}`);
      console.log(`  - running: ${responseData.data.running}`);
      console.log(`  - completed: ${responseData.data.completed}`);
      console.log(`  - failed: ${responseData.data.failed}`);
      console.log(`  - pending: ${responseData.data.pending}`);
      console.log(`  - paused: ${responseData.data.paused}`);
      console.log(`  - cancelled: ${responseData.data.cancelled}`);
      
      console.log('\n原始详细数据:');
      console.log(`  - taskStats: ${responseData.data.taskStats?.length || 0} 项`);
      console.log(`  - platformStats: ${responseData.data.platformStats?.length || 0} 项`);
      console.log(`  - resultStats: ${responseData.data.resultStats?.length || 0} 项`);
      console.log(`  - todayTaskCount: ${responseData.data.todayTaskCount}`);
    }
    
    // 3. 验证数据正确性
    console.log('\n📋 步骤3: 验证数据正确性...');
    
    if (!responseData.success) {
      console.log('❌ 接口调用失败');
      return;
    }
    
    const data = responseData.data;
    
    // 验证总数是否正确
    if (data.total === totalTasks) {
      console.log('✅ 总任务数正确');
    } else {
      console.log(`❌ 总任务数不正确，期望: ${totalTasks}, 实际: ${data.total}`);
    }
    
    // 验证各状态数量是否正确
    const statusesToCheck = ['running', 'completed', 'failed', 'pending', 'paused', 'cancelled'];
    let allStatusCorrect = true;
    
    statusesToCheck.forEach(status => {
      const expected = statusCounts[status] || 0;
      const actual = data[status] || 0;
      
      if (expected === actual) {
        console.log(`✅ ${status} 数量正确: ${actual}`);
      } else {
        console.log(`❌ ${status} 数量不正确，期望: ${expected}, 实际: ${actual}`);
        allStatusCorrect = false;
      }
    });
    
    // 验证计算的总数是否等于各状态之和
    const calculatedTotal = (data.running || 0) + (data.completed || 0) + (data.failed || 0) + 
                           (data.pending || 0) + (data.paused || 0) + (data.cancelled || 0);
    
    if (data.total === calculatedTotal) {
      console.log('✅ 总数等于各状态之和');
    } else {
      console.log(`❌ 总数不等于各状态之和，总数: ${data.total}, 各状态之和: ${calculatedTotal}`);
      allStatusCorrect = false;
    }
    
    // 验证是否包含原始数据（向后兼容）
    if (data.taskStats && Array.isArray(data.taskStats)) {
      console.log('✅ 包含原始 taskStats 数据（向后兼容）');
    } else {
      console.log('❌ 缺少原始 taskStats 数据');
    }
    
    if (data.platformStats && Array.isArray(data.platformStats)) {
      console.log('✅ 包含原始 platformStats 数据（向后兼容）');
    } else {
      console.log('❌ 缺少原始 platformStats 数据');
    }
    
    if (data.resultStats && Array.isArray(data.resultStats)) {
      console.log('✅ 包含原始 resultStats 数据（向后兼容）');
    } else {
      console.log('❌ 缺少原始 resultStats 数据');
    }
    
    // 4. 总结
    console.log('\n📋 步骤4: 测试总结...');
    
    if (allStatusCorrect && data.total === totalTasks) {
      console.log('🎉 爬虫统计接口修复成功！');
      console.log('✅ 前端现在可以正确显示统计数据');
      console.log('✅ 保持了向后兼容性');
    } else {
      console.log('❌ 修复存在问题，需要进一步检查');
    }
    
    console.log('\n🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
  } finally {
    // 确保数据库连接关闭
    await sequelize.close();
    console.log('📝 数据库连接已关闭');
  }
}

// 运行测试
if (require.main === module) {
  testCrawlerStatsfix().catch(console.error);
}

module.exports = testCrawlerStatsfix;
