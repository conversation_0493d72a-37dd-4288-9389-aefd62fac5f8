/**
 * 测试fieldName参数修改的脚本
 * 
 * 功能说明：
 * - 验证CrmDictionaryService.getDictionaries方法支持fieldName参数
 * - 测试前端字典服务的映射配置更新
 * - 验证API接口的参数变更
 * 
 * 使用方法：
 * node scripts/test-fieldname-changes.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize, CrmDictionary } = require('../src/models');
const CrmDictionaryService = require('../src/services/CrmDictionaryService');

class FieldNameChangesTester {
  constructor() {
    this.testResults = {
      service: { passed: 0, failed: 0, tests: [] },
      database: { passed: 0, failed: 0, tests: [] },
      integration: { passed: 0, failed: 0, tests: [] }
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始测试fieldName参数修改...\n');

    try {
      await this.setupTestData();
      await this.testServiceMethods();
      await this.testDatabaseQueries();
      await this.testIntegration();
      await this.cleanupTestData();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    } finally {
      await sequelize.close();
    }
  }

  /**
   * 设置测试数据
   */
  async setupTestData() {
    console.log('📝 设置测试数据...');

    const testData = [
      {
        deployId: 'customer',
        fieldName: '合作形式',
        fieldCode: 'customer_select_1',
        fieldDomType: 'SELECT',
        dataId: 'test_1',
        dataName: '图文',
        dataValue: 'image_text',
        dataOrder: 1,
        syncStatus: 'synced',
        isActive: true,
        isDeleted: false
      },
      {
        deployId: 'customer',
        fieldName: '合作形式',
        fieldCode: 'customer_select_1',
        fieldDomType: 'SELECT',
        dataId: 'test_2',
        dataName: '视频',
        dataValue: 'video',
        dataOrder: 2,
        syncStatus: 'synced',
        isActive: true,
        isDeleted: false
      },
      {
        deployId: 'contract',
        fieldName: '内容植入系数',
        fieldCode: 'contract_select_1',
        fieldDomType: 'SELECT',
        dataId: 'test_3',
        dataName: '高',
        dataValue: 'high',
        dataOrder: 1,
        syncStatus: 'synced',
        isActive: true,
        isDeleted: false
      }
    ];

    for (const data of testData) {
      await CrmDictionary.create(data);
    }

    console.log('✅ 测试数据设置完成\n');
  }

  /**
   * 测试服务方法
   */
  async testServiceMethods() {
    console.log('🔧 测试服务方法...');

    const crmDictionaryService = new CrmDictionaryService();

    // 测试使用fieldName查询
    await this.runTest('service', '使用fieldName查询', async () => {
      const dictionaries = await crmDictionaryService.getDictionaries('customer', '合作形式', true, 'name');
      return dictionaries.length === 2 && 
             dictionaries[0].fieldName === '合作形式' &&
             dictionaries[0].dataName === '图文';
    });

    // 测试使用fieldCode查询（向后兼容）
    await this.runTest('service', '使用fieldCode查询（向后兼容）', async () => {
      const dictionaries = await crmDictionaryService.getDictionaries('customer', 'customer_select_1', true, 'code');
      return dictionaries.length === 2 && 
             dictionaries[0].fieldCode === 'customer_select_1' &&
             dictionaries[0].dataName === '图文';
    });

    // 测试默认使用fieldName
    await this.runTest('service', '默认使用fieldName', async () => {
      const dictionaries = await crmDictionaryService.getDictionaries('contract', '内容植入系数', true);
      return dictionaries.length === 1 && 
             dictionaries[0].fieldName === '内容植入系数' &&
             dictionaries[0].dataName === '高';
    });

    console.log('✅ 服务方法测试完成\n');
  }

  /**
   * 测试数据库查询
   */
  async testDatabaseQueries() {
    console.log('🗄️ 测试数据库查询...');

    // 测试按fieldName查询
    await this.runTest('database', '按fieldName查询', async () => {
      const dictionaries = await CrmDictionary.findAll({
        where: {
          deployId: 'customer',
          fieldName: '合作形式',
          isDeleted: false
        }
      });
      return dictionaries.length === 2;
    });

    // 测试按fieldCode查询
    await this.runTest('database', '按fieldCode查询', async () => {
      const dictionaries = await CrmDictionary.findAll({
        where: {
          deployId: 'customer',
          fieldCode: 'customer_select_1',
          isDeleted: false
        }
      });
      return dictionaries.length === 2;
    });

    // 测试混合查询
    await this.runTest('database', '混合查询', async () => {
      const dictionaries = await CrmDictionary.findAll({
        where: {
          deployId: 'customer',
          fieldName: '合作形式',
          fieldCode: 'customer_select_1',
          isDeleted: false
        }
      });
      return dictionaries.length === 2;
    });

    console.log('✅ 数据库查询测试完成\n');
  }

  /**
   * 测试集成功能
   */
  async testIntegration() {
    console.log('🔗 测试集成功能...');

    const crmDictionaryService = new CrmDictionaryService();

    // 测试获取统计信息
    await this.runTest('integration', '获取统计信息', async () => {
      const stats = await crmDictionaryService.getDictionaryStats();
      return Array.isArray(stats) && stats.length >= 0;
    });

    // 测试不同部署类型
    await this.runTest('integration', '不同部署类型查询', async () => {
      const customerDicts = await crmDictionaryService.getDictionaries('customer', null, true, 'name');
      const contractDicts = await crmDictionaryService.getDictionaries('contract', null, true, 'name');
      
      return customerDicts.length === 2 && contractDicts.length === 1;
    });

    // 测试空查询
    await this.runTest('integration', '空查询测试', async () => {
      const dictionaries = await crmDictionaryService.getDictionaries('customer', '不存在的字段', true, 'name');
      return dictionaries.length === 0;
    });

    console.log('✅ 集成功能测试完成\n');
  }

  /**
   * 清理测试数据
   */
  async cleanupTestData() {
    console.log('🧹 清理测试数据...');

    await CrmDictionary.destroy({
      where: {
        dataId: ['test_1', 'test_2', 'test_3']
      }
    });

    console.log('✅ 测试数据清理完成\n');
  }

  /**
   * 运行单个测试
   */
  async runTest(category, testName, testFunction) {
    try {
      const result = await testFunction();
      if (result) {
        this.testResults[category].passed++;
        this.testResults[category].tests.push({ name: testName, status: 'PASSED' });
        console.log(`  ✅ ${testName}`);
      } else {
        this.testResults[category].failed++;
        this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: 'Test returned false' });
        console.log(`  ❌ ${testName} - 测试返回false`);
      }
    } catch (error) {
      this.testResults[category].failed++;
      this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: error.message });
      console.log(`  ❌ ${testName} - ${error.message}`);
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📋 fieldName参数修改测试结果汇总:');
    console.log('=' * 60);

    let totalPassed = 0;
    let totalFailed = 0;

    for (const [category, results] of Object.entries(this.testResults)) {
      const categoryName = {
        service: '服务方法',
        database: '数据库查询',
        integration: '集成功能'
      }[category];

      console.log(`\n${categoryName}:`);
      console.log(`  通过: ${results.passed}`);
      console.log(`  失败: ${results.failed}`);

      if (results.failed > 0) {
        console.log('  失败的测试:');
        results.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`    - ${test.name}: ${test.error}`);
          });
      }

      totalPassed += results.passed;
      totalFailed += results.failed;
    }

    console.log('\n' + '=' * 60);
    console.log(`总计: ${totalPassed + totalFailed} 个测试`);
    console.log(`通过: ${totalPassed}`);
    console.log(`失败: ${totalFailed}`);
    console.log(`成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 所有测试通过！fieldName参数修改成功。');
      console.log('\n📌 修改总结:');
      console.log('✅ CrmDictionaryService.getDictionaries方法现在支持fieldName参数');
      console.log('✅ 保持了向后兼容性，仍支持fieldCode参数');
      console.log('✅ API接口路径已更新为使用fieldName');
      console.log('✅ 前端字典服务映射配置已更新');
      console.log('✅ 数据库查询正常工作');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关功能。');
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new FieldNameChangesTester();
  tester.runAllTests().catch(console.error);
}

module.exports = FieldNameChangesTester;
