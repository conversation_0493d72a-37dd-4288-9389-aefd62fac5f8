/**
 * 数据库迁移脚本：为用户表添加中文名称字段
 * 
 * 功能说明：
 * 1. 为 users 表添加 chinese_name 字段
 * 2. 提供安全的迁移和回滚机制
 * 3. 保持现有数据完整性
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize } = require('../src/config/database');

async function migrateAddChineseNameField() {
  let transaction;
  
  try {
    console.log('🔄 开始执行用户表中文名称字段迁移...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 开始事务
    transaction = await sequelize.transaction();
    console.log('🔄 开始数据库事务...');
    
    // 检查字段是否已存在
    const [results] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'chinese_name'
    `, { transaction });
    
    if (results.length > 0) {
      console.log('ℹ️ chinese_name 字段已存在，跳过迁移');
      await transaction.commit();
      return;
    }
    
    // 添加 chinese_name 字段
    await sequelize.query(`
      ALTER TABLE users 
      ADD COLUMN chinese_name VARCHAR(100) NULL 
      COMMENT '中文名称' 
      AFTER email
    `, { transaction });
    
    console.log('✅ chinese_name 字段添加成功');
    
    // 验证字段添加是否成功
    const [verifyResults] = await sequelize.query(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'chinese_name'
    `, { transaction });
    
    if (verifyResults.length === 0) {
      throw new Error('字段添加验证失败');
    }
    
    console.log('✅ 字段验证成功:', verifyResults[0]);
    
    // 提交事务
    await transaction.commit();
    console.log('✅ 数据库迁移完成！');
    
    // 显示迁移结果
    console.log('\n📊 迁移结果统计:');
    console.log('- 添加字段: chinese_name (VARCHAR(100), 可为空)');
    console.log('- 字段位置: email 字段之后');
    console.log('- 字段注释: 中文名称');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    
    if (transaction) {
      try {
        await transaction.rollback();
        console.log('🔄 事务已回滚');
      } catch (rollbackError) {
        console.error('❌ 事务回滚失败:', rollbackError.message);
      }
    }
    
    throw error;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 数据库连接已关闭');
    } catch (closeError) {
      console.error('❌ 关闭数据库连接失败:', closeError.message);
    }
  }
}

// 回滚函数
async function rollbackChineseNameField() {
  let transaction;
  
  try {
    console.log('🔄 开始回滚中文名称字段...');
    
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    transaction = await sequelize.transaction();
    
    // 检查字段是否存在
    const [results] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'chinese_name'
    `, { transaction });
    
    if (results.length === 0) {
      console.log('ℹ️ chinese_name 字段不存在，无需回滚');
      await transaction.commit();
      return;
    }
    
    // 删除字段
    await sequelize.query(`
      ALTER TABLE users 
      DROP COLUMN chinese_name
    `, { transaction });
    
    console.log('✅ chinese_name 字段删除成功');
    
    await transaction.commit();
    console.log('✅ 回滚完成！');
    
  } catch (error) {
    console.error('❌ 回滚失败:', error.message);
    
    if (transaction) {
      try {
        await transaction.rollback();
        console.log('🔄 回滚事务已回滚');
      } catch (rollbackError) {
        console.error('❌ 回滚事务失败:', rollbackError.message);
      }
    }
    
    throw error;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 数据库连接已关闭');
    } catch (closeError) {
      console.error('❌ 关闭数据库连接失败:', closeError.message);
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--rollback')) {
    await rollbackChineseNameField();
  } else {
    await migrateAddChineseNameField();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  migrateAddChineseNameField,
  rollbackChineseNameField
};
