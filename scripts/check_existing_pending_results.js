/**
 * 检查现有的pending状态爬取结果
 * 并提供手动更新为processed状态的选项
 */

const { CrawlTask, CrawlResult, sequelize } = require('../src/models');

async function checkExistingPendingResults() {
  try {
    console.log('🔍 检查现有的pending状态爬取结果...\n');

    // 1. 查找所有pending状态的爬取结果
    const pendingResults = await CrawlResult.findAll({
      where: { status: 'pending' },
      include: [{
        model: CrawlTask,
        as: 'task',
        attributes: ['id', 'taskName', 'status', 'completedAt']
      }],
      attributes: ['id', 'taskId', 'nickname', 'platformUserId', 'status', 'createdAt'],
      order: [['taskId', 'ASC'], ['createdAt', 'ASC']]
    });

    if (pendingResults.length === 0) {
      console.log('✅ 没有找到pending状态的爬取结果');
      return;
    }

    console.log(`📊 找到 ${pendingResults.length} 个pending状态的爬取结果:\n`);

    // 按任务分组显示
    const resultsByTask = {};
    pendingResults.forEach(result => {
      const taskId = result.taskId;
      if (!resultsByTask[taskId]) {
        resultsByTask[taskId] = {
          task: result.task,
          results: []
        };
      }
      resultsByTask[taskId].results.push(result);
    });

    // 显示每个任务的pending结果
    Object.keys(resultsByTask).forEach(taskId => {
      const { task, results } = resultsByTask[taskId];
      console.log(`📋 任务 ${taskId}: ${task?.taskName || '未知任务'}`);
      console.log(`   任务状态: ${task?.status || '未知'}`);
      console.log(`   完成时间: ${task?.completedAt ? task.completedAt.toLocaleString() : '未完成'}`);
      console.log(`   pending结果数量: ${results.length}`);
      
      results.slice(0, 3).forEach((result, index) => {
        console.log(`   ${index + 1}. ID: ${result.id}, 昵称: ${result.nickname}`);
      });
      
      if (results.length > 3) {
        console.log(`   ... 还有 ${results.length - 3} 个结果`);
      }
      console.log('');
    });

    // 2. 检查已完成任务的pending结果
    const completedTasksWithPending = Object.keys(resultsByTask).filter(taskId => {
      const task = resultsByTask[taskId].task;
      return task && task.status === 'completed';
    });

    if (completedTasksWithPending.length > 0) {
      console.log(`⚠️ 发现 ${completedTasksWithPending.length} 个已完成任务仍有pending状态的结果:`);
      completedTasksWithPending.forEach(taskId => {
        const { task, results } = resultsByTask[taskId];
        console.log(`   任务 ${taskId}: ${task.taskName} (${results.length} 个pending结果)`);
      });
      console.log('');

      // 提供修复选项
      console.log('🔧 可以手动修复这些已完成任务的结果状态...');
      
      // 自动修复已完成任务的pending结果
      for (const taskId of completedTasksWithPending) {
        const { task, results } = resultsByTask[taskId];
        console.log(`🔄 正在修复任务 ${taskId} 的 ${results.length} 个pending结果...`);
        
        const updateResult = await CrawlResult.update(
          { status: 'processed' },
          {
            where: {
              taskId: taskId,
              status: 'pending'
            }
          }
        );
        
        const updatedCount = updateResult[0];
        console.log(`✅ 已将任务 ${taskId} 的 ${updatedCount} 个结果状态更新为 'processed'`);
      }
      
      console.log('\n🎉 修复完成！');
    } else {
      console.log('ℹ️ 所有pending结果都来自未完成的任务，这是正常的');
    }

    // 3. 显示最终统计
    console.log('\n📊 最终统计:');
    const finalStats = await CrawlResult.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    finalStats.forEach(stat => {
      console.log(`   ${stat.status}: ${stat.count}`);
    });

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
  } finally {
    await sequelize.close();
    console.log('\n📝 数据库连接已关闭');
  }
}

// 运行检查
if (require.main === module) {
  checkExistingPendingResults().catch(console.error);
}

module.exports = checkExistingPendingResults;
