/**
 * 测试videoDetails字段删除功能
 * 验证迁移脚本中的字段删除功能是否正常工作
 */

const { initializeAndSyncDatabase, Influencer, sequelize } = require('../src/models');
const VideoDetailsMigrator = require('./migrate_video_details_to_author_videos');

async function testColumnDrop() {
  try {
    console.log('🚀 开始测试videoDetails字段删除功能...\n');

    // 1. 初始化数据库
    console.log('📋 步骤1: 初始化数据库...');
    await initializeAndSyncDatabase();
    console.log('✅ 数据库初始化完成\n');

    // 2. 检查字段是否存在
    console.log('📋 步骤2: 检查videoDetails字段状态...');
    const columnExists = await checkColumnExists();
    console.log(`📊 videoDetails字段存在: ${columnExists ? '✅ 是' : '❌ 否'}\n`);

    if (!columnExists) {
      console.log('⚠️ videoDetails字段不存在，无法测试删除功能');
      console.log('💡 请先确保数据库表结构包含videoDetails字段\n');
      return;
    }

    // 3. 创建测试数据
    console.log('📋 步骤3: 创建测试数据...');
    const testInfluencer = await Influencer.create({
      nickname: '字段删除测试达人',
      platform: 'juxingtu',
      platformId: `test_column_drop_${Date.now()}`,
      avatarUrl: 'https://example.com/avatar.jpg',
      followersCount: 1000,
      videoDetails: [
        {
          videoId: `test_video_${Date.now()}`,
          title: '测试视频',
          playCount: 1000,
          likeCount: 100
        }
      ],
      status: 'active'
    });
    console.log(`✅ 创建测试达人: ${testInfluencer.nickname} (ID: ${testInfluencer.id})\n`);

    // 4. 测试不自动删除字段的情况
    console.log('📋 步骤4: 测试默认行为（不删除字段）...');
    const migrator1 = new VideoDetailsMigrator({ autoDropColumn: false });
    migrator1.batchSize = 1; // 设置小批量用于测试
    
    await migrator1.executeMigration();
    
    const columnExistsAfterMigration = await checkColumnExists();
    console.log(`📊 迁移后字段存在: ${columnExistsAfterMigration ? '✅ 是' : '❌ 否'}`);
    
    if (columnExistsAfterMigration) {
      console.log('✅ 默认行为测试通过：字段未被删除\n');
    } else {
      console.log('❌ 默认行为测试失败：字段被意外删除\n');
      throw new Error('默认行为测试失败');
    }

    // 5. 测试自动删除字段的情况
    console.log('📋 步骤5: 测试自动删除字段功能...');
    const migrator2 = new VideoDetailsMigrator({ autoDropColumn: true });
    
    // 由于数据已经迁移，这次主要测试字段删除逻辑
    await migrator2.dropVideoDetailsColumn();
    
    const columnExistsAfterDrop = await checkColumnExists();
    console.log(`📊 删除后字段存在: ${columnExistsAfterDrop ? '❌ 是' : '✅ 否'}`);
    
    if (!columnExistsAfterDrop) {
      console.log('✅ 字段删除测试通过：字段已被成功删除\n');
    } else {
      console.log('❌ 字段删除测试失败：字段未被删除\n');
      throw new Error('字段删除测试失败');
    }

    // 6. 验证表结构
    console.log('📋 步骤6: 验证表结构...');
    const tableStructure = await getTableStructure();
    console.log('📊 当前表结构:');
    tableStructure.forEach(column => {
      console.log(`   - ${column.Field}: ${column.Type}`);
    });
    
    const hasVideoDetailsColumn = tableStructure.some(col => col.Field === 'video_details');
    if (!hasVideoDetailsColumn) {
      console.log('✅ 表结构验证通过：videoDetails字段已从表结构中移除\n');
    } else {
      console.log('❌ 表结构验证失败：videoDetails字段仍存在于表结构中\n');
      throw new Error('表结构验证失败');
    }

    // 7. 清理测试数据
    console.log('📋 步骤7: 清理测试数据...');
    // 注意：由于字段已删除，需要使用原生SQL删除
    await sequelize.query('DELETE FROM influencers WHERE id = ?', {
      replacements: [testInfluencer.id]
    });
    console.log('✅ 测试数据清理完成\n');

    console.log('🎉 videoDetails字段删除功能测试全部通过！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  }
}

/**
 * 检查videoDetails字段是否存在
 */
async function checkColumnExists() {
  try {
    const [results] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'influencers' 
      AND COLUMN_NAME = 'video_details'
    `);
    
    return results.length > 0;
  } catch (error) {
    console.error('检查字段存在性失败:', error.message);
    return false;
  }
}

/**
 * 获取表结构
 */
async function getTableStructure() {
  try {
    const [results] = await sequelize.query('DESCRIBE influencers');
    return results;
  } catch (error) {
    console.error('获取表结构失败:', error.message);
    return [];
  }
}

/**
 * 恢复videoDetails字段（用于测试准备）
 */
async function restoreVideoDetailsColumn() {
  try {
    console.log('🔧 恢复videoDetails字段用于测试...');
    
    const columnExists = await checkColumnExists();
    if (!columnExists) {
      await sequelize.query(`
        ALTER TABLE influencers 
        ADD COLUMN video_details JSON NULL 
        COMMENT '达人视频详情数据'
      `);
      console.log('✅ videoDetails字段已恢复');
    } else {
      console.log('ℹ️ videoDetails字段已存在，无需恢复');
    }
  } catch (error) {
    console.error('恢复字段失败:', error.message);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--restore-column')) {
    // 恢复字段用于测试
    restoreVideoDetailsColumn()
      .then(() => {
        console.log('\n✅ 字段恢复完成');
        process.exit(0);
      })
      .catch((error) => {
        console.error('\n❌ 字段恢复失败:', error);
        process.exit(1);
      });
  } else {
    // 运行正常测试
    testColumnDrop()
      .then(() => {
        console.log('\n✅ 测试完成');
        process.exit(0);
      })
      .catch((error) => {
        console.error('\n❌ 测试异常:', error);
        process.exit(1);
      });
  }
}

module.exports = { testColumnDrop, restoreVideoDetailsColumn };
