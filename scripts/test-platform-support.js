/**
 * 平台支持功能测试脚本
 * 
 * 功能说明：
 * - 测试新增平台的基本功能
 * - 验证平台验证逻辑
 * - 检查智能提报限制
 * 
 * 使用方法：
 * node scripts/test-platform-support.js
 */

const { PLATFORM_CONSTANTS } = require('../src/config/constants');

console.log('🧪 开始测试平台支持功能...\n');

// 测试1: 验证平台常量配置
console.log('📋 测试1: 平台常量配置');
console.log('所有支持的平台:', PLATFORM_CONSTANTS.ALL_PLATFORMS);
console.log('支持爬虫的平台:', PLATFORM_CONSTANTS.CRAWLER_SUPPORTED);
console.log('平台名称映射:', PLATFORM_CONSTANTS.NAMES);
console.log('✅ 平台常量配置正常\n');

// 测试2: 验证平台验证逻辑
console.log('🔍 测试2: 平台验证逻辑');

function testPlatformValidation(platform) {
  const isValid = PLATFORM_CONSTANTS.ALL_PLATFORMS.includes(platform);
  const supportsCrawler = PLATFORM_CONSTANTS.CRAWLER_SUPPORTED.includes(platform);
  
  console.log(`平台: ${platform}`);
  console.log(`  - 有效性: ${isValid ? '✅' : '❌'}`);
  console.log(`  - 支持爬虫: ${supportsCrawler ? '✅' : '❌'}`);
  console.log(`  - 显示名称: ${PLATFORM_CONSTANTS.NAMES[platform] || '未定义'}`);
  
  return { isValid, supportsCrawler };
}

// 测试所有平台
const testPlatforms = [
  'xiaohongshu',
  'juxingtu', 
  '淘宝',
  '快手',
  'B站',
  '微信&视频号',
  '微博',
  '知乎',
  '其他',
  '置换',
  'invalid_platform' // 无效平台测试
];

testPlatforms.forEach(platform => {
  testPlatformValidation(platform);
  console.log('');
});

// 测试3: 模拟API验证逻辑
console.log('🚀 测试3: 模拟API验证逻辑');

function simulateCreateReportValidation(platform) {
  console.log(`模拟创建提报 - 平台: ${platform}`);
  
  // 基本平台验证
  if (!PLATFORM_CONSTANTS.ALL_PLATFORMS.includes(platform)) {
    console.log('  ❌ 平台类型无效');
    return false;
  }
  
  console.log('  ✅ 平台验证通过');
  return true;
}

function simulateSmartReportValidation(platform) {
  console.log(`模拟智能提报 - 平台: ${platform}`);
  
  // 基本平台验证
  if (!PLATFORM_CONSTANTS.ALL_PLATFORMS.includes(platform)) {
    console.log('  ❌ 平台类型无效');
    return false;
  }
  
  // 爬虫支持验证
  if (!PLATFORM_CONSTANTS.CRAWLER_SUPPORTED.includes(platform)) {
    console.log('  ❌ 该平台暂不支持智能提报功能，请使用传统提报方式');
    return false;
  }
  
  console.log('  ✅ 智能提报验证通过');
  return true;
}

// 测试新平台
const newPlatforms = ['淘宝', '快手', 'B站'];
newPlatforms.forEach(platform => {
  simulateCreateReportValidation(platform);
  simulateSmartReportValidation(platform);
  console.log('');
});

// 测试4: 前端工具函数测试
console.log('🎨 测试4: 前端工具函数模拟');

// 模拟前端工具函数
function getPlatformName(platform) {
  const platformMap = {
    xiaohongshu: '小红书',
    juxingtu: '巨量星图',
    淘宝: '淘宝',
    快手: '快手',
    B站: 'B站',
    '微信&视频号': '微信&视频号',
    微博: '微博',
    知乎: '知乎',
    其他: '其他',
    置换: '置换'
  };
  return platformMap[platform] || platform;
}

function getPlatformColor(platform) {
  const colorMap = {
    xiaohongshu: 'red',
    juxingtu: 'blue',
    淘宝: 'orange',
    快手: 'yellow',
    B站: 'cyan',
    '微信&视频号': 'green',
    微博: 'purple',
    知乎: 'blue',
    其他: 'gray',
    置换: 'magenta'
  };
  return colorMap[platform] || 'gray';
}

PLATFORM_CONSTANTS.ALL_PLATFORMS.forEach(platform => {
  console.log(`平台: ${platform}`);
  console.log(`  - 显示名称: ${getPlatformName(platform)}`);
  console.log(`  - 颜色: ${getPlatformColor(platform)}`);
});

console.log('\n🎉 平台支持功能测试完成！');
console.log('\n📊 测试总结:');
console.log(`- 总平台数: ${PLATFORM_CONSTANTS.ALL_PLATFORMS.length}`);
console.log(`- 支持爬虫平台数: ${PLATFORM_CONSTANTS.CRAWLER_SUPPORTED.length}`);
console.log(`- 新增平台数: ${PLATFORM_CONSTANTS.ALL_PLATFORMS.length - 2}`);
console.log('\n✅ 所有功能正常，可以开始使用新的平台支持功能！');
