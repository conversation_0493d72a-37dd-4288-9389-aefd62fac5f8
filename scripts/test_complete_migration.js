/**
 * 完整测试脚本：验证视频字段迁移的完整流程
 */

const { sequelize } = require('../src/config/database');
const { CrawlResult, Influencer, CrawlTask } = require('../src/models');

async function testCompleteMigration() {
  try {
    console.log('🚀 开始完整的视频字段迁移测试...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 1. 先创建一个测试任务
    console.log('\n📋 步骤1: 创建测试爬虫任务...');
    const testTask = await CrawlTask.create({
      taskName: '迁移测试任务',
      platform: 'juxingtu',
      keywords: '迁移测试',
      config: { maxPages: 1 },
      status: 'completed',
      createdBy: 1
    });
    console.log('✅ 测试任务创建成功，ID:', testTask.id);

    // 2. 测试爬虫结果创建（模拟爬虫爬取到的数据）
    console.log('\n📊 步骤2: 创建模拟爬虫结果...');
    const mockCrawlData = {
      taskId: testTask.id,
      platform: 'juxingtu',
      platformUserId: 'migration_test_' + Date.now(),
      nickname: '迁移测试达人',
      avatarUrl: 'https://example.com/test-avatar.jpg',
      followersCount: 88888,
      city: '上海',
      uniqueId: 'migration_test_unique',
      contactInfo: {
        wechat: 'migration_test_wechat',
        phone: '188****8888'
      },
      videoStats: {
        videoCount: 25,
        averagePlay: 35000,
        totalPlay: 875000,
        totalLike: 87500,
        totalComment: 8750,
        totalShare: 1750
      },
      videoDetails: [
        {
          videoId: 'migration_video_001',
          title: '迁移测试视频1 - 美食分享',
          playCount: 45000,
          likeCount: 4500,
          commentCount: 450,
          shareCount: 225,
          publishTime: '2025-01-12T16:30:00Z',
          duration: 180,
          videoUrl: 'https://example.com/video1.mp4',
          videoCover: 'https://example.com/cover1.jpg'
        },
        {
          videoId: 'migration_video_002',
          title: '迁移测试视频2 - 生活日常',
          playCount: 25000,
          likeCount: 2500,
          commentCount: 250,
          shareCount: 125,
          publishTime: '2025-01-11T10:15:00Z',
          duration: 120,
          videoUrl: 'https://example.com/video2.mp4',
          videoCover: 'https://example.com/cover2.jpg'
        }
      ],
      rawData: {
        source: 'migration_test',
        crawledAt: new Date().toISOString(),
        platform: 'juxingtu'
      },
      status: 'pending'
    };
    
    const crawlResult = await CrawlResult.create(mockCrawlData);
    console.log('✅ 爬虫结果创建成功，ID:', crawlResult.id);
    
    // 3. 验证爬虫结果中的视频数据
    console.log('\n🔍 步骤3: 验证爬虫结果中的视频数据...');
    console.log('videoStats:', JSON.stringify(crawlResult.videoStats, null, 2));
    console.log('videoDetails 数量:', crawlResult.videoDetails.length);
    
    // 4. 模拟导入过程
    console.log('\n📥 步骤4: 执行导入到达人表...');
    
    // 检查是否已存在相同的达人
    const existingInfluencer = await Influencer.findOne({
      where: {
        platform: crawlResult.platform,
        platformId: crawlResult.platformUserId
      }
    });
    
    if (existingInfluencer) {
      console.log('ℹ️ 删除已存在的达人记录...');
      await existingInfluencer.destroy();
    }
    
    // 创建达人记录（模拟导入逻辑）
    const influencerData = {
      platform: crawlResult.platform,
      platformId: crawlResult.platformUserId,
      nickname: crawlResult.nickname,
      avatarUrl: crawlResult.avatarUrl,
      followersCount: crawlResult.followersCount,
      contactInfo: crawlResult.contactInfo,
      videoStats: crawlResult.videoStats,
      videoDetails: crawlResult.videoDetails,
      status: 'active',
      notes: '迁移测试 - 从爬虫结果导入'
    };
    
    const influencer = await Influencer.create(influencerData);
    console.log('✅ 达人记录创建成功，ID:', influencer.id);
    
    // 5. 验证导入后的数据完整性
    console.log('\n✅ 步骤5: 验证导入后的数据完整性...');
    const savedInfluencer = await Influencer.findByPk(influencer.id);
    
    // 验证基本信息
    console.log('基本信息验证:');
    console.log('  - 昵称:', savedInfluencer.nickname);
    console.log('  - 平台:', savedInfluencer.platform);
    console.log('  - 粉丝数:', savedInfluencer.followersCount);
    
    // 验证视频统计数据
    if (savedInfluencer.videoStats) {
      console.log('✅ videoStats 迁移成功:');
      console.log('  - 视频总数:', savedInfluencer.videoStats.videoCount);
      console.log('  - 平均播放量:', savedInfluencer.videoStats.averagePlay);
      console.log('  - 总播放量:', savedInfluencer.videoStats.totalPlay);
      console.log('  - 总点赞数:', savedInfluencer.videoStats.totalLike);
      console.log('  - 总评论数:', savedInfluencer.videoStats.totalComment);
      console.log('  - 总分享数:', savedInfluencer.videoStats.totalShare);
    } else {
      console.log('❌ videoStats 迁移失败');
    }
    
    // 验证视频详情数据
    if (savedInfluencer.videoDetails && Array.isArray(savedInfluencer.videoDetails)) {
      console.log('✅ videoDetails 迁移成功:');
      console.log('  - 视频详情数量:', savedInfluencer.videoDetails.length);
      savedInfluencer.videoDetails.forEach((video, index) => {
        console.log(`  - 视频${index + 1}:`);
        console.log(`    标题: ${video.title}`);
        console.log(`    播放量: ${video.playCount}`);
        console.log(`    点赞数: ${video.likeCount}`);
        console.log(`    发布时间: ${video.publishTime}`);
      });
    } else {
      console.log('❌ videoDetails 迁移失败');
    }
    
    // 6. 更新爬虫结果状态
    console.log('\n📝 步骤6: 更新爬虫结果状态...');
    await crawlResult.update({
      status: 'imported',
      importedInfluencerId: influencer.id
    });
    console.log('✅ 爬虫结果状态更新为已导入');
    
    // 7. 数据对比验证
    console.log('\n🔄 步骤7: 数据对比验证...');
    const originalVideoCount = crawlResult.videoStats.videoCount;
    const importedVideoCount = savedInfluencer.videoStats.videoCount;
    const originalDetailsCount = crawlResult.videoDetails.length;
    const importedDetailsCount = savedInfluencer.videoDetails.length;
    
    console.log('数据对比结果:');
    console.log(`  - 视频统计数量: ${originalVideoCount} → ${importedVideoCount} ${originalVideoCount === importedVideoCount ? '✅' : '❌'}`);
    console.log(`  - 视频详情数量: ${originalDetailsCount} → ${importedDetailsCount} ${originalDetailsCount === importedDetailsCount ? '✅' : '❌'}`);
    
    console.log('\n🎉 完整迁移测试成功！所有视频字段已正确迁移到达人表！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行测试
if (require.main === module) {
  testCompleteMigration()
    .then(() => {
      console.log('\n✅ 完整迁移测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 完整迁移测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = testCompleteMigration;
