/**
 * 修复达人公海中状态为 'collected' 的记录
 * 将其改为 'imported' 状态
 */

const { sequelize } = require('../src/config/database');

async function fixCollectedStatus() {
  try {
    console.log('🔧 开始修复 collected 状态记录...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 查找状态为 'collected' 的记录
    const [collectedRecords] = await sequelize.query(`
      SELECT 
        id,
        nickname,
        platform,
        platform_user_id,
        status,
        imported_influencer_id,
        created_at
      FROM public_influencers 
      WHERE status = 'collected'
      ORDER BY created_at DESC
    `);
    
    console.log(`🔍 发现 ${collectedRecords.length} 条状态为 'collected' 的记录:`);
    
    if (collectedRecords.length === 0) {
      console.log('✅ 没有需要修复的记录');
      return;
    }
    
    // 显示这些记录
    collectedRecords.forEach((record, index) => {
      console.log(`   ${index + 1}. ID: ${record.id}, 昵称: ${record.nickname}, 平台: ${record.platform}, 导入ID: ${record.imported_influencer_id}`);
    });
    
    // 更新状态为 'imported'
    console.log('\n🔄 开始更新状态...');
    
    const [updateResult] = await sequelize.query(`
      UPDATE public_influencers 
      SET status = 'imported' 
      WHERE status = 'collected'
    `);
    
    console.log(`✅ 成功更新了 ${updateResult.affectedRows || collectedRecords.length} 条记录的状态`);
    
    // 验证更新结果
    const [verifyResult] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM public_influencers 
      WHERE status = 'collected'
    `);
    
    if (verifyResult[0].count === 0) {
      console.log('✅ 验证成功：已无 collected 状态的记录');
    } else {
      console.log(`⚠️ 仍有 ${verifyResult[0].count} 条 collected 状态的记录`);
    }
    
    // 显示当前状态统计
    const [statusStats] = await sequelize.query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM public_influencers 
      GROUP BY status
      ORDER BY status
    `);
    
    console.log('\n📊 当前状态统计:');
    statusStats.forEach(stat => {
      console.log(`   ${stat.status}: ${stat.count} 条记录`);
    });
    
    console.log('\n🎉 修复完成！');
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
    process.exit(0);
  }
}

// 执行修复
fixCollectedStatus();
