/**
 * 测试达人批量删除功能
 * 验证外键约束问题的解决方案
 */

const { sequelize, Influencer, CrawlResult, CrawlTask } = require('../src/models');
const { Op } = require('sequelize');

async function testBatchDeleteInfluencer() {
  try {
    console.log('🧪 开始测试达人批量删除功能...\n');

    // 1. 创建测试数据
    console.log('📋 步骤1: 创建测试数据...');
    
    const testTask = await CrawlTask.create({
      taskName: '批量删除测试任务',
      platform: 'xiaohongshu',
      keywords: '批量测试关键词',
      status: 'completed'
    });

    // 创建多个测试达人
    const testInfluencers = [];
    for (let i = 1; i <= 3; i++) {
      const influencer = await Influencer.create({
        nickname: `批量测试达人_${i}`,
        platform: 'xiaohongshu',
        platformId: `batch_test_${Date.now()}_${i}`,
        status: 'active'
      });
      testInfluencers.push(influencer);

      // 为每个达人创建爬虫结果
      await CrawlResult.create({
        taskId: testTask.id,
        platform: 'xiaohongshu',
        platformUserId: influencer.platformId,
        nickname: influencer.nickname,
        status: 'imported',
        importedInfluencerId: influencer.id
      });
    }

    console.log(`✅ 创建了 ${testInfluencers.length} 个测试达人和对应的爬虫结果`);
    const testIds = testInfluencers.map(inf => inf.id);
    console.log(`📊 测试达人IDs: ${testIds.join(', ')}`);

    // 2. 检查关联的爬虫结果
    console.log('\n📋 步骤2: 检查关联的爬虫结果...');
    const relatedResults = await CrawlResult.findAll({
      where: {
        importedInfluencerId: {
          [Op.in]: testIds
        }
      }
    });
    console.log(`📊 找到 ${relatedResults.length} 个关联的爬虫结果`);

    // 3. 模拟批量删除过程（使用修改后的逻辑）
    console.log('\n🗑️ 步骤3: 执行批量删除操作...');
    
    // 先清除关联关系
    if (relatedResults.length > 0) {
      console.log('🔄 清除爬虫结果的关联关系...');
      await CrawlResult.update(
        { importedInfluencerId: null, status: 'processed' },
        { 
          where: { 
            importedInfluencerId: {
              [Op.in]: testIds
            }
          } 
        }
      );
      console.log(`✅ 清除了 ${relatedResults.length} 个爬虫结果的关联关系`);
    }

    // 批量删除达人记录
    console.log('🗑️ 批量删除达人记录...');
    const deletedCount = await Influencer.destroy({
      where: {
        id: {
          [Op.in]: testIds
        }
      }
    });
    console.log(`✅ 成功删除 ${deletedCount} 个达人记录`);

    // 4. 验证删除结果
    console.log('\n🔍 步骤4: 验证删除结果...');
    
    // 检查达人是否已删除
    const remainingInfluencers = await Influencer.findAll({
      where: {
        id: {
          [Op.in]: testIds
        }
      }
    });
    
    if (remainingInfluencers.length === 0) {
      console.log('✅ 所有达人记录已成功删除');
    } else {
      console.log(`❌ 还有 ${remainingInfluencers.length} 个达人记录未删除`);
    }

    // 检查爬虫结果的关联关系是否已清除
    const updatedResults = await CrawlResult.findAll({
      where: {
        id: relatedResults.map(r => r.id)
      }
    });
    
    const hasNullReference = updatedResults.every(r => r.importedInfluencerId === null);
    if (hasNullReference) {
      console.log('✅ 所有爬虫结果的关联关系已成功清除');
    } else {
      console.log('❌ 部分爬虫结果的关联关系清除失败');
    }

    // 清理测试任务
    console.log('\n🧹 清理测试数据...');
    await CrawlResult.destroy({ where: { taskId: testTask.id } });
    await testTask.destroy();
    console.log('✅ 测试数据清理完成');

    console.log('\n🎉 达人批量删除功能测试完成，所有功能正常！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 运行测试
if (require.main === module) {
  testBatchDeleteInfluencer()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = testBatchDeleteInfluencer;
