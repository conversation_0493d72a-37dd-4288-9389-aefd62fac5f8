/**
 * 测试删除API接口
 * 验证外键约束问题的解决方案
 */

const axios = require('axios');
const { sequelize, Influencer, CrawlResult, CrawlTask } = require('../src/models');

const API_BASE = 'http://localhost:3001/api';

// 模拟登录获取token
async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      username: 'admin',
      password: 'admin123456'
    });
    return response.data.data.token;
  } catch (error) {
    console.error('登录失败:', error.response?.data || error.message);
    throw error;
  }
}

// 创建测试数据
async function createTestData() {
  const testTask = await CrawlTask.create({
    taskName: 'API删除测试任务',
    platform: 'xiaohongshu',
    keywords: 'API测试关键词',
    status: 'completed'
  });

  const testInfluencer = await Influencer.create({
    nickname: 'API测试达人',
    platform: 'xiaohongshu',
    platformId: 'api_test_' + Date.now(),
    status: 'active'
  });

  const testResult = await CrawlResult.create({
    taskId: testTask.id,
    platform: 'xiaohongshu',
    platformUserId: testInfluencer.platformId,
    nickname: testInfluencer.nickname,
    status: 'imported',
    importedInfluencerId: testInfluencer.id
  });

  return { testTask, testInfluencer, testResult };
}

async function testDeleteAPI() {
  try {
    console.log('🧪 开始测试删除API接口...\n');

    // 1. 登录获取token
    console.log('🔐 步骤1: 登录获取token...');
    const token = await login();
    console.log('✅ 登录成功，获取到token');

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 创建测试数据
    console.log('\n📋 步骤2: 创建测试数据...');
    const { testTask, testInfluencer, testResult } = await createTestData();
    console.log(`✅ 创建测试数据成功`);
    console.log(`  - 达人ID: ${testInfluencer.id}`);
    console.log(`  - 爬虫结果ID: ${testResult.id}`);

    // 3. 测试单个删除API
    console.log('\n🗑️ 步骤3: 测试单个删除API...');
    try {
      const deleteResponse = await axios.delete(
        `${API_BASE}/influencers/${testInfluencer.id}`,
        { headers }
      );
      
      if (deleteResponse.data.success) {
        console.log('✅ 单个删除API调用成功');
        console.log(`📝 响应消息: ${deleteResponse.data.message}`);
      } else {
        console.log('❌ 单个删除API调用失败');
        console.log(`📝 错误消息: ${deleteResponse.data.message}`);
      }
    } catch (error) {
      console.error('❌ 单个删除API调用异常:', error.response?.data || error.message);
    }

    // 4. 验证删除结果
    console.log('\n🔍 步骤4: 验证删除结果...');
    const deletedInfluencer = await Influencer.findByPk(testInfluencer.id);
    if (!deletedInfluencer) {
      console.log('✅ 达人记录已成功删除');
    } else {
      console.log('❌ 达人记录删除失败');
    }

    const updatedResult = await CrawlResult.findByPk(testResult.id);
    if (updatedResult && updatedResult.importedInfluencerId === null) {
      console.log('✅ 爬虫结果的关联关系已成功清除');
    } else {
      console.log('❌ 爬虫结果的关联关系清除失败');
    }

    // 5. 创建批量删除测试数据
    console.log('\n📋 步骤5: 创建批量删除测试数据...');
    const batchTestData = [];
    for (let i = 1; i <= 2; i++) {
      const influencer = await Influencer.create({
        nickname: `批量API测试达人_${i}`,
        platform: 'xiaohongshu',
        platformId: `batch_api_test_${Date.now()}_${i}`,
        status: 'active'
      });

      await CrawlResult.create({
        taskId: testTask.id,
        platform: 'xiaohongshu',
        platformUserId: influencer.platformId,
        nickname: influencer.nickname,
        status: 'imported',
        importedInfluencerId: influencer.id
      });

      batchTestData.push(influencer);
    }
    console.log(`✅ 创建了 ${batchTestData.length} 个批量测试达人`);

    // 6. 测试批量删除API
    console.log('\n🗑️ 步骤6: 测试批量删除API...');
    const batchIds = batchTestData.map(inf => inf.id);
    try {
      const batchDeleteResponse = await axios.post(
        `${API_BASE}/influencers/batch-delete`,
        { ids: batchIds },
        { headers }
      );
      
      if (batchDeleteResponse.data.success) {
        console.log('✅ 批量删除API调用成功');
        console.log(`📝 响应消息: ${batchDeleteResponse.data.message}`);
        console.log(`📊 删除数量: ${batchDeleteResponse.data.data.deletedCount}`);
      } else {
        console.log('❌ 批量删除API调用失败');
        console.log(`📝 错误消息: ${batchDeleteResponse.data.message}`);
      }
    } catch (error) {
      console.error('❌ 批量删除API调用异常:', error.response?.data || error.message);
    }

    // 7. 验证批量删除结果
    console.log('\n🔍 步骤7: 验证批量删除结果...');
    const remainingInfluencers = await Influencer.findAll({
      where: {
        id: batchIds
      }
    });
    
    if (remainingInfluencers.length === 0) {
      console.log('✅ 所有批量删除的达人记录已成功删除');
    } else {
      console.log(`❌ 还有 ${remainingInfluencers.length} 个达人记录未删除`);
    }

    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    await CrawlResult.destroy({ where: { taskId: testTask.id } });
    await testTask.destroy();
    console.log('✅ 测试数据清理完成');

    console.log('\n🎉 删除API接口测试完成，所有功能正常！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 运行测试
if (require.main === module) {
  testDeleteAPI()
    .then(() => {
      console.log('✅ API测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ API测试失败:', error);
      process.exit(1);
    });
}

module.exports = testDeleteAPI;
