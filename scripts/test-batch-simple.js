/**
 * 简化的批量字典获取测试
 * 
 * 功能说明：
 * - 测试批量字典获取控制器方法
 * - 验证数据格式和响应结构
 * 
 * 使用方法：
 * node scripts/test-batch-simple.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize } = require('../src/models');
const DictionaryController = require('../src/controllers/DictionaryController');

class SimpleBatchTester {
  /**
   * 运行测试
   */
  async runTest() {
    console.log('🚀 开始测试批量字典获取控制器...\n');

    try {
      await this.testBatchController();
      console.log('\n✅ 测试完成');
    } catch (error) {
      console.error('❌ 测试失败:', error);
    } finally {
      await sequelize.close();
    }
  }

  /**
   * 测试批量控制器
   */
  async testBatchController() {
    console.log('🔧 测试批量字典获取控制器方法...');

    // 模拟请求上下文
    const mockCtx = {
      query: {
        categories: 'cooperation_form,cooperation_brand,rebate_status',
        activeOnly: 'true'
      },
      body: null,
      status: 200
    };

    try {
      // 调用控制器方法
      await DictionaryController.getBatchDictionaries(mockCtx);

      console.log('📋 响应状态:', mockCtx.status);
      console.log('📋 响应数据:', JSON.stringify(mockCtx.body, null, 2));

      // 验证响应结构
      if (mockCtx.body && mockCtx.body.success) {
        console.log('✅ 批量获取成功');
        
        const data = mockCtx.body.data;
        if (data && data.dictionaries) {
          console.log('✅ 数据结构正确');
          
          const categories = Object.keys(data.dictionaries);
          console.log('📊 获取的分类:', categories);
          
          categories.forEach(category => {
            const items = data.dictionaries[category];
            console.log(`  - ${category}: ${items.length} 项`);
            
            if (items.length > 0) {
              const firstItem = items[0];
              console.log(`    示例数据:`, {
                value: firstItem.value,
                label: firstItem.label,
                source: firstItem.source
              });
            }
          });
          
          if (data.summary) {
            console.log('📈 汇总信息:', data.summary);
          }
        } else {
          console.log('⚠️ 数据结构异常');
        }
      } else {
        console.log('❌ 批量获取失败:', mockCtx.body?.message || '未知错误');
      }

    } catch (error) {
      console.error('❌ 控制器调用失败:', error.message);
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new SimpleBatchTester();
  tester.runTest().catch(console.error);
}

module.exports = SimpleBatchTester;
