/**
 * 修复现有已收藏达人的playMid数据
 * 为playMid为null的记录从videoStats.clickMidNum中补充数据
 */

const { MyInfluencer, PublicInfluencer } = require('../src/models');
const { sequelize } = require('../src/config/database');

async function fixExistingPlayMidData() {
  try {
    console.log('🚀 开始修复现有已收藏达人的playMid数据...\n');

    // 查找playMid为null但videoStats.clickMidNum有值的我的达人记录
    const influencersNeedFix = await MyInfluencer.findAll({
      where: {
        playMid: null
      },
      attributes: ['id', 'nickname', 'platform', 'platformId', 'videoStats', 'playMid'],
      order: [['createdAt', 'DESC']]
    });

    console.log(`📊 找到 ${influencersNeedFix.length} 个需要检查的达人记录\n`);

    if (influencersNeedFix.length === 0) {
      console.log('✅ 所有达人记录都已有playMid数据或无需修复');
      return;
    }

    let fixedCount = 0;
    let skippedCount = 0;
    let fromVideoStatsCount = 0;
    let fromPublicInfluencerCount = 0;

    // 逐个检查和修复
    for (let i = 0; i < influencersNeedFix.length; i++) {
      const influencer = influencersNeedFix[i];
      
      try {
        console.log(`🔄 [${i + 1}/${influencersNeedFix.length}] 检查达人: ${influencer.nickname} (${influencer.platformId})`);

        let playMidValue = null;
        let source = '';

        // 方法1：从videoStats.clickMidNum获取
        if (influencer.videoStats?.clickMidNum) {
          playMidValue = influencer.videoStats.clickMidNum.toString();
          source = 'videoStats.clickMidNum';
          fromVideoStatsCount++;
        }

        // 方法2：如果方法1没有数据，尝试从对应的公海记录获取
        if (!playMidValue) {
          const publicInfluencer = await PublicInfluencer.findOne({
            where: {
              platform: influencer.platform,
              platformUserId: influencer.platformId,
              playMid: { [require('sequelize').Op.ne]: null }
            },
            attributes: ['playMid', 'videoStats'],
            order: [['updatedAt', 'DESC']]
          });

          if (publicInfluencer?.playMid) {
            playMidValue = publicInfluencer.playMid;
            source = 'public_influencers.playMid';
            fromPublicInfluencerCount++;
          } else if (publicInfluencer?.videoStats?.clickMidNum) {
            playMidValue = publicInfluencer.videoStats.clickMidNum.toString();
            source = 'public_influencers.videoStats.clickMidNum';
            fromVideoStatsCount++;
          }
        }

        if (playMidValue) {
          // 更新数据库
          await MyInfluencer.update(
            { playMid: playMidValue },
            { where: { id: influencer.id } }
          );
          
          console.log(`✅ 修复成功: ${influencer.nickname} -> ${playMidValue} (来源: ${source})`);
          fixedCount++;
        } else {
          console.log(`⚠️ 跳过: ${influencer.nickname} (无可用的播放量中位数数据)`);
          skippedCount++;
        }

      } catch (error) {
        console.error(`❌ 修复失败: ${influencer.nickname}`, error.message);
        skippedCount++;
      }
    }

    console.log('\n🎉 修复完成！');
    console.log(`📊 统计结果:`);
    console.log(`   总检查数量: ${influencersNeedFix.length}`);
    console.log(`   修复成功: ${fixedCount}`);
    console.log(`   跳过数量: ${skippedCount}`);
    console.log(`   从videoStats获取: ${fromVideoStatsCount}`);
    console.log(`   从公海记录获取: ${fromPublicInfluencerCount}`);

  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 运行修复脚本
if (require.main === module) {
  fixExistingPlayMidData();
}

module.exports = { fixExistingPlayMidData };
