/**
 * 示例脚本：插入达人视频作品测试数据
 * 用于测试 author_videos 表的功能和关联关系
 */

const { sequelize, AuthorVide<PERSON>, MyInfluencer, CrawlTask } = require('../src/models');

// 示例视频数据（基于提供的JSON格式）
const sampleVideos = [
  {
    title: "人一定要200%地相信自己。",
    videoId: "7526909774353845561",
    duration: 59,
    videoUrl: "https://www.iesdouyin.com/share/video/7526909774353845561/",
    likeCount: 9671,
    playCount: 149002,
    shareCount: 792,
    videoCover: "tos-cn-i-0813c000-ce/oUDupSfSAF9PoFAAfr7EAReWEdQE6Imw93DAM6",
    publishTime: "2025-07-14",
    commentCount: 146,
    platform: "juxingtu",
    platformUserId: "test_user_001",
    description: "励志短视频，鼓励大家要相信自己",
    tags: ["励志", "正能量", "自信"],
    location: "北京",
    status: "active"
  },
  {
    title: "今日穿搭分享 | 夏日清新风",
    videoId: "7526909774353845562",
    duration: 45,
    videoUrl: "https://www.iesdouyin.com/share/video/7526909774353845562/",
    likeCount: 5432,
    playCount: 89765,
    shareCount: 234,
    videoCover: "tos-cn-i-0813c000-ce/example2",
    publishTime: "2025-07-13",
    commentCount: 89,
    platform: "juxingtu",
    platformUserId: "test_user_002",
    description: "夏日穿搭推荐，清新自然风格",
    tags: ["穿搭", "时尚", "夏日"],
    location: "上海",
    status: "active"
  },
  {
    title: "美食制作教程 | 简单易学的家常菜",
    videoId: "7526909774353845563",
    duration: 120,
    videoUrl: "https://www.iesdouyin.com/share/video/7526909774353845563/",
    likeCount: 12345,
    playCount: 234567,
    shareCount: 1234,
    videoCover: "tos-cn-i-0813c000-ce/example3",
    publishTime: "2025-07-12",
    commentCount: 567,
    platform: "juxingtu",
    platformUserId: "test_user_003",
    description: "教你制作简单美味的家常菜",
    tags: ["美食", "教程", "家常菜"],
    location: "广州",
    status: "active"
  }
];

async function insertSampleAuthorVideos() {
  try {
    console.log('🔄 开始插入达人视频作品测试数据...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 查找或创建测试达人
    console.log('\n📋 准备测试达人数据...');
    
    const testInfluencers = [];
    for (let i = 1; i <= 3; i++) {
      const [influencer] = await MyInfluencer.findOrCreate({
        where: { 
          platform: 'juxingtu',
          platformId: `test_user_00${i}`
        },
        defaults: {
          nickname: `测试达人${i}`,
          platform: 'juxingtu',
          platformId: `test_user_00${i}`,
          avatarUrl: `https://example.com/avatar${i}.jpg`,
          followersCount: 10000 + i * 5000,
          category: i === 1 ? '励志' : i === 2 ? '时尚' : '美食',
          status: 'active',
          createdBy: 1
        }
      });
      testInfluencers.push(influencer);
      console.log(`- 达人${i}: ${influencer.nickname} (ID: ${influencer.id})`);
    }
    
    // 查找或创建测试爬虫任务
    console.log('\n📋 准备测试爬虫任务...');
    const [crawlTask] = await CrawlTask.findOrCreate({
      where: { taskName: '达人视频作品测试任务' },
      defaults: {
        taskName: '达人视频作品测试任务',
        platform: 'juxingtu',
        keywords: '测试关键词',
        status: 'completed',
        createdBy: 1
      }
    });
    console.log(`- 爬虫任务: ${crawlTask.taskName} (ID: ${crawlTask.id})`);
    
    // 插入视频数据
    console.log('\n📹 插入视频作品数据...');
    
    for (let i = 0; i < sampleVideos.length; i++) {
      const videoData = sampleVideos[i];
      const influencer = testInfluencers[i];
      
      const [video, created] = await AuthorVideo.findOrCreate({
        where: {
          platform: videoData.platform,
          platformUserId: videoData.platformUserId,
          videoId: videoData.videoId
        },
        defaults: {
          ...videoData,
          authorId: influencer.id,
          authorNickname: influencer.nickname,
          crawlTaskId: crawlTask.id,
          videoStats: {
            engagement_rate: ((videoData.likeCount + videoData.commentCount + videoData.shareCount) / videoData.playCount * 100).toFixed(2),
            avg_play_duration: Math.floor(videoData.duration * 0.7)
          },
          rawData: {
            original_data: videoData,
            crawl_timestamp: new Date().toISOString()
          },
          lastUpdated: new Date()
        }
      });
      
      if (created) {
        console.log(`✅ 创建视频: ${video.title} (ID: ${video.id})`);
      } else {
        console.log(`⏭️ 视频已存在: ${video.title} (ID: ${video.id})`);
      }
    }
    
    // 验证插入结果
    console.log('\n📊 验证插入结果:');
    const totalVideos = await AuthorVideo.count();
    console.log(`- 总视频数量: ${totalVideos}`);
    
    // 测试关联查询
    console.log('\n🔗 测试关联查询:');
    const videosWithAuthor = await AuthorVideo.findAll({
      include: [
        {
          model: MyInfluencer,
          as: 'author',
          attributes: ['id', 'nickname', 'platform']
        },
        {
          model: CrawlTask,
          as: 'crawlTask',
          attributes: ['id', 'taskName']
        }
      ],
      limit: 3
    });
    
    videosWithAuthor.forEach(video => {
      console.log(`- 视频: ${video.title}`);
      console.log(`  达人: ${video.author?.nickname || '未知'}`);
      console.log(`  任务: ${video.crawlTask?.taskName || '未关联'}`);
      console.log(`  播放量: ${video.playCount.toLocaleString()}`);
    });
    
    console.log('\n🎉 达人视频作品测试数据插入完成！');
    
  } catch (error) {
    console.error('❌ 插入失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await sequelize.close();
  }
}

// 执行插入
if (require.main === module) {
  insertSampleAuthorVideos();
}

module.exports = insertSampleAuthorVideos;
