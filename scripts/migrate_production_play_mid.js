/**
 * 生产环境播放量中位数字段迁移脚本
 * 用于在生产环境数据库中安全地添加 play_mid 字段
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 生产环境数据库配置
const productionConfig = {
  host: '*************',
  port: 3306,
  user: 'daren_db',
  password: 'daren_db_2025!',
  database: 'daren_db',
  charset: 'utf8mb4'
};

async function migrateProductionPlayMid() {
  let connection;
  
  try {
    console.log('🚀 开始生产环境播放量中位数字段迁移...\n');

    // 连接生产环境数据库
    console.log('📡 连接生产环境数据库...');
    connection = await mysql.createConnection(productionConfig);
    console.log('✅ 生产环境数据库连接成功');

    // 验证数据库连接
    const [dbInfo] = await connection.execute('SELECT DATABASE() as current_db');
    const [timeInfo] = await connection.execute('SELECT NOW() as current_datetime');
    console.log(`📋 当前数据库: ${dbInfo[0].current_db}`);
    console.log(`⏰ 当前时间: ${timeInfo[0].current_datetime}\n`);

    // 检查表是否存在
    console.log('🔍 检查表结构...');
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME, TABLE_ROWS, CREATE_TIME
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'daren_db' 
        AND TABLE_NAME IN ('my_influencers', 'public_influencers')
    `);
    
    console.log('📊 现有表信息:');
    tables.forEach(table => {
      console.log(`   ${table.TABLE_NAME}: ${table.TABLE_ROWS} 条记录`);
    });

    // 检查字段是否已存在
    const [existingFields] = await connection.execute(`
      SELECT TABLE_NAME, COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'daren_db' 
        AND TABLE_NAME IN ('my_influencers', 'public_influencers')
        AND COLUMN_NAME = 'play_mid'
    `);

    if (existingFields.length > 0) {
      console.log('\n⚠️  play_mid 字段已存在:');
      existingFields.forEach(field => {
        console.log(`   ${field.TABLE_NAME}.${field.COLUMN_NAME}`);
      });
      console.log('跳过字段添加步骤。');
    } else {
      console.log('\n📝 添加 play_mid 字段...');
      
      // 为 my_influencers 表添加字段
      try {
        await connection.execute(`
          ALTER TABLE my_influencers 
          ADD COLUMN play_mid VARCHAR(50) COMMENT '播放量中位数' 
          AFTER influencer_tags
        `);
        console.log('✅ my_influencers 表添加 play_mid 字段成功');
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log('ℹ️  my_influencers.play_mid 字段已存在');
        } else {
          throw error;
        }
      }

      // 为 public_influencers 表添加字段
      try {
        await connection.execute(`
          ALTER TABLE public_influencers 
          ADD COLUMN play_mid VARCHAR(50) COMMENT '播放量中位数' 
          AFTER influencer_tags
        `);
        console.log('✅ public_influencers 表添加 play_mid 字段成功');
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log('ℹ️  public_influencers.play_mid 字段已存在');
        } else {
          throw error;
        }
      }
    }

    // 验证字段添加结果
    console.log('\n🔍 验证字段添加结果...');
    const [newFields] = await connection.execute(`
      SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'daren_db' 
        AND TABLE_NAME IN ('my_influencers', 'public_influencers')
        AND COLUMN_NAME = 'play_mid'
    `);

    console.log('📋 play_mid 字段信息:');
    newFields.forEach(field => {
      console.log(`   ${field.TABLE_NAME}.${field.COLUMN_NAME}: ${field.DATA_TYPE}, NULL=${field.IS_NULLABLE}`);
    });

    // 统计现有数据
    console.log('\n📊 数据统计:');
    const [myInfluencerStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(play_mid) as records_with_play_mid,
        (COUNT(*) - COUNT(play_mid)) as records_without_play_mid
      FROM my_influencers
    `);

    const [publicInfluencerStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(play_mid) as records_with_play_mid,
        (COUNT(*) - COUNT(play_mid)) as records_without_play_mid
      FROM public_influencers
    `);

    console.log(`   my_influencers: ${myInfluencerStats[0].total_records} 总记录, ${myInfluencerStats[0].records_with_play_mid} 有播放量中位数`);
    console.log(`   public_influencers: ${publicInfluencerStats[0].total_records} 总记录, ${publicInfluencerStats[0].records_with_play_mid} 有播放量中位数`);

    console.log('\n🎉 生产环境播放量中位数字段迁移完成！');

  } catch (error) {
    console.error('\n❌ 迁移失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔒 数据库连接已关闭');
    }
  }
}

// 运行迁移
if (require.main === module) {
  migrateProductionPlayMid();
}

module.exports = migrateProductionPlayMid;
