/**
 * 测试爬虫任务重试功能
 * 验证重试功能是否正常工作，不再出现 "ctx is not defined" 错误
 */

const { CrawlTask, sequelize } = require('../src/models');
const CrawlerService = require('../src/services/crawler');

async function testRetryFunction() {
  try {
    console.log('🧪 开始测试爬虫任务重试功能...\n');

    // 1. 初始化爬虫服务
    console.log('📋 步骤1: 初始化爬虫服务...');
    await CrawlerService.initialize();
    console.log('✅ 爬虫服务初始化成功\n');

    // 2. 创建一个测试任务
    console.log('📋 步骤2: 创建测试任务...');
    const testTask = await CrawlerService.createTask({
      taskName: '重试功能测试任务',
      platform: 'juxingtu',
      keywords: '测试关键词',
      maxPages: 1,
      config: {
        pageSize: 2,
        delay: { min: 100, max: 200 }
      },
      createdBy: 1
    });

    console.log(`✅ 测试任务创建成功: ${testTask.id} - ${testTask.taskName}\n`);

    // 3. 手动将任务状态设置为失败，模拟失败场景
    console.log('📋 步骤3: 模拟任务失败状态...');
    await testTask.update({
      status: 'failed',
      errorMessage: '模拟的失败错误',
      completedAt: new Date(),
      retryCount: 0
    });
    console.log('✅ 任务状态已设置为失败\n');

    // 4. 测试重试功能
    console.log('📋 步骤4: 测试重试功能...');
    try {
      const retriedTask = await CrawlerService.retryTask(testTask.id);
      console.log('✅ 重试功能测试成功！');
      console.log(`   - 任务ID: ${retriedTask.id}`);
      console.log(`   - 新状态: ${retriedTask.status}`);
      console.log(`   - 重试次数: ${retriedTask.retryCount}`);
      console.log(`   - 错误信息已清除: ${retriedTask.errorMessage === null ? '是' : '否'}\n`);
    } catch (retryError) {
      console.error('❌ 重试功能测试失败:', retryError.message);
      console.error('错误详情:', retryError);
      throw retryError;
    }

    // 5. 测试重试次数限制
    console.log('📋 步骤5: 测试重试次数限制...');
    
    // 将重试次数设置为最大值
    await testTask.reload();
    await testTask.update({
      status: 'failed',
      retryCount: 3, // 达到最大重试次数
      maxRetries: 3
    });

    try {
      await CrawlerService.retryTask(testTask.id);
      console.log('❌ 重试次数限制测试失败：应该抛出错误但没有');
    } catch (limitError) {
      if (limitError.message.includes('最大重试次数')) {
        console.log('✅ 重试次数限制测试成功！');
        console.log(`   - 错误信息: ${limitError.message}\n`);
      } else {
        console.log('❌ 重试次数限制测试失败：错误信息不正确');
        console.log(`   - 实际错误: ${limitError.message}\n`);
      }
    }

    // 6. 测试批量重试功能
    console.log('📋 步骤6: 测试批量重试功能...');
    
    // 创建另一个测试任务
    const testTask2 = await CrawlerService.createTask({
      taskName: '批量重试测试任务2',
      platform: 'juxingtu',
      keywords: '测试关键词2',
      maxPages: 1,
      config: { pageSize: 2 },
      createdBy: 1
    });

    // 设置为失败状态
    await testTask2.update({
      status: 'failed',
      errorMessage: '模拟的失败错误2',
      retryCount: 0
    });

    // 重置第一个任务的重试次数
    await testTask.update({
      retryCount: 0,
      status: 'failed'
    });

    try {
      const batchResult = await CrawlerService.batchRetryTasks([testTask.id, testTask2.id]);
      console.log('✅ 批量重试功能测试成功！');
      console.log(`   - 成功重试: ${batchResult.successCount} 个任务`);
      console.log(`   - 失败重试: ${batchResult.errors.length} 个任务\n`);
    } catch (batchError) {
      console.error('❌ 批量重试功能测试失败:', batchError.message);
      throw batchError;
    }

    // 7. 清理测试数据
    console.log('📋 步骤7: 清理测试数据...');
    await testTask.destroy();
    await testTask2.destroy();
    console.log('✅ 测试数据清理完成');

    console.log('\n🎉 所有重试功能测试通过！');
    console.log('✅ "ctx is not defined" 错误已修复');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
  } finally {
    // 确保数据库连接关闭
    await sequelize.close();
    console.log('📝 数据库连接已关闭');
  }
}

// 运行测试
if (require.main === module) {
  testRetryFunction().catch(console.error);
}

module.exports = testRetryFunction;
