/**
 * 创建合作对接管理表脚本
 * 
 * 功能说明：
 * - 连接数据库并创建合作对接管理表
 * - 执行SQL脚本创建表结构和索引
 * - 插入示例数据用于测试
 * 
 * 使用方法：
 * node scripts/create_cooperation_table.js
 */

const fs = require('fs');
const path = require('path');
const { sequelize } = require('../src/models');

async function createCooperationTable() {
  try {
    console.log('🔄 开始创建合作对接管理表...');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, '../sql/cooperation_management.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // 分割SQL语句（按分号分割，但要处理多行语句）
    const sqlStatements = [];
    const lines = sqlContent.split('\n');
    let currentStatement = '';

    for (const line of lines) {
      const trimmedLine = line.trim();

      // 跳过注释行和空行
      if (trimmedLine.startsWith('--') || trimmedLine === '') {
        continue;
      }

      currentStatement += ' ' + trimmedLine;

      // 如果行以分号结尾，表示语句结束
      if (trimmedLine.endsWith(';')) {
        const statement = currentStatement.trim().slice(0, -1); // 移除最后的分号
        if (statement.length > 0) {
          sqlStatements.push(statement);
        }
        currentStatement = '';
      }
    }

    // 处理最后一个语句（如果没有分号结尾）
    if (currentStatement.trim().length > 0) {
      sqlStatements.push(currentStatement.trim());
    }

    console.log(`📋 找到 ${sqlStatements.length} 条SQL语句`);

    // 执行每条SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      
      if (statement.toLowerCase().includes('create table')) {
        console.log('🔨 创建合作对接管理表...');
      } else if (statement.toLowerCase().includes('insert into')) {
        console.log('📝 插入示例数据...');
      } else if (statement.toLowerCase().includes('create index')) {
        console.log('🔍 创建索引...');
      } else if (statement.toLowerCase().includes('describe')) {
        console.log('📊 显示表结构...');
      }

      try {
        const result = await sequelize.query(statement);
        
        // 如果是DESCRIBE语句，显示结果
        if (statement.toLowerCase().includes('describe')) {
          console.log('📋 合作对接管理表结构:');
          console.table(result[0]);
        }
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('⚠️ 表已存在，跳过创建');
        } else {
          console.error(`❌ 执行SQL语句失败: ${statement.substring(0, 50)}...`);
          console.error('错误信息:', error.message);
        }
      }
    }

    console.log('✅ 合作对接管理表创建完成！');

    // 验证表是否创建成功
    const [results] = await sequelize.query("SHOW TABLES LIKE 'cooperation_management'");
    if (results.length > 0) {
      console.log('✅ 表创建验证成功');
      
      // 查询表中的数据
      const [rows] = await sequelize.query('SELECT COUNT(*) as count FROM cooperation_management');
      console.log(`📊 表中当前有 ${rows[0].count} 条记录`);
    } else {
      console.log('❌ 表创建验证失败');
    }

  } catch (error) {
    console.error('❌ 创建合作对接管理表失败:', error.message);
    throw error;
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行脚本
if (require.main === module) {
  createCooperationTable()
    .then(() => {
      console.log('🎉 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = createCooperationTable;
