/**
 * 综合播放量中位数测试
 * 测试不同类型达人的播放量中位数获取效果
 */

const { sequelize, PublicInfluencer } = require('../src/models');
const XingtuCrawler = require('../src/services/crawler/crawlers/XingtuCrawler');

async function comprehensivePlayMidTest() {
  try {
    console.log('🧪 开始综合播放量中位数测试...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 准备测试用例
    console.log('\n📋 步骤1: 准备测试用例');
    
    const testCases = [
      {
        name: '已知有数据的达人',
        authorId: '6870164318311153677', // 然然
        expected: 'success'
      },
      {
        name: '可能无数据的达人1',
        authorId: '7089360510797217832', // 慧姐家
        expected: 'no_data'
      },
      {
        name: '可能无数据的达人2',
        authorId: '6862342145185939470', // 大珍珠
        expected: 'no_data'
      }
    ];

    console.log(`准备了 ${testCases.length} 个测试用例:`);
    testCases.forEach((testCase, index) => {
      console.log(`   ${index + 1}. ${testCase.name} (${testCase.authorId})`);
    });

    // 2. 初始化爬虫
    console.log('\n📋 步骤2: 初始化巨量星图爬虫');
    
    const crawler = new XingtuCrawler();
    await crawler.initialize();
    console.log('✅ 爬虫初始化成功');

    // 3. 执行测试
    console.log('\n📋 步骤3: 执行综合测试');
    
    const results = [];

    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`\n🔍 [${i + 1}/${testCases.length}] 测试: ${testCase.name}`);
      console.log(`   达人ID: ${testCase.authorId}`);
      console.log(`   预期结果: ${testCase.expected}`);
      
      try {
        const startTime = Date.now();
        const playMid = await crawler.getAuthorMedianPlay(testCase.authorId);
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        const result = {
          name: testCase.name,
          authorId: testCase.authorId,
          expected: testCase.expected,
          playMid: playMid,
          status: playMid !== null ? 'success' : 'no_data',
          duration: duration,
          timestamp: new Date().toISOString()
        };
        
        results.push(result);
        
        if (playMid !== null) {
          console.log(`   ✅ 成功: ${playMid} (耗时: ${duration}ms)`);
        } else {
          console.log(`   ⚠️ 无数据 (耗时: ${duration}ms)`);
        }
        
        // 验证预期结果
        const isExpected = result.status === testCase.expected;
        console.log(`   预期匹配: ${isExpected ? '✅' : '❌'}`);
        
      } catch (error) {
        console.log(`   ❌ 错误: ${error.message}`);
        results.push({
          name: testCase.name,
          authorId: testCase.authorId,
          expected: testCase.expected,
          playMid: null,
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
      
      // 添加延迟
      if (i < testCases.length - 1) {
        console.log(`   ⏳ 等待 3秒...`);
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // 4. 分析结果
    console.log('\n📊 步骤4: 分析测试结果');
    
    const successCount = results.filter(r => r.status === 'success').length;
    const noDataCount = results.filter(r => r.status === 'no_data').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    const expectedCount = results.filter(r => r.status === r.expected).length;
    
    console.log(`\n📋 测试结果统计:`);
    console.log(`   总测试数: ${results.length}`);
    console.log(`   成功获取: ${successCount}`);
    console.log(`   无数据: ${noDataCount}`);
    console.log(`   错误: ${errorCount}`);
    console.log(`   符合预期: ${expectedCount}/${results.length}`);
    console.log(`   准确率: ${((expectedCount / results.length) * 100).toFixed(2)}%`);

    // 5. 详细结果
    console.log('\n📋 详细测试结果:');
    results.forEach((result, index) => {
      console.log(`\n   ${index + 1}. ${result.name}:`);
      console.log(`      达人ID: ${result.authorId}`);
      console.log(`      播放量中位数: ${result.playMid || 'null'}`);
      console.log(`      状态: ${result.status}`);
      console.log(`      预期: ${result.expected}`);
      console.log(`      匹配: ${result.status === result.expected ? '✅' : '❌'}`);
      if (result.duration) {
        console.log(`      耗时: ${result.duration}ms`);
      }
      if (result.error) {
        console.log(`      错误: ${result.error}`);
      }
    });

    // 6. 策略效果评估
    console.log('\n📋 步骤5: 降级策略效果评估');
    
    console.log('\n🎯 降级策略优势:');
    console.log('   ✅ 先尝试 range=3 获取更准确的长期数据');
    console.log('   ✅ 自动降级到 range=2 提高数据覆盖率');
    console.log('   ✅ 最大化播放量中位数获取成功率');
    console.log('   ✅ 对有数据的达人不增加额外请求');
    console.log('   ✅ 对无数据的达人提供更多尝试机会');

    if (successCount > 0) {
      console.log('\n🎉 降级策略测试成功！');
      console.log('💡 建议: 将此策略应用到生产环境中');
    } else {
      console.log('\n📝 当前测试批次中的达人可能都没有播放量中位数数据');
      console.log('💡 这是正常现象，降级策略在有数据的达人上会发挥作用');
    }

    console.log('\n✅ 综合播放量中位数测试完成');
    
    return {
      total: results.length,
      success: successCount,
      noData: noDataCount,
      error: errorCount,
      expected: expectedCount,
      accuracy: ((expectedCount / results.length) * 100).toFixed(2),
      results: results
    };

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  comprehensivePlayMidTest()
    .then(result => {
      console.log('\n📊 最终测试摘要:', {
        total: result.total,
        success: result.success,
        noData: result.noData,
        error: result.error,
        accuracy: result.accuracy + '%'
      });
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = comprehensivePlayMidTest;
