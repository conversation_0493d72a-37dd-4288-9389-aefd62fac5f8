/**
 * 测试达人删除功能
 * 验证外键约束问题的解决方案
 */

const { sequelize, MyInfluencer, PublicInfluencer, CrawlTask } = require('../src/models');

async function testDeleteInfluencer() {
  try {
    console.log('🧪 开始测试达人删除功能...\n');

    // 1. 查找有关联爬虫结果的达人
    console.log('📋 步骤1: 查找有关联爬虫结果的达人...');
    const influencersWithResults = await MyInfluencer.findAll({
      include: [{
        model: PublicInfluencer,
        as: 'publicInfluencer',
        required: true
      }],
      limit: 1
    });

    if (influencersWithResults.length === 0) {
      console.log('ℹ️ 没有找到有关联爬虫结果的达人，创建测试数据...');
      
      // 创建测试数据
      const testTask = await CrawlTask.create({
        taskName: '删除测试任务',
        platform: 'xiaohongshu',
        keywords: '测试关键词',
        status: 'completed'
      });

      const testInfluencer = await MyInfluencer.create({
        nickname: '测试达人_删除',
        platform: 'xiaohongshu',
        platformId: 'test_delete_' + Date.now(),
        status: 'active',
        createdBy: 1  // 添加必需的创建者ID
      });

      const testResult = await PublicInfluencer.create({
        taskId: testTask.id,
        platform: 'xiaohongshu',
        platformUserId: testInfluencer.platformId,
        nickname: testInfluencer.nickname,
        status: 'imported',
        importedInfluencerId: testInfluencer.id
      });

      console.log('✅ 测试数据创建成功');
      console.log(`  - 达人ID: ${testInfluencer.id}`);
      console.log(`  - 爬虫结果ID: ${testResult.id}`);
      
      var targetInfluencer = testInfluencer;
    } else {
      var targetInfluencer = influencersWithResults[0];
      console.log(`✅ 找到测试达人: ${targetInfluencer.id} - ${targetInfluencer.nickname}`);
    }

    // 2. 检查关联的爬虫结果
    console.log('\n📋 步骤2: 检查关联的爬虫结果...');
    const relatedResults = await PublicInfluencer.findAll({
      where: {
        importedInfluencerId: targetInfluencer.id
      }
    });
    console.log(`📊 找到 ${relatedResults.length} 个关联的爬虫结果`);

    // 3. 模拟删除过程（使用修改后的逻辑）
    console.log('\n🗑️ 步骤3: 执行删除操作...');

    // 先清除关联关系
    if (relatedResults.length > 0) {
      console.log('🔄 清除爬虫结果的关联关系...');
      await PublicInfluencer.update(
        { importedInfluencerId: null, status: 'processed' },
        {
          where: {
            importedInfluencerId: targetInfluencer.id
          }
        }
      );
      console.log(`✅ 清除了 ${relatedResults.length} 个爬虫结果的关联关系`);
    }

    // 删除达人记录
    console.log('🗑️ 删除达人记录...');
    await targetInfluencer.destroy();
    console.log('✅ 达人删除成功');

    // 4. 验证删除结果
    console.log('\n🔍 步骤4: 验证删除结果...');
    
    // 检查达人是否已删除
    const deletedInfluencer = await MyInfluencer.findByPk(targetInfluencer.id);
    if (!deletedInfluencer) {
      console.log('✅ 达人记录已成功删除');
    } else {
      console.log('❌ 达人记录删除失败');
    }

    // 检查爬虫结果的关联关系是否已清除
    const updatedResults = await PublicInfluencer.findAll({
      where: {
        id: relatedResults.map(r => r.id)
      }
    });
    
    const hasNullReference = updatedResults.every(r => r.importedInfluencerId === null);
    if (hasNullReference) {
      console.log('✅ 爬虫结果的关联关系已成功清除');
    } else {
      console.log('❌ 爬虫结果的关联关系清除失败');
    }

    console.log('\n🎉 达人删除功能测试完成，所有功能正常！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 运行测试
if (require.main === module) {
  testDeleteInfluencer()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = testDeleteInfluencer;
