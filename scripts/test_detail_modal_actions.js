/**
 * 详情弹窗操作按钮测试脚本
 * 测试详情弹窗中的各种操作按钮功能
 */

const axios = require('axios');

async function testDetailModalActions() {
  try {
    console.log('🚀 开始测试详情弹窗操作按钮功能...\n');

    // 1. 登录获取token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'testadmin',
      password: '123456'
    });

    if (!loginResponse.data.success) {
      throw new Error('登录失败');
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    // 2. 获取提报列表
    const listResponse = await axios.get('http://localhost:3001/api/influencer-reports', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (!listResponse.data.success || listResponse.data.data.length === 0) {
      throw new Error('没有找到提报记录');
    }

    console.log('✅ 获取到提报列表，共', listResponse.data.data.length, '条记录');

    // 3. 测试不同状态下的按钮显示逻辑
    for (const report of listResponse.data.data) {
      console.log(`\n📋 分析提报记录 ID: ${report.id}`);
      console.log(`  达人名称: ${report.influencerName}`);
      console.log(`  状态: ${report.status}`);
      console.log(`  提报人ID: ${report.submittedBy}`);
      
      // 模拟权限检查
      const isAdmin = true; // 当前用户是管理员
      const currentUserId = 3; // 当前用户ID
      const canEdit = isAdmin || report.submittedBy === currentUserId;
      const canDelete = isAdmin || report.submittedBy === currentUserId;
      
      console.log('  权限分析:');
      console.log(`    可编辑: ${canEdit ? '是' : '否'}`);
      console.log(`    可删除: ${canDelete ? '是' : '否'}`);
      
      // 分析应该显示的按钮
      const buttons = [];
      
      // 管理员专属按钮
      if (isAdmin) {
        if (report.status !== 'approved') buttons.push('审核通过');
        if (report.status !== 'rejected') buttons.push('审核拒绝');
        if (report.status !== 'need_confirmation') buttons.push('需二次确认');
      }
      
      // 通用操作按钮
      if (canEdit) buttons.push('编辑');
      if (report.status === 'rejected') buttons.push('重新提报');
      if (canDelete) buttons.push('删除');
      
      console.log(`  应显示按钮: ${buttons.join(', ')}`);
    }

    // 4. 测试具体的操作功能
    const testReport = listResponse.data.data[0];
    console.log(`\n🔧 测试具体操作功能 (ID: ${testReport.id})`);

    // 测试获取详情
    console.log('📖 测试获取详情...');
    const detailResponse = await axios.get(
      `http://localhost:3001/api/influencer-reports/${testReport.id}`,
      {
        headers: { 'Authorization': `Bearer ${token}` }
      }
    );

    if (detailResponse.data.success) {
      const detail = detailResponse.data.data;
      console.log('✅ 获取详情成功');
      console.log(`  状态: ${detail.status}`);
      console.log(`  提报人: ${detail.submitter?.username}`);
      console.log(`  审核人: ${detail.reviewer?.username || '无'}`);
      console.log(`  审核意见: ${detail.reviewComment || '无'}`);
    } else {
      console.error('❌ 获取详情失败:', detailResponse.data.message);
    }

    // 5. 测试操作按钮的API调用
    console.log('\n🎯 测试操作按钮对应的API调用...');

    // 测试审核操作（如果当前状态允许）
    if (testReport.status !== 'need_confirmation') {
      console.log('📝 测试标记为需二次确认...');
      try {
        const reviewResponse = await axios.put(
          `http://localhost:3001/api/influencer-reports/${testReport.id}/need-confirmation`,
          { reviewComment: '详情弹窗测试：标记为需二次确认' },
          {
            headers: { 'Authorization': `Bearer ${token}` }
          }
        );
        
        if (reviewResponse.data.success) {
          console.log('✅ 标记为需二次确认成功');
        } else {
          console.error('❌ 标记失败:', reviewResponse.data.message);
        }
      } catch (error) {
        console.error('❌ 标记操作失败:', error.response?.data?.message || error.message);
      }
    }

    console.log('\n🎉 详情弹窗操作按钮功能测试完成！');
    console.log('💡 前端界面现在应该显示完整的操作按钮组');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testDetailModalActions();
