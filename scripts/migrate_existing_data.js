/**
 * 存量数据迁移脚本
 * 从 authorExtInfo 字段中提取数据到 content_theme 和 influencer_tags 字段
 */

const { sequelize, PublicInfluencer, MyInfluencer } = require('../src/models');
const { Op } = require('sequelize');

class DataMigrator {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.batchSize = options.batchSize || 50;
    this.report = {
      startTime: new Date(),
      endTime: null,
      publicInfluencers: {
        total: 0,
        processed: 0,
        updated: 0,
        skipped: 0,
        errors: []
      },
      myInfluencers: {
        total: 0,
        processed: 0,
        updated: 0,
        skipped: 0,
        errors: []
      }
    };
  }

  async executeMigration() {
    try {
      console.log('🚀 开始存量数据迁移...');
      if (this.dryRun) {
        console.log('🔍 运行模式: 预演模式（不会实际修改数据）\n');
      }

      // 初始化数据库
      await sequelize.authenticate();
      console.log('✅ 数据库连接成功\n');

      // 分析数据
      await this.analyzeData();

      // 迁移达人公海数据
      await this.migratePublicInfluencers();

      // 迁移我的达人数据
      await this.migrateMyInfluencers();

      // 生成报告
      this.generateReport();

      console.log('\n🎉 数据迁移完成！');
      return this.report;

    } catch (error) {
      console.error('❌ 迁移失败:', error.message);
      throw error;
    } finally {
      this.report.endTime = new Date();
      await sequelize.close();
    }
  }

  async analyzeData() {
    console.log('📋 分析需要迁移的数据...');

    // 分析达人公海数据
    const publicTotal = await PublicInfluencer.count();
    const publicWithExtInfo = await PublicInfluencer.count({
      where: {
        author_ext_info: { [Op.ne]: null }
      }
    });

    // 分析我的达人数据
    const myTotal = await MyInfluencer.count();
    const myWithExtInfo = await MyInfluencer.count({
      where: {
        author_ext_info: { [Op.ne]: null }
      }
    });

    this.report.publicInfluencers.total = publicWithExtInfo;
    this.report.myInfluencers.total = myWithExtInfo;

    console.log(`📊 达人公海: 总数 ${publicTotal}, 有扩展信息 ${publicWithExtInfo}`);
    console.log(`📊 我的达人: 总数 ${myTotal}, 有扩展信息 ${myWithExtInfo}\n`);
  }

  async migratePublicInfluencers() {
    console.log('📋 迁移达人公海数据...');
    await this.migrateTableData(PublicInfluencer, 'publicInfluencers', '达人公海');
  }

  async migrateMyInfluencers() {
    console.log('\n📋 迁移我的达人数据...');
    await this.migrateTableData(MyInfluencer, 'myInfluencers', '我的达人');
  }

  async migrateTableData(Model, reportKey, tableName) {
    const totalCount = await Model.count({
      where: {
        author_ext_info: { [Op.ne]: null }
      }
    });

    if (totalCount === 0) {
      console.log(`ℹ️ ${tableName}表中没有需要迁移的数据`);
      return;
    }

    console.log(`🔄 开始迁移${tableName}数据，总计 ${totalCount} 条记录`);

    let offset = 0;
    let batchNumber = 1;

    while (offset < totalCount) {
      console.log(`\n📦 处理第 ${batchNumber} 批数据 (${offset + 1}-${Math.min(offset + this.batchSize, totalCount)})...`);

      const records = await Model.findAll({
        where: {
          author_ext_info: { [Op.ne]: null }
        },
        limit: this.batchSize,
        offset: offset,
        raw: false
      });

      for (const record of records) {
        try {
          const updated = await this.migrateRecord(record);
          this.report[reportKey].processed++;
          
          if (updated) {
            this.report[reportKey].updated++;
            console.log(`✅ 记录 ${record.id} 迁移成功`);
          } else {
            this.report[reportKey].skipped++;
            console.log(`⏭️ 记录 ${record.id} 跳过（无需迁移）`);
          }
        } catch (error) {
          this.report[reportKey].errors.push({
            id: record.id,
            error: error.message
          });
          console.error(`❌ 记录 ${record.id} 迁移失败:`, error.message);
        }
      }

      offset += this.batchSize;
      batchNumber++;
    }

    console.log(`✅ ${tableName}数据迁移完成`);
  }

  async migrateRecord(record) {
    const authorExtInfo = record.authorExtInfo || record.author_ext_info;

    if (!authorExtInfo || typeof authorExtInfo !== 'object') {
      return false;
    }

    const updateData = {};
    let hasUpdates = false;

    // 提取内容主题数据
    if (authorExtInfo.content_theme_labels_180d && !record.contentTheme) {
      updateData.contentTheme = authorExtInfo.content_theme_labels_180d;
      hasUpdates = true;
    }

    // 提取达人标签数据
    if (authorExtInfo.tags_relation && !record.influencerTags) {
      updateData.influencerTags = authorExtInfo.tags_relation;
      hasUpdates = true;
    }

    // 如果有数据需要更新且不是预演模式
    if (hasUpdates && !this.dryRun) {
      await record.update(updateData);
    }

    return hasUpdates;
  }

  generateReport() {
    console.log('\n📊 迁移报告:');
    console.log('=====================================');
    
    const duration = this.report.endTime - this.report.startTime;
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    
    console.log('\n📈 达人公海:');
    console.log(`   总数: ${this.report.publicInfluencers.total}`);
    console.log(`   已处理: ${this.report.publicInfluencers.processed}`);
    console.log(`   已更新: ${this.report.publicInfluencers.updated}`);
    console.log(`   已跳过: ${this.report.publicInfluencers.skipped}`);
    console.log(`   错误数: ${this.report.publicInfluencers.errors.length}`);
    
    console.log('\n📈 我的达人:');
    console.log(`   总数: ${this.report.myInfluencers.total}`);
    console.log(`   已处理: ${this.report.myInfluencers.processed}`);
    console.log(`   已更新: ${this.report.myInfluencers.updated}`);
    console.log(`   已跳过: ${this.report.myInfluencers.skipped}`);
    console.log(`   错误数: ${this.report.myInfluencers.errors.length}`);

    if (this.report.publicInfluencers.errors.length > 0 || this.report.myInfluencers.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      [...this.report.publicInfluencers.errors, ...this.report.myInfluencers.errors].forEach(error => {
        console.log(`   ID ${error.id}: ${error.error}`);
      });
    }
  }
}

// 解析命令行参数
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    batchSize: 50
  };

  args.forEach(arg => {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--batch-size=')) {
      options.batchSize = parseInt(arg.split('=')[1]) || 50;
    }
  });

  return options;
}

// 主执行函数
async function runMigration() {
  const options = parseArguments();
  const migrator = new DataMigrator(options);

  try {
    await migrator.executeMigration();
    process.exit(0);
  } catch (error) {
    console.error('\n❌ 迁移失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runMigration();
}

module.exports = DataMigrator;
