/**
 * 测试视频保存集成功能
 * 验证XingtuCrawler与AuthorVideoService的集成是否正常工作
 */

const { initializeAndSyncDatabase, Influencer, AuthorVideo, CrawlTask } = require('../src/models');
const AuthorVideoService = require('../src/services/AuthorVideoService');

async function testVideoSaveIntegration() {
  try {
    console.log('🚀 开始测试视频保存集成功能...\n');

    // 1. 初始化数据库
    console.log('📋 步骤1: 初始化数据库...');
    await initializeAndSyncDatabase();
    console.log('✅ 数据库初始化完成\n');

    // 2. 创建测试爬虫任务
    console.log('📋 步骤2: 创建测试爬虫任务...');
    const testTask = await CrawlTask.create({
      taskName: '视频保存集成测试',
      platform: 'juxingtu',
      keywords: '测试关键词',
      maxPages: 1,
      status: 'running',
      createdBy: 1
    });
    console.log(`✅ 测试任务创建成功，ID: ${testTask.id}\n`);

    // 3. 准备测试达人数据（包含视频数据）
    console.log('📋 步骤3: 准备测试达人数据...');
    const testAuthorData = {
      platform: 'juxingtu',
      platformUserId: `test_author_${Date.now()}`,
      nickname: '测试达人_视频保存',
      avatarUrl: 'https://example.com/avatar.jpg',
      followersCount: 50000,
      city: '北京',
      uniqueId: 'test_unique_id',
      contactInfo: {
        wechat: 'test_wechat',
        phone: '138****1234'
      },
      videoStats: {
        videoCount: 3,
        averagePlay: 15000,
        totalPlay: 45000,
        totalLike: 2500,
        totalComment: 300,
        totalShare: 150
      },
      videoDetails: [
        {
          videoId: `video_${Date.now()}_1`,
          title: '测试视频1 - 精彩内容',
          playCount: 20000,
          likeCount: 1200,
          commentCount: 150,
          shareCount: 80,
          publishTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          duration: 120,
          videoUrl: 'https://example.com/video1.mp4',
          videoCover: 'https://example.com/cover1.jpg'
        },
        {
          videoId: `video_${Date.now()}_2`,
          title: '测试视频2 - 热门话题',
          playCount: 15000,
          likeCount: 800,
          commentCount: 100,
          shareCount: 50,
          publishTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          duration: 90,
          videoUrl: 'https://example.com/video2.mp4',
          videoCover: 'https://example.com/cover2.jpg'
        },
        {
          videoId: `video_${Date.now()}_3`,
          title: '测试视频3 - 最新发布',
          playCount: 10000,
          likeCount: 500,
          commentCount: 50,
          shareCount: 20,
          publishTime: new Date().toISOString(),
          duration: 60,
          videoUrl: 'https://example.com/video3.mp4',
          videoCover: 'https://example.com/cover3.jpg'
        }
      ],
      authorExtInfo: {
        testField: 'test_value',
        category: '生活方式'
      },
      rawData: {
        source: 'integration_test',
        timestamp: new Date().toISOString()
      }
    };
    console.log(`✅ 测试数据准备完成，包含 ${testAuthorData.videoDetails.length} 个视频\n`);

    // 4. 测试视频保存功能
    console.log('📋 步骤4: 测试视频保存功能...');
    const saveResult = await AuthorVideoService.saveVideosFromCrawlTask(
      testAuthorData,
      testTask.id
    );

    console.log('✅ 视频保存结果:', {
      total: saveResult.total,
      success: saveResult.success,
      created: saveResult.created,
      updated: saveResult.updated,
      failed: saveResult.failed,
      authorCreated: saveResult.authorCreated,
      authorId: saveResult.authorId
    });

    if (saveResult.errors.length > 0) {
      console.log('⚠️ 保存过程中的错误:', saveResult.errors);
    }
    console.log('');

    // 5. 验证达人是否创建成功
    console.log('📋 步骤5: 验证达人创建...');
    const createdAuthor = await Influencer.findByPk(saveResult.authorId);
    if (createdAuthor) {
      console.log('✅ 达人创建验证成功:', {
        id: createdAuthor.id,
        nickname: createdAuthor.nickname,
        platform: createdAuthor.platform,
        platformId: createdAuthor.platformId,
        followersCount: createdAuthor.followersCount
      });
    } else {
      throw new Error('达人创建验证失败');
    }
    console.log('');

    // 6. 验证视频是否保存成功
    console.log('📋 步骤6: 验证视频保存...');
    const savedVideos = await AuthorVideo.findAll({
      where: { authorId: saveResult.authorId },
      order: [['publishTime', 'DESC']]
    });

    console.log(`✅ 视频保存验证成功，共找到 ${savedVideos.length} 个视频:`);
    savedVideos.forEach((video, index) => {
      console.log(`  ${index + 1}. ${video.title}`);
      console.log(`     播放: ${video.playCount}, 点赞: ${video.likeCount}, 评论: ${video.commentCount}`);
      console.log(`     发布时间: ${video.publishTime}`);
    });
    console.log('');

    // 7. 测试重复保存（更新场景）
    console.log('📋 步骤7: 测试重复保存（更新场景）...');
    // 修改视频数据
    testAuthorData.videoDetails[0].playCount = 25000; // 增加播放量
    testAuthorData.videoDetails[0].likeCount = 1500;  // 增加点赞数

    const updateResult = await AuthorVideoService.saveVideosFromCrawlTask(
      testAuthorData,
      testTask.id
    );

    console.log('✅ 更新保存结果:', {
      total: updateResult.total,
      success: updateResult.success,
      created: updateResult.created,
      updated: updateResult.updated,
      failed: updateResult.failed,
      authorCreated: updateResult.authorCreated
    });
    console.log('');

    // 8. 验证数据更新
    console.log('📋 步骤8: 验证数据更新...');
    const updatedVideo = await AuthorVideo.findOne({
      where: {
        authorId: saveResult.authorId,
        videoId: testAuthorData.videoDetails[0].videoId
      }
    });

    if (updatedVideo && updatedVideo.playCount === 25000) {
      console.log('✅ 视频数据更新验证成功:', {
        videoId: updatedVideo.videoId,
        title: updatedVideo.title,
        playCount: updatedVideo.playCount,
        likeCount: updatedVideo.likeCount
      });
    } else {
      throw new Error('视频数据更新验证失败');
    }
    console.log('');

    // 9. 获取视频统计信息
    console.log('📋 步骤9: 获取视频统计信息...');
    const videoStats = await AuthorVideoService.getVideoStats(saveResult.authorId);
    console.log('✅ 视频统计信息:', videoStats);
    console.log('');

    // 10. 清理测试数据
    console.log('📋 步骤10: 清理测试数据...');
    await AuthorVideo.destroy({ where: { authorId: saveResult.authorId } });
    await Influencer.destroy({ where: { id: saveResult.authorId } });
    await CrawlTask.destroy({ where: { id: testTask.id } });
    console.log('✅ 测试数据清理完成\n');

    console.log('🎉 视频保存集成测试全部通过！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testVideoSaveIntegration()
    .then(() => {
      console.log('\n✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试异常:', error);
      process.exit(1);
    });
}

module.exports = { testVideoSaveIntegration };
