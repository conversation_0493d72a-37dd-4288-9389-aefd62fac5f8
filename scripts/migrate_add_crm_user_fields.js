/**
 * 数据库迁移脚本：为用户表添加CRM用户相关字段
 * 
 * 功能说明：
 * 1. 为 users 表添加CRM用户绑定相关字段
 * 2. 提供安全的迁移和回滚机制
 * 3. 保持现有数据完整性
 * 
 * 新增字段：
 * - crm_user_id: CRM系统用户ID
 * - crm_user_pic_url: CRM用户头像URL
 * - crm_department: CRM部门代码
 * - crm_department_name: CRM部门名称
 * - crm_data_id: CRM数据ID
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize } = require('../src/config/database');

async function migrateAddCrmUserFields() {
  let transaction;
  
  try {
    console.log('🔄 开始执行用户表CRM字段迁移...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 开始事务
    transaction = await sequelize.transaction();
    console.log('🔄 开始数据库事务...');
    
    // 定义要添加的字段
    const fieldsToAdd = [
      {
        name: 'crm_user_id',
        definition: 'VARCHAR(100) NULL COMMENT "CRM系统用户ID"',
        after: 'chinese_name'
      },
      {
        name: 'crm_user_pic_url',
        definition: 'TEXT NULL COMMENT "CRM用户头像URL"',
        after: 'crm_user_id'
      },
      {
        name: 'crm_department',
        definition: 'VARCHAR(100) NULL COMMENT "CRM部门代码"',
        after: 'crm_user_pic_url'
      },
      {
        name: 'crm_department_name',
        definition: 'VARCHAR(200) NULL COMMENT "CRM部门名称"',
        after: 'crm_department'
      },
      {
        name: 'crm_data_id',
        definition: 'INT NULL COMMENT "CRM数据ID"',
        after: 'crm_department_name'
      }
    ];
    
    // 检查并添加字段
    for (const field of fieldsToAdd) {
      // 检查字段是否已存在
      const [results] = await sequelize.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'users' 
          AND COLUMN_NAME = '${field.name}'
      `, { transaction });
      
      if (results.length > 0) {
        console.log(`ℹ️ ${field.name} 字段已存在，跳过`);
        continue;
      }
      
      // 添加字段
      await sequelize.query(`
        ALTER TABLE users 
        ADD COLUMN ${field.name} ${field.definition} 
        AFTER ${field.after}
      `, { transaction });
      
      console.log(`✅ ${field.name} 字段添加成功`);
    }
    
    // 验证字段添加是否成功
    const [verifyResults] = await sequelize.query(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME IN ('crm_user_id', 'crm_user_pic_url', 'crm_department', 'crm_department_name', 'crm_data_id')
      ORDER BY ORDINAL_POSITION
    `, { transaction });
    
    console.log('✅ 字段验证成功:');
    verifyResults.forEach(field => {
      console.log(`  - ${field.COLUMN_NAME}: ${field.DATA_TYPE} (${field.COLUMN_COMMENT})`);
    });
    
    // 提交事务
    await transaction.commit();
    console.log('✅ 数据库迁移完成！');
    
    // 显示迁移结果
    console.log('\n📊 迁移结果统计:');
    console.log('- 添加字段: crm_user_id (VARCHAR(100), 可为空)');
    console.log('- 添加字段: crm_user_pic_url (TEXT, 可为空)');
    console.log('- 添加字段: crm_department (VARCHAR(100), 可为空)');
    console.log('- 添加字段: crm_department_name (VARCHAR(200), 可为空)');
    console.log('- 添加字段: crm_data_id (INT, 可为空)');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    
    if (transaction) {
      try {
        await transaction.rollback();
        console.log('🔄 事务已回滚');
      } catch (rollbackError) {
        console.error('❌ 事务回滚失败:', rollbackError.message);
      }
    }
    
    throw error;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 数据库连接已关闭');
    } catch (closeError) {
      console.error('❌ 关闭数据库连接失败:', closeError.message);
    }
  }
}

// 回滚函数
async function rollbackCrmUserFields() {
  let transaction;
  
  try {
    console.log('🔄 开始回滚CRM用户字段...');
    
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    transaction = await sequelize.transaction();
    
    const fieldsToRemove = ['crm_user_id', 'crm_user_pic_url', 'crm_department', 'crm_department_name', 'crm_data_id'];
    
    for (const fieldName of fieldsToRemove) {
      // 检查字段是否存在
      const [results] = await sequelize.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'users' 
          AND COLUMN_NAME = '${fieldName}'
      `, { transaction });
      
      if (results.length === 0) {
        console.log(`ℹ️ ${fieldName} 字段不存在，跳过`);
        continue;
      }
      
      // 删除字段
      await sequelize.query(`
        ALTER TABLE users 
        DROP COLUMN ${fieldName}
      `, { transaction });
      
      console.log(`✅ ${fieldName} 字段删除成功`);
    }
    
    await transaction.commit();
    console.log('✅ 回滚完成！');
    
  } catch (error) {
    console.error('❌ 回滚失败:', error.message);
    
    if (transaction) {
      try {
        await transaction.rollback();
        console.log('🔄 回滚事务已回滚');
      } catch (rollbackError) {
        console.error('❌ 回滚事务失败:', rollbackError.message);
      }
    }
    
    throw error;
  } finally {
    try {
      await sequelize.close();
      console.log('🔌 数据库连接已关闭');
    } catch (closeError) {
      console.error('❌ 关闭数据库连接失败:', closeError.message);
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--rollback')) {
    await rollbackCrmUserFields();
  } else {
    await migrateAddCrmUserFields();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  migrateAddCrmUserFields,
  rollbackCrmUserFields
};
