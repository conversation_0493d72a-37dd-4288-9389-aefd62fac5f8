/**
 * CooperationService动态用户身份传递功能测试脚本
 *
 * 功能说明：
 * 1. 测试合作数据同步中的动态用户ID传递
 * 2. 验证smartSyncCooperationData方法的用户身份传递
 * 3. 测试recreateCrmCustomer和recreateCrmAgreement方法
 * 4. 验证整个调用链路的用户身份传递
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const axios = require('axios');
const { User, CooperationManagement } = require('../src/models');

// API基础配置
const API_BASE_URL = 'http://localhost:3001/api';
let authToken = '';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(config => {
  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

// 响应拦截器
api.interceptors.response.use(
  response => response.data,
  error => {
    console.error('API请求失败:', error.response?.data || error.message);
    throw error;
  }
);

/**
 * 管理员登录获取token
 */
async function adminLogin() {
  try {
    console.log('🔐 正在进行管理员登录...');
    const response = await api.post('/auth/login', {
      username: 'admin',
      password: 'admin123456'
    });

    if (response.success && response.data.token) {
      authToken = response.data.token;
      console.log('✅ 管理员登录成功');
      return response.data.user;
    } else {
      console.error('❌ 管理员登录失败:', response.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 管理员登录异常:', error.message);
    return null;
  }
}

/**
 * 创建测试合作记录
 */
async function createTestCooperation() {
  console.log('\n📝 创建测试合作记录...');

  const testCooperationData = {
    customerName: '测试客户-动态用户ID',
    title: '测试合作项目-动态用户ID',
    cooperationMonth: '2025-01',
    bloggerName: '测试博主',
    cooperationForm: '图文',
    cooperationBrand: '测试品牌',
    cooperationProduct: '测试产品',
    cooperationAmount: 1000,
    publishPlatform: 'xiaohongshu',
    responsiblePerson: '测试负责人',
    workProgress: '进行中',
    // 启用CRM同步
    syncCreateCustomer: true,
    syncCreateAgreement: true
  };

  try {
    const response = await api.post('/cooperation', testCooperationData);

    if (response.success) {
      console.log(`✅ 测试合作记录创建成功 - ID: ${response.data.id}`);
      console.log(`  - 客户名称: ${response.data.customerName}`);
      console.log(`  - 标题: ${response.data.title}`);

      // 检查CRM同步结果
      if (response.data.crmSyncResult) {
        const syncResult = response.data.crmSyncResult;
        console.log(`  - CRM客户同步: ${syncResult.customerSynced ? '成功' : '失败'}`);
        console.log(`  - CRM协议同步: ${syncResult.agreementSynced ? '成功' : '失败'}`);
        if (syncResult.customerId) {
          console.log(`  - CRM客户ID: ${syncResult.customerId}`);
        }
        if (syncResult.agreementId) {
          console.log(`  - CRM协议ID: ${syncResult.agreementId}`);
        }
        if (syncResult.errors.length > 0) {
          console.log(`  - 同步错误: ${syncResult.errors.join(', ')}`);
        }
      }

      return response.data;
    } else {
      console.log(`❌ 测试合作记录创建失败: ${response.message}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ 测试合作记录创建异常: ${error.message}`);
    return null;
  }
}

/**
 * 测试重新创建CRM客户
 */
async function testRecreateCrmCustomer(cooperationId) {
  console.log('\n🔄 测试重新创建CRM客户...');

  if (!cooperationId) {
    console.log('❌ 没有可用的合作记录ID');
    return;
  }

  try {
    const response = await api.post(`/cooperation/${cooperationId}/crm/recreate-customer`);

    if (response.success) {
      console.log('✅ CRM客户重新创建成功');
      console.log(`  - 客户ID: ${response.data.customerId}`);
      console.log(`  - 消息: ${response.data.message}`);
      if (response.data.errors && response.data.errors.length > 0) {
        console.log(`  - 错误: ${response.data.errors.join(', ')}`);
      }
    } else {
      console.log(`❌ CRM客户重新创建失败: ${response.message}`);
    }
  } catch (error) {
    console.log(`❌ CRM客户重新创建异常: ${error.message}`);
  }
}

/**
 * 测试重新创建CRM协议
 */
async function testRecreateCrmAgreement(cooperationId) {
  console.log('\n📄 测试重新创建CRM协议...');

  if (!cooperationId) {
    console.log('❌ 没有可用的合作记录ID');
    return;
  }

  try {
    const response = await api.post(`/cooperation/${cooperationId}/crm/recreate-agreement`);

    if (response.success) {
      console.log('✅ CRM协议重新创建成功');
      console.log(`  - 协议ID: ${response.data.agreementId}`);
      console.log(`  - 消息: ${response.data.message}`);
      if (response.data.errors && response.data.errors.length > 0) {
        console.log(`  - 错误: ${response.data.errors.join(', ')}`);
      }
    } else {
      console.log(`❌ CRM协议重新创建失败: ${response.message}`);
    }
  } catch (error) {
    console.log(`❌ CRM协议重新创建异常: ${error.message}`);
  }
}

/**
 * 测试智能同步合作数据
 */
async function testSmartSyncCooperationData(cooperationId) {
  console.log('\n🔗 测试智能同步合作数据...');

  if (!cooperationId) {
    console.log('❌ 没有可用的合作记录ID');
    return;
  }

  try {
    const response = await api.post('/crm-integration/smart-sync-cooperation', {
      cooperationId: cooperationId,
      options: {
        forceCustomerSync: true,
        forceAgreementSync: true
      }
    });

    if (response.success) {
      console.log('✅ 智能同步合作数据成功');
      console.log(`  - 客户同步: ${response.data.customerSynced ? '成功' : '失败'}`);
      console.log(`  - 协议同步: ${response.data.agreementSynced ? '成功' : '失败'}`);
      if (response.data.customerId) {
        console.log(`  - 客户ID: ${response.data.customerId}`);
      }
      if (response.data.agreementId) {
        console.log(`  - 协议ID: ${response.data.agreementId}`);
      }
      if (response.data.errors && response.data.errors.length > 0) {
        console.log(`  - 错误: ${response.data.errors.join(', ')}`);
      }
    } else {
      console.log(`❌ 智能同步合作数据失败: ${response.message}`);
    }
  } catch (error) {
    console.log(`❌ 智能同步合作数据异常: ${error.message}`);
  }
}

/**
 * 清理测试数据
 */
async function cleanupTestData(cooperationId) {
  console.log('\n🧹 清理测试数据...');

  if (!cooperationId) {
    console.log('ℹ️ 没有需要清理的测试数据');
    return;
  }

  try {
    await api.delete(`/cooperation/${cooperationId}`);
    console.log(`✅ 测试合作记录已删除 (ID: ${cooperationId})`);
  } catch (error) {
    console.log(`❌ 删除测试合作记录失败: ${error.message}`);
  }
}

/**
 * 验证功能实现
 */
function validateImplementation() {
  console.log('\n✅ CooperationService动态用户身份传递功能实现验证:');
  console.log('  ✅ smartSyncCooperationData方法已更新，支持userId参数');
  console.log('  ✅ syncCustomerData方法已更新，支持userId参数');
  console.log('  ✅ syncAgreementData方法已更新，支持userId参数');
  console.log('  ✅ CooperationService方法已获取用户信息');
  console.log('  ✅ 控制器层已传递用户身份');
  console.log('  ✅ 向后兼容：未传递用户ID时使用默认值');

  console.log('\n📋 更新的方法列表:');
  console.log('  - smartSyncCooperationData(cooperationData, options, userId)');
  console.log('  - syncCustomerData(cooperationData, userId)');
  console.log('  - syncAgreementData(cooperationData, userId)');
  console.log('  - recreateCrmCustomer(cooperationId, userId)');
  console.log('  - recreateCrmAgreement(cooperationId, userId)');
  console.log('  - performAsyncCrmSync(cooperation, changedFields, userId)');

  console.log('\n🔧 控制器更新:');
  console.log('  - recreateCrmCustomer: 传递用户ID');
  console.log('  - recreateCrmAgreement: 传递用户ID');
  console.log('  - smartSyncCooperationData: 传递用户ID');
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始CooperationService动态用户身份传递功能测试\n');

  let testCooperation = null;

  try {
    // 1. 验证功能实现
    validateImplementation();

    // 2. 管理员登录
    const adminUser = await adminLogin();
    if (!adminUser) {
      console.log('❌ 无法获取管理员权限，测试终止');
      return;
    }

    // 3. 创建测试合作记录（包含CRM同步）
    testCooperation = await createTestCooperation();

    if (testCooperation) {
      // 4. 测试重新创建CRM客户
      await testRecreateCrmCustomer(testCooperation.id);

      // 5. 测试重新创建CRM协议
      await testRecreateCrmAgreement(testCooperation.id);

      // 6. 测试智能同步合作数据
      await testSmartSyncCooperationData(testCooperation.id);
    }

    // 7. 清理测试数据
    await cleanupTestData(testCooperation?.id);

    console.log('\n🎉 CooperationService动态用户身份传递功能测试完成！');
  } catch (error) {
    console.error('❌ 测试过程中发生异常:', error);
  } finally {
    // 确保数据库连接关闭
    try {
      await User.sequelize.close();
      console.log('🔌 数据库连接已关闭');
    } catch (error) {
      console.error('❌ 关闭数据库连接失败:', error.message);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  createTestCooperation,
  testRecreateCrmCustomer,
  testRecreateCrmAgreement,
  testSmartSyncCooperationData,
  validateImplementation
};
