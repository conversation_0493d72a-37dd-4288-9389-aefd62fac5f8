/**
 * 更新合作价格字段类型脚本
 * 
 * 功能说明：
 * - 修改合作价格字段为字符串类型
 * - 支持文本描述的价格信息
 * - 如："8000元+提成"、"面议"、"按效果付费"等
 * 
 * 使用方法：
 * node scripts/update_cooperation_price.js
 */

const fs = require('fs');
const path = require('path');
const { sequelize } = require('../src/models');

async function updateCooperationPrice() {
  try {
    console.log('🔄 开始更新合作价格字段类型...');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, '../sql/update_cooperation_price.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // 分割SQL语句（按分号分割）
    const sqlStatements = sqlContent
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0 && !statement.startsWith('--'));

    console.log(`📋 找到 ${sqlStatements.length} 条SQL语句`);

    // 执行每条SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      
      if (statement.toLowerCase().includes('alter table')) {
        console.log('🔨 修改合作价格字段类型为字符串...');
      } else if (statement.toLowerCase().includes('describe')) {
        console.log('📊 显示表结构...');
      }

      try {
        const result = await sequelize.query(statement);
        
        // 如果是DESCRIBE语句，显示结果
        if (statement.toLowerCase().includes('describe')) {
          console.log('📋 合作对接管理表结构（更新后）:');
          console.table(result[0]);
        }
      } catch (error) {
        console.error(`❌ 执行SQL语句失败: ${statement.substring(0, 50)}...`);
        console.error('错误信息:', error.message);
      }
    }

    console.log('✅ 合作价格字段更新完成！');

    // 验证字段类型是否更新成功
    const [results] = await sequelize.query(`
      SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'cooperation_management' 
      AND COLUMN_NAME = 'cooperation_price'
    `);
    
    if (results.length > 0) {
      console.log('✅ 合作价格字段类型验证:');
      console.table(results);
    }

  } catch (error) {
    console.error('❌ 更新合作价格字段失败:', error.message);
    throw error;
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行脚本
if (require.main === module) {
  updateCooperationPrice()
    .then(() => {
      console.log('🎉 合作价格字段更新脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 合作价格字段更新脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = updateCooperationPrice;
