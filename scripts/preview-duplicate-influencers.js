/**
 * 预览线上数据库中重复的达人数据
 * 只查看不删除，用于确认清理策略
 */

const { sequelize } = require('../src/config/database');

async function previewDuplicateInfluencers() {
  try {
    console.log('🔍 开始预览重复达人数据...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 1. 预览我的达人库中的重复数据
    console.log('\n📋 我的达人库重复数据预览：');
    await previewMyInfluencersDuplicates();
    
    // 2. 预览达人公海中的重复数据
    console.log('\n📋 达人公海重复数据预览：');
    await previewPublicInfluencersDuplicates();
    
    console.log('\n🎉 预览完成！');
    
  } catch (error) {
    console.error('❌ 预览失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
    process.exit(0);
  }
}

/**
 * 预览我的达人库中的重复数据
 */
async function previewMyInfluencersDuplicates() {
  // 查找重复的达人（按platform + platformId分组）
  const [duplicates] = await sequelize.query(`
    SELECT 
      platform,
      platform_id,
      COUNT(*) as count,
      GROUP_CONCAT(CONCAT(id, ':', nickname, ':', created_at) ORDER BY created_at DESC SEPARATOR ' | ') as records
    FROM my_influencers 
    WHERE platform_id IS NOT NULL AND platform_id != ''
    GROUP BY platform, platform_id 
    HAVING COUNT(*) > 1
    ORDER BY platform, platform_id
  `);
  
  console.log(`🔍 发现 ${duplicates.length} 组重复数据`);
  
  let totalDuplicates = 0;
  
  for (const duplicate of duplicates) {
    totalDuplicates += (duplicate.count - 1); // 每组保留1个，其他的都是重复
    
    console.log(`\n📝 平台: ${duplicate.platform}, 平台ID: ${duplicate.platform_id} (${duplicate.count}条记录)`);
    
    const records = duplicate.records.split(' | ');
    records.forEach((record, index) => {
      const [id, nickname, createdAt] = record.split(':');
      const status = index === 0 ? '✅ 保留' : '❌ 删除';
      console.log(`   ${status} ID: ${id}, 昵称: ${nickname}, 创建时间: ${createdAt}`);
    });
  }
  
  console.log(`\n📊 统计: 总共将删除 ${totalDuplicates} 条重复记录`);
}

/**
 * 预览达人公海中的重复数据
 */
async function previewPublicInfluencersDuplicates() {
  // 查找重复的达人（按platform + platformUserId分组）
  const [duplicates] = await sequelize.query(`
    SELECT 
      platform,
      platform_user_id,
      COUNT(*) as count,
      GROUP_CONCAT(CONCAT(id, ':', nickname, ':', created_at) ORDER BY created_at DESC SEPARATOR ' | ') as records
    FROM public_influencers 
    WHERE platform_user_id IS NOT NULL AND platform_user_id != ''
    GROUP BY platform, platform_user_id 
    HAVING COUNT(*) > 1
    ORDER BY platform, platform_user_id
  `);
  
  console.log(`🔍 发现 ${duplicates.length} 组重复数据`);
  
  let totalDuplicates = 0;
  
  for (const duplicate of duplicates) {
    totalDuplicates += (duplicate.count - 1); // 每组保留1个，其他的都是重复
    
    console.log(`\n📝 平台: ${duplicate.platform}, 平台ID: ${duplicate.platform_user_id} (${duplicate.count}条记录)`);
    
    const records = duplicate.records.split(' | ');
    records.forEach((record, index) => {
      const [id, nickname, createdAt] = record.split(':');
      const status = index === 0 ? '✅ 保留' : '❌ 删除';
      console.log(`   ${status} ID: ${id}, 昵称: ${nickname}, 创建时间: ${createdAt}`);
    });
  }
  
  console.log(`\n📊 统计: 总共将删除 ${totalDuplicates} 条重复记录`);
}

// 执行预览
previewDuplicateInfluencers();
