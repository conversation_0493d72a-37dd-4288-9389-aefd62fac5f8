/**
 * 达人提报表结构迁移脚本
 * 
 * 功能说明：
 * - 添加合作对接记录关联字段
 * - 保持现有数据不变
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4'
};

// 新字段定义
const newColumnDefinitions = [
  { name: 'cooperation_record_id', definition: 'INT NULL COMMENT "关联的合作对接记录ID"' },
  { name: 'cooperation_record_created', definition: 'BOOLEAN DEFAULT FALSE COMMENT "是否已创建合作对接记录"' }
];

async function migrateInfluencerReportTable() {
  let connection;
  
  try {
    console.log('🔄 开始达人提报表结构迁移...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查表是否存在
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'influencer_reports'"
    );

    if (tables.length === 0) {
      console.log('❌ influencer_reports 表不存在，无法迁移');
      return;
    }

    // 获取现有字段
    const [existingColumns] = await connection.execute(
      'DESCRIBE influencer_reports'
    );
    const existingFieldNames = existingColumns.map(col => col.Field);

    console.log('📋 开始添加新字段...');
    
    let addedCount = 0;
    let skippedCount = 0;

    for (const column of newColumnDefinitions) {
      if (existingFieldNames.includes(column.name)) {
        console.log(`⏭️ 字段 ${column.name} 已存在，跳过`);
        skippedCount++;
        continue;
      }

      try {
        const alterSQL = `ALTER TABLE influencer_reports ADD COLUMN ${column.name} ${column.definition}`;
        await connection.execute(alterSQL);
        console.log(`✅ 添加字段: ${column.name}`);
        addedCount++;
      } catch (error) {
        console.error(`❌ 添加字段 ${column.name} 失败:`, error.message);
      }
    }

    console.log(`📊 迁移完成: 新增 ${addedCount} 个字段，跳过 ${skippedCount} 个字段`);

    // 验证迁移结果
    const [finalColumns] = await connection.execute(
      'DESCRIBE influencer_reports'
    );
    console.log(`📋 迁移后表结构字段数: ${finalColumns.length}`);

    // 检查数据完整性
    const [dataCount] = await connection.execute(
      'SELECT COUNT(*) as count FROM influencer_reports'
    );
    console.log(`📊 数据完整性检查: ${dataCount[0].count} 条记录保持完整`);

    console.log('🎉 达人提报表结构迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行迁移
migrateInfluencerReportTable();
