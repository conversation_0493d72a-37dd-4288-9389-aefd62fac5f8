/**
 * 检查管理员账户脚本
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4'
};

async function checkAdmin() {
  let connection;
  
  try {
    console.log('🔄 检查管理员账户...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查询用户表
    const [users] = await connection.execute(
      'SELECT id, username, role, created_at FROM users ORDER BY id'
    );
    
    console.log('👥 用户列表:');
    users.forEach(user => {
      console.log(`  - ID: ${user.id}, 用户名: ${user.username}, 角色: ${user.role}, 创建时间: ${user.created_at}`);
    });

    // 检查字典表数据
    const [dictCount] = await connection.execute(
      'SELECT COUNT(*) as count FROM dictionaries'
    );
    console.log(`📊 字典数据量: ${dictCount[0].count} 条`);

    // 检查字典分类
    const [categories] = await connection.execute(
      'SELECT DISTINCT category FROM dictionaries ORDER BY category'
    );
    console.log('📋 字典分类:');
    categories.forEach(cat => {
      console.log(`  - ${cat.category}`);
    });

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

checkAdmin();
