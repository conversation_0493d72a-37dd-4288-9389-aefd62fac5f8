/**
 * 重新创建合作对接管理表脚本
 * 
 * 功能说明：
 * - 删除现有的合作对接管理表
 * - 使用最新的表结构重新创建表
 * - 确保表结构与Sequelize模型定义完全匹配
 * - 插入示例数据用于测试
 * 
 * 使用方法：
 * node scripts/recreate_cooperation_table.js
 */

const fs = require('fs');
const path = require('path');
const { sequelize } = require('../src/models');

async function recreateCooperationTable() {
  try {
    console.log('🔄 开始重新创建合作对接管理表...');

    // 删除现有表
    console.log('🗑️ 删除现有的合作对接管理表...');
    await sequelize.query('DROP TABLE IF EXISTS `cooperation_management`');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, '../sql/cooperation_management.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // 分割SQL语句（按分号分割）
    const sqlStatements = [];
    const lines = sqlContent.split('\n');
    let currentStatement = '';
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 跳过注释行和空行
      if (trimmedLine.startsWith('--') || trimmedLine === '') {
        continue;
      }
      
      currentStatement += ' ' + trimmedLine;
      
      // 如果行以分号结尾，表示语句结束
      if (trimmedLine.endsWith(';')) {
        const statement = currentStatement.trim().slice(0, -1); // 移除最后的分号
        if (statement.length > 0) {
          sqlStatements.push(statement);
        }
        currentStatement = '';
      }
    }
    
    // 处理最后一个语句（如果没有分号结尾）
    if (currentStatement.trim().length > 0) {
      sqlStatements.push(currentStatement.trim());
    }

    console.log(`📋 找到 ${sqlStatements.length} 条SQL语句`);

    // 执行每条SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      
      if (statement.toLowerCase().includes('create table')) {
        console.log('🔨 创建合作对接管理表...');
      } else if (statement.toLowerCase().includes('insert into')) {
        console.log('📝 插入示例数据...');
      } else if (statement.toLowerCase().includes('create index')) {
        console.log('🔍 创建索引...');
      } else if (statement.toLowerCase().includes('describe')) {
        console.log('📊 显示表结构...');
      }

      try {
        const result = await sequelize.query(statement);
        
        // 如果是DESCRIBE语句，显示结果
        if (statement.toLowerCase().includes('describe')) {
          console.log('📋 合作对接管理表结构:');
          console.table(result[0]);
        }
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('⚠️ 表已存在，跳过创建');
        } else {
          console.error(`❌ 执行SQL语句失败: ${statement.substring(0, 50)}...`);
          console.error('错误信息:', error.message);
        }
      }
    }

    console.log('✅ 合作对接管理表重新创建完成！');

    // 验证表是否创建成功
    const [results] = await sequelize.query("SHOW TABLES LIKE 'cooperation_management'");
    if (results.length > 0) {
      console.log('✅ 表创建验证成功');
      
      // 查询表中的数据
      const [rows] = await sequelize.query('SELECT COUNT(*) as count FROM cooperation_management');
      console.log(`📊 表中当前有 ${rows[0].count} 条记录`);

      // 验证字段类型
      const [fieldResults] = await sequelize.query(`
        SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'cooperation_management' 
        AND COLUMN_NAME IN ('cooperation_price', 'scheduled_publish_time', 'work_progress', 'note_link_update_time', 'scheduled_fetch_time', 'data_fetch_time')
        ORDER BY COLUMN_NAME
      `);
      
      if (fieldResults.length > 0) {
        console.log('✅ 关键字段类型验证:');
        console.table(fieldResults);
      }
    } else {
      console.log('❌ 表创建验证失败');
    }

  } catch (error) {
    console.error('❌ 重新创建合作对接管理表失败:', error.message);
    throw error;
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行脚本
if (require.main === module) {
  recreateCooperationTable()
    .then(() => {
      console.log('🎉 表重建脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 表重建脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = recreateCooperationTable;
