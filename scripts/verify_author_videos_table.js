/**
 * 验证脚本：检查达人视频作品表结构
 * 验证 author_videos 表是否正确创建并包含所有必需字段
 */

const { sequelize, AuthorVideo, Influencer, CrawlTask } = require('../src/models');

async function verifyAuthorVideosTable() {
  try {
    console.log('🔄 开始验证达人视频作品表结构...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 查看表结构
    console.log('\n📋 查看 author_videos 表结构:');
    const [results] = await sequelize.query(`DESCRIBE author_videos`);
    
    console.log('\n字段列表:');
    console.log('----------------------------------------');
    results.forEach(field => {
      console.log(`${field.Field.padEnd(20)} | ${field.Type.padEnd(25)} | ${field.Null.padEnd(5)} | ${field.Comment || ''}`);
    });
    
    // 查看索引
    console.log('\n📋 查看表索引:');
    const [indexes] = await sequelize.query(`SHOW INDEX FROM author_videos`);
    
    console.log('\n索引列表:');
    console.log('----------------------------------------');
    indexes.forEach(index => {
      console.log(`${index.Key_name.padEnd(30)} | ${index.Column_name.padEnd(20)} | ${index.Non_unique ? '非唯一' : '唯一'}`);
    });
    
    // 测试模型关联
    console.log('\n🔗 测试模型关联关系:');
    
    // 测试 AuthorVideo 模型是否正确加载
    console.log('- AuthorVideo 模型:', AuthorVideo.name);
    console.log('- 表名:', AuthorVideo.tableName);
    
    // 测试关联关系
    const associations = AuthorVideo.associations;
    console.log('- 关联关系:');
    Object.keys(associations).forEach(key => {
      const assoc = associations[key];
      console.log(`  - ${key}: ${assoc.associationType} -> ${assoc.target.name}`);
    });
    
    // 测试基本查询
    console.log('\n🧪 测试基本查询功能:');
    const count = await AuthorVideo.count();
    console.log(`- 当前视频记录数: ${count}`);
    
    // 测试字段验证
    console.log('\n✅ 验证必需字段:');
    const requiredFields = [
      'id', 'authorId', 'platform', 'platformUserId', 'videoId', 'title',
      'videoUrl', 'videoCover', 'duration', 'publishTime', 'likeCount',
      'playCount', 'shareCount', 'commentCount', 'collectCount'
    ];
    
    const modelAttributes = Object.keys(AuthorVideo.rawAttributes);
    requiredFields.forEach(field => {
      const exists = modelAttributes.includes(field);
      console.log(`- ${field}: ${exists ? '✅' : '❌'}`);
    });
    
    console.log('\n🎉 达人视频作品表验证完成！');
    
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await sequelize.close();
  }
}

// 执行验证
if (require.main === module) {
  verifyAuthorVideosTable();
}

module.exports = verifyAuthorVideosTable;
