/**
 * 数据库迁移脚本：为达人提报表添加审核流程相关字段
 * 
 * 功能说明：
 * 1. 为 influencer_reports 表添加审核流程相关字段
 * 2. 更新现有数据的状态值，确保向后兼容性
 * 3. 添加必要的索引优化查询性能
 * 4. 提供完整的迁移和验证流程
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize } = require('../src/config/database');

class InfluencerReportAuditMigrator {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.migrationReport = {
      startTime: new Date(),
      endTime: null,
      fieldsAdded: false,
      statusUpdated: false,
      indexesAdded: false,
      dataUpdated: false,
      errors: []
    };
  }

  async executeMigration() {
    try {
      console.log('🚀 开始达人提报审核字段迁移...');
      if (this.dryRun) {
        console.log('🔍 运行模式: 预演模式（不会实际修改数据）\n');
      }

      // 1. 连接数据库
      await sequelize.authenticate();
      console.log('✅ 数据库连接成功\n');

      // 2. 检查表是否存在
      await this.checkTableExists();

      // 3. 添加新字段
      await this.addAuditFields();

      // 4. 更新状态枚举值
      await this.updateStatusEnum();

      // 5. 更新现有数据
      await this.updateExistingData();

      // 6. 添加索引
      await this.addIndexes();

      // 7. 验证迁移结果
      await this.validateMigration();

      // 8. 生成最终报告
      this.generateFinalReport();

      console.log('🎉 达人提报审核字段迁移完成！\n');
      return this.migrationReport;

    } catch (error) {
      console.error('❌ 迁移失败:', error.message);
      this.migrationReport.errors.push({
        stage: 'migration_process',
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    } finally {
      this.migrationReport.endTime = new Date();
      await sequelize.close();
    }
  }

  /**
   * 检查表是否存在
   */
  async checkTableExists() {
    console.log('📋 步骤1: 检查表结构...');
    
    const [tables] = await sequelize.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'influencer_reports'
    `);

    if (tables.length === 0) {
      throw new Error('influencer_reports 表不存在，请先创建表结构');
    }

    console.log('✅ influencer_reports 表存在\n');
  }

  /**
   * 添加审核相关字段
   */
  async addAuditFields() {
    console.log('📋 步骤2: 添加审核相关字段...');

    if (this.dryRun) {
      console.log('🔍 预演模式: 跳过字段添加');
      return;
    }

    const transaction = await sequelize.transaction();

    try {
      // 检查字段是否已存在
      const [existingFields] = await sequelize.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'influencer_reports'
          AND COLUMN_NAME IN ('review_comment', 'reviewed_by', 'reviewed_at', 'resubmit_reason', 'resubmit_count')
      `, { transaction });

      const existingFieldNames = existingFields.map(field => field.COLUMN_NAME);

      // 添加 review_comment 字段
      if (!existingFieldNames.includes('review_comment')) {
        await sequelize.query(`
          ALTER TABLE influencer_reports 
          ADD COLUMN review_comment TEXT COMMENT '审核意见' 
          AFTER last_submitted_at
        `, { transaction });
        console.log('✅ review_comment 字段添加成功');
      } else {
        console.log('ℹ️ review_comment 字段已存在，跳过');
      }

      // 添加 reviewed_by 字段
      if (!existingFieldNames.includes('reviewed_by')) {
        await sequelize.query(`
          ALTER TABLE influencer_reports 
          ADD COLUMN reviewed_by INT COMMENT '审核人员ID' 
          AFTER review_comment
        `, { transaction });
        console.log('✅ reviewed_by 字段添加成功');
      } else {
        console.log('ℹ️ reviewed_by 字段已存在，跳过');
      }

      // 添加 reviewed_at 字段
      if (!existingFieldNames.includes('reviewed_at')) {
        await sequelize.query(`
          ALTER TABLE influencer_reports 
          ADD COLUMN reviewed_at DATETIME COMMENT '审核时间' 
          AFTER reviewed_by
        `, { transaction });
        console.log('✅ reviewed_at 字段添加成功');
      } else {
        console.log('ℹ️ reviewed_at 字段已存在，跳过');
      }

      // 添加 resubmit_reason 字段
      if (!existingFieldNames.includes('resubmit_reason')) {
        await sequelize.query(`
          ALTER TABLE influencer_reports 
          ADD COLUMN resubmit_reason TEXT COMMENT '重新提报说明/理由' 
          AFTER reviewed_at
        `, { transaction });
        console.log('✅ resubmit_reason 字段添加成功');
      } else {
        console.log('ℹ️ resubmit_reason 字段已存在，跳过');
      }

      // 添加 resubmit_count 字段
      if (!existingFieldNames.includes('resubmit_count')) {
        await sequelize.query(`
          ALTER TABLE influencer_reports 
          ADD COLUMN resubmit_count INT DEFAULT 0 COMMENT '重新提报次数' 
          AFTER resubmit_reason
        `, { transaction });
        console.log('✅ resubmit_count 字段添加成功');
      } else {
        console.log('ℹ️ resubmit_count 字段已存在，跳过');
      }

      await transaction.commit();
      this.migrationReport.fieldsAdded = true;
      console.log('✅ 审核字段添加完成\n');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 添加字段失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新状态枚举值
   */
  async updateStatusEnum() {
    console.log('📋 步骤3: 更新状态枚举值...');

    if (this.dryRun) {
      console.log('🔍 预演模式: 跳过状态枚举更新');
      return;
    }

    const transaction = await sequelize.transaction();

    try {
      // 先添加新的枚举值到现有枚举中
      await sequelize.query(`
        ALTER TABLE influencer_reports
        MODIFY COLUMN status ENUM('submitting', 'resubmitting', 'need_communication', 'approved', 'pending', 'rejected', 'need_confirmation')
        DEFAULT 'pending'
        COMMENT '审核状态：审核中/审核通过/审核拒绝/需二次确认'
      `, { transaction });

      await transaction.commit();
      this.migrationReport.statusUpdated = true;
      console.log('✅ 状态枚举值更新完成\n');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 更新状态枚举失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新现有数据
   */
  async updateExistingData() {
    console.log('📋 步骤4: 更新现有数据状态...');

    if (this.dryRun) {
      console.log('🔍 预演模式: 跳过数据更新');
      return;
    }

    const transaction = await sequelize.transaction();

    try {
      // 将旧状态值映射到新状态值
      const statusMapping = {
        'submitting': 'pending',
        'resubmitting': 'pending', 
        'need_communication': 'need_confirmation',
        'approved': 'approved'
      };

      for (const [oldStatus, newStatus] of Object.entries(statusMapping)) {
        const [result] = await sequelize.query(`
          UPDATE influencer_reports 
          SET status = :newStatus 
          WHERE status = :oldStatus
        `, {
          replacements: { oldStatus, newStatus },
          transaction
        });

        if (result.affectedRows > 0) {
          console.log(`✅ 更新 ${oldStatus} -> ${newStatus}: ${result.affectedRows} 条记录`);
        }
      }

      await transaction.commit();
      this.migrationReport.dataUpdated = true;
      console.log('✅ 现有数据状态更新完成\n');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 更新现有数据失败:', error.message);
      throw error;
    }
  }

  /**
   * 添加索引
   */
  async addIndexes() {
    console.log('📋 步骤5: 添加索引...');

    if (this.dryRun) {
      console.log('🔍 预演模式: 跳过索引添加');
      return;
    }

    const transaction = await sequelize.transaction();

    try {
      // 检查索引是否已存在
      const [existingIndexes] = await sequelize.query(`
        SELECT INDEX_NAME 
        FROM INFORMATION_SCHEMA.STATISTICS 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'influencer_reports'
          AND INDEX_NAME IN ('idx_reviewed_by', 'idx_reviewed_at', 'idx_review_query')
      `, { transaction });

      const existingIndexNames = existingIndexes.map(index => index.INDEX_NAME);

      // 添加 reviewed_by 索引
      if (!existingIndexNames.includes('idx_reviewed_by')) {
        await sequelize.query(`
          CREATE INDEX idx_reviewed_by ON influencer_reports (reviewed_by)
        `, { transaction });
        console.log('✅ idx_reviewed_by 索引添加成功');
      } else {
        console.log('ℹ️ idx_reviewed_by 索引已存在，跳过');
      }

      // 添加 reviewed_at 索引
      if (!existingIndexNames.includes('idx_reviewed_at')) {
        await sequelize.query(`
          CREATE INDEX idx_reviewed_at ON influencer_reports (reviewed_at)
        `, { transaction });
        console.log('✅ idx_reviewed_at 索引添加成功');
      } else {
        console.log('ℹ️ idx_reviewed_at 索引已存在，跳过');
      }

      // 添加复合索引
      if (!existingIndexNames.includes('idx_review_query')) {
        await sequelize.query(`
          CREATE INDEX idx_review_query ON influencer_reports (status, reviewed_by, reviewed_at)
        `, { transaction });
        console.log('✅ idx_review_query 复合索引添加成功');
      } else {
        console.log('ℹ️ idx_review_query 索引已存在，跳过');
      }

      await transaction.commit();
      this.migrationReport.indexesAdded = true;
      console.log('✅ 索引添加完成\n');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 添加索引失败:', error.message);
      throw error;
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    console.log('📋 步骤6: 验证迁移结果...');

    // 验证字段是否添加成功
    const [fields] = await sequelize.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'influencer_reports'
        AND COLUMN_NAME IN ('review_comment', 'reviewed_by', 'reviewed_at', 'resubmit_reason', 'resubmit_count')
      ORDER BY ORDINAL_POSITION
    `);

    console.log('✅ 新增字段验证:');
    fields.forEach(field => {
      console.log(`  - ${field.COLUMN_NAME}: ${field.DATA_TYPE}, 可空: ${field.IS_NULLABLE}, 注释: ${field.COLUMN_COMMENT}`);
    });

    // 验证状态枚举值
    const [statusEnum] = await sequelize.query(`
      SELECT COLUMN_TYPE 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'influencer_reports'
        AND COLUMN_NAME = 'status'
    `);

    console.log('✅ 状态枚举验证:');
    console.log(`  - status: ${statusEnum[0].COLUMN_TYPE}`);

    // 验证数据统计
    const [stats] = await sequelize.query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM influencer_reports 
      GROUP BY status
      ORDER BY status
    `);

    console.log('✅ 数据状态统计:');
    stats.forEach(stat => {
      console.log(`  - ${stat.status}: ${stat.count} 条记录`);
    });

    console.log('✅ 迁移验证完成\n');
  }

  /**
   * 生成最终报告
   */
  generateFinalReport() {
    this.migrationReport.endTime = new Date();
    console.log('📊 迁移报告:');
    console.log('=====================================');
    console.log(`开始时间: ${this.migrationReport.startTime.toLocaleString()}`);
    console.log(`结束时间: ${this.migrationReport.endTime.toLocaleString()}`);
    console.log(`执行时长: ${Math.round((this.migrationReport.endTime - this.migrationReport.startTime) / 1000)}秒`);
    console.log(`字段添加: ${this.migrationReport.fieldsAdded ? '✅ 成功' : '❌ 失败'}`);
    console.log(`状态更新: ${this.migrationReport.statusUpdated ? '✅ 成功' : '❌ 失败'}`);
    console.log(`数据更新: ${this.migrationReport.dataUpdated ? '✅ 成功' : '❌ 失败'}`);
    console.log(`索引添加: ${this.migrationReport.indexesAdded ? '✅ 成功' : '❌ 失败'}`);
    
    if (this.migrationReport.errors.length > 0) {
      console.log('\n❌ 错误列表:');
      this.migrationReport.errors.forEach((error, index) => {
        console.log(`${index + 1}. [${error.stage}] ${error.error} (${error.timestamp.toLocaleString()})`);
      });
    }
    
    console.log('=====================================\n');
  }
}

// 主执行函数
async function runMigration(options = {}) {
  const migrator = new InfluencerReportAuditMigrator(options);

  try {
    const report = await migrator.executeMigration();
    console.log('✅ 迁移流程完成');
    process.exit(0);

  } catch (error) {
    console.error('❌ 迁移流程失败:', error.message);
    process.exit(1);
  }
}

// 解析命令行参数
const args = process.argv.slice(2);
const options = {
  dryRun: args.includes('--dry-run')
};

// 执行迁移
if (require.main === module) {
  runMigration(options);
}

module.exports = { InfluencerReportAuditMigrator, runMigration };
