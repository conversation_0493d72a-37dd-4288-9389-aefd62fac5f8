/**
 * 创建测试数据脚本
 * 用于测试统计功能
 */

const axios = require('axios');

async function createTestData() {
  try {
    // 登录获取token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'testadmin',
      password: '123456'
    });

    if (!loginResponse.data.success) {
      throw new Error('登录失败');
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    // 创建不同状态的测试数据
    const testReports = [
      {
        platform: 'xiaohongshu',
        influencerName: '测试达人_审核中_' + Date.now(),
        operationManager: '测试运营',
        selectionReason: '测试审核中状态',
        platformUserId: 'test_pending_' + Date.now(),
        followersCount: 5000,
        playMid: '2500',
        platformPrice: '500元',
        cooperationPrice: '400元',
        notes: '审核中状态测试'
      },
      {
        platform: 'juxingtu',
        influencerName: '测试达人_待审核_' + Date.now(),
        operationManager: '测试运营',
        selectionReason: '测试待审核状态',
        platformUserId: 'test_pending2_' + Date.now(),
        followersCount: 8000,
        playMid: '4000',
        platformPrice: '800元',
        cooperationPrice: '600元',
        notes: '待审核状态测试'
      }
    ];

    console.log('📝 创建测试提报数据...');
    
    for (let i = 0; i < testReports.length; i++) {
      const reportData = testReports[i];
      
      const response = await axios.post('http://localhost:3001/api/influencer-reports', reportData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        console.log(`✅ 创建提报 ${i + 1} 成功，ID: ${response.data.data.id}`);
        
        // 如果是第二个提报，将其审核拒绝
        if (i === 1) {
          const rejectResponse = await axios.put(
            `http://localhost:3001/api/influencer-reports/${response.data.data.id}/reject`,
            { reviewComment: '测试审核拒绝，用于验证重新提报功能' },
            {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            }
          );
          
          if (rejectResponse.data.success) {
            console.log(`✅ 提报 ${response.data.data.id} 审核拒绝成功`);
          }
        }
      } else {
        console.error(`❌ 创建提报 ${i + 1} 失败:`, response.data.message);
      }
    }

    // 获取最新统计
    console.log('\n📊 获取最新统计数据...');
    const statsResponse = await axios.get('http://localhost:3001/api/influencer-reports?limit=1000', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (statsResponse.data.success) {
      const data = statsResponse.data.data;
      const stats = {
        total: data.length,
        pending: data.filter(item => item.status === 'pending').length,
        approved: data.filter(item => item.status === 'approved').length,
        rejected: data.filter(item => item.status === 'rejected').length,
        needConfirmation: data.filter(item => item.status === 'need_confirmation').length
      };

      console.log('📈 最新统计结果:');
      console.log('  总提报数:', stats.total);
      console.log('  审核中:', stats.pending);
      console.log('  审核通过:', stats.approved);
      console.log('  审核拒绝:', stats.rejected);
      console.log('  需二次确认:', stats.needConfirmation);
    }

    console.log('\n🎉 测试数据创建完成！');
    console.log('💡 现在可以刷新前端页面查看统计卡片是否显示正确数据');

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error.message);
  }
}

createTestData();
