/**
 * 清理线上数据库中重复的达人数据
 * 按照平台ID去重，只保留最新的记录
 */

const { sequelize } = require('../src/config/database');
const { MyInfluencer, PublicInfluencer } = require('../src/models');

async function cleanDuplicateInfluencers() {
  const transaction = await sequelize.transaction();
  
  try {
    console.log('🧹 开始清理重复达人数据...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 1. 清理我的达人库中的重复数据
    console.log('\n📋 第一步：清理我的达人库重复数据...');
    await cleanMyInfluencersDuplicates(transaction);
    
    // 2. 清理达人公海中的重复数据
    console.log('\n📋 第二步：清理达人公海重复数据...');
    await cleanPublicInfluencersDuplicates(transaction);
    
    // 提交事务
    await transaction.commit();
    console.log('\n🎉 重复数据清理完成！');
    
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('❌ 清理失败，已回滚:', error.message);
    console.error('详细错误:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
    process.exit(0);
  }
}

/**
 * 清理我的达人库中的重复数据
 */
async function cleanMyInfluencersDuplicates(transaction) {
  // 查找重复的达人（按platform + platformId分组）
  const [duplicates] = await sequelize.query(`
    SELECT 
      platform,
      platform_id,
      COUNT(*) as count,
      GROUP_CONCAT(id ORDER BY created_at DESC) as ids
    FROM my_influencers 
    WHERE platform_id IS NOT NULL AND platform_id != ''
    GROUP BY platform, platform_id 
    HAVING COUNT(*) > 1
    ORDER BY platform, platform_id
  `, { transaction });
  
  console.log(`🔍 我的达人库发现 ${duplicates.length} 组重复数据`);
  
  let deletedCount = 0;
  
  for (const duplicate of duplicates) {
    const ids = duplicate.ids.split(',');
    const keepId = ids[0]; // 保留最新的（created_at DESC排序后的第一个）
    const deleteIds = ids.slice(1); // 删除其他的
    
    console.log(`📝 平台: ${duplicate.platform}, ID: ${duplicate.platform_id}`);
    console.log(`   保留记录 ID: ${keepId}, 删除记录 IDs: ${deleteIds.join(', ')}`);
    
    // 删除重复记录
    const deleted = await MyInfluencer.destroy({
      where: {
        id: deleteIds
      },
      transaction
    });
    
    deletedCount += deleted;
  }
  
  console.log(`✅ 我的达人库清理完成，删除了 ${deletedCount} 条重复记录`);
}

/**
 * 清理达人公海中的重复数据
 */
async function cleanPublicInfluencersDuplicates(transaction) {
  // 查找重复的达人（按platform + platformUserId分组）
  const [duplicates] = await sequelize.query(`
    SELECT 
      platform,
      platform_user_id,
      COUNT(*) as count,
      GROUP_CONCAT(id ORDER BY created_at DESC) as ids
    FROM public_influencers 
    WHERE platform_user_id IS NOT NULL AND platform_user_id != ''
    GROUP BY platform, platform_user_id 
    HAVING COUNT(*) > 1
    ORDER BY platform, platform_user_id
  `, { transaction });
  
  console.log(`🔍 达人公海发现 ${duplicates.length} 组重复数据`);
  
  let deletedCount = 0;
  
  for (const duplicate of duplicates) {
    const ids = duplicate.ids.split(',');
    const keepId = ids[0]; // 保留最新的（created_at DESC排序后的第一个）
    const deleteIds = ids.slice(1); // 删除其他的
    
    console.log(`📝 平台: ${duplicate.platform}, ID: ${duplicate.platform_user_id}`);
    console.log(`   保留记录 ID: ${keepId}, 删除记录 IDs: ${deleteIds.join(', ')}`);
    
    // 删除重复记录
    const deleted = await PublicInfluencer.destroy({
      where: {
        id: deleteIds
      },
      transaction
    });
    
    deletedCount += deleted;
  }
  
  console.log(`✅ 达人公海清理完成，删除了 ${deletedCount} 条重复记录`);
}

// 执行清理
cleanDuplicateInfluencers();
