/**
 * 清理合作对接管理表中的时间数据脚本
 * 
 * 功能说明：
 * - 清理格式错误的时间数据
 * - 将现有的错误格式时间数据重置为NULL
 * - 确保数据的一致性
 * 
 * 使用方法：
 * node scripts/clean_time_data.js
 */

const { sequelize } = require('../src/models');

async function cleanTimeData() {
  try {
    console.log('🔄 开始清理合作对接管理表中的时间数据...');

    // 清理note_link_update_time字段中的错误数据
    const updateResult1 = await sequelize.query(`
      UPDATE cooperation_management 
      SET note_link_update_time = NULL 
      WHERE note_link_update_time IS NOT NULL 
      AND (note_link_update_time LIKE '{%' OR LENGTH(note_link_update_time) > 30)
    `);
    console.log(`✅ 清理note_link_update_time字段: 影响 ${updateResult1[1].affectedRows} 行`);

    // 清理scheduled_fetch_time字段中的错误数据
    const updateResult2 = await sequelize.query(`
      UPDATE cooperation_management 
      SET scheduled_fetch_time = NULL 
      WHERE scheduled_fetch_time IS NOT NULL 
      AND (scheduled_fetch_time LIKE '{%' OR LENGTH(scheduled_fetch_time) > 30)
    `);
    console.log(`✅ 清理scheduled_fetch_time字段: 影响 ${updateResult2[1].affectedRows} 行`);

    // 清理data_fetch_time字段中的错误数据
    const updateResult3 = await sequelize.query(`
      UPDATE cooperation_management 
      SET data_fetch_time = NULL 
      WHERE data_fetch_time IS NOT NULL 
      AND (data_fetch_time LIKE '{%' OR LENGTH(data_fetch_time) > 30)
    `);
    console.log(`✅ 清理data_fetch_time字段: 影响 ${updateResult3[1].affectedRows} 行`);

    // 查询清理后的数据
    const [results] = await sequelize.query(`
      SELECT id, blogger_name, note_link_update_time, scheduled_fetch_time, data_fetch_time 
      FROM cooperation_management 
      ORDER BY id
    `);

    console.log('📋 清理后的数据:');
    console.table(results);

    console.log('✅ 时间数据清理完成！');

  } catch (error) {
    console.error('❌ 清理时间数据失败:', error.message);
    throw error;
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 执行脚本
if (require.main === module) {
  cleanTimeData()
    .then(() => {
      console.log('🎉 时间数据清理脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 时间数据清理脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = cleanTimeData;
