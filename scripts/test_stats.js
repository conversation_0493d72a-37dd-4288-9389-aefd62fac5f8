/**
 * 统计功能测试脚本
 */

const axios = require('axios');

async function testStats() {
  try {
    // 登录获取token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'testadmin',
      password: '123456'
    });

    if (!loginResponse.data.success) {
      throw new Error('登录失败');
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    // 获取统计数据
    const statsResponse = await axios.get('http://localhost:3001/api/influencer-reports?limit=1000', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (statsResponse.data.success) {
      const data = statsResponse.data.data;
      console.log('✅ 获取数据成功，总数:', data.length);
      
      // 计算统计
      const stats = {
        total: data.length,
        pending: data.filter(item => item.status === 'pending').length,
        approved: data.filter(item => item.status === 'approved').length,
        rejected: data.filter(item => item.status === 'rejected').length,
        needConfirmation: data.filter(item => item.status === 'need_confirmation').length
      };

      console.log('📊 统计结果:');
      console.log('  总提报数:', stats.total);
      console.log('  审核中:', stats.pending);
      console.log('  审核通过:', stats.approved);
      console.log('  审核拒绝:', stats.rejected);
      console.log('  需二次确认:', stats.needConfirmation);

      // 显示数据详情
      console.log('\n📋 数据详情:');
      data.forEach((item, index) => {
        console.log(`${index + 1}. ${item.influencerName} - ${item.status} (ID: ${item.id})`);
      });

    } else {
      console.error('❌ 获取数据失败:', statsResponse.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testStats();
