/**
 * 视频数据迁移功能测试脚本
 * 测试从influencers表的videoDetails字段迁移到author_videos表的完整流程
 */

const { initializeAndSyncDatabase, Influencer, AuthorVideo, sequelize } = require('../src/models');
const { Op } = require('sequelize');
const VideoDetailsMigrator = require('./migrate_video_details_to_author_videos');

async function testVideoMigration() {
  try {
    console.log('🚀 开始测试视频数据迁移功能...\n');

    // 1. 初始化数据库
    console.log('📋 步骤1: 初始化数据库...');
    await initializeAndSyncDatabase();
    console.log('✅ 数据库初始化完成\n');

    // 2. 准备测试数据
    console.log('📋 步骤2: 准备测试数据...');
    const testInfluencers = await createTestData();
    console.log(`✅ 创建了 ${testInfluencers.length} 个测试达人\n`);

    // 3. 执行迁移前的数据统计
    console.log('📋 步骤3: 迁移前数据统计...');
    const beforeStats = await getDataStatistics();
    console.log('迁移前统计:', beforeStats);
    console.log('');

    // 4. 执行迁移
    console.log('📋 步骤4: 执行数据迁移...');
    const migrator = new VideoDetailsMigrator();
    
    // 设置较小的批量大小用于测试
    migrator.batchSize = 2;
    
    const migrationReport = await migrator.executeMigration();
    console.log('✅ 迁移执行完成\n');

    // 5. 验证迁移结果
    console.log('📋 步骤5: 验证迁移结果...');
    const afterStats = await getDataStatistics();
    console.log('迁移后统计:', afterStats);
    
    const validationResult = validateMigrationResults(beforeStats, afterStats, migrationReport);
    if (validationResult.success) {
      console.log('✅ 迁移结果验证通过\n');
    } else {
      console.log('❌ 迁移结果验证失败:', validationResult.errors);
      throw new Error('迁移验证失败');
    }

    // 6. 测试数据完整性
    console.log('📋 步骤6: 测试数据完整性...');
    await testDataIntegrity(testInfluencers);
    console.log('✅ 数据完整性验证通过\n');

    // 7. 清理测试数据
    console.log('📋 步骤7: 清理测试数据...');
    await cleanupTestData(testInfluencers);
    console.log('✅ 测试数据清理完成\n');

    console.log('🎉 视频数据迁移功能测试全部通过！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  }
}

/**
 * 创建测试数据
 */
async function createTestData() {
  const testInfluencers = [];

  // 创建有视频数据的达人
  for (let i = 1; i <= 3; i++) {
    const videoDetails = [];
    
    // 为每个达人创建2-4个视频
    const videoCount = Math.floor(Math.random() * 3) + 2;
    for (let j = 1; j <= videoCount; j++) {
      videoDetails.push({
        videoId: `test_video_${i}_${j}_${Date.now()}`,
        title: `测试视频${i}-${j}`,
        playCount: Math.floor(Math.random() * 10000) + 1000,
        likeCount: Math.floor(Math.random() * 1000) + 100,
        commentCount: Math.floor(Math.random() * 100) + 10,
        shareCount: Math.floor(Math.random() * 50) + 5,
        publishTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        duration: Math.floor(Math.random() * 300) + 30,
        videoUrl: `https://example.com/video_${i}_${j}.mp4`,
        videoCover: `https://example.com/cover_${i}_${j}.jpg`
      });
    }

    const influencer = await Influencer.create({
      nickname: `测试达人${i}_迁移`,
      platform: 'juxingtu',
      platformId: `test_platform_${i}_${Date.now()}`,
      avatarUrl: `https://example.com/avatar_${i}.jpg`,
      followersCount: Math.floor(Math.random() * 50000) + 10000,
      videoDetails: videoDetails, // 包含视频数据
      status: 'active'
    });

    testInfluencers.push(influencer);
  }

  // 创建没有视频数据的达人
  const influencerWithoutVideos = await Influencer.create({
    nickname: '测试达人_无视频_迁移',
    platform: 'juxingtu',
    platformId: `test_platform_no_videos_${Date.now()}`,
    avatarUrl: 'https://example.com/avatar_no_videos.jpg',
    followersCount: 5000,
    videoDetails: null, // 没有视频数据
    status: 'active'
  });

  testInfluencers.push(influencerWithoutVideos);

  return testInfluencers;
}

/**
 * 获取数据统计
 */
async function getDataStatistics() {
  const totalInfluencers = await Influencer.count();
  
  const influencersWithVideos = await Influencer.count({
    where: {
      videoDetails: {
        [Op.ne]: null
      }
    }
  });

  const totalVideosInAuthorVideos = await AuthorVideo.count();

  // 统计videoDetails中的视频总数
  const influencersWithVideoDetails = await Influencer.findAll({
    where: {
      videoDetails: {
        [Op.ne]: null
      }
    },
    attributes: ['videoDetails']
  });

  let totalVideosInVideoDetails = 0;
  influencersWithVideoDetails.forEach(influencer => {
    if (influencer.videoDetails && Array.isArray(influencer.videoDetails)) {
      totalVideosInVideoDetails += influencer.videoDetails.length;
    }
  });

  return {
    totalInfluencers,
    influencersWithVideos,
    totalVideosInVideoDetails,
    totalVideosInAuthorVideos
  };
}

/**
 * 验证迁移结果
 */
function validateMigrationResults(beforeStats, afterStats, migrationReport) {
  const errors = [];

  // 验证达人数量没有变化
  if (beforeStats.totalInfluencers !== afterStats.totalInfluencers) {
    errors.push('达人总数发生变化');
  }

  // 验证视频数量迁移正确
  // 注意：由于可能存在重复视频被跳过的情况，所以实际迁移的视频数量可能小于原始数量
  // 这里只验证迁移后的数量不少于迁移前的数量
  if (afterStats.totalVideosInAuthorVideos < beforeStats.totalVideosInAuthorVideos) {
    errors.push(`author_videos表中的视频数量减少了，迁移前 ${beforeStats.totalVideosInAuthorVideos}，迁移后 ${afterStats.totalVideosInAuthorVideos}`);
  }

  // 验证迁移报告中的实际迁移数量是否合理
  if (migrationReport.totalVideosMigrated > beforeStats.totalVideosInVideoDetails) {
    errors.push(`实际迁移的视频数量 ${migrationReport.totalVideosMigrated} 超过了原始数量 ${beforeStats.totalVideosInVideoDetails}`);
  }

  // 验证迁移报告数据
  if (migrationReport.influencersWithVideos !== beforeStats.influencersWithVideos) {
    errors.push('迁移报告中的达人数量与实际不符');
  }

  if (migrationReport.totalVideosToMigrate !== beforeStats.totalVideosInVideoDetails) {
    errors.push('迁移报告中的视频数量与实际不符');
  }

  return {
    success: errors.length === 0,
    errors
  };
}

/**
 * 测试数据完整性
 */
async function testDataIntegrity(testInfluencers) {
  for (const influencer of testInfluencers) {
    // 重新获取达人数据
    const updatedInfluencer = await Influencer.findByPk(influencer.id);
    
    if (updatedInfluencer.videoDetails && Array.isArray(updatedInfluencer.videoDetails)) {
      // 如果还有videoDetails，说明清理没有完成（可能是因为迁移失败）
      console.log(`⚠️ 达人 ${updatedInfluencer.nickname} 的videoDetails字段未被清理`);
    }

    // 检查author_videos表中的数据
    const migratedVideos = await AuthorVideo.findAll({
      where: { authorId: influencer.id }
    });

    if (influencer.videoDetails && Array.isArray(influencer.videoDetails)) {
      const originalVideoCount = influencer.videoDetails.length;
      if (migratedVideos.length !== originalVideoCount) {
        throw new Error(`达人 ${influencer.nickname} 的视频数量不匹配: 原始 ${originalVideoCount}, 迁移 ${migratedVideos.length}`);
      }

      // 验证视频数据字段
      for (const migratedVideo of migratedVideos) {
        if (!migratedVideo.videoId || !migratedVideo.title) {
          throw new Error(`达人 ${influencer.nickname} 的视频数据字段不完整`);
        }
        
        if (migratedVideo.authorId !== influencer.id) {
          throw new Error(`达人 ${influencer.nickname} 的视频关联关系错误`);
        }
      }
    }
  }
}

/**
 * 清理测试数据
 */
async function cleanupTestData(testInfluencers) {
  const transaction = await sequelize.transaction();

  try {
    // 删除迁移的视频数据
    for (const influencer of testInfluencers) {
      await AuthorVideo.destroy({
        where: { authorId: influencer.id },
        transaction
      });
    }

    // 删除测试达人
    const influencerIds = testInfluencers.map(inf => inf.id);
    await Influencer.destroy({
      where: {
        id: {
          [Op.in]: influencerIds
        }
      },
      transaction
    });

    await transaction.commit();

  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testVideoMigration()
    .then(() => {
      console.log('\n✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试异常:', error);
      process.exit(1);
    });
}

module.exports = { testVideoMigration };
