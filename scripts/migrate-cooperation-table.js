/**
 * 合作对接管理表结构迁移脚本
 * 
 * 功能说明：
 * - 安全地添加新字段到现有表
 * - 保持现有数据不变
 * - 支持回滚操作
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4'
};

// 新字段定义
const newColumnDefinitions = [
  // 客户信息模块
  { name: 'customer_name', definition: 'VARCHAR(100) NULL COMMENT "客户名称"' },
  { name: 'customer_homepage', definition: 'TEXT NULL COMMENT "主页链接"' },
  { name: 'customer_public_sea', definition: 'VARCHAR(100) NULL COMMENT "所属公海"' },
  { name: 'seeding_platform', definition: 'VARCHAR(100) NULL COMMENT "种草平台"' },
  { name: 'blogger_fans_count', definition: 'VARCHAR(100) NULL COMMENT "博主粉丝量"' },
  { name: 'influencer_platform_id', definition: 'VARCHAR(100) NULL COMMENT "达人平台ID（抖音填星图ID，小红书填UID）"' },
  { name: 'blogger_wechat_and_notes', definition: 'TEXT NULL COMMENT "博主微信以及其他备注"' },

  // 协议信息模块 - 合作前信息
  { name: 'title', definition: 'VARCHAR(200) NULL COMMENT "标题（格式：平台-达人昵称-约稿日）"' },
  { name: 'cooperation_form', definition: 'VARCHAR(100) NULL COMMENT "合作形式（后端字典下拉选择）"' },
  { name: 'publish_platform', definition: 'VARCHAR(100) NULL COMMENT "发布平台"' },
  { name: 'cooperation_brand', definition: 'VARCHAR(100) NULL COMMENT "合作品牌（后端字典下拉选择）"' },
  { name: 'cooperation_notes', definition: 'TEXT NULL COMMENT "备注（如坑几、返点金额等）"' },
  { name: 'cooperation_amount', definition: 'VARCHAR(100) NULL COMMENT "合作金额"' },
  { name: 'influencer_commission_rate', definition: 'VARCHAR(50) NULL COMMENT "达人佣金比例（可选）"' },
  { name: 'payee_name', definition: 'VARCHAR(100) NULL COMMENT "收款人姓名"' },
  { name: 'bank_account', definition: 'VARCHAR(100) NULL COMMENT "银行账号"' },
  { name: 'bank_name', definition: 'VARCHAR(200) NULL COMMENT "开户行"' },
  { name: 'rebate_completed', definition: 'VARCHAR(50) NULL COMMENT "如有返点是否完成（后端字典下拉选择）"' },

  // 协议信息模块 - 发布后登记
  { name: 'publish_link', definition: 'TEXT NULL COMMENT "发布链接（要求长链接）"' },
  { name: 'actual_publish_date', definition: 'DATE NULL COMMENT "实际发布日期"' },
  { name: 'data_registration_date', definition: 'DATE NULL COMMENT "数据登记日期（发布十日内登记）"' },
  { name: 'view_count', definition: 'INT NULL COMMENT "观看量（数字类型，用于帖子详情获取）"' },
  { name: 'like_count', definition: 'INT NULL COMMENT "点赞数（数字类型，用于帖子详情获取）"' },
  { name: 'collect_count', definition: 'INT NULL COMMENT "收藏数（数字类型，用于帖子详情获取）"' },
  { name: 'comment_count', definition: 'INT NULL COMMENT "评论数（数字类型，用于帖子详情获取）"' },
  { name: 'content_implant_coefficient', definition: 'VARCHAR(50) NULL COMMENT "内容植入系数（后端字典下拉选择）"' },
  { name: 'comment_maintenance_coefficient', definition: 'VARCHAR(50) NULL COMMENT "评论区维护系数（后端字典下拉选择）"' },
  { name: 'brand_topic_included', definition: 'VARCHAR(50) NULL COMMENT "是否加入品牌话题或产品词（后端字典下拉选择）"' },
  { name: 'self_evaluation', definition: 'VARCHAR(100) NULL COMMENT "自评（付费笔记与置换爆文自评，后端字典下拉选择）"' },

  // CRM集成关联字段
  { name: 'external_customer_id', definition: 'VARCHAR(100) NULL COMMENT "外部客户ID（关联CRM系统客户）"' },
  { name: 'external_agreement_id', definition: 'VARCHAR(100) NULL COMMENT "外部协议ID（关联CRM系统协议）"' },
  { name: 'crm_link_status', definition: 'ENUM("unlinked", "customer_linked", "fully_linked") DEFAULT "unlinked" COMMENT "CRM关联状态（未关联/客户已关联/完全关联）"' },

  // 达人提报关联字段
  { name: 'influencer_report_id', definition: 'INT NULL COMMENT "关联的达人提报记录ID"' }
];

async function migrateCooperationTable() {
  let connection;
  
  try {
    console.log('🔄 开始合作对接管理表结构迁移...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查表是否存在
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'cooperation_management'"
    );

    if (tables.length === 0) {
      console.log('❌ cooperation_management 表不存在，无法迁移');
      return;
    }

    // 获取现有字段
    const [existingColumns] = await connection.execute(
      'DESCRIBE cooperation_management'
    );
    const existingFieldNames = existingColumns.map(col => col.Field);

    console.log('📋 开始添加新字段...');
    
    let addedCount = 0;
    let skippedCount = 0;

    for (const column of newColumnDefinitions) {
      if (existingFieldNames.includes(column.name)) {
        console.log(`⏭️ 字段 ${column.name} 已存在，跳过`);
        skippedCount++;
        continue;
      }

      try {
        const alterSQL = `ALTER TABLE cooperation_management ADD COLUMN ${column.name} ${column.definition}`;
        await connection.execute(alterSQL);
        console.log(`✅ 添加字段: ${column.name}`);
        addedCount++;
      } catch (error) {
        console.error(`❌ 添加字段 ${column.name} 失败:`, error.message);
      }
    }

    console.log(`📊 迁移完成: 新增 ${addedCount} 个字段，跳过 ${skippedCount} 个字段`);

    // 验证迁移结果
    const [finalColumns] = await connection.execute(
      'DESCRIBE cooperation_management'
    );
    console.log(`📋 迁移后表结构字段数: ${finalColumns.length}`);

    // 检查数据完整性
    const [dataCount] = await connection.execute(
      'SELECT COUNT(*) as count FROM cooperation_management'
    );
    console.log(`📊 数据完整性检查: ${dataCount[0].count} 条记录保持完整`);

    console.log('🎉 合作对接管理表结构迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行迁移
migrateCooperationTable();
