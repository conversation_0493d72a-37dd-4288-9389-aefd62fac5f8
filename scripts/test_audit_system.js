/**
 * 达人提报审核系统测试脚本
 * 
 * 功能说明：
 * 1. 测试审核流程的完整性
 * 2. 验证权限控制
 * 3. 检查数据完整性
 * 4. 测试状态变更
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const axios = require('axios');

class AuditSystemTester {
  constructor() {
    this.baseURL = 'http://localhost:3001/api';
    this.adminToken = null;
    this.userToken = null;
    this.testReportId = null;
    this.testResults = [];
  }

  async runTests() {
    try {
      console.log('🚀 开始达人提报审核系统测试...\n');

      // 1. 登录测试
      await this.testLogin();

      // 2. 创建测试提报
      await this.testCreateReport();

      // 3. 测试审核功能
      await this.testAuditFunctions();

      // 4. 测试重新提报功能
      await this.testResubmitFunction();

      // 5. 生成测试报告
      this.generateTestReport();

    } catch (error) {
      console.error('❌ 测试失败:', error.message);
    }
  }

  async testLogin() {
    console.log('📋 测试1: 用户登录');
    
    try {
      // 管理员登录
      const adminResponse = await axios.post(`${this.baseURL}/auth/login`, {
        username: 'testadmin',
        password: '123456'
      });

      if (adminResponse.data.success) {
        this.adminToken = adminResponse.data.data.token;
        console.log('✅ 管理员登录成功');
        this.testResults.push({ test: '管理员登录', result: '成功' });
      } else {
        throw new Error('管理员登录失败');
      }

      // 普通用户登录（如果有的话）
      try {
        const userResponse = await axios.post(`${this.baseURL}/auth/login`, {
          username: 'user',
          password: 'user123'
        });

        if (userResponse.data.success) {
          this.userToken = userResponse.data.data.token;
          console.log('✅ 普通用户登录成功');
          this.testResults.push({ test: '普通用户登录', result: '成功' });
        }
      } catch (error) {
        console.log('ℹ️ 普通用户不存在，跳过普通用户测试');
        this.testResults.push({ test: '普通用户登录', result: '跳过' });
      }

    } catch (error) {
      console.error('❌ 登录测试失败:', error.message);
      this.testResults.push({ test: '用户登录', result: '失败' });
      throw error;
    }

    console.log('');
  }

  async testCreateReport() {
    console.log('📋 测试2: 创建测试提报');

    try {
      const reportData = {
        platform: 'xiaohongshu',
        influencerName: '测试达人_' + Date.now(),
        operationManager: '测试运营',
        selectionReason: '这是一个测试提报，用于验证审核系统功能',
        platformUserId: 'test_' + Date.now(),
        followersCount: 10000,
        playMid: '5000',
        platformPrice: '1000元',
        cooperationPrice: '800元',
        notes: '测试备注信息'
      };

      const response = await axios.post(`${this.baseURL}/influencer-reports`, reportData, {
        headers: {
          'Authorization': `Bearer ${this.adminToken}`
        }
      });

      if (response.data.success) {
        this.testReportId = response.data.data.id;
        console.log('✅ 测试提报创建成功，ID:', this.testReportId);
        console.log('✅ 初始状态:', response.data.data.status);
        this.testResults.push({ test: '创建测试提报', result: '成功' });
      } else {
        throw new Error('创建提报失败');
      }

    } catch (error) {
      console.error('❌ 创建提报测试失败:', error.message);
      this.testResults.push({ test: '创建测试提报', result: '失败' });
      throw error;
    }

    console.log('');
  }

  async testAuditFunctions() {
    console.log('📋 测试3: 审核功能测试');

    if (!this.testReportId) {
      console.error('❌ 没有测试提报ID，跳过审核测试');
      return;
    }

    try {
      // 测试审核拒绝
      console.log('🔍 测试审核拒绝...');
      const rejectResponse = await axios.put(
        `${this.baseURL}/influencer-reports/${this.testReportId}/reject`,
        { reviewComment: '测试审核拒绝意见' },
        {
          headers: {
            'Authorization': `Bearer ${this.adminToken}`
          }
        }
      );

      if (rejectResponse.data.success) {
        console.log('✅ 审核拒绝成功');
        console.log('✅ 状态变更为:', rejectResponse.data.data.status);
        console.log('✅ 审核意见:', rejectResponse.data.data.reviewComment);
        this.testResults.push({ test: '审核拒绝', result: '成功' });
      } else {
        throw new Error('审核拒绝失败');
      }

    } catch (error) {
      console.error('❌ 审核功能测试失败:', error.message);
      this.testResults.push({ test: '审核功能', result: '失败' });
    }

    console.log('');
  }

  async testResubmitFunction() {
    console.log('📋 测试4: 重新提报功能');

    if (!this.testReportId) {
      console.error('❌ 没有测试提报ID，跳过重新提报测试');
      return;
    }

    try {
      // 测试重新提报
      console.log('🔍 测试重新提报...');
      const resubmitResponse = await axios.put(
        `${this.baseURL}/influencer-reports/${this.testReportId}/resubmit`,
        { resubmitReason: '根据审核意见进行了修改，重新提报' },
        {
          headers: {
            'Authorization': `Bearer ${this.adminToken}`
          }
        }
      );

      if (resubmitResponse.data.success) {
        console.log('✅ 重新提报成功');
        console.log('✅ 状态变更为:', resubmitResponse.data.data.status);
        console.log('✅ 重新提报次数:', resubmitResponse.data.data.resubmitCount);
        this.testResults.push({ test: '重新提报', result: '成功' });

        // 测试审核通过
        console.log('🔍 测试审核通过...');
        const approveResponse = await axios.put(
          `${this.baseURL}/influencer-reports/${this.testReportId}/approve`,
          { reviewComment: '测试审核通过意见' },
          {
            headers: {
              'Authorization': `Bearer ${this.adminToken}`
            }
          }
        );

        if (approveResponse.data.success) {
          console.log('✅ 审核通过成功');
          console.log('✅ 最终状态:', approveResponse.data.data.status);
          this.testResults.push({ test: '审核通过', result: '成功' });
        }

      } else {
        throw new Error('重新提报失败');
      }

    } catch (error) {
      console.error('❌ 重新提报功能测试失败:', error.message);
      this.testResults.push({ test: '重新提报功能', result: '失败' });
    }

    console.log('');
  }

  generateTestReport() {
    console.log('📊 测试报告');
    console.log('=====================================');
    
    const totalTests = this.testResults.length;
    const successTests = this.testResults.filter(r => r.result === '成功').length;
    const failedTests = this.testResults.filter(r => r.result === '失败').length;
    const skippedTests = this.testResults.filter(r => r.result === '跳过').length;

    console.log(`总测试数: ${totalTests}`);
    console.log(`成功: ${successTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`跳过: ${skippedTests}`);
    console.log(`成功率: ${Math.round((successTests / (totalTests - skippedTests)) * 100)}%`);
    
    console.log('\n详细结果:');
    this.testResults.forEach((result, index) => {
      const icon = result.result === '成功' ? '✅' : result.result === '失败' ? '❌' : 'ℹ️';
      console.log(`${index + 1}. ${icon} ${result.test}: ${result.result}`);
    });

    console.log('\n🎉 测试完成！');
    
    if (failedTests === 0) {
      console.log('🎊 所有测试通过，审核系统运行正常！');
    } else {
      console.log('⚠️ 部分测试失败，请检查系统配置');
    }
  }
}

// 主执行函数
async function runAuditSystemTest() {
  const tester = new AuditSystemTester();
  await tester.runTests();
}

// 执行测试
if (require.main === module) {
  runAuditSystemTest();
}

module.exports = { AuditSystemTester, runAuditSystemTest };
