/**
 * 测试 status 字段修复是否有效
 */

const { sequelize } = require('../src/config/database');
const { PublicInfluencer } = require('../src/models');

async function testStatusFix() {
  try {
    console.log('🧪 开始测试 status 字段修复...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 查看当前的字段定义
    console.log('📋 查看当前字段定义...');
    const [currentStatus] = await sequelize.query(`
      SELECT 
        COLUMN_TYPE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'daren_db' 
        AND TABLE_NAME = 'public_influencers'
        AND COLUMN_NAME = 'status'
    `);
    
    console.log('当前字段定义:', currentStatus[0]);
    
    // 测试查询一个记录
    console.log('🔍 测试查询记录...');
    const testRecord = await PublicInfluencer.findOne({
      limit: 1,
      order: [['id', 'DESC']]
    });
    
    if (testRecord) {
      console.log('测试记录:', {
        id: testRecord.id,
        nickname: testRecord.nickname,
        status: testRecord.status
      });
      
      // 测试更新状态为 imported
      console.log('🔄 测试更新状态为 imported...');
      const originalStatus = testRecord.status;
      
      await testRecord.update({
        status: 'imported'
      });
      
      console.log('✅ 状态更新成功！');
      
      // 恢复原状态
      await testRecord.update({
        status: originalStatus
      });
      
      console.log('🔄 已恢复原状态');
      
    } else {
      console.log('⚠️ 没有找到测试记录');
    }
    
    console.log('🎉 测试完成！status 字段修复有效');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
    process.exit(0);
  }
}

// 执行测试
testStatusFix();
