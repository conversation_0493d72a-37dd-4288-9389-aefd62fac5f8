/**
 * 数据库重置脚本
 * 警告：此操作会删除所有数据！
 */

const { sequelize } = require('../src/config/database');
const fs = require('fs');
const path = require('path');

async function resetDatabase() {
  console.log('⚠️  警告：即将重置数据库，这会删除所有现有数据！');
  console.log('🚀 开始重置数据库...\n');

  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 读取 init.sql 文件
    const initSqlPath = path.join(__dirname, '../sql/init.sql');
    const initSql = fs.readFileSync(initSqlPath, 'utf8');

    console.log('📋 读取初始化脚本成功');

    // 分割 SQL 语句（按分号分割，但忽略注释行）
    const sqlStatements = initSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📋 共找到 ${sqlStatements.length} 条 SQL 语句`);

    // 执行每条 SQL 语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      
      if (statement.trim()) {
        try {
          console.log(`📝 执行语句 ${i + 1}/${sqlStatements.length}...`);
          await sequelize.query(statement);
        } catch (error) {
          // 忽略一些常见的无害错误
          if (error.message.includes('database exists') || 
              error.message.includes('table exists') ||
              error.message.includes('Duplicate entry')) {
            console.log(`ℹ️  跳过: ${error.message.split('\n')[0]}`);
          } else {
            console.error(`❌ 执行失败: ${statement.substring(0, 100)}...`);
            throw error;
          }
        }
      }
    }

    console.log('\n🎉 数据库重置完成！');
    
    // 验证表是否创建成功
    console.log('\n📋 验证表结构...');
    
    const [tables] = await sequelize.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
      ORDER BY TABLE_NAME
    `);

    console.log('✅ 已创建的表:');
    tables.forEach(table => {
      console.log(`   - ${table.TABLE_NAME}`);
    });

    // 验证关键字段是否存在
    console.log('\n📋 验证关键字段...');
    
    try {
      const [myInfluencersColumns] = await sequelize.query(`
        SHOW COLUMNS FROM my_influencers 
        WHERE Field IN ('content_theme', 'influencer_tags')
      `);
      
      const [publicInfluencersColumns] = await sequelize.query(`
        SHOW COLUMNS FROM public_influencers 
        WHERE Field IN ('content_theme', 'influencer_tags')
      `);

      console.log(`✅ my_influencers 新字段: ${myInfluencersColumns.map(col => col.Field).join(', ')}`);
      console.log(`✅ public_influencers 新字段: ${publicInfluencersColumns.map(col => col.Field).join(', ')}`);
      
    } catch (error) {
      console.log('ℹ️  字段验证跳过（表可能不存在）');
    }

  } catch (error) {
    console.error('❌ 数据库重置失败:', error.message);
    throw error;
  } finally {
    await sequelize.close();
    console.log('\n📋 数据库连接已关闭');
  }
}

// 执行重置
if (require.main === module) {
  resetDatabase()
    .then(() => {
      console.log('\n✅ 数据库重置完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 数据库重置失败:', error.message);
      process.exit(1);
    });
}

module.exports = resetDatabase;
