/**
 * 验证脚本：检查达人表结构是否包含视频字段
 */

const { sequelize } = require('../src/config/database');

async function verifyTableStructure() {
  try {
    console.log('🔄 开始验证表结构...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 查看表结构
    console.log('📋 查看 influencers 表结构:');
    const [results] = await sequelize.query(`DESCRIBE influencers`);
    
    console.log('\n字段列表:');
    console.log('----------------------------------------');
    results.forEach(field => {
      console.log(`${field.Field.padEnd(20)} | ${field.Type.padEnd(15)} | ${field.Null.padEnd(5)} | ${field.Comment || ''}`);
    });
    
    // 特别检查视频相关字段
    console.log('\n🎥 视频相关字段检查:');
    const videoStatsField = results.find(field => field.Field === 'video_stats');
    const videoDetailsField = results.find(field => field.Field === 'video_details');
    
    if (videoStatsField) {
      console.log('✅ video_stats 字段存在');
      console.log(`   类型: ${videoStatsField.Type}`);
      console.log(`   注释: ${videoStatsField.Comment || '无'}`);
    } else {
      console.log('❌ video_stats 字段不存在');
    }
    
    if (videoDetailsField) {
      console.log('✅ video_details 字段存在');
      console.log(`   类型: ${videoDetailsField.Type}`);
      console.log(`   注释: ${videoDetailsField.Comment || '无'}`);
    } else {
      console.log('❌ video_details 字段不存在');
    }
    
    // 显示完整的表创建语句
    console.log('\n📄 表创建语句:');
    const [createResults] = await sequelize.query(`SHOW CREATE TABLE influencers`);
    console.log(createResults[0]['Create Table']);
    
    console.log('\n🎉 表结构验证完成！');
    
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行验证
if (require.main === module) {
  verifyTableStructure()
    .then(() => {
      console.log('✅ 验证脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 验证脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = verifyTableStructure;
