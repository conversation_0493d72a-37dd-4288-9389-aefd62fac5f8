/**
 * 验证播放量中位数数据完整性脚本
 * 检查和修复播放量中位数字段的数据完整性
 */

const { sequelize, PublicInfluencer, CrawlTask } = require('../src/models');

async function verifyPlayMidData() {
  try {
    console.log('🔍 开始验证播放量中位数数据完整性...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 统计总体数据情况
    console.log('\n📊 步骤1: 统计总体数据情况');
    
    const totalRecords = await PublicInfluencer.count();
    console.log(`   总记录数: ${totalRecords}`);

    const recordsWithPlayMid = await PublicInfluencer.count({
      where: {
        playMid: {
          [sequelize.Sequelize.Op.not]: null
        }
      }
    });
    console.log(`   有播放量中位数的记录: ${recordsWithPlayMid}`);

    const recordsWithoutPlayMid = totalRecords - recordsWithPlayMid;
    console.log(`   缺少播放量中位数的记录: ${recordsWithoutPlayMid}`);

    const playMidCoverage = totalRecords > 0 ? ((recordsWithPlayMid / totalRecords) * 100).toFixed(2) : 0;
    console.log(`   播放量中位数覆盖率: ${playMidCoverage}%`);

    // 2. 按平台统计
    console.log('\n📊 步骤2: 按平台统计播放量中位数数据');
    
    const platforms = ['xiaohongshu', 'juxingtu'];
    for (const platform of platforms) {
      const platformTotal = await PublicInfluencer.count({
        where: { platform }
      });
      
      const platformWithPlayMid = await PublicInfluencer.count({
        where: {
          platform,
          playMid: {
            [sequelize.Sequelize.Op.not]: null
          }
        }
      });

      const platformCoverage = platformTotal > 0 ? ((platformWithPlayMid / platformTotal) * 100).toFixed(2) : 0;
      console.log(`   ${platform}: ${platformWithPlayMid}/${platformTotal} (${platformCoverage}%)`);
    }

    // 3. 按任务统计
    console.log('\n📊 步骤3: 按任务统计播放量中位数数据');

    const tasksWithResults = await sequelize.query(`
      SELECT
        ct.id,
        ct.task_name as taskName,
        ct.platform,
        ct.status,
        ct.created_at as createdAt,
        COUNT(pi.id) as totalResults,
        SUM(CASE WHEN pi.play_mid IS NOT NULL THEN 1 ELSE 0 END) as resultsWithPlayMid
      FROM crawl_tasks ct
      LEFT JOIN public_influencers pi ON ct.id = pi.task_id
      GROUP BY ct.id
      HAVING COUNT(pi.id) > 0
      ORDER BY ct.created_at DESC
      LIMIT 10
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    console.log('   最近10个有结果的任务:');
    tasksWithResults.forEach(task => {
      const coverage = task.totalResults > 0 ? ((task.resultsWithPlayMid / task.totalResults) * 100).toFixed(2) : 0;
      console.log(`   任务${task.id} (${task.platform}): ${task.resultsWithPlayMid}/${task.totalResults} (${coverage}%) - ${task.taskName}`);
    });

    // 4. 检查数据质量
    console.log('\n🔍 步骤4: 检查播放量中位数数据质量');
    
    // 检查非空但无效的数据
    const invalidPlayMidRecords = await PublicInfluencer.findAll({
      where: {
        playMid: {
          [sequelize.Sequelize.Op.and]: [
            { [sequelize.Sequelize.Op.not]: null },
            { [sequelize.Sequelize.Op.not]: '' }
          ]
        }
      },
      attributes: ['id', 'nickname', 'platform', 'playMid'],
      limit: 10
    });

    console.log(`   检查了前10条有播放量中位数的记录:`);
    invalidPlayMidRecords.forEach(record => {
      const isValid = /^\d+$/.test(record.playMid);
      console.log(`   ID${record.id} (${record.platform}): ${record.nickname} - playMid: "${record.playMid}" ${isValid ? '✅' : '❌'}`);
    });

    // 5. 检查最近的爬取结果
    console.log('\n📋 步骤5: 检查最近的爬取结果');
    
    const recentResults = await PublicInfluencer.findAll({
      attributes: ['id', 'nickname', 'platform', 'playMid', 'createdAt', 'taskId'],
      order: [['createdAt', 'DESC']],
      limit: 20
    });

    console.log('   最近20条爬取结果:');
    recentResults.forEach(result => {
      const hasPlayMid = result.playMid !== null && result.playMid !== '';
      const status = hasPlayMid ? '✅' : '❌';
      console.log(`   ${status} ID${result.id} (任务${result.taskId}): ${result.nickname} - playMid: ${result.playMid || 'null'}`);
    });

    // 6. 生成修复建议
    console.log('\n💡 步骤6: 生成修复建议');
    
    if (recordsWithoutPlayMid > 0) {
      console.log(`   发现 ${recordsWithoutPlayMid} 条记录缺少播放量中位数数据`);
      console.log('   建议修复方案:');
      console.log('   1. 检查爬虫逻辑中播放量中位数API调用是否正常');
      console.log('   2. 检查数据保存逻辑是否正确传递playMid字段');
      console.log('   3. 对于历史数据，可以考虑重新爬取播放量中位数');
      console.log('   4. 检查Cookie是否有效，无效Cookie可能导致API调用失败');
    } else {
      console.log('   ✅ 所有记录都有播放量中位数数据，数据完整性良好');
    }

    // 7. 检查重复数据问题
    console.log('\n🔍 步骤7: 检查重复数据问题');
    
    const duplicateRecords = await sequelize.query(`
      SELECT platform, platform_user_id, COUNT(*) as count, 
             GROUP_CONCAT(id) as ids,
             GROUP_CONCAT(task_id) as task_ids
      FROM public_influencers 
      GROUP BY platform, platform_user_id 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    if (duplicateRecords.length > 0) {
      console.log(`   ⚠️ 发现 ${duplicateRecords.length} 组重复数据:`);
      duplicateRecords.forEach(record => {
        console.log(`   ${record.platform} - ${record.platform_user_id}: ${record.count}条记录 (IDs: ${record.ids}, 任务: ${record.task_ids})`);
      });
      console.log('   建议: 清理重复数据，保留最新的记录');
    } else {
      console.log('   ✅ 未发现重复数据');
    }

    console.log('\n✅ 播放量中位数数据完整性验证完成');
    
    // 返回验证结果摘要
    return {
      totalRecords,
      recordsWithPlayMid,
      recordsWithoutPlayMid,
      playMidCoverage: parseFloat(playMidCoverage),
      duplicateCount: duplicateRecords.length,
      recentResultsCount: recentResults.length,
      recentResultsWithPlayMid: recentResults.filter(r => r.playMid !== null && r.playMid !== '').length
    };

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  verifyPlayMidData()
    .then(result => {
      console.log('\n📊 验证结果摘要:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = verifyPlayMidData;
