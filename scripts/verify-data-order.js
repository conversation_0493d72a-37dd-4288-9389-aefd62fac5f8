/**
 * 验证数据库中达人数据的排序情况
 */

const { sequelize } = require('../src/config/database');

async function verifyDataOrder() {
  try {
    console.log('🔍 开始验证数据排序...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 1. 验证我的达人库数据排序
    console.log('\n📋 我的达人库数据统计：');
    await verifyMyInfluencersOrder();
    
    // 2. 验证达人公海数据排序
    console.log('\n📋 达人公海数据统计：');
    await verifyPublicInfluencersOrder();
    
    console.log('\n🎉 验证完成！');
    
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
    process.exit(0);
  }
}

/**
 * 验证我的达人库数据排序
 */
async function verifyMyInfluencersOrder() {
  // 统计总数
  const [totalCount] = await sequelize.query(`
    SELECT COUNT(*) as total FROM my_influencers
  `);
  
  console.log(`📊 总记录数: ${totalCount[0].total}`);
  
  // 按平台统计
  const [platformStats] = await sequelize.query(`
    SELECT 
      platform,
      COUNT(*) as count
    FROM my_influencers 
    GROUP BY platform
    ORDER BY platform
  `);
  
  console.log('📊 按平台统计:');
  platformStats.forEach(stat => {
    console.log(`   ${stat.platform}: ${stat.count} 条记录`);
  });
  
  // 显示最新的5条记录
  const [latestRecords] = await sequelize.query(`
    SELECT 
      id,
      nickname,
      platform,
      platform_id,
      created_at
    FROM my_influencers 
    ORDER BY created_at DESC
    LIMIT 5
  `);
  
  console.log('📋 最新的5条记录:');
  latestRecords.forEach((record, index) => {
    console.log(`   ${index + 1}. ID: ${record.id}, 昵称: ${record.nickname}, 平台: ${record.platform}, 创建时间: ${record.created_at}`);
  });
}

/**
 * 验证达人公海数据排序
 */
async function verifyPublicInfluencersOrder() {
  // 统计总数
  const [totalCount] = await sequelize.query(`
    SELECT COUNT(*) as total FROM public_influencers
  `);
  
  console.log(`📊 总记录数: ${totalCount[0].total}`);
  
  // 按平台统计
  const [platformStats] = await sequelize.query(`
    SELECT 
      platform,
      COUNT(*) as count
    FROM public_influencers 
    GROUP BY platform
    ORDER BY platform
  `);
  
  console.log('📊 按平台统计:');
  platformStats.forEach(stat => {
    console.log(`   ${stat.platform}: ${stat.count} 条记录`);
  });
  
  // 按状态统计
  const [statusStats] = await sequelize.query(`
    SELECT 
      status,
      COUNT(*) as count
    FROM public_influencers 
    GROUP BY status
    ORDER BY status
  `);
  
  console.log('📊 按状态统计:');
  statusStats.forEach(stat => {
    console.log(`   ${stat.status}: ${stat.count} 条记录`);
  });
  
  // 显示最新的10条记录（按创建时间排序）
  const [latestRecords] = await sequelize.query(`
    SELECT 
      id,
      nickname,
      platform,
      platform_user_id,
      status,
      created_at
    FROM public_influencers 
    ORDER BY created_at DESC
    LIMIT 10
  `);
  
  console.log('📋 最新的10条记录（按创建时间排序）:');
  latestRecords.forEach((record, index) => {
    console.log(`   ${index + 1}. ID: ${record.id}, 昵称: ${record.nickname}, 平台: ${record.platform}, 状态: ${record.status}, 创建时间: ${record.created_at}`);
  });
  
  // 验证是否还有重复的平台ID
  const [duplicateCheck] = await sequelize.query(`
    SELECT 
      platform,
      platform_user_id,
      COUNT(*) as count
    FROM public_influencers 
    WHERE platform_user_id IS NOT NULL AND platform_user_id != ''
    GROUP BY platform, platform_user_id 
    HAVING COUNT(*) > 1
  `);
  
  if (duplicateCheck.length > 0) {
    console.log('⚠️ 仍然存在重复数据:');
    duplicateCheck.forEach(dup => {
      console.log(`   平台: ${dup.platform}, ID: ${dup.platform_user_id}, 重复数: ${dup.count}`);
    });
  } else {
    console.log('✅ 确认无重复数据');
  }
}

// 执行验证
verifyDataOrder();
