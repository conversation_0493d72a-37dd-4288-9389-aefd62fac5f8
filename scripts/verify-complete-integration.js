/**
 * 完整集成功能验证脚本
 * 
 * 功能说明：
 * - 验证达人提报到合作对接记录的完整数据映射
 * - 检查所有字段的映射逻辑
 * - 确保数据流转的完整性
 */

const fs = require('fs');

function verifyCompleteIntegration() {
  console.log('🔄 验证完整集成功能...\n');
  
  let hasErrors = false;
  
  try {
    const reportViewContent = fs.readFileSync('frontend/src/views/InfluencerReportView.vue', 'utf8');
    
    console.log('📋 验证完整的字段映射:');
    
    // 定义所有应该映射的字段
    const fieldMappings = [
      {
        name: '客户名称',
        field: 'customerName',
        source: 'reportData.influencerName',
        description: '达人名称 → 客户名称'
      },
      {
        name: '客户主页',
        field: 'customerHomepage', 
        source: 'reportData.influencerUrl',
        description: '达人链接 → 客户主页'
      },
      {
        name: '种草平台',
        field: 'seedingPlatform',
        source: 'platformMap[reportData.platform]',
        description: '平台类型 → 种草平台（映射转换）'
      },
      {
        name: '博主粉丝量',
        field: 'bloggerFansCount',
        source: 'reportData.followersCount',
        description: '粉丝数量 → 博主粉丝量'
      },
      {
        name: '达人平台ID',
        field: 'influencerPlatformId',
        source: 'reportData.platformUserId',
        description: '平台用户ID → 达人平台ID'
      },
      {
        name: '标题',
        field: 'title',
        source: 'auto-generated',
        description: '自动生成：平台-达人昵称-当前日期'
      },
      {
        name: '发布平台',
        field: 'publishPlatform',
        source: 'platformMap[reportData.platform]',
        description: '平台类型 → 发布平台（映射转换）'
      },
      {
        name: '合作金额',
        field: 'cooperationAmount',
        source: 'reportData.cooperationPrice',
        description: '合作价格 → 合作金额'
      },
      {
        name: '达人提报ID',
        field: 'influencerReportId',
        source: 'reportData.id',
        description: '提报记录ID → 关联字段'
      }
    ];
    
    let mappingComplete = true;
    fieldMappings.forEach(mapping => {
      if (reportViewContent.includes(`${mapping.field}:`)) {
        console.log(`✅ ${mapping.name} (${mapping.field}): 已映射`);
      } else {
        console.log(`❌ ${mapping.name} (${mapping.field}): 未映射`);
        mappingComplete = false;
      }
    });
    
    if (mappingComplete) {
      console.log('✅ 所有关键字段映射完成');
    } else {
      console.log('❌ 部分关键字段映射缺失');
      hasErrors = true;
    }
    
    console.log('\n📋 验证平台映射逻辑:');
    
    // 检查平台映射的一致性
    const platformMappingChecks = [
      {
        field: 'seedingPlatform',
        description: '种草平台使用平台映射'
      },
      {
        field: 'publishPlatform', 
        description: '发布平台使用平台映射'
      },
      {
        field: 'title',
        description: '标题生成使用平台映射'
      }
    ];
    
    let platformMappingConsistent = true;
    platformMappingChecks.forEach(check => {
      const regex = new RegExp(`${check.field}:.*platformMap\\[reportData\\.platform\\]`);
      if (reportViewContent.match(regex)) {
        console.log(`✅ ${check.description}`);
      } else {
        console.log(`❌ ${check.description}`);
        platformMappingConsistent = false;
      }
    });
    
    if (platformMappingConsistent) {
      console.log('✅ 平台映射逻辑一致');
    } else {
      console.log('❌ 平台映射逻辑不一致');
      hasErrors = true;
    }
    
    console.log('\n📋 验证数据组合生成:');
    
    // 检查复杂字段的生成逻辑
    const complexFields = [
      {
        name: '博主微信及备注',
        field: 'bloggerWechatAndNotes',
        contains: ['来源：达人提报', 'selectionReason', 'platformPrice', 'notes'],
        description: '组合生成：来源+选择理由+平台报价+备注'
      },
      {
        name: '合作备注',
        field: 'cooperationNotes',
        contains: ['达人提报ID', 'operationManager', 'lastSubmittedAt'],
        description: '组合生成：提报ID+负责人+提报时间'
      }
    ];
    
    let complexFieldsComplete = true;
    complexFields.forEach(field => {
      const fieldExists = reportViewContent.includes(`${field.field}:`);
      if (fieldExists) {
        const allContainsExist = field.contains.every(contain => 
          reportViewContent.includes(contain)
        );
        if (allContainsExist) {
          console.log(`✅ ${field.name}: ${field.description}`);
        } else {
          console.log(`❌ ${field.name}: 部分组合元素缺失`);
          complexFieldsComplete = false;
        }
      } else {
        console.log(`❌ ${field.name}: 字段未定义`);
        complexFieldsComplete = false;
      }
    });
    
    if (complexFieldsComplete) {
      console.log('✅ 复杂字段生成逻辑完整');
    } else {
      console.log('❌ 复杂字段生成逻辑不完整');
      hasErrors = true;
    }
    
    console.log('\n📋 验证向后兼容性:');
    
    // 检查保留的原有字段
    const legacyFields = [
      'cooperationMonth',
      'platform', 
      'bloggerName',
      'responsiblePerson',
      'influencerHomepage',
      'cooperationPrice'
    ];
    
    let legacyFieldsComplete = true;
    legacyFields.forEach(field => {
      if (reportViewContent.includes(`${field}:`)) {
        console.log(`✅ 保留字段 ${field}: 已映射`);
      } else {
        console.log(`❌ 保留字段 ${field}: 未映射`);
        legacyFieldsComplete = false;
      }
    });
    
    if (legacyFieldsComplete) {
      console.log('✅ 向后兼容性保持完整');
    } else {
      console.log('❌ 向后兼容性不完整');
      hasErrors = true;
    }
    
    console.log('\n📋 验证流程完整性:');
    
    // 检查完整的流程步骤
    const flowSteps = [
      { name: '数据预填充方法', check: 'generateCooperationDataFromReport' },
      { name: '表单弹窗显示', check: 'cooperationFormVisible.value = true' },
      { name: '成功处理回调', check: 'handleCooperationFormSuccess' },
      { name: '关联状态更新', check: 'updateReportCooperationStatus' },
      { name: '数据刷新', check: 'loadData()' },
      { name: '用户提示', check: 'Message.success' }
    ];
    
    let flowComplete = true;
    flowSteps.forEach(step => {
      if (reportViewContent.includes(step.check)) {
        console.log(`✅ ${step.name}: 已实现`);
      } else {
        console.log(`❌ ${step.name}: 未实现`);
        flowComplete = false;
      }
    });
    
    if (flowComplete) {
      console.log('✅ 完整流程已实现');
    } else {
      console.log('❌ 流程实现不完整');
      hasErrors = true;
    }
    
    // 生成示例数据映射
    console.log('\n📋 示例数据映射:');
    console.log('输入（达人提报数据）:');
    console.log('```json');
    console.log(JSON.stringify({
      id: 123,
      platform: 'xiaohongshu',
      influencerName: '美妆达人小红',
      influencerUrl: 'https://www.xiaohongshu.com/user/profile/123456',
      followersCount: 50000,
      platformUserId: 'xhs_123456',
      selectionReason: '粉丝画像匹配，互动率高',
      platformPrice: '3000元',
      cooperationPrice: '5000元',
      operationManager: '张三',
      notes: '优质达人，值得长期合作'
    }, null, 2));
    console.log('```');
    
    console.log('\n输出（合作对接记录数据）:');
    console.log('```json');
    const currentDate = new Date().toISOString().split('T')[0];
    console.log(JSON.stringify({
      customerName: '美妆达人小红',
      customerHomepage: 'https://www.xiaohongshu.com/user/profile/123456',
      seedingPlatform: '小红书',
      bloggerFansCount: '50000',
      influencerPlatformId: 'xhs_123456',
      title: `小红书-美妆达人小红-${currentDate}`,
      publishPlatform: '小红书',
      cooperationAmount: '5000元',
      influencerReportId: 123,
      bloggerWechatAndNotes: '来源：达人提报\\n选择理由：粉丝画像匹配，互动率高\\n平台报价：3000元\\n备注：优质达人，值得长期合作',
      cooperationNotes: '达人提报ID：123\\n运营负责人：张三\\n提报时间：...'
    }, null, 2));
    console.log('```');
    
  } catch (error) {
    console.log(`❌ 验证过程中发生错误: ${error.message}`);
    hasErrors = true;
  }
  
  console.log('\n🎯 验证结果:');
  if (hasErrors) {
    console.log('❌ 发现问题，请检查修改');
    return false;
  } else {
    console.log('✅ 完整集成功能验证通过！');
    console.log('\n🎊 功能特性总结:');
    console.log('• ✅ 9个核心字段完整映射');
    console.log('• ✅ 种草平台智能映射（xiaohongshu→小红书，douyin→抖音）');
    console.log('• ✅ 复杂字段自动组合生成');
    console.log('• ✅ 向后兼容性完整保持');
    console.log('• ✅ 完整的用户交互流程');
    console.log('• ✅ 数据关联和状态管理');
    console.log('\n🚀 系统已准备就绪，可以开始使用新的集成功能！');
    return true;
  }
}

verifyCompleteIntegration();
