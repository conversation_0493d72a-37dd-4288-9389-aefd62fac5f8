/**
 * 编辑界面测试脚本
 * 测试前端编辑功能的安全性
 */

const axios = require('axios');

async function testEditUI() {
  try {
    console.log('🚀 开始测试编辑界面安全性...\n');

    // 1. 登录获取token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'testadmin',
      password: '123456'
    });

    if (!loginResponse.data.success) {
      throw new Error('登录失败');
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    // 2. 获取提报列表
    const listResponse = await axios.get('http://localhost:3001/api/influencer-reports?limit=5', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (!listResponse.data.success || listResponse.data.data.length === 0) {
      throw new Error('没有找到提报记录');
    }

    console.log('✅ 获取到提报列表，共', listResponse.data.data.length, '条记录');

    // 3. 测试不同状态的提报记录
    for (const report of listResponse.data.data) {
      console.log(`\n📋 测试提报记录 ID: ${report.id}`);
      console.log(`  达人名称: ${report.influencerName}`);
      console.log(`  状态: ${report.status}`);
      console.log(`  审核意见: ${report.reviewComment || '无'}`);
      console.log(`  重新提报次数: ${report.resubmitCount || 0}`);
      console.log(`  重新提报说明: ${report.resubmitReason || '无'}`);
      
      // 验证字段是否为null
      const nullFields = [];
      if (report.reviewComment === null) nullFields.push('reviewComment');
      if (report.resubmitReason === null) nullFields.push('resubmitReason');
      if (report.resubmitCount === null) nullFields.push('resubmitCount');
      
      if (nullFields.length > 0) {
        console.log(`  ⚠️ 发现null字段: ${nullFields.join(', ')}`);
      } else {
        console.log(`  ✅ 所有字段都有值或为空字符串`);
      }
    }

    // 4. 测试获取单个提报详情
    const firstReport = listResponse.data.data[0];
    console.log(`\n🔍 测试获取提报详情 ID: ${firstReport.id}`);
    
    const detailResponse = await axios.get(
      `http://localhost:3001/api/influencer-reports/${firstReport.id}`,
      {
        headers: { 'Authorization': `Bearer ${token}` }
      }
    );

    if (detailResponse.data.success) {
      const detail = detailResponse.data.data;
      console.log('✅ 获取详情成功');
      console.log(`  包含提报人信息: ${detail.submitter ? '是' : '否'}`);
      console.log(`  包含审核人信息: ${detail.reviewer ? '是' : '否'}`);
      console.log(`  审核意见字段: ${typeof detail.reviewComment} (${detail.reviewComment})`);
      console.log(`  重新提报说明字段: ${typeof detail.resubmitReason} (${detail.resubmitReason})`);
    } else {
      console.error('❌ 获取详情失败:', detailResponse.data.message);
    }

    console.log('\n🎉 编辑界面安全性测试完成！');
    console.log('💡 前端组件现在应该能够安全处理null值了');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testEditUI();
