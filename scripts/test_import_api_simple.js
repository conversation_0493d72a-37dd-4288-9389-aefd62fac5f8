/**
 * 简单的导入API测试
 * 直接测试导入功能的HTTP接口
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

// 测试获取达人公海列表
async function testGetPublicInfluencers() {
  try {
    console.log('🔄 测试获取达人公海列表...');
    
    const response = await axios.get(`${BASE_URL}/api/public-influencers?page=1&limit=5`);
    
    console.log('✅ 获取达人公海列表成功');
    console.log(`   - 状态码: ${response.status}`);
    console.log(`   - 数据数量: ${response.data.data?.length || 0}`);
    
    if (response.data.data && response.data.data.length > 0) {
      console.log('   - 前3个达人:');
      response.data.data.slice(0, 3).forEach((influencer, index) => {
        console.log(`     ${index + 1}. ${influencer.nickname} (ID: ${influencer.id}) - 状态: ${influencer.status}`);
      });
    }
    
    return response.data.data || [];
  } catch (error) {
    console.error('❌ 获取达人公海列表失败:', error.response?.status, error.response?.data || error.message);
    return [];
  }
}

// 测试单个导入（不需要认证的测试）
async function testSingleImportWithoutAuth(influencerId) {
  try {
    console.log(`🔄 测试单个导入 (ID: ${influencerId})...`);
    
    const response = await axios.post(`${BASE_URL}/api/public-influencers/${influencerId}/import`);
    
    console.log('✅ 单个导入请求成功');
    console.log(`   - 状态码: ${response.status}`);
    console.log(`   - 响应: ${JSON.stringify(response.data, null, 2)}`);
    
    return response.data;
  } catch (error) {
    console.log('❌ 单个导入失败:');
    console.log(`   - 状态码: ${error.response?.status}`);
    console.log(`   - 错误信息: ${JSON.stringify(error.response?.data, null, 2)}`);
    return { success: false, error: error.response?.data || error.message };
  }
}

// 测试批量导入（不需要认证的测试）
async function testBatchImportWithoutAuth(influencerIds) {
  try {
    console.log(`🔄 测试批量导入 (IDs: ${influencerIds.join(', ')})...`);
    
    const response = await axios.post(`${BASE_URL}/api/public-influencers/batch-import`, {
      ids: influencerIds
    });
    
    console.log('✅ 批量导入请求成功');
    console.log(`   - 状态码: ${response.status}`);
    console.log(`   - 响应: ${JSON.stringify(response.data, null, 2)}`);
    
    return response.data;
  } catch (error) {
    console.log('❌ 批量导入失败:');
    console.log(`   - 状态码: ${error.response?.status}`);
    console.log(`   - 错误信息: ${JSON.stringify(error.response?.data, null, 2)}`);
    return { success: false, error: error.response?.data || error.message };
  }
}

// 测试服务器连接
async function testServerConnection() {
  try {
    console.log('🔄 测试服务器连接...');
    
    const response = await axios.get(`${BASE_URL}/api/public-influencers?page=1&limit=1`);
    
    console.log('✅ 服务器连接正常');
    console.log(`   - 状态码: ${response.status}`);
    
    return true;
  } catch (error) {
    console.error('❌ 服务器连接失败:', error.code, error.message);
    return false;
  }
}

// 主测试函数
async function runSimpleTests() {
  try {
    console.log('🧪 开始简单的导入API测试...\n');

    // 1. 测试服务器连接
    console.log('📋 步骤1: 测试服务器连接...');
    const serverOk = await testServerConnection();
    if (!serverOk) {
      console.log('❌ 服务器连接失败，请确保服务器正在运行');
      return;
    }
    console.log('');

    // 2. 获取达人公海列表
    console.log('📋 步骤2: 获取达人公海列表...');
    const publicInfluencers = await testGetPublicInfluencers();
    console.log('');

    if (publicInfluencers.length === 0) {
      console.log('⚠️ 没有达人公海数据可供测试');
      return;
    }

    // 3. 测试单个导入
    console.log('📋 步骤3: 测试单个导入...');
    const firstInfluencer = publicInfluencers[0];
    await testSingleImportWithoutAuth(firstInfluencer.id);
    console.log('');

    // 4. 测试批量导入
    console.log('📋 步骤4: 测试批量导入...');
    const testIds = publicInfluencers.slice(0, 2).map(inf => inf.id);
    await testBatchImportWithoutAuth(testIds);
    console.log('');

    // 5. 测试无效ID
    console.log('📋 步骤5: 测试无效ID导入...');
    await testSingleImportWithoutAuth(99999);
    console.log('');

    // 6. 测试空批量导入
    console.log('📋 步骤6: 测试空批量导入...');
    await testBatchImportWithoutAuth([]);
    console.log('');

    console.log('🎉 所有API测试完成！');
    console.log('✅ 测试结果总结:');
    console.log('   - ✅ 服务器连接正常');
    console.log('   - ✅ 达人公海列表API正常');
    console.log('   - ✅ 单个导入API测试完成');
    console.log('   - ✅ 批量导入API测试完成');
    console.log('   - ✅ 错误处理测试完成');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  runSimpleTests().catch(console.error);
}

module.exports = runSimpleTests;
