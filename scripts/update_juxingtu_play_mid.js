/**
 * 更新巨量星图播放量中位数脚本
 * 为现有的巨量星图达人记录重新获取播放量中位数
 */

const { sequelize, PublicInfluencer } = require('../src/models');
const XingtuCrawler = require('../src/services/crawler/crawlers/XingtuCrawler');

async function updateJuxingtuPlayMid(limit = 20, dryRun = true) {
  try {
    console.log('🔄 开始更新巨量星图播放量中位数...\n');
    console.log(`模式: ${dryRun ? '预览模式（不会实际更新数据）' : '执行模式（会实际更新数据）'}`);
    console.log(`处理限制: ${limit} 条记录\n`);

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 查找需要更新的记录
    console.log('\n📋 步骤1: 查找需要更新的巨量星图记录');
    
    const recordsToUpdate = await PublicInfluencer.findAll({
      where: {
        platform: 'juxingtu',
        playMid: null
      },
      order: [['createdAt', 'DESC']],
      limit: limit,
      attributes: ['id', 'platformUserId', 'nickname', 'playMid', 'createdAt']
    });

    console.log(`找到 ${recordsToUpdate.length} 条需要更新的记录`);

    if (recordsToUpdate.length === 0) {
      console.log('✅ 没有需要更新的记录');
      return { total: 0, updated: 0, failed: 0 };
    }

    // 2. 初始化爬虫
    console.log('\n📋 步骤2: 初始化巨量星图爬虫');
    
    const crawler = new XingtuCrawler();
    await crawler.initialize();
    console.log('✅ 爬虫初始化成功');

    // 3. 批量更新播放量中位数
    console.log('\n📋 步骤3: 批量更新播放量中位数');
    
    let updateCount = 0;
    let failedCount = 0;
    const results = [];

    for (let i = 0; i < recordsToUpdate.length; i++) {
      const record = recordsToUpdate[i];
      console.log(`\n🔍 [${i + 1}/${recordsToUpdate.length}] 处理: ${record.nickname} (${record.platformUserId})`);
      
      try {
        // 获取播放量中位数
        const playMid = await crawler.getAuthorMedianPlay(record.platformUserId);
        
        if (playMid !== null && playMid !== undefined) {
          console.log(`   ✅ 获取成功: ${playMid}`);
          
          if (!dryRun) {
            // 更新数据库记录
            await record.update({ playMid: playMid });
            console.log(`   💾 数据库更新成功`);
          } else {
            console.log(`   💡 预览模式: 将更新为 ${playMid}`);
          }
          
          updateCount++;
          results.push({
            id: record.id,
            nickname: record.nickname,
            platformUserId: record.platformUserId,
            oldPlayMid: record.playMid,
            newPlayMid: playMid,
            status: 'success'
          });
        } else {
          console.log(`   ⚠️ 该达人没有播放量中位数数据`);
          results.push({
            id: record.id,
            nickname: record.nickname,
            platformUserId: record.platformUserId,
            oldPlayMid: record.playMid,
            newPlayMid: null,
            status: 'no_data'
          });
        }
        
      } catch (error) {
        console.log(`   ❌ 获取失败: ${error.message}`);
        failedCount++;
        results.push({
          id: record.id,
          nickname: record.nickname,
          platformUserId: record.platformUserId,
          oldPlayMid: record.playMid,
          newPlayMid: null,
          status: 'error',
          error: error.message
        });
      }
      
      // 添加延迟避免请求过于频繁
      if (i < recordsToUpdate.length - 1) {
        const delay = 2000 + Math.random() * 2000; // 2-4秒随机延迟
        console.log(`   ⏳ 等待 ${Math.round(delay)}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // 4. 统计结果
    console.log('\n📊 步骤4: 统计更新结果');
    
    const successResults = results.filter(r => r.status === 'success');
    const noDataResults = results.filter(r => r.status === 'no_data');
    const errorResults = results.filter(r => r.status === 'error');

    console.log(`\n📋 更新结果摘要:`);
    console.log(`   总处理记录: ${recordsToUpdate.length}`);
    console.log(`   成功获取播放量中位数: ${successResults.length}`);
    console.log(`   达人无播放量中位数: ${noDataResults.length}`);
    console.log(`   获取失败: ${errorResults.length}`);

    if (successResults.length > 0) {
      console.log(`\n✅ 成功更新的记录:`);
      successResults.forEach(result => {
        console.log(`   ID${result.id}: ${result.nickname} - ${result.newPlayMid}`);
      });
    }

    if (errorResults.length > 0) {
      console.log(`\n❌ 失败的记录:`);
      errorResults.forEach(result => {
        console.log(`   ID${result.id}: ${result.nickname} - ${result.error}`);
      });
    }

    // 5. 验证更新结果
    if (!dryRun && successResults.length > 0) {
      console.log('\n📋 步骤5: 验证更新结果');
      
      const updatedRecords = await PublicInfluencer.findAll({
        where: {
          id: successResults.map(r => r.id)
        },
        attributes: ['id', 'nickname', 'playMid']
      });

      console.log(`验证结果:`);
      updatedRecords.forEach(record => {
        const expected = successResults.find(r => r.id === record.id);
        const isCorrect = record.playMid == expected.newPlayMid;
        console.log(`   ID${record.id}: ${record.nickname} - ${record.playMid} ${isCorrect ? '✅' : '❌'}`);
      });
    }

    console.log('\n✅ 巨量星图播放量中位数更新完成');
    
    return {
      total: recordsToUpdate.length,
      updated: successResults.length,
      noData: noDataResults.length,
      failed: errorResults.length,
      results: results
    };

  } catch (error) {
    console.error('❌ 更新过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const args = process.argv.slice(2);
  const executeMode = args.includes('--execute');
  const limitArg = args.find(arg => arg.startsWith('--limit='));
  const limit = limitArg ? parseInt(limitArg.split('=')[1]) : 20;
  
  updateJuxingtuPlayMid(limit, !executeMode)
    .then(result => {
      console.log('\n📊 更新结果摘要:', result);
      if (!executeMode) {
        console.log('\n💡 如需实际执行更新，请使用参数 --execute');
        console.log('💡 如需指定处理数量，请使用参数 --limit=数量');
      }
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = updateJuxingtuPlayMid;
