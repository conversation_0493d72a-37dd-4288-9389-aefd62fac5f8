/**
 * 视频数据迁移脚本
 * 将达人表(influencers)中的videoDetails字段数据迁移到独立的author_videos表
 * 
 * 功能特性：
 * - 检测和统计需要迁移的数据
 * - 批量迁移视频数据到author_videos表
 * - 数据验证和完整性检查
 * - 安全的清理操作
 * - 详细的迁移报告
 * - 事务安全和回滚机制
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { initializeAndSyncDatabase, Influencer, AuthorVideo, sequelize } = require('../src/models');
const { Op } = require('sequelize');
const AuthorVideoService = require('../src/services/AuthorVideoService');

class VideoDetailsMigrator {
  constructor(options = {}) {
    this.batchSize = 10; // 批量处理大小，避免长时间锁表
    this.autoDropColumn = options.autoDropColumn || false; // 是否自动删除字段
    this.migrationReport = {
      totalInfluencers: 0,
      influencersWithVideos: 0,
      totalVideosToMigrate: 0,
      successfulMigrations: 0,
      failedMigrations: 0,
      totalVideosMigrated: 0,
      errors: [],
      columnDropped: false,
      startTime: null,
      endTime: null
    };
  }

  /**
   * 执行完整的迁移流程
   */
  async executeMigration() {
    try {
      console.log('🚀 开始视频数据迁移流程...\n');
      this.migrationReport.startTime = new Date();

      // 1. 初始化数据库
      await this.initializeDatabase();

      // 2. 检测和统计数据
      await this.detectAndAnalyzeData();

      // 3. 确认是否继续迁移
      if (this.migrationReport.influencersWithVideos === 0) {
        console.log('✅ 没有需要迁移的视频数据，迁移完成。\n');
        return this.migrationReport;
      }

      const shouldContinue = await this.confirmMigration();
      if (!shouldContinue) {
        console.log('❌ 用户取消迁移操作。\n');
        return this.migrationReport;
      }

      // 4. 执行数据迁移
      await this.migrateVideoData();

      // 5. 验证迁移结果
      await this.validateMigration();

      // 6. 清理冗余数据
      await this.cleanupRedundantData();

      // 7. 删除videoDetails字段（可选）
      await this.dropVideoDetailsColumn();

      // 8. 生成最终报告
      this.generateFinalReport();

      console.log('🎉 视频数据迁移流程完成！\n');
      return this.migrationReport;

    } catch (error) {
      console.error('❌ 迁移流程失败:', error.message);
      this.migrationReport.errors.push({
        stage: 'migration_process',
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    } finally {
      this.migrationReport.endTime = new Date();
    }
  }

  /**
   * 初始化数据库
   */
  async initializeDatabase() {
    console.log('📋 步骤1: 初始化数据库...');
    await initializeAndSyncDatabase();
    console.log('✅ 数据库初始化完成\n');
  }

  /**
   * 检测和分析需要迁移的数据
   */
  async detectAndAnalyzeData() {
    console.log('📋 步骤2: 检测和分析数据...');

    try {
      // 统计总达人数
      this.migrationReport.totalInfluencers = await Influencer.count();
      console.log(`📊 总达人数: ${this.migrationReport.totalInfluencers}`);

      // 查找有videoDetails数据的达人
      const influencersWithVideos = await Influencer.findAll({
        where: {
          videoDetails: {
            [Op.ne]: null
          }
        },
        attributes: ['id', 'nickname', 'platform', 'platformId', 'videoDetails']
      });

      this.migrationReport.influencersWithVideos = influencersWithVideos.length;
      console.log(`📊 有视频数据的达人数: ${this.migrationReport.influencersWithVideos}`);

      // 统计总视频数量
      let totalVideos = 0;
      for (const influencer of influencersWithVideos) {
        if (influencer.videoDetails && Array.isArray(influencer.videoDetails)) {
          totalVideos += influencer.videoDetails.length;
        }
      }

      this.migrationReport.totalVideosToMigrate = totalVideos;
      console.log(`📊 需要迁移的视频总数: ${this.migrationReport.totalVideosToMigrate}`);

      // 显示详细信息
      if (influencersWithVideos.length > 0) {
        console.log('\n📋 详细信息:');
        influencersWithVideos.forEach((influencer, index) => {
          const videoCount = Array.isArray(influencer.videoDetails) ? influencer.videoDetails.length : 0;
          console.log(`  ${index + 1}. ${influencer.nickname} (${influencer.platform}): ${videoCount} 个视频`);
        });
      }

      console.log('✅ 数据分析完成\n');

    } catch (error) {
      console.error('❌ 数据分析失败:', error.message);
      throw error;
    }
  }

  /**
   * 确认是否继续迁移
   */
  async confirmMigration() {
    console.log('📋 步骤3: 迁移确认...');
    console.log('⚠️ 即将执行以下操作:');
    console.log(`   - 迁移 ${this.migrationReport.influencersWithVideos} 个达人的视频数据`);
    console.log(`   - 总共 ${this.migrationReport.totalVideosToMigrate} 个视频将被迁移到 author_videos 表`);
    console.log(`   - 迁移完成后将清空 influencers 表的 videoDetails 字段`);
    console.log('   - 此操作不可逆，请确保已备份重要数据\n');

    // 在实际环境中，这里可以添加用户交互确认
    // 为了自动化测试，这里默认返回true
    console.log('✅ 确认继续迁移操作\n');
    return true;
  }

  /**
   * 执行数据迁移
   */
  async migrateVideoData() {
    console.log('📋 步骤4: 执行数据迁移...');

    try {
      // 获取需要迁移的达人
      const influencersWithVideos = await Influencer.findAll({
        where: {
          videoDetails: {
            [Op.ne]: null
          }
        },
        attributes: ['id', 'nickname', 'platform', 'platformId', 'videoDetails']
      });

      // 分批处理
      for (let i = 0; i < influencersWithVideos.length; i += this.batchSize) {
        const batch = influencersWithVideos.slice(i, i + this.batchSize);
        console.log(`🔄 处理第 ${Math.floor(i / this.batchSize) + 1} 批达人 (${i + 1}-${Math.min(i + this.batchSize, influencersWithVideos.length)}/${influencersWithVideos.length})`);

        await this.processBatch(batch);

        // 批次间短暂延迟
        if (i + this.batchSize < influencersWithVideos.length) {
          await this.delay(1000);
        }
      }

      console.log('✅ 数据迁移完成\n');

    } catch (error) {
      console.error('❌ 数据迁移失败:', error.message);
      throw error;
    }
  }

  /**
   * 处理单个批次的达人
   */
  async processBatch(influencers) {
    for (const influencer of influencers) {
      await this.migrateInfluencerVideos(influencer);
    }
  }

  /**
   * 迁移单个达人的视频数据
   */
  async migrateInfluencerVideos(influencer) {
    const transaction = await sequelize.transaction();

    try {
      console.log(`📹 迁移达人 ${influencer.nickname} (ID: ${influencer.id}) 的视频数据...`);

      // 验证videoDetails数据
      if (!influencer.videoDetails || !Array.isArray(influencer.videoDetails)) {
        console.log(`⚠️ 达人 ${influencer.nickname} 的videoDetails数据格式无效，跳过`);
        await transaction.rollback();
        return;
      }

      const videos = influencer.videoDetails;
      if (videos.length === 0) {
        console.log(`⚠️ 达人 ${influencer.nickname} 没有视频数据，跳过`);
        await transaction.rollback();
        return;
      }

      // 转换视频数据格式
      const formattedVideos = this.formatVideoData(videos, influencer);

      // 批量保存视频数据
      let successCount = 0;
      let failCount = 0;

      for (const video of formattedVideos) {
        try {
          // 检查视频是否已存在
          const existingVideo = await AuthorVideo.findOne({
            where: {
              platform: influencer.platform,
              platformUserId: influencer.platformId,
              videoId: video.videoId
            },
            transaction
          });

          if (existingVideo) {
            console.log(`⏭️ 视频 ${video.videoId} 已存在，跳过`);
            continue;
          }

          // 创建新视频记录
          await AuthorVideo.create({
            authorId: influencer.id,
            platform: influencer.platform,
            platformUserId: influencer.platformId,
            ...video,
            status: 'active',
            crawlTaskId: null, // 迁移的数据没有关联的爬虫任务
            lastUpdated: new Date()
          }, { transaction });

          successCount++;

        } catch (videoError) {
          console.error(`❌ 保存视频 ${video.videoId} 失败:`, videoError.message);
          failCount++;
          this.migrationReport.errors.push({
            stage: 'video_migration',
            influencerId: influencer.id,
            videoId: video.videoId,
            error: videoError.message,
            timestamp: new Date()
          });
        }
      }

      // 提交事务
      await transaction.commit();

      console.log(`✅ 达人 ${influencer.nickname} 迁移完成: 成功 ${successCount}, 失败 ${failCount}`);

      // 更新统计
      this.migrationReport.successfulMigrations++;
      this.migrationReport.totalVideosMigrated += successCount;

    } catch (error) {
      await transaction.rollback();
      console.error(`❌ 迁移达人 ${influencer.nickname} 失败:`, error.message);
      
      this.migrationReport.failedMigrations++;
      this.migrationReport.errors.push({
        stage: 'influencer_migration',
        influencerId: influencer.id,
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  /**
   * 格式化视频数据
   */
  formatVideoData(videos, influencer) {
    return videos.map(video => ({
      videoId: video.videoId || `migrated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: video.title || '未知标题',
      videoUrl: video.videoUrl || null,
      videoCover: video.videoCover || video.item_cover || null,
      duration: parseInt(video.duration) || 0,
      publishTime: video.publishTime ? new Date(video.publishTime) : (video.item_date ? new Date(video.item_date) : null),
      likeCount: parseInt(video.likeCount || video.like) || 0,
      playCount: parseInt(video.playCount || video.play) || 0,
      shareCount: parseInt(video.shareCount || video.share) || 0,
      commentCount: parseInt(video.commentCount || video.comment) || 0,
      collectCount: parseInt(video.collectCount) || 0,
      tags: video.tags || null,
      description: video.description || null,
      location: video.location || null,
      musicInfo: video.musicInfo || null,
      videoStats: video.videoStats || null,
      rawData: video
    }));
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    console.log('📋 步骤5: 验证迁移结果...');

    try {
      // 获取已迁移的达人
      const influencersWithVideos = await Influencer.findAll({
        where: {
          videoDetails: {
            [Op.ne]: null
          }
        },
        attributes: ['id', 'nickname', 'videoDetails']
      });

      let validationErrors = 0;

      for (const influencer of influencersWithVideos) {
        if (!influencer.videoDetails || !Array.isArray(influencer.videoDetails)) {
          continue;
        }

        // 统计原始视频数量
        const originalVideoCount = influencer.videoDetails.length;

        // 统计迁移后的视频数量
        const migratedVideoCount = await AuthorVideo.count({
          where: { authorId: influencer.id }
        });

        if (originalVideoCount !== migratedVideoCount) {
          console.log(`⚠️ 达人 ${influencer.nickname} 视频数量不匹配: 原始 ${originalVideoCount}, 迁移 ${migratedVideoCount}`);
          validationErrors++;
        } else {
          console.log(`✅ 达人 ${influencer.nickname} 验证通过: ${migratedVideoCount} 个视频`);
        }
      }

      if (validationErrors === 0) {
        console.log('✅ 迁移结果验证通过\n');
      } else {
        console.log(`⚠️ 发现 ${validationErrors} 个验证错误\n`);
      }

    } catch (error) {
      console.error('❌ 迁移验证失败:', error.message);
      throw error;
    }
  }

  /**
   * 清理冗余数据
   */
  async cleanupRedundantData() {
    console.log('📋 步骤6: 清理冗余数据...');

    try {
      // 获取已成功迁移的达人ID列表
      const migratedInfluencerIds = await this.getMigratedInfluencerIds();

      if (migratedInfluencerIds.length === 0) {
        console.log('⚠️ 没有需要清理的数据\n');
        return;
      }

      console.log(`🧹 准备清理 ${migratedInfluencerIds.length} 个达人的videoDetails字段...`);

      // 分批清理videoDetails字段
      const transaction = await sequelize.transaction();

      try {
        const updateResult = await Influencer.update(
          { videoDetails: null },
          {
            where: {
              id: {
                [Op.in]: migratedInfluencerIds
              }
            },
            transaction
          }
        );

        await transaction.commit();

        console.log(`✅ 成功清理 ${updateResult[0]} 个达人的videoDetails字段`);
        console.log('✅ 冗余数据清理完成\n');

      } catch (error) {
        await transaction.rollback();
        throw error;
      }

    } catch (error) {
      console.error('❌ 数据清理失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取已成功迁移的达人ID列表
   */
  async getMigratedInfluencerIds() {
    try {
      // 查找在author_videos表中有记录的达人ID
      const migratedAuthorIds = await AuthorVideo.findAll({
        attributes: [[sequelize.fn('DISTINCT', sequelize.col('author_id')), 'authorId']],
        raw: true
      });

      return migratedAuthorIds.map(item => item.authorId);

    } catch (error) {
      console.error('❌ 获取已迁移达人ID失败:', error.message);
      return [];
    }
  }

  /**
   * 删除videoDetails字段
   */
  async dropVideoDetailsColumn() {
    console.log('📋 步骤7: 删除videoDetails字段...');

    try {
      // 检查是否还有未清理的videoDetails数据
      const remainingData = await Influencer.count({
        where: {
          videoDetails: {
            [Op.ne]: null
          }
        }
      });

      if (remainingData > 0) {
        console.log(`⚠️ 发现 ${remainingData} 个达人仍有videoDetails数据，跳过字段删除`);
        console.log('💡 建议先解决迁移失败的数据，再手动删除字段\n');
        return;
      }

      console.log('🔍 检查videoDetails字段是否存在...');

      // 检查字段是否存在
      const [results] = await sequelize.query(`
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'influencers'
        AND COLUMN_NAME = 'video_details'
      `);

      if (results.length === 0) {
        console.log('ℹ️ videoDetails字段不存在，跳过删除操作\n');
        return;
      }

      if (this.autoDropColumn) {
        console.log('⚠️ 即将删除influencers表的videoDetails字段');
        console.log('⚠️ 此操作不可逆，请确保数据已完全迁移');
        console.log('⚠️ 建议在执行前再次备份数据库\n');

        console.log('🗑️ 正在删除videoDetails字段...');
        await sequelize.query('ALTER TABLE influencers DROP COLUMN video_details');
        console.log('✅ videoDetails字段删除成功\n');

        // 更新迁移报告
        this.migrationReport.columnDropped = true;
      } else {
        console.log('🔒 为了安全起见，字段删除需要手动执行');
        console.log('💡 如需删除字段，请使用以下命令:');
        console.log('   node scripts/migrate_video_details_to_author_videos.js --drop-column');
        console.log('💡 或手动执行SQL:');
        console.log('   ALTER TABLE influencers DROP COLUMN video_details;');
        console.log('');

        // 更新迁移报告
        this.migrationReport.columnDropped = false;
      }

    } catch (error) {
      console.error('❌ 删除videoDetails字段失败:', error.message);
      console.log('💡 可以稍后手动执行: ALTER TABLE influencers DROP COLUMN video_details;\n');
    }
  }

  /**
   * 生成最终报告
   */
  generateFinalReport() {
    console.log('📋 步骤7: 生成迁移报告...');

    const duration = this.migrationReport.endTime - this.migrationReport.startTime;
    const durationMinutes = Math.round(duration / 1000 / 60 * 100) / 100;

    console.log('\n📊 ===== 视频数据迁移报告 =====');
    console.log(`⏱️ 迁移耗时: ${durationMinutes} 分钟`);
    console.log(`📈 总达人数: ${this.migrationReport.totalInfluencers}`);
    console.log(`📹 有视频数据的达人: ${this.migrationReport.influencersWithVideos}`);
    console.log(`🎬 需要迁移的视频总数: ${this.migrationReport.totalVideosToMigrate}`);
    console.log(`✅ 成功迁移的达人: ${this.migrationReport.successfulMigrations}`);
    console.log(`❌ 失败的达人: ${this.migrationReport.failedMigrations}`);
    console.log(`🎯 实际迁移的视频数: ${this.migrationReport.totalVideosMigrated}`);
    console.log(`📊 迁移成功率: ${this.migrationReport.influencersWithVideos > 0 ? Math.round(this.migrationReport.successfulMigrations / this.migrationReport.influencersWithVideos * 100) : 0}%`);
    console.log(`🗑️ videoDetails字段删除: ${this.migrationReport.columnDropped ? '✅ 已删除' : '❌ 未删除'}`);

    if (this.migrationReport.errors.length > 0) {
      console.log(`\n⚠️ 错误详情 (${this.migrationReport.errors.length} 个):`);
      this.migrationReport.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. [${error.stage}] ${error.error}`);
        if (error.influencerId) {
          console.log(`     达人ID: ${error.influencerId}`);
        }
        if (error.videoId) {
          console.log(`     视频ID: ${error.videoId}`);
        }
      });
    }

    console.log('\n🎉 迁移报告生成完成！');
  }

  /**
   * 延迟函数
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 回滚迁移（紧急情况使用）
   */
  async rollbackMigration() {
    console.log('🔄 开始回滚迁移...');

    try {
      const transaction = await sequelize.transaction();

      // 删除所有迁移的视频数据（没有crawlTaskId的记录）
      const deleteResult = await AuthorVideo.destroy({
        where: {
          crawlTaskId: null
        },
        transaction
      });

      await transaction.commit();

      console.log(`✅ 回滚完成，删除了 ${deleteResult} 条迁移的视频记录`);

    } catch (error) {
      console.error('❌ 回滚失败:', error.message);
      throw error;
    }
  }
}

// 主执行函数
async function runMigration(options = {}) {
  const migrator = new VideoDetailsMigrator(options);

  try {
    const report = await migrator.executeMigration();

    console.log('\n✅ 迁移流程完成');
    process.exit(0);

  } catch (error) {
    console.error('\n❌ 迁移流程失败:', error.message);
    console.error('💡 如需回滚，请运行: node scripts/migrate_video_details_to_author_videos.js --rollback');
    process.exit(1);
  }
}

// 命令行参数处理
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes('--rollback')) {
    // 执行回滚
    const migrator = new VideoDetailsMigrator();
    migrator.rollbackMigration()
      .then(() => {
        console.log('\n✅ 回滚完成');
        process.exit(0);
      })
      .catch((error) => {
        console.error('\n❌ 回滚失败:', error);
        process.exit(1);
      });
  } else {
    // 解析命令行参数
    const options = {
      autoDropColumn: args.includes('--drop-column') || args.includes('--auto-drop')
    };

    if (options.autoDropColumn) {
      console.log('⚠️ 启用自动删除videoDetails字段功能');
      console.log('⚠️ 请确保已备份数据库\n');
    }

    // 执行正常迁移
    runMigration(options);
  }
}

module.exports = VideoDetailsMigrator;
