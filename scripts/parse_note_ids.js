/**
 * 批量解析合作记录的noteId脚本
 *
 * 为现有的合作记录从publishLink中解析出noteId
 */

const { CooperationManagement } = require('../src/models');
const { Op } = require('sequelize');

class NoteIdParser {
  constructor() {
    this.processedCount = 0;
    this.successCount = 0;
    this.failedCount = 0;
    this.skippedCount = 0;
  }

  /**
   * 从小红书链接中提取noteId
   */
  extractNoteIdFromXiaohongshuLink(publishLink) {
    if (!publishLink) return null;

    try {
      // 小红书链接格式示例：
      // https://www.xiaohongshu.com/explore/64a1b2c3d4e5f6789abcdef0
      // https://www.xiaohongshu.com/discovery/item/64a1b2c3d4e5f6789abcdef0

      const patterns = [
        /xiaohongshu\.com\/explore\/([a-f0-9]{24})/i,
        /xiaohongshu\.com\/discovery\/item\/([a-f0-9]{24})/i,
        /xhslink\.com.*?\/([a-f0-9]{24})/i
      ];

      for (const pattern of patterns) {
        const match = publishLink.match(pattern);
        if (match && match[1]) {
          return match[1];
        }
      }

      return null;
    } catch (error) {
      console.error('解析链接失败:', error.message);
      return null;
    }
  }

  /**
   * 批量处理合作记录
   */
  async processRecords() {
    try {
      console.log('🔄 开始批量解析noteId...\n');

      // 获取所有没有noteId但有publishLink的记录
      const records = await CooperationManagement.findAll({
        where: {
          noteId: null,
          publishLink: {
            [Op.ne]: null,
            [Op.ne]: ''
          }
        },
        attributes: ['id', 'customerName', 'publishLink', 'platform'],
        limit: 10000 // 先处理100条测试
      });

      console.log(`📋 找到 ${records.length} 条需要处理的记录\n`);

      if (records.length === 0) {
        console.log('✅ 没有需要处理的记录');
        return;
      }

      // 逐条处理
      for (const record of records) {
        await this.processRecord(record);
      }

      this.printSummary();
    } catch (error) {
      console.error('❌ 批量处理失败:', error.message);
    }
  }

  /**
   * 处理单条记录
   */
  async processRecord(record) {
    try {
      this.processedCount++;

      console.log(`处理第 ${this.processedCount} 条: ${record.customerName}`);
      console.log(`  链接: ${record.publishLink}`);

      // 根据平台类型解析noteId
      let noteId = null;

      if (record.platform === 'xiaohongshu' || record.publishLink.includes('xiaohongshu.com')) {
        noteId = this.extractNoteIdFromXiaohongshuLink(record.publishLink);
      }

      if (noteId) {
        // 更新记录
        await CooperationManagement.update(
          {
            noteId: noteId,
            platform: record.platform || 'xiaohongshu' // 确保平台字段有值
          },
          {
            where: { id: record.id }
          }
        );

        console.log(`  ✅ 成功解析: ${noteId}`);
        this.successCount++;
      } else {
        console.log(`  ❌ 解析失败: 无法从链接中提取noteId`);
        this.failedCount++;
      }

      console.log(''); // 空行分隔
    } catch (error) {
      console.error(`  ❌ 处理记录失败: ${error.message}`);
      this.failedCount++;
    }
  }

  /**
   * 打印处理结果汇总
   */
  printSummary() {
    console.log('📊 批量解析结果汇总:');
    console.log('='.repeat(40));
    console.log(`总处理数: ${this.processedCount}`);
    console.log(`成功解析: ${this.successCount} ✅`);
    console.log(`解析失败: ${this.failedCount} ❌`);
    console.log(`跳过记录: ${this.skippedCount} ⏭️`);
    console.log(
      `成功率: ${this.processedCount > 0 ? ((this.successCount / this.processedCount) * 100).toFixed(1) : 0}%`
    );

    if (this.successCount > 0) {
      console.log('\n🎉 部分记录已成功解析noteId，现在可以在任务管理面板中看到这些任务了！');
      console.log('💡 建议：刷新任务管理面板页面查看更新后的数据');
    }

    if (this.failedCount > 0) {
      console.log('\n⚠️  部分记录解析失败，可能的原因：');
      console.log('   - 发布链接格式不标准');
      console.log('   - 链接中不包含有效的noteId');
      console.log('   - 链接格式未被识别（需要更新解析规则）');
    }
  }

  /**
   * 预览模式：只显示会被处理的记录，不实际修改
   */
  async previewRecords() {
    try {
      console.log('👀 预览模式：查看将要处理的记录...\n');

      const records = await CooperationManagement.findAll({
        where: {
          noteId: null,
          publishLink: {
            [Op.ne]: null,
            [Op.ne]: ''
          }
        },
        attributes: ['id', 'customerName', 'publishLink', 'platform'],
        limit: 10
      });

      console.log(`📋 预览前10条记录:\n`);

      records.forEach((record, index) => {
        const noteId = this.extractNoteIdFromXiaohongshuLink(record.publishLink);
        console.log(`${index + 1}. 客户: ${record.customerName}`);
        console.log(`   链接: ${record.publishLink}`);
        console.log(`   预期noteId: ${noteId || '无法解析'} ${noteId ? '✅' : '❌'}`);
        console.log('');
      });
    } catch (error) {
      console.error('❌ 预览失败:', error.message);
    }
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const isPreview = args.includes('--preview');

async function main() {
  const parser = new NoteIdParser();

  if (isPreview) {
    await parser.previewRecords();
  } else {
    await parser.processRecords();
  }

  process.exit(0);
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = NoteIdParser;
