/**
 * CRM用户绑定功能测试脚本
 * 
 * 功能说明：
 * 1. 测试CRM用户查询接口
 * 2. 测试用户CRM绑定功能
 * 3. 测试用户列表中CRM信息显示
 * 4. 测试认证接口中CRM信息返回
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const axios = require('axios');
const { User } = require('../src/models');

// API基础配置
const API_BASE_URL = 'http://localhost:3001/api';
let authToken = '';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(config => {
  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

// 响应拦截器
api.interceptors.response.use(
  response => response.data,
  error => {
    console.error('API请求失败:', error.response?.data || error.message);
    throw error;
  }
);

/**
 * 管理员登录获取token
 */
async function adminLogin() {
  try {
    console.log('🔐 正在进行管理员登录...');
    const response = await api.post('/auth/login', {
      username: 'admin',
      password: 'admin123456'
    });
    
    if (response.success && response.data.token) {
      authToken = response.data.token;
      console.log('✅ 管理员登录成功');
      return true;
    } else {
      console.error('❌ 管理员登录失败:', response.message);
      return false;
    }
  } catch (error) {
    console.error('❌ 管理员登录异常:', error.message);
    return false;
  }
}

/**
 * 测试CRM用户查询接口
 */
async function testCrmUserSearch() {
  console.log('\n🔍 测试CRM用户查询接口...');
  
  const testNames = ['杨子曦', '张三', '李四'];
  
  for (const userName of testNames) {
    try {
      console.log(`  查询用户: ${userName}`);
      const response = await api.get('/crm-integration/users/search', {
        params: { userName, page: 1, pageSize: 20 }
      });
      
      if (response.success) {
        console.log(`    ✅ 查询成功，找到 ${response.data.length} 个用户`);
        response.data.forEach((user, index) => {
          console.log(`      ${index + 1}. ${user.userName} (${user.departmentName}) - ID: ${user.userId}`);
        });
      } else {
        console.log(`    ❌ 查询失败: ${response.message}`);
      }
    } catch (error) {
      console.log(`    ❌ 查询异常: ${error.message}`);
    }
  }
}

/**
 * 测试用户CRM绑定功能
 */
async function testUserCrmBinding() {
  console.log('\n🔗 测试用户CRM绑定功能...');
  
  try {
    // 首先获取一个测试用户
    const usersResponse = await api.get('/users', { params: { page: 1, limit: 5 } });
    
    if (!usersResponse.success || usersResponse.data.length === 0) {
      console.log('  ❌ 没有可用的测试用户');
      return;
    }
    
    const testUser = usersResponse.data[0];
    console.log(`  使用测试用户: ${testUser.username} (ID: ${testUser.id})`);
    
    // 模拟CRM用户数据
    const mockCrmUser = {
      crmUserId: '216865175626190590',
      crmUserPicUrl: 'https://static-legacy.dingtalk.com/media/lADPDgQ9q26Z93LNAfTNAfT.jpg',
      crmDepartment: 'crm_1670637779344',
      crmDepartmentName: '宋朝种草',
      crmDataId: 43978
    };
    
    // 测试绑定CRM用户
    console.log('  绑定CRM用户...');
    const bindResponse = await api.put(`/users/${testUser.id}/bind-crm`, mockCrmUser);
    
    if (bindResponse.success) {
      console.log('  ✅ CRM用户绑定成功');
      console.log(`    - CRM用户ID: ${bindResponse.data.crmUserId}`);
      console.log(`    - CRM部门: ${bindResponse.data.crmDepartmentName}`);
      console.log(`    - CRM头像: ${bindResponse.data.crmUserPicUrl ? '已设置' : '未设置'}`);
      
      // 验证绑定结果
      const userDetailResponse = await api.get(`/users/${testUser.id}`);
      if (userDetailResponse.success) {
        const updatedUser = userDetailResponse.data;
        console.log('  📊 绑定验证:');
        console.log(`    - CRM用户ID: ${updatedUser.crmUserId}`);
        console.log(`    - CRM部门名称: ${updatedUser.crmDepartmentName}`);
        console.log(`    - CRM数据ID: ${updatedUser.crmDataId}`);
      }
      
      return testUser.id;
    } else {
      console.log(`  ❌ CRM用户绑定失败: ${bindResponse.message}`);
      return null;
    }
  } catch (error) {
    console.log(`  ❌ CRM用户绑定异常: ${error.message}`);
    return null;
  }
}

/**
 * 测试用户解绑CRM功能
 */
async function testUserCrmUnbinding(userId) {
  console.log('\n🔓 测试用户CRM解绑功能...');
  
  if (!userId) {
    console.log('  ⚠️ 没有可用的测试用户ID，跳过解绑测试');
    return;
  }
  
  try {
    console.log(`  解绑用户ID: ${userId}`);
    const unbindResponse = await api.put(`/users/${userId}/unbind-crm`);
    
    if (unbindResponse.success) {
      console.log('  ✅ CRM用户解绑成功');
      
      // 验证解绑结果
      const userDetailResponse = await api.get(`/users/${userId}`);
      if (userDetailResponse.success) {
        const updatedUser = userDetailResponse.data;
        console.log('  📊 解绑验证:');
        console.log(`    - CRM用户ID: ${updatedUser.crmUserId || '已清空'}`);
        console.log(`    - CRM部门名称: ${updatedUser.crmDepartmentName || '已清空'}`);
        console.log(`    - CRM数据ID: ${updatedUser.crmDataId || '已清空'}`);
      }
    } else {
      console.log(`  ❌ CRM用户解绑失败: ${unbindResponse.message}`);
    }
  } catch (error) {
    console.log(`  ❌ CRM用户解绑异常: ${error.message}`);
  }
}

/**
 * 测试用户列表中CRM信息显示
 */
async function testUserListWithCrm() {
  console.log('\n📋 测试用户列表中CRM信息显示...');
  
  try {
    const response = await api.get('/users', { params: { page: 1, limit: 10 } });
    
    if (response.success) {
      console.log(`  ✅ 获取用户列表成功，共 ${response.data.length} 个用户`);
      
      // 检查CRM字段
      const usersWithCrm = response.data.filter(user => user.crmUserId);
      console.log(`  📊 其中 ${usersWithCrm.length} 个用户已绑定CRM`);
      
      // 显示部分用户信息
      response.data.slice(0, 3).forEach(user => {
        console.log(`    - ${user.username} (${user.chineseName || '无中文名'})`);
        console.log(`      CRM用户ID: ${user.crmUserId || '未绑定'}`);
        console.log(`      CRM部门: ${user.crmDepartmentName || '未绑定'}`);
        console.log(`      CRM头像: ${user.crmUserPicUrl ? '已设置' : '未设置'}`);
      });
    } else {
      console.log(`  ❌ 获取用户列表失败: ${response.message}`);
    }
  } catch (error) {
    console.log(`  ❌ 获取用户列表异常: ${error.message}`);
  }
}

/**
 * 验证数据库字段
 */
async function validateDatabaseFields() {
  console.log('\n🗄️ 验证数据库CRM字段...');
  
  try {
    // 检查CRM相关字段是否存在
    const [results] = await User.sequelize.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME IN ('crm_user_id', 'crm_user_pic_url', 'crm_department', 'crm_department_name', 'crm_data_id')
      ORDER BY ORDINAL_POSITION
    `);
    
    if (results.length > 0) {
      console.log('  ✅ CRM相关字段存在:');
      results.forEach(field => {
        console.log(`    - ${field.COLUMN_NAME}: ${field.DATA_TYPE} (${field.COLUMN_COMMENT})`);
      });
    } else {
      console.log('  ❌ CRM相关字段不存在');
    }
    
    // 检查现有用户数据
    const userCount = await User.count();
    const usersWithCrm = await User.count({
      where: {
        crmUserId: { [User.sequelize.Op.ne]: null }
      }
    });
    
    console.log(`  📊 数据库统计:`);
    console.log(`    - 总用户数: ${userCount}`);
    console.log(`    - 已绑定CRM的用户: ${usersWithCrm}`);
    
  } catch (error) {
    console.log(`  ❌ 数据库验证异常: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始CRM用户绑定功能测试\n');
  
  try {
    // 1. 数据库验证
    await validateDatabaseFields();
    
    // 2. 管理员登录
    const loginSuccess = await adminLogin();
    if (!loginSuccess) {
      console.log('❌ 无法获取管理员权限，测试终止');
      return;
    }
    
    // 3. 测试CRM用户查询接口
    await testCrmUserSearch();
    
    // 4. 测试用户CRM绑定功能
    const testUserId = await testUserCrmBinding();
    
    // 5. 测试用户列表中CRM信息显示
    await testUserListWithCrm();
    
    // 6. 测试用户解绑CRM功能
    await testUserCrmUnbinding(testUserId);
    
    console.log('\n🎉 CRM用户绑定功能测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生异常:', error);
  } finally {
    // 确保数据库连接关闭
    try {
      await User.sequelize.close();
      console.log('🔌 数据库连接已关闭');
    } catch (error) {
      console.error('❌ 关闭数据库连接失败:', error.message);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testCrmUserSearch,
  testUserCrmBinding,
  testUserCrmUnbinding,
  testUserListWithCrm,
  validateDatabaseFields
};
