/**
 * 测试任务恢复功能
 * 验证数据库字段和恢复逻辑是否正常工作
 */

const { sequelize, CrawlTask } = require('../src/models');

async function testResumeTask() {
  try {
    console.log('🧪 开始测试任务恢复功能...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 查找测试任务
    console.log('\n📋 步骤1: 查找测试任务');
    
    const task = await CrawlTask.findByPk(17);
    if (!task) {
      console.log('❌ 任务17不存在');
      return;
    }
    
    console.log('✅ 找到任务17:', {
      id: task.id,
      taskName: task.taskName,
      status: task.status,
      pausedAt: task.pausedAt,
      resumedAt: task.resumedAt
    });

    // 2. 测试暂停操作
    console.log('\n📋 步骤2: 测试暂停操作');
    
    await task.update({
      status: 'paused',
      pausedAt: new Date()
    });
    
    await task.reload();
    console.log('✅ 任务暂停成功:', {
      status: task.status,
      pausedAt: task.pausedAt
    });

    // 3. 测试恢复操作
    console.log('\n📋 步骤3: 测试恢复操作');
    
    await task.update({
      status: 'running',
      resumedAt: new Date(),
      pausedAt: null
    });
    
    await task.reload();
    console.log('✅ 任务恢复成功:', {
      status: task.status,
      pausedAt: task.pausedAt,
      resumedAt: task.resumedAt
    });

    // 4. 验证字段类型
    console.log('\n📋 步骤4: 验证字段类型');
    
    console.log('字段类型验证:');
    console.log(`   pausedAt: ${typeof task.pausedAt} (${task.pausedAt})`);
    console.log(`   resumedAt: ${typeof task.resumedAt} (${task.resumedAt})`);
    console.log(`   resumedAt instanceof Date: ${task.resumedAt instanceof Date}`);

    // 5. 测试查询功能
    console.log('\n📋 步骤5: 测试查询功能');
    
    const pausedTasks = await CrawlTask.findAll({
      where: { status: 'paused' },
      attributes: ['id', 'taskName', 'status', 'pausedAt'],
      limit: 5
    });
    
    console.log(`找到 ${pausedTasks.length} 个暂停的任务:`);
    pausedTasks.forEach(t => {
      console.log(`   ID${t.id}: ${t.taskName} - 暂停于 ${t.pausedAt}`);
    });

    const runningTasks = await CrawlTask.findAll({
      where: { status: 'running' },
      attributes: ['id', 'taskName', 'status', 'resumedAt'],
      limit: 5
    });
    
    console.log(`找到 ${runningTasks.length} 个运行中的任务:`);
    runningTasks.forEach(t => {
      console.log(`   ID${t.id}: ${t.taskName} - 恢复于 ${t.resumedAt || '未恢复'}`);
    });

    console.log('\n✅ 任务恢复功能测试完成');
    
    return {
      success: true,
      taskId: task.id,
      finalStatus: task.status,
      pausedAt: task.pausedAt,
      resumedAt: task.resumedAt
    };

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testResumeTask()
    .then(result => {
      console.log('\n📊 测试结果:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = testResumeTask;
