#!/usr/bin/env node

/**
 * MCP服务连接测试脚本
 * 用于验证MCP服务是否正常运行并可以接受外部连接
 */

const fetch = require('node-fetch');
const EventSource = require('eventsource');

class MCPConnectionTester {
  constructor(serverUrl = 'http://localhost:3000') {
    this.serverUrl = serverUrl;
    this.sessionId = null;
    this.eventSource = null;
    this.testResults = {
      healthCheck: false,
      agentConnect: false,
      sseConnection: false,
      toolExecution: false,
      disconnect: false
    };
  }

  async runAllTests() {
    console.log('🧪 开始MCP服务连接测试\n');
    console.log(`📍 测试服务器: ${this.serverUrl}\n`);

    try {
      // 1. 健康检查
      await this.testHealthCheck();
      
      // 2. Agent连接测试
      await this.testAgentConnect();
      
      // 3. SSE连接测试
      await this.testSSEConnection();
      
      // 4. 工具执行测试
      await this.testToolExecution();
      
      // 5. 断开连接测试
      await this.testDisconnect();
      
      // 显示测试结果
      this.showResults();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error.message);
      this.showResults();
      process.exit(1);
    }
  }

  async testHealthCheck() {
    console.log('1️⃣ 测试健康检查...');
    
    try {
      const response = await fetch(`${this.serverUrl}/health`, {
        timeout: 5000
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('   ✅ 健康检查通过');
        console.log(`   📊 服务状态: ${data.message}`);
        this.testResults.healthCheck = true;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.log('   ❌ 健康检查失败:', error.message);
      throw error;
    }
    
    console.log('');
  }

  async testAgentConnect() {
    console.log('2️⃣ 测试Agent连接...');
    
    try {
      const connectData = {
        agentId: 'test_agent_001',
        agentName: '连接测试Agent',
        version: '1.0.0',
        capabilities: ['crawler', 'data_analysis'],
        metadata: {
          description: '用于测试MCP连接的Agent',
          testMode: true
        }
      };

      const response = await fetch(`${this.serverUrl}/api/mcp/agent/connect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(connectData),
        timeout: 10000
      });

      if (response.ok) {
        const result = await response.json();
        
        if (result.success && result.data.sessionId) {
          this.sessionId = result.data.sessionId;
          console.log('   ✅ Agent连接成功');
          console.log(`   🔑 Session ID: ${this.sessionId}`);
          console.log(`   🛠️  可用工具数量: ${result.data.tools.length}`);
          console.log(`   📡 SSE端点: ${result.data.sseEndpoint}`);
          this.testResults.agentConnect = true;
        } else {
          throw new Error('连接响应格式错误');
        }
      } else {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
    } catch (error) {
      console.log('   ❌ Agent连接失败:', error.message);
      throw error;
    }
    
    console.log('');
  }

  async testSSEConnection() {
    console.log('3️⃣ 测试SSE连接...');
    
    if (!this.sessionId) {
      console.log('   ❌ 无Session ID，跳过SSE测试');
      return;
    }

    return new Promise((resolve, reject) => {
      const sseUrl = `${this.serverUrl}/api/mcp/agent/events/${this.sessionId}`;
      console.log(`   🔗 连接SSE: ${sseUrl}`);
      
      this.eventSource = new EventSource(sseUrl);
      
      const timeout = setTimeout(() => {
        console.log('   ❌ SSE连接超时');
        this.eventSource.close();
        reject(new Error('SSE连接超时'));
      }, 10000);

      this.eventSource.onopen = () => {
        console.log('   ✅ SSE连接建立成功');
      };

      this.eventSource.addEventListener('connected', (event) => {
        clearTimeout(timeout);
        const data = JSON.parse(event.data);
        console.log('   📨 收到连接确认事件:', data.message);
        this.testResults.sseConnection = true;
        console.log('');
        resolve();
      });

      this.eventSource.onerror = (error) => {
        clearTimeout(timeout);
        console.log('   ❌ SSE连接错误:', error.message || 'Unknown error');
        this.eventSource.close();
        reject(error);
      };
    });
  }

  async testToolExecution() {
    console.log('4️⃣ 测试工具执行...');
    
    if (!this.sessionId) {
      console.log('   ❌ 无Session ID，跳过工具测试');
      return;
    }

    try {
      // 测试获取任务状态工具（使用一个不存在的任务ID）
      const executeData = {
        sessionId: this.sessionId,
        tool: 'get_task_status',
        arguments: {
          taskId: 999999 // 使用一个不存在的任务ID进行测试
        },
        requestId: 'test_req_001'
      };

      const response = await fetch(`${this.serverUrl}/api/mcp/agent/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(executeData),
        timeout: 15000
      });

      if (response.ok) {
        const result = await response.json();
        console.log('   ✅ 工具执行成功');
        console.log(`   📋 工具: ${executeData.tool}`);
        console.log(`   📊 执行结果: ${result.success ? '成功' : '失败（预期）'}`);
        
        // 即使工具执行失败（因为任务不存在），只要能正常调用就算测试通过
        this.testResults.toolExecution = true;
      } else {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
    } catch (error) {
      console.log('   ❌ 工具执行失败:', error.message);
      throw error;
    }
    
    console.log('');
  }

  async testDisconnect() {
    console.log('5️⃣ 测试断开连接...');
    
    try {
      // 关闭SSE连接
      if (this.eventSource) {
        this.eventSource.close();
        console.log('   🔌 SSE连接已关闭');
      }

      // 发送断开连接请求
      if (this.sessionId) {
        const response = await fetch(`${this.serverUrl}/api/mcp/agent/disconnect`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            sessionId: this.sessionId
          }),
          timeout: 5000
        });

        if (response.ok) {
          console.log('   ✅ 断开连接成功');
          this.testResults.disconnect = true;
        } else {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
      }
    } catch (error) {
      console.log('   ❌ 断开连接失败:', error.message);
      // 断开连接失败不算致命错误
    }
    
    console.log('');
  }

  showResults() {
    console.log('📊 测试结果汇总:');
    console.log('================');
    
    const tests = [
      { name: '健康检查', key: 'healthCheck' },
      { name: 'Agent连接', key: 'agentConnect' },
      { name: 'SSE连接', key: 'sseConnection' },
      { name: '工具执行', key: 'toolExecution' },
      { name: '断开连接', key: 'disconnect' }
    ];

    let passedCount = 0;
    
    tests.forEach((test, index) => {
      const status = this.testResults[test.key];
      const icon = status ? '✅' : '❌';
      console.log(`${index + 1}. ${test.name}: ${icon} ${status ? '通过' : '失败'}`);
      if (status) passedCount++;
    });

    console.log('================');
    console.log(`总计: ${passedCount}/${tests.length} 项测试通过`);
    
    if (passedCount === tests.length) {
      console.log('🎉 所有测试通过！MCP服务运行正常，可以接受外部连接。');
    } else if (passedCount >= 3) {
      console.log('⚠️  部分测试通过，MCP服务基本可用，但可能存在一些问题。');
    } else {
      console.log('❌ 多项测试失败，MCP服务可能存在问题，请检查服务状态。');
    }

    console.log('\n📋 建议的下一步操作:');
    
    if (!this.testResults.healthCheck) {
      console.log('- 检查MCP服务是否正在运行');
      console.log('- 确认服务器地址和端口是否正确');
    }
    
    if (!this.testResults.agentConnect) {
      console.log('- 检查MCP Agent路由是否正确配置');
      console.log('- 查看服务器端日志获取详细错误信息');
    }
    
    if (!this.testResults.sseConnection) {
      console.log('- 检查SSE事件服务是否正常启动');
      console.log('- 确认防火墙设置允许SSE连接');
    }
    
    if (passedCount >= 3) {
      console.log('- 可以开始使用MCP客户端进行开发');
      console.log('- 参考 docs/MCP_CLIENT_EXAMPLES.md 获取更多示例');
    }
  }
}

// 命令行参数处理
function parseArgs() {
  const args = process.argv.slice(2);
  let serverUrl = 'http://localhost:3000';
  
  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--server' || args[i] === '-s') {
      serverUrl = args[i + 1];
      i++;
    } else if (args[i] === '--help' || args[i] === '-h') {
      console.log('MCP服务连接测试脚本');
      console.log('');
      console.log('用法: node test-mcp-connection.js [选项]');
      console.log('');
      console.log('选项:');
      console.log('  -s, --server <url>  指定MCP服务器地址 (默认: http://localhost:3000)');
      console.log('  -h, --help          显示帮助信息');
      console.log('');
      console.log('示例:');
      console.log('  node test-mcp-connection.js');
      console.log('  node test-mcp-connection.js --server http://*************:3000');
      process.exit(0);
    }
  }
  
  return { serverUrl };
}

// 主函数
async function main() {
  const { serverUrl } = parseArgs();
  const tester = new MCPConnectionTester(serverUrl);
  await tester.runAllTests();
}

// 运行测试
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 测试脚本执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = MCPConnectionTester;
