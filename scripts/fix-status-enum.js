/**
 * 修复远程数据库中 public_influencers 表的 status 字段 ENUM 值
 * 添加 'collected' 状态到 ENUM 中
 */

const { sequelize } = require('../src/config/database');

async function fixStatusEnum() {
  try {
    console.log('🔧 开始修复 public_influencers 表的 status 字段...');

    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 先查看当前的字段定义
    console.log('📋 查看当前字段定义...');
    const [currentStatus] = await sequelize.query(`
      SELECT
        COLUMN_TYPE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = 'daren_db'
        AND TABLE_NAME = 'public_influencers'
        AND COLUMN_NAME = 'status'
    `);

    console.log('当前字段定义:', currentStatus[0]);

    // 检查表是否有锁定或正在使用
    console.log('🔍 检查表状态...');
    const [processlist] = await sequelize.query('SHOW PROCESSLIST');
    console.log('当前数据库连接数:', processlist.length);

    // 使用更安全的方式修改ENUM，先添加临时列
    console.log('🔄 第一步：添加临时列...');
    await sequelize.query(`
      ALTER TABLE public_influencers
      ADD COLUMN status_new ENUM('pending', 'processed', 'collected', 'failed', 'imported')
      DEFAULT 'pending'
      COMMENT '新的处理状态'
    `);

    console.log('🔄 第二步：复制数据到临时列...');
    await sequelize.query(`
      UPDATE public_influencers
      SET status_new = status
    `);

    console.log('🔄 第三步：删除原列...');
    await sequelize.query(`
      ALTER TABLE public_influencers
      DROP COLUMN status
    `);

    console.log('🔄 第四步：重命名临时列...');
    await sequelize.query(`
      ALTER TABLE public_influencers
      CHANGE COLUMN status_new status ENUM('pending', 'processed', 'collected', 'failed', 'imported')
      DEFAULT 'pending'
      COMMENT '处理状态：pending-待处理，processed-已处理，collected-已收藏，failed-失败，imported-已导入'
    `);

    console.log('✅ status 字段 ENUM 值修复成功');

    // 验证修改结果
    console.log('🔍 验证修改结果...');
    const [results] = await sequelize.query(`
      SELECT
        TABLE_NAME,
        COLUMN_NAME,
        COLUMN_TYPE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = 'daren_db'
        AND TABLE_NAME = 'public_influencers'
        AND COLUMN_NAME = 'status'
    `);

    console.log('📊 修改后的字段信息:');
    console.table(results);

    console.log('🎉 修复完成！现在可以正常使用 collected 状态了');

  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
    process.exit(0);
  }
}

// 执行修复
fixStatusEnum();
