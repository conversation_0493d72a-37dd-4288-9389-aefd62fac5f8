/**
 * 数据库迁移脚本：添加暂停和恢复字段
 * 为 crawl_tasks 表添加 paused_at 和 resumed_at 字段
 */

const { sequelize } = require('../src/models');

async function addPauseResumeFields() {
  try {
    console.log('🔧 开始添加暂停和恢复字段...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 检查字段是否已存在
    console.log('\n📋 步骤1: 检查字段是否已存在');
    
    const [results] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'crawl_tasks' 
        AND COLUMN_NAME IN ('paused_at', 'resumed_at')
    `);

    const existingFields = results.map(row => row.COLUMN_NAME);
    console.log(`现有字段: ${existingFields.length > 0 ? existingFields.join(', ') : '无'}`);

    // 2. 添加 paused_at 字段
    if (!existingFields.includes('paused_at')) {
      console.log('\n📋 步骤2: 添加 paused_at 字段');
      
      await sequelize.query(`
        ALTER TABLE crawl_tasks 
        ADD COLUMN paused_at DATETIME COMMENT '暂停时间'
      `);
      
      console.log('✅ paused_at 字段添加成功');
    } else {
      console.log('\n📋 步骤2: paused_at 字段已存在，跳过');
    }

    // 3. 添加 resumed_at 字段
    if (!existingFields.includes('resumed_at')) {
      console.log('\n📋 步骤3: 添加 resumed_at 字段');
      
      await sequelize.query(`
        ALTER TABLE crawl_tasks 
        ADD COLUMN resumed_at DATETIME COMMENT '恢复时间'
      `);
      
      console.log('✅ resumed_at 字段添加成功');
    } else {
      console.log('\n📋 步骤3: resumed_at 字段已存在，跳过');
    }

    // 4. 验证字段添加结果
    console.log('\n📋 步骤4: 验证字段添加结果');
    
    const [finalResults] = await sequelize.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'crawl_tasks' 
        AND COLUMN_NAME IN ('paused_at', 'resumed_at')
      ORDER BY COLUMN_NAME
    `);

    console.log('最终字段状态:');
    finalResults.forEach(field => {
      console.log(`   ${field.COLUMN_NAME}: ${field.DATA_TYPE} ${field.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'} - ${field.COLUMN_COMMENT}`);
    });

    // 5. 测试字段使用
    console.log('\n📋 步骤5: 测试字段使用');
    
    try {
      // 尝试查询包含新字段的记录
      const [testResults] = await sequelize.query(`
        SELECT id, task_name, status, paused_at, resumed_at 
        FROM crawl_tasks 
        LIMIT 1
      `);
      
      console.log('✅ 字段查询测试成功');
      if (testResults.length > 0) {
        console.log(`   示例记录: ID${testResults[0].id} - ${testResults[0].task_name}`);
        console.log(`   paused_at: ${testResults[0].paused_at || 'null'}`);
        console.log(`   resumed_at: ${testResults[0].resumed_at || 'null'}`);
      }
    } catch (error) {
      console.error('❌ 字段查询测试失败:', error.message);
      throw error;
    }

    // 6. 显示完整的表结构
    console.log('\n📋 步骤6: 显示完整的表结构');
    
    const [tableStructure] = await sequelize.query(`
      DESCRIBE crawl_tasks
    `);

    console.log('crawl_tasks 表结构:');
    tableStructure.forEach(field => {
      console.log(`   ${field.Field}: ${field.Type} ${field.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${field.Key ? `KEY(${field.Key})` : ''} ${field.Default !== null ? `DEFAULT(${field.Default})` : ''}`);
    });

    console.log('\n✅ 暂停和恢复字段添加完成');
    
    return {
      pausedAtAdded: !existingFields.includes('paused_at'),
      resumedAtAdded: !existingFields.includes('resumed_at'),
      totalFieldsAdded: finalResults.length,
      success: true
    };

  } catch (error) {
    console.error('❌ 添加字段过程中发生错误:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addPauseResumeFields()
    .then(result => {
      console.log('\n📊 迁移结果摘要:', result);
      console.log('\n💡 现在可以正常使用任务暂停和恢复功能了！');
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = addPauseResumeFields;
