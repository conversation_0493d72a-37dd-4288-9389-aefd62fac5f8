/**
 * 数据库迁移脚本：修改 author_videos 表的 author_id 字段允许为 null
 * 支持独立视频保存功能，不依赖达人数据的存在状态
 */

const { sequelize } = require('../src/models');

async function migrateAuthorVideoAllowNull() {
  try {
    console.log('🔧 开始数据库迁移：修改 author_videos 表结构...\n');

    // 1. 检查当前表结构
    console.log('📋 步骤1: 检查当前表结构...');
    const [results] = await sequelize.query(`
      DESCRIBE author_videos;
    `);
    
    console.log('当前 author_videos 表结构:');
    results.forEach(column => {
      if (column.Field === 'author_id') {
        console.log(`  - ${column.Field}: ${column.Type}, Null: ${column.Null}, Default: ${column.Default}`);
      }
    });
    console.log('');

    // 2. 修改字段允许为 null
    console.log('📋 步骤2: 修改 author_id 字段允许为 null...');
    await sequelize.query(`
      ALTER TABLE author_videos 
      MODIFY COLUMN author_id INT NULL 
      COMMENT '关联的达人ID（my_influencers表），可为null表示通过platformUserId关联';
    `);
    console.log('✅ author_id 字段修改成功');

    // 3. 验证修改结果
    console.log('\n📋 步骤3: 验证修改结果...');
    const [newResults] = await sequelize.query(`
      DESCRIBE author_videos;
    `);
    
    const authorIdColumn = newResults.find(column => column.Field === 'author_id');
    if (authorIdColumn) {
      console.log('修改后的 author_id 字段:');
      console.log(`  - 字段名: ${authorIdColumn.Field}`);
      console.log(`  - 类型: ${authorIdColumn.Type}`);
      console.log(`  - 允许NULL: ${authorIdColumn.Null}`);
      console.log(`  - 默认值: ${authorIdColumn.Default}`);
      
      if (authorIdColumn.Null === 'YES') {
        console.log('✅ 字段修改验证成功！author_id 现在允许为 null');
      } else {
        console.log('❌ 字段修改验证失败！author_id 仍然不允许为 null');
      }
    }

    // 4. 测试插入 null 值
    console.log('\n📋 步骤4: 测试插入 null 值...');
    const testVideoId = `test_null_author_${Date.now()}`;
    const testPlatformUserId = `test_platform_${Date.now()}`;
    
    try {
      await sequelize.query(`
        INSERT INTO author_videos (
          author_id, platform, platform_user_id, video_id, title, 
          status, created_at, updated_at
        ) VALUES (
          NULL, 'juxingtu', '${testPlatformUserId}', '${testVideoId}', 
          '测试视频 - null author_id', 'active', NOW(), NOW()
        );
      `);
      console.log('✅ 成功插入 author_id 为 null 的测试记录');
      
      // 清理测试数据
      await sequelize.query(`
        DELETE FROM author_videos 
        WHERE video_id = '${testVideoId}' AND platform_user_id = '${testPlatformUserId}';
      `);
      console.log('✅ 测试数据清理完成');
      
    } catch (insertError) {
      console.error('❌ 插入测试失败:', insertError.message);
    }

    console.log('\n🎉 数据库迁移完成！');
    console.log('✅ author_videos 表现在支持独立视频保存功能');
    console.log('✅ author_id 字段可以为 null，通过 platform_user_id 实现松耦合关联');

  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    console.error('错误详情:', error.message);
    throw error;
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('📝 数据库连接已关闭');
  }
}

// 运行迁移
if (require.main === module) {
  migrateAuthorVideoAllowNull().catch(console.error);
}

module.exports = migrateAuthorVideoAllowNull;
