/**
 * 更新现有数据的播放量中位数
 * 为已存在的达人数据补充播放量中位数信息
 */

const { sequelize, PublicInfluencer } = require('../src/models');
const XingtuCrawler = require('../src/services/crawler/crawlers/XingtuCrawler');

async function updateExistingPlayMid() {
  let crawler;
  
  try {
    console.log('🚀 开始更新现有数据的播放量中位数...\n');

    // 初始化爬虫
    crawler = new XingtuCrawler();
    await crawler.initialize();
    console.log('✅ 爬虫初始化成功');

    // 查找没有播放量中位数的巨量星图达人
    const influencersWithoutPlayMid = await PublicInfluencer.findAll({
      where: {
        platform: 'juxingtu',
        playMid: null
      },
      attributes: ['id', 'platformUserId', 'nickname'],
      order: [['createdAt', 'DESC']],
      limit: 10 // 限制处理数量，避免过多API调用
    });

    console.log(`📊 找到 ${influencersWithoutPlayMid.length} 个需要更新播放量中位数的达人\n`);

    if (influencersWithoutPlayMid.length === 0) {
      console.log('✅ 所有达人都已有播放量中位数数据');
      return;
    }

    let successCount = 0;
    let failedCount = 0;
    let skippedCount = 0;

    // 逐个更新播放量中位数
    for (let i = 0; i < influencersWithoutPlayMid.length; i++) {
      const influencer = influencersWithoutPlayMid[i];
      
      try {
        console.log(`🔄 [${i + 1}/${influencersWithoutPlayMid.length}] 处理达人: ${influencer.nickname} (${influencer.platformUserId})`);

        // 获取播放量中位数
        const playMid = await crawler.getAuthorMedianPlay(influencer.platformUserId);

        if (playMid) {
          // 更新数据库
          await PublicInfluencer.update(
            { playMid: playMid },
            { where: { id: influencer.id } }
          );
          
          console.log(`✅ 更新成功: ${influencer.nickname} -> ${playMid}`);
          successCount++;
        } else {
          console.log(`⚠️ 跳过: ${influencer.nickname} (无播放量中位数数据)`);
          skippedCount++;
        }

        // 添加延迟避免API限制
        if (i < influencersWithoutPlayMid.length - 1) {
          const delay = 2000 + Math.random() * 2000; // 2-4秒随机延迟
          console.log(`⏳ 等待 ${Math.round(delay)}ms...\n`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

      } catch (error) {
        console.error(`❌ 处理失败: ${influencer.nickname} - ${error.message}`);
        failedCount++;
        
        // 如果是Cookie相关错误，停止处理
        if (error.message.includes('Cookie') || error.message.includes('认证')) {
          console.error('🛑 Cookie相关错误，停止处理');
          break;
        }
      }
    }

    // 输出统计结果
    console.log('\n📈 更新统计:');
    console.log(`   成功更新: ${successCount} 个`);
    console.log(`   跳过处理: ${skippedCount} 个`);
    console.log(`   处理失败: ${failedCount} 个`);

    // 验证更新结果
    const [updatedStats] = await PublicInfluencer.findAll({
      attributes: [
        [PublicInfluencer.sequelize.fn('COUNT', '*'), 'total'],
        [PublicInfluencer.sequelize.fn('COUNT', PublicInfluencer.sequelize.col('play_mid')), 'with_play_mid']
      ],
      where: { platform: 'juxingtu' },
      raw: true
    });

    console.log('\n📊 当前统计:');
    console.log(`   总记录数: ${updatedStats.total}`);
    console.log(`   有播放量中位数: ${updatedStats.with_play_mid}`);
    console.log(`   无播放量中位数: ${updatedStats.total - updatedStats.with_play_mid}`);

    if (successCount > 0) {
      console.log('\n🎉 播放量中位数更新完成！');
      console.log('💡 提示: 新的爬虫任务将自动包含播放量中位数数据');
    }

  } catch (error) {
    console.error('\n❌ 更新失败:', error.message);
    console.error(error.stack);
  } finally {
    if (crawler) {
      // 清理爬虫资源
      try {
        if (crawler.cookieManager) {
          // 爬虫清理逻辑
        }
      } catch (cleanupError) {
        console.warn('清理资源时出现警告:', cleanupError.message);
      }
    }
    
    await sequelize.close();
    console.log('\n🔒 数据库连接已关闭');
  }
}

// 运行更新
if (require.main === module) {
  updateExistingPlayMid();
}

module.exports = updateExistingPlayMid;
