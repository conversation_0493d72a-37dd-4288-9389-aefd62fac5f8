/**
 * 数据库迁移脚本：为达人表添加视频统计和详情字段
 */

const { sequelize } = require('../src/config/database');

async function migrateVideoFields() {
  try {
    console.log('🔄 开始执行数据库迁移...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 添加 video_stats 字段
    try {
      await sequelize.query(`
        ALTER TABLE influencers 
        ADD COLUMN video_stats JSON COMMENT '视频统计数据（播放量、点赞数等）' 
        AFTER cooperation_history
      `);
      console.log('✅ video_stats 字段添加成功');
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ video_stats 字段已存在，跳过');
      } else {
        throw error;
      }
    }
    
    // 添加 video_details 字段
    try {
      await sequelize.query(`
        ALTER TABLE influencers 
        ADD COLUMN video_details JSON COMMENT '详细视频信息列表' 
        AFTER video_stats
      `);
      console.log('✅ video_details 字段添加成功');
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ video_details 字段已存在，跳过');
      } else {
        throw error;
      }
    }
    
    // 验证字段是否添加成功
    const [results] = await sequelize.query(`DESCRIBE influencers`);
    const videoStatsField = results.find(field => field.Field === 'video_stats');
    const videoDetailsField = results.find(field => field.Field === 'video_details');
    
    if (videoStatsField && videoDetailsField) {
      console.log('✅ 字段验证成功：');
      console.log('  - video_stats:', videoStatsField.Type, videoStatsField.Comment);
      console.log('  - video_details:', videoDetailsField.Type, videoDetailsField.Comment);
    } else {
      console.log('❌ 字段验证失败');
    }
    
    console.log('🎉 数据库迁移完成');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行迁移
if (require.main === module) {
  migrateVideoFields()
    .then(() => {
      console.log('✅ 迁移脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 迁移脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = migrateVideoFields;
