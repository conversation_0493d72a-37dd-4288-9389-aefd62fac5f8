/**
 * 测试爬虫任务状态更新修复
 * 验证爬取结果状态能正确从 'pending' 更新为 'processed'
 */

const { CrawlTask, CrawlResult, CrawlLog, sequelize } = require('../src/models');
const CrawlerService = require('../src/services/crawler');

async function testCrawlerStatusFix() {
  try {
    console.log('🧪 开始测试爬虫任务状态更新修复...\n');

    // 1. 初始化爬虫服务
    console.log('📋 步骤1: 初始化爬虫服务...');
    await CrawlerService.initialize();
    console.log('✅ 爬虫服务初始化成功\n');

    // 2. 创建测试任务
    console.log('📋 步骤2: 创建测试任务...');
    const testTask = await CrawlerService.createTask({
      taskName: '状态更新测试任务',
      platform: 'xiaohongshu', // 使用小红书平台（模拟数据）
      keywords: '测试关键词',
      maxPages: 2,
      config: {
        pageSize: 5,
        delay: { min: 100, max: 200 } // 减少延迟以加快测试
      },
      createdBy: 1 // 使用整数ID
    });

    console.log(`✅ 测试任务创建成功: ${testTask.id} - ${testTask.taskName}\n`);

    // 3. 启动任务并等待完成
    console.log('📋 步骤3: 启动任务并监控状态...');
    await CrawlerService.startTask(testTask.id);

    // 等待任务完成（轮询检查）
    let taskCompleted = false;
    let checkCount = 0;
    const maxChecks = 30; // 最多检查30次（约30秒）

    while (!taskCompleted && checkCount < maxChecks) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
      
      const updatedTask = await CrawlTask.findByPk(testTask.id);
      console.log(`🔍 检查任务状态 (${checkCount + 1}/${maxChecks}): ${updatedTask.status}`);
      
      if (updatedTask.status === 'completed' || updatedTask.status === 'failed') {
        taskCompleted = true;
        console.log(`✅ 任务已完成，最终状态: ${updatedTask.status}\n`);
      }
      
      checkCount++;
    }

    if (!taskCompleted) {
      console.log('⚠️ 任务未在预期时间内完成，继续检查结果状态...\n');
    }

    // 4. 检查爬取结果状态
    console.log('📋 步骤4: 检查爬取结果状态...');
    const crawlResults = await CrawlResult.findAll({
      where: { taskId: testTask.id },
      attributes: ['id', 'status', 'nickname', 'platformUserId', 'createdAt', 'updatedAt']
    });

    console.log(`📊 找到 ${crawlResults.length} 个爬取结果:`);
    
    let pendingCount = 0;
    let processedCount = 0;
    let importedCount = 0;
    let failedCount = 0;

    crawlResults.forEach((result, index) => {
      console.log(`  ${index + 1}. ID: ${result.id}, 状态: ${result.status}, 昵称: ${result.nickname}`);
      
      switch (result.status) {
        case 'pending':
          pendingCount++;
          break;
        case 'processed':
          processedCount++;
          break;
        case 'imported':
          importedCount++;
          break;
        case 'failed':
          failedCount++;
          break;
      }
    });

    console.log('\n📊 状态统计:');
    console.log(`  - pending: ${pendingCount}`);
    console.log(`  - processed: ${processedCount}`);
    console.log(`  - imported: ${importedCount}`);
    console.log(`  - failed: ${failedCount}`);

    // 5. 验证修复结果
    console.log('\n📋 步骤5: 验证修复结果...');
    
    if (pendingCount === 0 && processedCount > 0) {
      console.log('✅ 修复成功！所有爬取结果状态都已正确更新为 processed');
    } else if (pendingCount > 0) {
      console.log('❌ 修复失败！仍有爬取结果状态为 pending');
      console.log(`   需要检查的记录: ${pendingCount} 个`);
    } else if (crawlResults.length === 0) {
      console.log('⚠️ 没有找到爬取结果，可能任务执行失败');
    } else {
      console.log('ℹ️ 结果状态异常，需要进一步检查');
    }

    // 6. 查看任务日志
    console.log('\n📋 步骤6: 查看任务日志...');
    const taskLogs = await CrawlLog.findAll({
      where: { taskId: testTask.id },
      order: [['createdAt', 'ASC']],
      limit: 10
    });

    console.log(`📊 最近 ${taskLogs.length} 条任务日志:`);
    taskLogs.forEach((log, index) => {
      console.log(`  ${index + 1}. [${log.level}] ${log.message} (${log.createdAt.toLocaleString()})`);
    });

    // 7. 清理测试数据
    console.log('\n📋 步骤7: 清理测试数据...');
    await CrawlResult.destroy({ where: { taskId: testTask.id } });
    await CrawlLog.destroy({ where: { taskId: testTask.id } });
    await testTask.destroy();
    console.log('✅ 测试数据清理完成');

    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
  } finally {
    // 确保数据库连接关闭
    await sequelize.close();
    console.log('📝 数据库连接已关闭');
  }
}

// 运行测试
if (require.main === module) {
  testCrawlerStatusFix().catch(console.error);
}

module.exports = testCrawlerStatusFix;
