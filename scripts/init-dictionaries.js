/**
 * 初始化字典数据脚本
 * 
 * 功能说明：
 * - 创建字典表（如果不存在）
 * - 插入初始字典数据
 * - 支持重复运行（幂等性）
 * 
 * 使用方法：
 * node scripts/init-dictionaries.js
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4'
};

// 字典数据
const dictionaryData = [
  // 合作形式
  { category: 'cooperation_form', dictKey: 'paid_promotion', dictLabel: '付费推广', sortOrder: 1 },
  { category: 'cooperation_form', dictKey: 'product_exchange', dictLabel: '产品置换', sortOrder: 2 },
  { category: 'cooperation_form', dictKey: 'commission_based', dictLabel: '佣金合作', sortOrder: 3 },
  { category: 'cooperation_form', dictKey: 'brand_ambassador', dictLabel: '品牌大使', sortOrder: 4 },
  { category: 'cooperation_form', dictKey: 'event_cooperation', dictLabel: '活动合作', sortOrder: 5 },

  // 合作品牌
  { category: 'cooperation_brand', dictKey: 'beauty_skincare', dictLabel: '美妆护肤', sortOrder: 1 },
  { category: 'cooperation_brand', dictKey: 'fashion_clothing', dictLabel: '时尚服饰', sortOrder: 2 },
  { category: 'cooperation_brand', dictKey: 'food_beverage', dictLabel: '食品饮料', sortOrder: 3 },
  { category: 'cooperation_brand', dictKey: 'digital_electronics', dictLabel: '数码电子', sortOrder: 4 },
  { category: 'cooperation_brand', dictKey: 'home_living', dictLabel: '家居生活', sortOrder: 5 },
  { category: 'cooperation_brand', dictKey: 'health_fitness', dictLabel: '健康健身', sortOrder: 6 },
  { category: 'cooperation_brand', dictKey: 'travel_tourism', dictLabel: '旅游出行', sortOrder: 7 },
  { category: 'cooperation_brand', dictKey: 'education_training', dictLabel: '教育培训', sortOrder: 8 },
  { category: 'cooperation_brand', dictKey: 'finance_investment', dictLabel: '金融投资', sortOrder: 9 },
  { category: 'cooperation_brand', dictKey: 'other', dictLabel: '其他', sortOrder: 10 },

  // 返点完成状态
  { category: 'rebate_status', dictKey: 'completed', dictLabel: '已完成', sortOrder: 1 },
  { category: 'rebate_status', dictKey: 'pending', dictLabel: '进行中', sortOrder: 2 },
  { category: 'rebate_status', dictKey: 'not_applicable', dictLabel: '不适用', sortOrder: 3 },

  // 内容植入系数
  { category: 'content_implant_coefficient', dictKey: 'high', dictLabel: '高', sortOrder: 1 },
  { category: 'content_implant_coefficient', dictKey: 'medium', dictLabel: '中', sortOrder: 2 },
  { category: 'content_implant_coefficient', dictKey: 'low', dictLabel: '低', sortOrder: 3 },
  { category: 'content_implant_coefficient', dictKey: 'none', dictLabel: '无', sortOrder: 4 },

  // 评论区维护系数
  { category: 'comment_maintenance_coefficient', dictKey: 'excellent', dictLabel: '优秀', sortOrder: 1 },
  { category: 'comment_maintenance_coefficient', dictKey: 'good', dictLabel: '良好', sortOrder: 2 },
  { category: 'comment_maintenance_coefficient', dictKey: 'average', dictLabel: '一般', sortOrder: 3 },
  { category: 'comment_maintenance_coefficient', dictKey: 'poor', dictLabel: '较差', sortOrder: 4 },

  // 品牌话题包含
  { category: 'brand_topic_included', dictKey: 'yes', dictLabel: '是', sortOrder: 1 },
  { category: 'brand_topic_included', dictKey: 'no', dictLabel: '否', sortOrder: 2 },
  { category: 'brand_topic_included', dictKey: 'partial', dictLabel: '部分', sortOrder: 3 },

  // 自评
  { category: 'self_evaluation', dictKey: 'excellent_paid', dictLabel: '优秀付费笔记', sortOrder: 1 },
  { category: 'self_evaluation', dictKey: 'good_paid', dictLabel: '良好付费笔记', sortOrder: 2 },
  { category: 'self_evaluation', dictKey: 'average_paid', dictLabel: '一般付费笔记', sortOrder: 3 },
  { category: 'self_evaluation', dictKey: 'excellent_exchange', dictLabel: '优秀置换爆文', sortOrder: 4 },
  { category: 'self_evaluation', dictKey: 'good_exchange', dictLabel: '良好置换爆文', sortOrder: 5 },
  { category: 'self_evaluation', dictKey: 'average_exchange', dictLabel: '一般置换爆文', sortOrder: 6 }
];

async function initDictionaries() {
  let connection;
  
  try {
    console.log('🔄 开始初始化字典数据...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查字典表是否存在
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'dictionaries'"
    );

    if (tables.length === 0) {
      console.log('📋 字典表不存在，开始创建...');
      
      // 创建字典表
      const createTableSQL = `
        CREATE TABLE \`dictionaries\` (
          \`id\` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
          \`category\` varchar(50) NOT NULL COMMENT '字典分类',
          \`dict_key\` varchar(100) NOT NULL COMMENT '字典键值',
          \`dict_label\` varchar(200) NOT NULL COMMENT '字典标签',
          \`dict_value\` varchar(200) DEFAULT NULL COMMENT '字典值',
          \`sort_order\` int DEFAULT '0' COMMENT '排序顺序',
          \`status\` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
          \`description\` text COMMENT '描述信息',
          \`created_by\` int NOT NULL DEFAULT '1' COMMENT '创建人ID',
          \`updated_by\` int NOT NULL DEFAULT '1' COMMENT '更新人ID',
          \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (\`id\`),
          KEY \`idx_category\` (\`category\`),
          KEY \`idx_dict_key\` (\`dict_key\`),
          KEY \`idx_status\` (\`status\`),
          KEY \`idx_category_status_sort\` (\`category\`,\`status\`,\`sort_order\`),
          UNIQUE KEY \`idx_category_key_unique\` (\`category\`,\`dict_key\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典管理表'
      `;
      
      await connection.execute(createTableSQL);
      console.log('✅ 字典表创建成功');
    } else {
      console.log('✅ 字典表已存在');
    }

    // 检查是否已有数据
    const [existingData] = await connection.execute(
      'SELECT COUNT(*) as count FROM dictionaries'
    );
    
    if (existingData[0].count > 0) {
      console.log(`📊 字典表中已有 ${existingData[0].count} 条数据`);
      console.log('🔄 清空现有数据并重新插入...');
      await connection.execute('DELETE FROM dictionaries');
    }

    // 插入字典数据
    console.log('📝 开始插入字典数据...');
    
    const insertSQL = `
      INSERT INTO dictionaries (category, dict_key, dict_label, sort_order, created_by, updated_by)
      VALUES (?, ?, ?, ?, 1, 1)
    `;

    let insertCount = 0;
    for (const item of dictionaryData) {
      try {
        await connection.execute(insertSQL, [
          item.category,
          item.dictKey,
          item.dictLabel,
          item.sortOrder
        ]);
        insertCount++;
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️ 跳过重复数据: ${item.category}.${item.dictKey}`);
        } else {
          throw error;
        }
      }
    }

    console.log(`✅ 成功插入 ${insertCount} 条字典数据`);

    // 验证数据
    const [finalCount] = await connection.execute(
      'SELECT COUNT(*) as count FROM dictionaries'
    );
    console.log(`📊 字典表最终数据量: ${finalCount[0].count} 条`);

    // 显示各分类的数据量
    const [categoryStats] = await connection.execute(`
      SELECT category, COUNT(*) as count 
      FROM dictionaries 
      GROUP BY category 
      ORDER BY category
    `);
    
    console.log('📋 各分类数据统计:');
    categoryStats.forEach(stat => {
      console.log(`  - ${stat.category}: ${stat.count} 条`);
    });

    console.log('🎉 字典数据初始化完成！');

  } catch (error) {
    console.error('❌ 字典数据初始化失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行初始化
initDictionaries();
