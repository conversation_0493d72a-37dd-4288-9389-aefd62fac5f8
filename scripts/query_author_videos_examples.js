/**
 * 达人视频作品查询示例脚本
 * 展示如何使用 AuthorVideo 模型进行各种查询操作
 */

const { sequelize, AuthorVideo, Influencer, CrawlTask } = require('../src/models');
const { Op } = require('sequelize');

async function runQueryExamples() {
  try {
    console.log('🔄 开始执行达人视频作品查询示例...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功\n');
    
    // 示例1: 基本查询 - 获取所有视频
    console.log('📋 示例1: 获取所有视频基本信息');
    const allVideos = await AuthorVideo.findAll({
      attributes: ['id', 'title', 'playCount', 'likeCount', 'publishTime'],
      order: [['playCount', 'DESC']]
    });
    
    allVideos.forEach(video => {
      console.log(`- ${video.title} | 播放: ${video.playCount.toLocaleString()} | 点赞: ${video.likeCount.toLocaleString()}`);
    });
    
    // 示例2: 关联查询 - 获取视频及达人信息
    console.log('\n📋 示例2: 获取视频及关联的达人信息');
    const videosWithAuthor = await AuthorVideo.findAll({
      include: [{
        model: Influencer,
        as: 'author',
        attributes: ['nickname', 'followersCount', 'category']
      }],
      attributes: ['title', 'playCount', 'likeCount', 'publishTime'],
      order: [['publishTime', 'DESC']]
    });
    
    videosWithAuthor.forEach(video => {
      console.log(`- 视频: ${video.title}`);
      console.log(`  达人: ${video.author.nickname} (${video.author.category}) | 粉丝: ${video.author.followersCount.toLocaleString()}`);
      console.log(`  数据: 播放 ${video.playCount.toLocaleString()} | 点赞 ${video.likeCount.toLocaleString()}`);
    });
    
    // 示例3: 条件查询 - 按平台筛选
    console.log('\n📋 示例3: 按平台筛选视频');
    const juxingtuVideos = await AuthorVideo.findAll({
      where: { platform: 'juxingtu' },
      attributes: ['title', 'platform', 'playCount'],
      order: [['playCount', 'DESC']]
    });
    
    console.log(`巨量星图平台视频 (${juxingtuVideos.length} 个):`);
    juxingtuVideos.forEach(video => {
      console.log(`- ${video.title} | 播放: ${video.playCount.toLocaleString()}`);
    });
    
    // 示例4: 聚合查询 - 统计数据
    console.log('\n📋 示例4: 视频统计数据');
    const stats = await AuthorVideo.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalVideos'],
        [sequelize.fn('SUM', sequelize.col('play_count')), 'totalPlays'],
        [sequelize.fn('AVG', sequelize.col('play_count')), 'avgPlays'],
        [sequelize.fn('MAX', sequelize.col('play_count')), 'maxPlays'],
        [sequelize.fn('SUM', sequelize.col('like_count')), 'totalLikes']
      ],
      raw: true
    });
    
    console.log('整体统计:');
    console.log(`- 总视频数: ${stats.totalVideos}`);
    console.log(`- 总播放量: ${parseInt(stats.totalPlays).toLocaleString()}`);
    console.log(`- 平均播放量: ${parseInt(stats.avgPlays).toLocaleString()}`);
    console.log(`- 最高播放量: ${parseInt(stats.maxPlays).toLocaleString()}`);
    console.log(`- 总点赞数: ${parseInt(stats.totalLikes).toLocaleString()}`);
    
    // 示例5: 按达人分组统计
    console.log('\n📋 示例5: 按达人分组统计');
    const authorStats = await AuthorVideo.findAll({
      include: [{
        model: Influencer,
        as: 'author',
        attributes: ['nickname']
      }],
      attributes: [
        'authorId',
        [sequelize.fn('COUNT', sequelize.col('AuthorVideo.id')), 'videoCount'],
        [sequelize.fn('SUM', sequelize.col('play_count')), 'totalPlays'],
        [sequelize.fn('AVG', sequelize.col('like_count')), 'avgLikes']
      ],
      group: ['authorId', 'author.id'],
      order: [[sequelize.fn('SUM', sequelize.col('play_count')), 'DESC']]
    });
    
    console.log('达人视频统计:');
    authorStats.forEach(stat => {
      console.log(`- ${stat.author.nickname}:`);
      console.log(`  视频数: ${stat.dataValues.videoCount}`);
      console.log(`  总播放: ${parseInt(stat.dataValues.totalPlays).toLocaleString()}`);
      console.log(`  平均点赞: ${parseInt(stat.dataValues.avgLikes).toLocaleString()}`);
    });
    
    // 示例6: 时间范围查询
    console.log('\n📋 示例6: 最近发布的视频');
    const recentVideos = await AuthorVideo.findAll({
      where: {
        publishTime: {
          [Op.gte]: new Date('2025-07-10')
        }
      },
      include: [{
        model: Influencer,
        as: 'author',
        attributes: ['nickname']
      }],
      attributes: ['title', 'publishTime', 'playCount'],
      order: [['publishTime', 'DESC']]
    });
    
    console.log('最近发布的视频:');
    recentVideos.forEach(video => {
      console.log(`- ${video.title} | ${video.author.nickname} | ${video.publishTime.toLocaleDateString()}`);
    });
    
    // 示例7: JSON字段查询
    console.log('\n📋 示例7: 包含特定标签的视频');
    const taggedVideos = await AuthorVideo.findAll({
      where: {
        tags: {
          [Op.ne]: null
        }
      },
      attributes: ['title', 'tags', 'playCount']
    });
    
    console.log('包含标签的视频:');
    taggedVideos.forEach(video => {
      const tags = Array.isArray(video.tags) ? video.tags.join(', ') : '无标签';
      console.log(`- ${video.title} | 标签: [${tags}] | 播放: ${video.playCount.toLocaleString()}`);
    });
    
    console.log('\n🎉 查询示例执行完成！');
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await sequelize.close();
  }
}

// 执行查询示例
if (require.main === module) {
  runQueryExamples();
}

module.exports = runQueryExamples;
