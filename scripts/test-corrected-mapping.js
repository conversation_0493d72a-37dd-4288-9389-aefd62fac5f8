/**
 * 修正后的CRM字典映射配置测试脚本
 * 
 * 功能说明：
 * - 测试修正后的CRM字典映射配置
 * - 验证前端字典服务能否正确获取CRM数据
 * - 模拟CooperationForm.vue的字典加载过程
 * 
 * 使用方法：
 * node scripts/test-corrected-mapping.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize } = require('../src/models');
const CrmDictionaryService = require('../src/services/CrmDictionaryService');

class CorrectedMappingTester {
  constructor() {
    // 修正后的映射配置（与前端保持一致）
    this.crmFieldMapping = {
      customer: {
        'customer_select_1': 'cooperation_form',      // 合作形式 (8项)
        'customer_select_15': 'cooperation_brand',    // 合作品牌 (210项)
        'customer_select_7': 'rebate_status',         // 返点状态 (9项)
        'customer_select_8': 'publish_platform',     // 发布平台 (8项)
        'customer_select_5': 'seeding_platform'      // 种草平台 (6项)
      },
      contract: {
        'contract_select_1': 'content_implant_coefficient',      // 内容植入系数 (2项)
        'contract_select_2': 'comment_maintenance_coefficient',  // 评论维护系数 (10项)
        'contract_select_3': 'brand_topic_included',            // 品牌话题包含 (10项)
        'contract_select_4': 'self_evaluation'                  // 自我评价 (2项)
      }
    };

    this.testResults = {
      mapping: { passed: 0, failed: 0, tests: [] },
      api: { passed: 0, failed: 0, tests: [] },
      integration: { passed: 0, failed: 0, tests: [] }
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始测试修正后的CRM字典映射配置...\n');

    try {
      await this.testMappingConfiguration();
      await this.testApiCalls();
      await this.testIntegrationScenario();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    } finally {
      await sequelize.close();
    }
  }

  /**
   * 测试映射配置
   */
  async testMappingConfiguration() {
    console.log('🗺️ 测试修正后的映射配置...');

    const crmDictionaryService = new CrmDictionaryService();

    // 测试每个映射的field_name是否存在数据
    for (const [deployId, fieldMap] of Object.entries(this.crmFieldMapping)) {
      for (const [fieldName, localCategory] of Object.entries(fieldMap)) {
        await this.runTest('mapping', `${deployId}.${fieldName} -> ${localCategory}`, async () => {
          const dictionaries = await crmDictionaryService.getDictionaries(deployId, fieldName, true, 'name');
          
          console.log(`  📊 ${fieldName}: ${dictionaries.length} 项数据`);
          
          if (dictionaries.length > 0) {
            // 显示前几个数据项作为示例
            const samples = dictionaries.slice(0, 3).map(dict => ({
              dataName: dict.dataName,
              dataValue: dict.dataValue
            }));
            console.log(`    示例数据:`, samples);
          }
          
          return dictionaries.length > 0;
        });
      }
    }

    console.log('✅ 映射配置测试完成\n');
  }

  /**
   * 测试API调用
   */
  async testApiCalls() {
    console.log('🔧 测试API调用...');

    // 模拟前端findCrmFieldByCategory方法
    const findCrmFieldByCategory = (category) => {
      for (const [deployId, fieldMap] of Object.entries(this.crmFieldMapping)) {
        for (const [fieldName, localCategory] of Object.entries(fieldMap)) {
          if (localCategory === category) {
            return { deployId, fieldName };
          }
        }
      }
      return null;
    };

    // 测试CooperationForm.vue需要的字典分类
    const requiredCategories = [
      'cooperation_form',
      'cooperation_brand',
      'rebate_status',
      'content_implant_coefficient',
      'comment_maintenance_coefficient',
      'brand_topic_included',
      'self_evaluation'
    ];

    for (const category of requiredCategories) {
      await this.runTest('api', `获取${category}字典`, async () => {
        const crmField = findCrmFieldByCategory(category);
        
        if (!crmField) {
          console.log(`  ⚠️ ${category}: 没有找到CRM映射`);
          return false;
        }

        const crmDictionaryService = new CrmDictionaryService();
        const dictionaries = await crmDictionaryService.getDictionaries(
          crmField.deployId, 
          crmField.fieldName, 
          true, 
          'name'
        );

        console.log(`  📊 ${category}: ${crmField.deployId}.${crmField.fieldName} -> ${dictionaries.length} 项`);

        return dictionaries.length > 0;
      });
    }

    console.log('✅ API调用测试完成\n');
  }

  /**
   * 测试集成场景
   */
  async testIntegrationScenario() {
    console.log('🔗 测试集成场景...');

    // 模拟CooperationForm.vue的批量字典获取
    await this.runTest('integration', '批量字典获取模拟', async () => {
      const requiredCategories = [
        'cooperation_form',
        'cooperation_brand',
        'rebate_status',
        'content_implant_coefficient',
        'comment_maintenance_coefficient',
        'brand_topic_included',
        'self_evaluation'
      ];

      const batchResult = {};
      let totalItems = 0;
      let successfulCategories = 0;

      for (const category of requiredCategories) {
        try {
          // 查找CRM映射
          let crmField = null;
          for (const [deployId, fieldMap] of Object.entries(this.crmFieldMapping)) {
            for (const [fieldName, localCategory] of Object.entries(fieldMap)) {
              if (localCategory === category) {
                crmField = { deployId, fieldName };
                break;
              }
            }
            if (crmField) break;
          }

          if (crmField) {
            const crmDictionaryService = new CrmDictionaryService();
            const dictionaries = await crmDictionaryService.getDictionaries(
              crmField.deployId, 
              crmField.fieldName, 
              true, 
              'name'
            );

            // 转换为前端需要的格式
            const standardizedData = dictionaries.map(item => ({
              value: item.dataValue || item.dataId,
              label: item.dataName,
              key: item.dataId,
              order: item.dataOrder || 0,
              source: 'crm',
              category: category,
              crmFieldName: crmField.fieldName
            }));

            batchResult[category] = standardizedData;
            totalItems += standardizedData.length;
            
            if (standardizedData.length > 0) {
              successfulCategories++;
            }
          } else {
            batchResult[category] = [];
          }
        } catch (error) {
          console.error(`  ❌ ${category}: ${error.message}`);
          batchResult[category] = [];
        }
      }

      console.log('\n📊 批量获取结果汇总:');
      console.log(`  请求分类数: ${requiredCategories.length}`);
      console.log(`  成功分类数: ${successfulCategories}`);
      console.log(`  总数据项: ${totalItems}`);
      console.log(`  成功率: ${((successfulCategories / requiredCategories.length) * 100).toFixed(1)}%`);

      console.log('\n📋 各分类详情:');
      Object.keys(batchResult).forEach(category => {
        const data = batchResult[category];
        console.log(`  ${category}: ${data.length} 项`);
      });

      return successfulCategories >= requiredCategories.length * 0.7; // 至少70%成功率
    });

    console.log('✅ 集成场景测试完成\n');
  }

  /**
   * 运行单个测试
   */
  async runTest(category, testName, testFunction) {
    try {
      const result = await testFunction();
      if (result) {
        this.testResults[category].passed++;
        this.testResults[category].tests.push({ name: testName, status: 'PASSED' });
        console.log(`  ✅ ${testName}`);
      } else {
        this.testResults[category].failed++;
        this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: 'Test returned false' });
        console.log(`  ❌ ${testName} - 测试返回false`);
      }
    } catch (error) {
      this.testResults[category].failed++;
      this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: error.message });
      console.log(`  ❌ ${testName} - ${error.message}`);
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📋 修正后的CRM字典映射配置测试结果汇总:');
    console.log('=' * 60);

    let totalPassed = 0;
    let totalFailed = 0;

    for (const [category, results] of Object.entries(this.testResults)) {
      const categoryName = {
        mapping: '映射配置',
        api: 'API调用',
        integration: '集成场景'
      }[category];

      console.log(`\n${categoryName}:`);
      console.log(`  通过: ${results.passed}`);
      console.log(`  失败: ${results.failed}`);

      if (results.failed > 0) {
        console.log('  失败的测试:');
        results.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`    - ${test.name}: ${test.error}`);
          });
      }

      totalPassed += results.passed;
      totalFailed += results.failed;
    }

    console.log('\n' + '=' * 60);
    console.log(`总计: ${totalPassed + totalFailed} 个测试`);
    console.log(`通过: ${totalPassed}`);
    console.log(`失败: ${totalFailed}`);
    console.log(`成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 所有测试通过！修正后的CRM字典映射配置正确。');
      console.log('\n📌 修正要点:');
      console.log('✅ 使用实际存在的field_name值');
      console.log('✅ 映射配置与数据库数据匹配');
      console.log('✅ API调用能正确返回数据');
      console.log('✅ 集成场景工作正常');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关配置。');
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new CorrectedMappingTester();
  tester.runAllTests().catch(console.error);
}

module.exports = CorrectedMappingTester;
