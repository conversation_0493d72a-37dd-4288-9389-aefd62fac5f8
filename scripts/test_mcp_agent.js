/**
 * MCP Agent接入测试脚本
 * 测试Agent连接、SSE事件流和实时通信功能
 */

const axios = require('axios');
const { EventSource } = require('eventsource');

// 配置
const BASE_URL = 'http://127.0.0.1:3001';
const AGENT_API_BASE = `${BASE_URL}/api/mcp/agent`;

class MCPAgentTester {
  constructor() {
    this.sessionId = null;
    this.eventSource = null;
    this.receivedEvents = [];
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🤖 开始MCP Agent接入测试...\n');

    try {
      // 测试Agent连接
      await this.testAgentConnect();
      
      if (this.sessionId) {
        // 建立SSE连接
        await this.testSSEConnection();
        
        // 等待SSE连接稳定
        await this.sleep(2000);
        
        // 测试工具执行
        await this.testToolExecution();
        
        // 测试任务订阅
        await this.testTaskSubscription();
        
        // 等待接收事件
        await this.sleep(5000);
        
        // 测试会话信息获取
        await this.testSessionInfo();
        
        // 测试Agent断开连接
        await this.testAgentDisconnect();
      }
      
      // 显示测试结果
      this.showTestResults();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error.message);
    } finally {
      // 清理资源
      this.cleanup();
    }
  }

  /**
   * 测试Agent连接
   */
  async testAgentConnect() {
    console.log('📋 测试1: Agent连接注册...');
    
    try {
      const agentData = {
        agentId: 'test_agent_001',
        agentName: 'MCP测试Agent',
        version: '1.0.0',
        capabilities: ['crawler', 'excel_export', 'real_time_monitoring'],
        metadata: {
          description: '用于测试MCP Agent接入功能',
          environment: 'test'
        }
      };

      const response = await axios.post(`${AGENT_API_BASE}/connect`, agentData);
      
      if (response.data.success) {
        this.sessionId = response.data.data.sessionId;
        console.log('✅ Agent连接成功');
        console.log(`   - 会话ID: ${this.sessionId}`);
        console.log(`   - Agent ID: ${response.data.data.agentId}`);
        console.log(`   - 可用工具数量: ${response.data.data.tools.length}`);
        console.log(`   - SSE端点: ${response.data.data.sseEndpoint}`);
        this.addTestResult('Agent连接注册', true);
      } else {
        throw new Error('Agent连接失败');
      }
    } catch (error) {
      console.log('❌ Agent连接失败:', error.message);
      this.addTestResult('Agent连接注册', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试SSE连接
   */
  async testSSEConnection() {
    console.log('📋 测试2: 建立SSE事件流连接...');
    
    try {
      const sseUrl = `${AGENT_API_BASE}/events/${this.sessionId}`;
      console.log(`   连接URL: ${sseUrl}`);
      
      this.eventSource = new EventSource(sseUrl);
      
      // 设置事件监听器
      this.eventSource.onopen = () => {
        console.log('✅ SSE连接建立成功');
        this.addTestResult('SSE事件流连接', true);
      };
      
      this.eventSource.onerror = (error) => {
        console.error('❌ SSE连接错误:', error);
        this.addTestResult('SSE事件流连接', false, 'SSE连接错误');
      };
      
      this.eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log(`📡 收到SSE事件: ${event.type || 'message'}`);
          console.log(`   数据:`, data);
          this.receivedEvents.push({
            type: event.type || 'message',
            data,
            timestamp: new Date()
          });
        } catch (e) {
          console.log(`📡 收到SSE消息: ${event.data}`);
        }
      };
      
      // 监听特定事件类型
      ['connected', 'welcome', 'task_status_update', 'task_completed', 'tool_execution_result', 'heartbeat'].forEach(eventType => {
        this.eventSource.addEventListener(eventType, (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log(`📡 收到${eventType}事件:`, data);
            this.receivedEvents.push({
              type: eventType,
              data,
              timestamp: new Date()
            });
          } catch (e) {
            console.log(`📡 收到${eventType}事件: ${event.data}`);
          }
        });
      });
      
      // 等待连接建立
      await this.sleep(1000);
      
    } catch (error) {
      console.log('❌ SSE连接失败:', error.message);
      this.addTestResult('SSE事件流连接', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试工具执行
   */
  async testToolExecution() {
    console.log('📋 测试3: Agent工具执行...');
    
    try {
      const executeData = {
        sessionId: this.sessionId,
        tool: 'create_crawler_task',
        arguments: {
          keywords: 'Agent测试达人',
          platform: 'xiaohongshu',
          taskName: 'Agent创建的测试任务',
          maxPages: 2,
          config: {
            pageSize: 10,
            delay: { min: 500, max: 1000 }
          }
        },
        requestId: 'test_req_001'
      };

      const response = await axios.post(`${AGENT_API_BASE}/execute`, executeData);
      
      if (response.data.success) {
        console.log('✅ 工具执行成功');
        console.log(`   - 工具: ${response.data.data.tool}`);
        console.log(`   - 请求ID: ${response.data.data.requestId}`);
        if (response.data.data.result.success) {
          console.log(`   - 任务ID: ${response.data.data.result.data.taskId}`);
          this.createdTaskId = response.data.data.result.data.taskId;
        }
        this.addTestResult('Agent工具执行', true);
      } else {
        throw new Error('工具执行失败');
      }
    } catch (error) {
      console.log('❌ 工具执行失败:', error.message);
      this.addTestResult('Agent工具执行', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试任务订阅
   */
  async testTaskSubscription() {
    if (!this.createdTaskId) {
      console.log('⚠️ 跳过任务订阅测试（没有可用的任务ID）\n');
      return;
    }

    console.log('📋 测试4: 任务状态订阅...');
    
    try {
      const subscribeData = {
        sessionId: this.sessionId,
        taskId: this.createdTaskId,
        action: 'subscribe'
      };

      const response = await axios.post(`${AGENT_API_BASE}/subscribe`, subscribeData);
      
      if (response.data.success) {
        console.log('✅ 任务订阅成功');
        console.log(`   - 任务ID: ${response.data.data.taskId}`);
        console.log(`   - 操作: ${response.data.data.action}`);
        this.addTestResult('任务状态订阅', true);
      } else {
        throw new Error('任务订阅失败');
      }
    } catch (error) {
      console.log('❌ 任务订阅失败:', error.message);
      this.addTestResult('任务状态订阅', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试会话信息获取
   */
  async testSessionInfo() {
    console.log('📋 测试5: 获取会话信息...');
    
    try {
      const response = await axios.get(`${AGENT_API_BASE}/session/${this.sessionId}`);
      
      if (response.data.success) {
        console.log('✅ 会话信息获取成功');
        console.log(`   - 会话ID: ${response.data.data.sessionId}`);
        console.log(`   - Agent ID: ${response.data.data.agentId}`);
        console.log(`   - 订阅任务数: ${response.data.data.subscribedTasks.length}`);
        console.log(`   - 活跃连接数: ${response.data.data.activeConnections}`);
        this.addTestResult('获取会话信息', true);
      } else {
        throw new Error('获取会话信息失败');
      }
    } catch (error) {
      console.log('❌ 获取会话信息失败:', error.message);
      this.addTestResult('获取会话信息', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试Agent断开连接
   */
  async testAgentDisconnect() {
    console.log('📋 测试6: Agent断开连接...');
    
    try {
      const disconnectData = {
        sessionId: this.sessionId
      };

      const response = await axios.post(`${AGENT_API_BASE}/disconnect`, disconnectData);
      
      if (response.data.success) {
        console.log('✅ Agent断开连接成功');
        console.log(`   - 会话ID: ${response.data.data.sessionId}`);
        this.addTestResult('Agent断开连接', true);
      } else {
        throw new Error('断开连接失败');
      }
    } catch (error) {
      console.log('❌ 断开连接失败:', error.message);
      this.addTestResult('Agent断开连接', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, error = null) {
    this.testResults.push({
      name: testName,
      success,
      error
    });
  }

  /**
   * 显示测试结果
   */
  showTestResults() {
    console.log('📊 MCP Agent接入测试结果汇总:');
    console.log('='.repeat(60));
    
    let passedCount = 0;
    let failedCount = 0;
    
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅ 通过' : '❌ 失败';
      console.log(`${index + 1}. ${result.name}: ${status}`);
      
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
      
      if (result.success) {
        passedCount++;
      } else {
        failedCount++;
      }
    });
    
    console.log('='.repeat(60));
    console.log(`总计: ${this.testResults.length} 个测试`);
    console.log(`通过: ${passedCount} 个`);
    console.log(`失败: ${failedCount} 个`);
    console.log(`成功率: ${((passedCount / this.testResults.length) * 100).toFixed(1)}%`);
    
    // 显示接收到的事件
    console.log('\n📡 接收到的SSE事件:');
    console.log('='.repeat(40));
    if (this.receivedEvents.length > 0) {
      this.receivedEvents.forEach((event, index) => {
        console.log(`${index + 1}. [${event.type}] ${event.timestamp.toLocaleTimeString()}`);
        if (event.data.message) {
          console.log(`   消息: ${event.data.message}`);
        }
      });
    } else {
      console.log('未接收到任何事件');
    }
    
    if (failedCount === 0) {
      console.log('\n🎉 所有测试都通过了！MCP Agent接入功能正常！');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关功能。');
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.eventSource) {
      this.eventSource.close();
      console.log('🧹 SSE连接已关闭');
    }
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行测试
async function runTests() {
  const tester = new MCPAgentTester();
  await tester.runAllTests();
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = MCPAgentTester;
