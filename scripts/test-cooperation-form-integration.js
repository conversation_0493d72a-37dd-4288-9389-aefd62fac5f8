/**
 * 合作对接表单集成测试脚本
 * 
 * 功能说明：
 * - 验证达人提报与合作对接记录的集成流程
 * - 检查数据预填充逻辑
 * - 验证组件间的通信
 */

const fs = require('fs');

function testIntegration() {
  console.log('🔄 测试合作对接表单集成...\n');
  
  let hasErrors = false;
  
  try {
    // 检查InfluencerReportView.vue的修改
    console.log('📋 检查InfluencerReportView.vue:');
    const reportViewContent = fs.readFileSync('frontend/src/views/InfluencerReportView.vue', 'utf8');
    
    // 检查导入
    if (reportViewContent.includes("import CooperationForm from '@/components/CooperationForm.vue'")) {
      console.log('✅ CooperationForm组件已正确导入');
    } else {
      console.log('❌ CooperationForm组件导入缺失');
      hasErrors = true;
    }
    
    // 检查响应式数据
    if (reportViewContent.includes('cooperationFormVisible') && 
        reportViewContent.includes('cooperationFormData') && 
        reportViewContent.includes('cooperationFormMode')) {
      console.log('✅ 合作对接表单相关响应式数据已定义');
    } else {
      console.log('❌ 合作对接表单相关响应式数据缺失');
      hasErrors = true;
    }
    
    // 检查数据预填充方法
    if (reportViewContent.includes('generateCooperationDataFromReport')) {
      console.log('✅ 数据预填充方法已实现');
    } else {
      console.log('❌ 数据预填充方法缺失');
      hasErrors = true;
    }
    
    // 检查事件处理方法
    if (reportViewContent.includes('handleCooperationFormSuccess') && 
        reportViewContent.includes('handleCooperationFormCancel')) {
      console.log('✅ 表单事件处理方法已实现');
    } else {
      console.log('❌ 表单事件处理方法缺失');
      hasErrors = true;
    }
    
    // 检查模板中的组件使用
    if (reportViewContent.includes('<CooperationForm') && 
        reportViewContent.includes('@success="handleCooperationFormSuccess"') && 
        reportViewContent.includes('@cancel="handleCooperationFormCancel"')) {
      console.log('✅ CooperationForm组件在模板中正确使用');
    } else {
      console.log('❌ CooperationForm组件在模板中使用不正确');
      hasErrors = true;
    }
    
    console.log('\n📋 检查CooperationForm.vue:');
    const cooperationFormContent = fs.readFileSync('frontend/src/components/CooperationForm.vue', 'utf8');
    
    // 检查成功事件是否传递数据
    if (cooperationFormContent.includes("emit('success', result.data || result)")) {
      console.log('✅ 成功事件正确传递创建的记录数据');
    } else {
      console.log('❌ 成功事件未传递创建的记录数据');
      hasErrors = true;
    }
    
    // 检查预填充数据处理
    if (cooperationFormContent.includes('(!props.editData || Object.keys(props.editData).length === 0)')) {
      console.log('✅ 预填充数据处理逻辑已优化');
    } else {
      console.log('❌ 预填充数据处理逻辑需要优化');
      hasErrors = true;
    }
    
    console.log('\n📋 数据映射检查:');
    
    // 检查关键字段映射
    const mappingFields = [
      'customerName',
      'bloggerFansCount', 
      'influencerPlatformId',
      'title',
      'publishPlatform',
      'cooperationAmount',
      'influencerReportId'
    ];
    
    let mappingComplete = true;
    mappingFields.forEach(field => {
      if (reportViewContent.includes(`${field}:`)) {
        console.log(`✅ ${field}: 已映射`);
      } else {
        console.log(`❌ ${field}: 未映射`);
        mappingComplete = false;
      }
    });
    
    if (mappingComplete) {
      console.log('✅ 所有关键字段映射完成');
    } else {
      console.log('❌ 部分关键字段映射缺失');
      hasErrors = true;
    }
    
    console.log('\n📋 流程检查:');
    
    // 检查流程步骤
    const flowSteps = [
      { name: '点击创建按钮', check: 'createCooperationRecord(record)' },
      { name: '生成预填充数据', check: 'generateCooperationDataFromReport' },
      { name: '显示表单弹窗', check: 'cooperationFormVisible.value = true' },
      { name: '处理表单成功', check: 'handleCooperationFormSuccess' },
      { name: '更新关联状态', check: 'updateReportCooperationStatus' },
      { name: '刷新列表数据', check: 'loadData()' }
    ];
    
    let flowComplete = true;
    flowSteps.forEach(step => {
      if (reportViewContent.includes(step.check)) {
        console.log(`✅ ${step.name}: 已实现`);
      } else {
        console.log(`❌ ${step.name}: 未实现`);
        flowComplete = false;
      }
    });
    
    if (flowComplete) {
      console.log('✅ 完整流程已实现');
    } else {
      console.log('❌ 流程实现不完整');
      hasErrors = true;
    }
    
  } catch (error) {
    console.log(`❌ 测试过程中发生错误: ${error.message}`);
    hasErrors = true;
  }
  
  console.log('\n🎯 测试结果:');
  if (hasErrors) {
    console.log('❌ 发现问题，请检查修改');
    return false;
  } else {
    console.log('✅ 所有检查通过！');
    console.log('\n💡 使用说明:');
    console.log('1. 在达人提报列表中，点击"创建合作对接"按钮');
    console.log('2. 系统会自动打开合作对接表单弹窗');
    console.log('3. 表单会预填充达人提报的相关信息');
    console.log('4. 用户可以查看、修改和完善信息');
    console.log('5. 点击确认后创建合作对接记录');
    console.log('6. 系统会自动更新提报的关联状态');
    console.log('\n🎊 集成完成，可以开始测试新的交互流程！');
    return true;
  }
}

testIntegration();
