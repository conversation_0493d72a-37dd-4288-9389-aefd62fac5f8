/**
 * 测试默认视频保存功能
 * 验证爬虫任务默认启用视频保存功能
 */

const { initializeAndSyncDatabase, CrawlTask, AuthorVideo, Influencer } = require('../src/models');
const XingtuCrawler = require('../src/services/crawler/crawlers/XingtuCrawler');

async function testDefaultVideoSave() {
  try {
    console.log('🚀 测试默认视频保存功能...\n');

    // 1. 初始化数据库
    console.log('📋 步骤1: 初始化数据库...');
    await initializeAndSyncDatabase();
    console.log('✅ 数据库初始化完成\n');

    // 2. 创建测试任务（不指定saveVideos参数）
    console.log('📋 步骤2: 创建测试任务（默认配置）...');
    const testTask = await CrawlTask.create({
      taskName: '默认视频保存测试',
      platform: 'juxingtu',
      keywords: '测试',
      maxPages: 1,
      status: 'pending',
      createdBy: 1
      // 注意：这里没有设置 config.saveVideos，应该默认为true
    });
    console.log(`✅ 测试任务创建成功，ID: ${testTask.id}`);
    console.log('📝 任务配置:', JSON.stringify(testTask.config || {}, null, 2));
    console.log('');

    // 3. 测试爬虫配置生成
    console.log('📋 步骤3: 测试爬虫配置生成...');
    
    // 模拟CrawlerManager中的配置生成逻辑
    const crawlConfig = {
      keywords: testTask.keywords,
      maxPages: testTask.maxPages || 5,
      pageSize: testTask.config?.pageSize || 20,
      delay: testTask.config?.delay || { min: 1000, max: 3000 },
      retries: testTask.config?.retries || 3,
      filters: testTask.config?.filters || {},
      crawlTaskId: testTask.id, // 自动添加任务ID
      saveVideos: testTask.config?.saveVideos !== false // 默认保存视频
    };

    console.log('✅ 生成的爬虫配置:');
    console.log(JSON.stringify(crawlConfig, null, 2));
    console.log('');

    // 4. 验证默认配置
    console.log('📋 步骤4: 验证默认配置...');
    
    if (crawlConfig.saveVideos === true) {
      console.log('✅ 默认视频保存功能已启用');
    } else {
      console.log('❌ 默认视频保存功能未启用');
      throw new Error('默认配置验证失败');
    }

    if (crawlConfig.crawlTaskId === testTask.id) {
      console.log('✅ 任务ID自动关联成功');
    } else {
      console.log('❌ 任务ID关联失败');
      throw new Error('任务ID关联验证失败');
    }
    console.log('');

    // 5. 测试明确禁用视频保存的情况
    console.log('📋 步骤5: 测试明确禁用视频保存...');
    const disabledTask = await CrawlTask.create({
      taskName: '禁用视频保存测试',
      platform: 'juxingtu',
      keywords: '测试',
      maxPages: 1,
      status: 'pending',
      createdBy: 1,
      config: {
        saveVideos: false // 明确禁用
      }
    });

    const disabledConfig = {
      keywords: disabledTask.keywords,
      maxPages: disabledTask.maxPages || 5,
      crawlTaskId: disabledTask.id,
      saveVideos: disabledTask.config?.saveVideos !== false
    };

    console.log('📝 禁用配置的任务:', JSON.stringify(disabledTask.config, null, 2));
    console.log('📝 生成的爬虫配置:', { saveVideos: disabledConfig.saveVideos });

    if (disabledConfig.saveVideos === false) {
      console.log('✅ 明确禁用视频保存功能正常工作');
    } else {
      console.log('❌ 明确禁用视频保存功能异常');
      throw new Error('禁用配置验证失败');
    }
    console.log('');

    // 6. 测试XingtuCrawler中的配置处理
    console.log('📋 步骤6: 测试XingtuCrawler配置处理...');
    
    // 模拟XingtuCrawler中的配置处理逻辑
    const testConfigs = [
      { name: '默认配置（未设置）', config: {} },
      { name: '明确启用', config: { saveVideos: true } },
      { name: '明确禁用', config: { saveVideos: false } },
      { name: '其他配置', config: { maxPages: 3 } }
    ];

    testConfigs.forEach(test => {
      const crawlOptions = {
        saveVideos: test.config.saveVideos !== false,
        crawlTaskId: test.config.crawlTaskId || null
      };
      
      console.log(`  ${test.name}:`, {
        输入: test.config,
        输出: crawlOptions.saveVideos
      });
    });
    console.log('');

    // 7. 模拟视频保存流程
    console.log('📋 步骤7: 模拟视频保存流程...');
    
    // 创建模拟达人数据
    const mockAuthorData = {
      platform: 'juxingtu',
      platformUserId: `test_default_${Date.now()}`,
      nickname: '默认保存测试达人',
      avatarUrl: 'https://example.com/avatar.jpg',
      followersCount: 10000,
      videoDetails: [
        {
          videoId: `default_video_${Date.now()}_1`,
          title: '默认保存测试视频1',
          playCount: 5000,
          likeCount: 300,
          commentCount: 50,
          shareCount: 20,
          publishTime: new Date().toISOString(),
          duration: 60
        },
        {
          videoId: `default_video_${Date.now()}_2`,
          title: '默认保存测试视频2',
          playCount: 8000,
          likeCount: 500,
          commentCount: 80,
          shareCount: 30,
          publishTime: new Date().toISOString(),
          duration: 90
        }
      ]
    };

    // 使用AuthorVideoService保存视频
    const AuthorVideoService = require('../src/services/AuthorVideoService');
    const saveResult = await AuthorVideoService.saveVideosFromCrawlTask(
      mockAuthorData,
      testTask.id
    );

    console.log('✅ 视频保存结果:', {
      total: saveResult.total,
      success: saveResult.success,
      created: saveResult.created,
      authorCreated: saveResult.authorCreated
    });
    console.log('');

    // 8. 验证数据保存
    console.log('📋 步骤8: 验证数据保存...');
    
    const savedAuthor = await Influencer.findByPk(saveResult.authorId);
    const savedVideos = await AuthorVideo.findAll({
      where: { authorId: saveResult.authorId }
    });

    console.log('✅ 保存验证结果:');
    console.log(`  达人: ${savedAuthor.nickname} (ID: ${savedAuthor.id})`);
    console.log(`  视频数量: ${savedVideos.length}`);
    savedVideos.forEach((video, index) => {
      console.log(`    ${index + 1}. ${video.title} (播放: ${video.playCount})`);
    });
    console.log('');

    // 9. 清理测试数据
    console.log('📋 步骤9: 清理测试数据...');
    await AuthorVideo.destroy({ where: { authorId: saveResult.authorId } });
    await Influencer.destroy({ where: { id: saveResult.authorId } });
    await CrawlTask.destroy({ where: { id: testTask.id } });
    await CrawlTask.destroy({ where: { id: disabledTask.id } });
    console.log('✅ 测试数据清理完成\n');

    // 10. 总结
    console.log('📋 步骤10: 功能总结...');
    console.log('🎉 默认视频保存功能测试全部通过！');
    console.log('');
    console.log('✅ 验证的功能点:');
    console.log('  1. 爬虫任务默认启用视频保存功能');
    console.log('  2. 任务ID自动关联到爬虫配置');
    console.log('  3. 明确禁用视频保存功能正常工作');
    console.log('  4. XingtuCrawler配置处理逻辑正确');
    console.log('  5. 视频数据保存流程正常');
    console.log('  6. 达人与视频关联关系建立成功');
    console.log('');
    console.log('🔧 配置说明:');
    console.log('  - 默认情况下，所有爬虫任务都会保存视频数据');
    console.log('  - 如需禁用，请在任务配置中设置 saveVideos: false');
    console.log('  - 任务ID会自动关联到视频数据，便于追溯');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testDefaultVideoSave()
    .then(() => {
      console.log('\n✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试异常:', error);
      process.exit(1);
    });
}

module.exports = { testDefaultVideoSave };
