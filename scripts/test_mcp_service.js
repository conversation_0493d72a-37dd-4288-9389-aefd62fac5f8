/**
 * MCP服务测试脚本
 * 测试MCP工具的完整功能流程
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://127.0.0.1:3001';
const MCP_API_BASE = `${BASE_URL}/api/mcp`;

class MCPServiceTester {
  constructor() {
    this.testResults = [];
    this.createdTaskId = null;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始MCP服务功能测试...\n');

    try {
      // 测试服务状态
      await this.testServiceStatus();
      
      // 测试获取工具列表
      await this.testGetTools();
      
      // 测试获取使用示例
      await this.testGetExamples();
      
      // 测试创建爬虫任务
      await this.testCreateCrawlerTask();
      
      // 等待任务执行一段时间
      if (this.createdTaskId) {
        console.log('⏳ 等待任务执行 30 秒...\n');
        await this.sleep(30000);
        
        // 测试获取任务状态
        await this.testGetTaskStatus();
        
        // 测试获取任务结果
        await this.testGetTaskResults();
        
        // 测试导出Excel
        await this.testExportToExcel();
      }
      
      // 测试MCP工具执行接口
      await this.testMCPExecuteInterface();
      
      // 显示测试结果
      this.showTestResults();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error.message);
    }
  }

  /**
   * 测试服务状态
   */
  async testServiceStatus() {
    console.log('📋 测试1: 检查MCP服务状态...');
    
    try {
      const response = await axios.get(`${MCP_API_BASE}/status`);
      
      if (response.data.success) {
        console.log('✅ MCP服务状态正常');
        console.log(`   - 服务版本: ${response.data.data.version}`);
        console.log(`   - 初始化状态: ${response.data.data.initialized}`);
        console.log(`   - 可用工具数量: ${response.data.data.tools.count}`);
        console.log(`   - 工具列表: ${response.data.data.tools.available.join(', ')}`);
        this.addTestResult('MCP服务状态检查', true);
      } else {
        throw new Error('服务状态异常');
      }
    } catch (error) {
      console.log('❌ MCP服务状态检查失败:', error.message);
      this.addTestResult('MCP服务状态检查', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试获取工具列表
   */
  async testGetTools() {
    console.log('📋 测试2: 获取MCP工具列表...');
    
    try {
      const response = await axios.get(`${MCP_API_BASE}/tools`);
      
      if (response.data.success) {
        console.log('✅ 工具列表获取成功');
        console.log(`   - 工具数量: ${response.data.data.count}`);
        
        response.data.data.tools.forEach((tool, index) => {
          console.log(`   ${index + 1}. ${tool.name}: ${tool.description}`);
        });
        
        this.addTestResult('获取工具列表', true);
      } else {
        throw new Error('获取工具列表失败');
      }
    } catch (error) {
      console.log('❌ 获取工具列表失败:', error.message);
      this.addTestResult('获取工具列表', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试获取使用示例
   */
  async testGetExamples() {
    console.log('📋 测试3: 获取使用示例...');
    
    try {
      const response = await axios.get(`${MCP_API_BASE}/examples`);
      
      if (response.data.success) {
        console.log('✅ 使用示例获取成功');
        const examples = response.data.data.examples;
        console.log(`   - 示例数量: ${Object.keys(examples).length}`);
        console.log(`   - 示例工具: ${Object.keys(examples).join(', ')}`);
        this.addTestResult('获取使用示例', true);
      } else {
        throw new Error('获取使用示例失败');
      }
    } catch (error) {
      console.log('❌ 获取使用示例失败:', error.message);
      this.addTestResult('获取使用示例', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试创建爬虫任务
   */
  async testCreateCrawlerTask() {
    console.log('📋 测试4: 创建爬虫任务...');
    
    const taskData = {
      keywords: 'MCP测试达人',
      platform: 'xiaohongshu',
      taskName: 'MCP服务测试任务',
      maxPages: 2,
      config: {
        pageSize: 10,
        delay: { min: 500, max: 1000 }
      },
      priority: 1
    };
    
    try {
      const response = await axios.post(`${MCP_API_BASE}/crawler/create`, taskData);
      
      if (response.data.success) {
        this.createdTaskId = response.data.data.taskId;
        console.log('✅ 爬虫任务创建成功');
        console.log(`   - 任务ID: ${response.data.data.taskId}`);
        console.log(`   - 任务名称: ${response.data.data.taskName}`);
        console.log(`   - 平台: ${response.data.data.platform}`);
        console.log(`   - 关键词: ${response.data.data.keywords}`);
        console.log(`   - 状态: ${response.data.data.status}`);
        console.log(`   - 预估时长: ${response.data.data.estimatedDuration}`);
        this.addTestResult('创建爬虫任务', true);
      } else {
        throw new Error('任务创建失败');
      }
    } catch (error) {
      console.log('❌ 创建爬虫任务失败:', error.message);
      this.addTestResult('创建爬虫任务', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试获取任务状态
   */
  async testGetTaskStatus() {
    if (!this.createdTaskId) {
      console.log('⚠️ 跳过任务状态测试（没有可用的任务ID）\n');
      return;
    }

    console.log('📋 测试5: 获取任务状态...');
    
    try {
      const response = await axios.get(`${MCP_API_BASE}/crawler/tasks/${this.createdTaskId}/status`);
      
      if (response.data.success) {
        console.log('✅ 任务状态获取成功');
        console.log(`   - 任务ID: ${response.data.data.taskId}`);
        console.log(`   - 状态: ${response.data.data.status}`);
        console.log(`   - 进度: ${response.data.data.progress}%`);
        console.log(`   - 当前页: ${response.data.data.currentPage}/${response.data.data.maxPages}`);
        console.log(`   - 成功数量: ${response.data.data.successCount}`);
        console.log(`   - 失败数量: ${response.data.data.failedCount}`);
        if (response.data.data.estimatedTimeRemaining) {
          console.log(`   - 预估剩余时间: ${response.data.data.estimatedTimeRemaining}`);
        }
        this.addTestResult('获取任务状态', true);
      } else {
        throw new Error('获取任务状态失败');
      }
    } catch (error) {
      console.log('❌ 获取任务状态失败:', error.message);
      this.addTestResult('获取任务状态', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试获取任务结果
   */
  async testGetTaskResults() {
    if (!this.createdTaskId) {
      console.log('⚠️ 跳过任务结果测试（没有可用的任务ID）\n');
      return;
    }

    console.log('📋 测试6: 获取任务结果...');
    
    try {
      const response = await axios.get(`${MCP_API_BASE}/crawler/tasks/${this.createdTaskId}/results?page=1&limit=10`);
      
      if (response.data.success) {
        console.log('✅ 任务结果获取成功');
        console.log(`   - 总结果数: ${response.data.data.pagination.total}`);
        console.log(`   - 当前页结果数: ${response.data.data.summary.currentPageCount}`);
        console.log(`   - 分页信息: 第${response.data.data.pagination.page}页，共${response.data.data.pagination.pages}页`);
        
        if (response.data.data.results.length > 0) {
          console.log('   - 示例结果:');
          const firstResult = response.data.data.results[0];
          console.log(`     昵称: ${firstResult.nickname}`);
          console.log(`     平台: ${firstResult.platform}`);
          console.log(`     粉丝数: ${firstResult.followersCount}`);
          console.log(`     状态: ${firstResult.status}`);
        }
        
        this.addTestResult('获取任务结果', true);
      } else {
        throw new Error('获取任务结果失败');
      }
    } catch (error) {
      console.log('❌ 获取任务结果失败:', error.message);
      this.addTestResult('获取任务结果', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试导出Excel
   */
  async testExportToExcel() {
    if (!this.createdTaskId) {
      console.log('⚠️ 跳过Excel导出测试（没有可用的任务ID）\n');
      return;
    }

    console.log('📋 测试7: 导出任务结果为Excel...');
    
    const exportData = {
      fileName: 'MCP测试导出',
      includeFields: ['nickname', 'platform', 'followersCount', 'city', 'status']
    };
    
    try {
      const response = await axios.post(`${MCP_API_BASE}/crawler/tasks/${this.createdTaskId}/export`, exportData);
      
      if (response.data.success) {
        console.log('✅ Excel导出成功');
        console.log(`   - 文件名: ${response.data.data.fileName}`);
        console.log(`   - 下载链接: ${response.data.data.downloadUrl}`);
        console.log(`   - 文件大小: ${response.data.data.fileSizeFormatted}`);
        console.log(`   - 记录数量: ${response.data.data.recordCount}`);
        console.log(`   - 任务信息: ${response.data.data.taskInfo.taskName} (${response.data.data.taskInfo.platform})`);
        this.addTestResult('导出Excel', true);
      } else {
        throw new Error('Excel导出失败');
      }
    } catch (error) {
      console.log('❌ Excel导出失败:', error.message);
      this.addTestResult('导出Excel', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试MCP工具执行接口
   */
  async testMCPExecuteInterface() {
    console.log('📋 测试8: MCP工具执行接口...');
    
    // 测试获取任务状态工具
    if (this.createdTaskId) {
      try {
        const response = await axios.post(`${MCP_API_BASE}/execute`, {
          tool: 'get_task_status',
          arguments: {
            taskId: this.createdTaskId
          }
        });
        
        if (response.data.success) {
          console.log('✅ MCP工具执行接口正常');
          console.log(`   - 工具: get_task_status`);
          console.log(`   - 任务状态: ${response.data.data.status}`);
          console.log(`   - 进度: ${response.data.data.progress}%`);
          this.addTestResult('MCP工具执行接口', true);
        } else {
          throw new Error('MCP工具执行失败');
        }
      } catch (error) {
        console.log('❌ MCP工具执行接口失败:', error.message);
        this.addTestResult('MCP工具执行接口', false, error.message);
      }
    } else {
      console.log('⚠️ 跳过MCP工具执行接口测试（没有可用的任务ID）');
      this.addTestResult('MCP工具执行接口', false, '没有可用的任务ID');
    }
    
    console.log('');
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, error = null) {
    this.testResults.push({
      name: testName,
      success,
      error
    });
  }

  /**
   * 显示测试结果
   */
  showTestResults() {
    console.log('📊 MCP服务测试结果汇总:');
    console.log('='.repeat(60));
    
    let passedCount = 0;
    let failedCount = 0;
    
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅ 通过' : '❌ 失败';
      console.log(`${index + 1}. ${result.name}: ${status}`);
      
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
      
      if (result.success) {
        passedCount++;
      } else {
        failedCount++;
      }
    });
    
    console.log('='.repeat(60));
    console.log(`总计: ${this.testResults.length} 个测试`);
    console.log(`通过: ${passedCount} 个`);
    console.log(`失败: ${failedCount} 个`);
    console.log(`成功率: ${((passedCount / this.testResults.length) * 100).toFixed(1)}%`);
    
    if (failedCount === 0) {
      console.log('\n🎉 所有测试都通过了！MCP服务功能正常！');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关功能。');
    }

    if (this.createdTaskId) {
      console.log(`\n📋 创建的测试任务ID: ${this.createdTaskId}`);
      console.log('💡 您可以继续监控此任务的执行情况');
    }
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行测试
async function runTests() {
  const tester = new MCPServiceTester();
  await tester.runAllTests();
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = MCPServiceTester;
