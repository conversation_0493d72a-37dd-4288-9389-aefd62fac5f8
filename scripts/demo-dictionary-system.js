/**
 * 字典管理系统演示脚本
 * 
 * 功能说明：
 * - 演示字典管理系统的完整功能
 * - 创建示例字典数据
 * - 展示CRM字典同步流程
 * - 演示字典数据的使用场景
 * 
 * 使用方法：
 * node scripts/demo-dictionary-system.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize, Dictionary } = require('../src/models');
const DictionaryService = require('../src/services/DictionaryService');

class DictionarySystemDemo {
  constructor() {
    this.dictionaryService = new DictionaryService();
  }

  /**
   * 运行完整演示
   */
  async runDemo() {
    console.log('🎬 字典管理系统功能演示');
    console.log('=' * 50);

    try {
      await this.setupSampleData();
      await this.demonstrateBasicOperations();
      await this.demonstrateAdvancedFeatures();
      await this.demonstrateUsageScenarios();
      
      console.log('\n🎉 演示完成！');
    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
    } finally {
      await sequelize.close();
    }
  }

  /**
   * 设置示例数据
   */
  async setupSampleData() {
    console.log('\n📝 1. 创建示例字典数据...');

    const sampleDictionaries = [
      // 合作形式
      { category: 'cooperation_form', dictKey: 'image_text', dictLabel: '图文', sortOrder: 1 },
      { category: 'cooperation_form', dictKey: 'video', dictLabel: '视频', sortOrder: 2 },
      { category: 'cooperation_form', dictKey: 'live_stream', dictLabel: '直播', sortOrder: 3 },
      
      // 合作品牌
      { category: 'cooperation_brand', dictKey: 'brand_a', dictLabel: '品牌A', sortOrder: 1 },
      { category: 'cooperation_brand', dictKey: 'brand_b', dictLabel: '品牌B', sortOrder: 2 },
      { category: 'cooperation_brand', dictKey: 'brand_c', dictLabel: '品牌C', sortOrder: 3 },
      
      // 返点状态
      { category: 'rebate_status', dictKey: 'completed', dictLabel: '已完成', sortOrder: 1 },
      { category: 'rebate_status', dictKey: 'pending', dictLabel: '进行中', sortOrder: 2 },
      { category: 'rebate_status', dictKey: 'not_applicable', dictLabel: '不适用', sortOrder: 3 },
      
      // 发布平台
      { category: 'publish_platform', dictKey: 'xiaohongshu', dictLabel: '小红书', sortOrder: 1 },
      { category: 'publish_platform', dictKey: 'douyin', dictLabel: '抖音', sortOrder: 2 },
      { category: 'publish_platform', dictKey: 'weibo', dictLabel: '微博', sortOrder: 3 },
      
      // 内容植入系数
      { category: 'content_implant_coefficient', dictKey: 'high', dictLabel: '高', sortOrder: 1 },
      { category: 'content_implant_coefficient', dictKey: 'medium', dictLabel: '中', sortOrder: 2 },
      { category: 'content_implant_coefficient', dictKey: 'low', dictLabel: '低', sortOrder: 3 }
    ];

    for (const dict of sampleDictionaries) {
      try {
        await this.dictionaryService.createDictionary({
          ...dict,
          status: 'active',
          description: `${dict.dictLabel}选项`
        }, 1);
        console.log(`  ✅ 创建字典项: ${dict.category}.${dict.dictKey} = ${dict.dictLabel}`);
      } catch (error) {
        if (error.message.includes('已存在')) {
          console.log(`  ⚠️ 字典项已存在: ${dict.category}.${dict.dictKey}`);
        } else {
          console.log(`  ❌ 创建失败: ${dict.category}.${dict.dictKey} - ${error.message}`);
        }
      }
    }
  }

  /**
   * 演示基础操作
   */
  async demonstrateBasicOperations() {
    console.log('\n🔧 2. 演示基础操作...');

    // 获取所有分类
    console.log('\n📋 获取所有字典分类:');
    const categories = await this.dictionaryService.getAllCategories();
    categories.forEach(category => {
      console.log(`  - ${category}`);
    });

    // 按分类获取字典项
    console.log('\n📝 获取合作形式字典项:');
    const cooperationForms = await this.dictionaryService.getDictionariesByCategory('cooperation_form');
    cooperationForms.forEach(item => {
      console.log(`  - ${item.dictKey}: ${item.dictLabel} (排序: ${item.sortOrder})`);
    });

    // 获取字典列表（分页）
    console.log('\n📄 获取字典列表（前5项）:');
    const listResult = await this.dictionaryService.getDictionaryList({
      page: 1,
      pageSize: 5,
      category: 'cooperation_form'
    });
    console.log(`  总数: ${listResult.total}, 当前页: ${listResult.page}/${listResult.totalPages}`);
    listResult.list.forEach(item => {
      console.log(`  - ${item.dictKey}: ${item.dictLabel} (状态: ${item.status})`);
    });
  }

  /**
   * 演示高级功能
   */
  async demonstrateAdvancedFeatures() {
    console.log('\n🚀 3. 演示高级功能...');

    // 批量创建字典项
    console.log('\n📦 批量创建字典项:');
    const batchData = [
      { category: 'demo_batch', dictKey: 'item1', dictLabel: '批量项目1', sortOrder: 1, status: 'active' },
      { category: 'demo_batch', dictKey: 'item2', dictLabel: '批量项目2', sortOrder: 2, status: 'active' },
      { category: 'demo_batch', dictKey: 'item3', dictLabel: '批量项目3', sortOrder: 3, status: 'active' }
    ];

    const batchResult = await this.dictionaryService.batchCreateDictionaries(batchData, 1);
    console.log(`  成功创建: ${batchResult.success.length} 项`);
    console.log(`  失败: ${batchResult.failed.length} 项`);

    // 更新字典项
    console.log('\n✏️ 更新字典项:');
    const itemToUpdate = await Dictionary.findOne({ 
      where: { category: 'demo_batch', dictKey: 'item1' } 
    });
    
    if (itemToUpdate) {
      await this.dictionaryService.updateDictionary(itemToUpdate.id, {
        dictLabel: '更新后的批量项目1',
        description: '这是一个更新后的描述'
      }, 1);
      console.log(`  ✅ 更新成功: ${itemToUpdate.dictKey} -> 更新后的批量项目1`);
    }

    // 搜索字典项
    console.log('\n🔍 搜索字典项（关键词: "品牌"）:');
    const searchResult = await this.dictionaryService.getDictionaryList({
      page: 1,
      pageSize: 10,
      keyword: '品牌'
    });
    searchResult.list.forEach(item => {
      console.log(`  - ${item.category}.${item.dictKey}: ${item.dictLabel}`);
    });
  }

  /**
   * 演示使用场景
   */
  async demonstrateUsageScenarios() {
    console.log('\n💼 4. 演示使用场景...');

    // 场景1：表单下拉选项
    console.log('\n📋 场景1: 为表单生成下拉选项');
    const formOptions = await this.generateFormOptions('cooperation_form');
    console.log('  合作形式下拉选项:');
    formOptions.forEach(option => {
      console.log(`    <option value="${option.value}">${option.label}</option>`);
    });

    // 场景2：数据验证
    console.log('\n✅ 场景2: 数据验证');
    const validationResult = await this.validateDictionaryValue('cooperation_form', 'video');
    console.log(`  验证 "video" 是否为有效的合作形式: ${validationResult ? '✅ 有效' : '❌ 无效'}`);

    // 场景3：数据转换
    console.log('\n🔄 场景3: 数据转换');
    const displayValue = await this.getDisplayValue('cooperation_brand', 'brand_a');
    console.log(`  将 "brand_a" 转换为显示文本: "${displayValue}"`);

    // 场景4：统计信息
    console.log('\n📊 场景4: 统计信息');
    const stats = await this.getStatistics();
    console.log(`  字典分类总数: ${stats.totalCategories}`);
    console.log(`  字典项总数: ${stats.totalItems}`);
    console.log(`  启用的字典项: ${stats.activeItems}`);

    // 清理演示数据
    console.log('\n🧹 清理演示数据...');
    await Dictionary.destroy({ where: { category: 'demo_batch' } });
    console.log('  ✅ 演示数据清理完成');
  }

  /**
   * 生成表单选项
   */
  async generateFormOptions(category) {
    const dictionaries = await this.dictionaryService.getDictionariesByCategory(category, true);
    return dictionaries.map(item => ({
      value: item.dictKey,
      label: item.dictLabel,
      order: item.sortOrder
    })).sort((a, b) => a.order - b.order);
  }

  /**
   * 验证字典值
   */
  async validateDictionaryValue(category, value) {
    const dictionaries = await this.dictionaryService.getDictionariesByCategory(category, true);
    return dictionaries.some(item => item.dictKey === value);
  }

  /**
   * 获取显示值
   */
  async getDisplayValue(category, key) {
    const dictionaries = await this.dictionaryService.getDictionariesByCategory(category, true);
    const item = dictionaries.find(dict => dict.dictKey === key);
    return item ? item.dictLabel : key;
  }

  /**
   * 获取统计信息
   */
  async getStatistics() {
    const categories = await this.dictionaryService.getAllCategories();
    const allDictionaries = await Dictionary.findAll();
    const activeDictionaries = allDictionaries.filter(item => item.status === 'active');

    return {
      totalCategories: categories.length,
      totalItems: allDictionaries.length,
      activeItems: activeDictionaries.length,
      inactiveItems: allDictionaries.length - activeDictionaries.length
    };
  }
}

// 运行演示
if (require.main === module) {
  const demo = new DictionarySystemDemo();
  demo.runDemo().catch(console.error);
}

module.exports = DictionarySystemDemo;
