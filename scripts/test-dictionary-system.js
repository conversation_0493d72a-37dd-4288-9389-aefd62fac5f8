/**
 * 字典管理系统测试脚本
 * 
 * 功能说明：
 * - 测试CRM字典数据表的创建
 * - 验证CRM字典同步服务
 * - 测试字典数据的获取和合并
 * - 验证API接口的正确性
 * 
 * 使用方法：
 * node scripts/test-dictionary-system.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize, CrmDictionary, Dictionary } = require('../src/models');
const CrmDictionaryService = require('../src/services/CrmDictionaryService');
const DictionaryService = require('../src/services/DictionaryService');

class DictionarySystemTester {
  constructor() {
    this.testResults = {
      database: { passed: 0, failed: 0, tests: [] },
      crmSync: { passed: 0, failed: 0, tests: [] },
      api: { passed: 0, failed: 0, tests: [] },
      integration: { passed: 0, failed: 0, tests: [] }
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始字典管理系统测试...\n');

    try {
      await this.testDatabaseSetup();
      await this.testCrmDictionarySync();
      await this.testLocalDictionaryOperations();
      await this.testIntegration();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    } finally {
      await sequelize.close();
    }
  }

  /**
   * 测试数据库设置
   */
  async testDatabaseSetup() {
    console.log('📊 测试数据库设置...');

    // 测试数据库连接
    await this.runTest('database', '数据库连接', async () => {
      await sequelize.authenticate();
      return true;
    });

    // 测试CRM字典表创建
    await this.runTest('database', 'CRM字典表创建', async () => {
      await CrmDictionary.sync({ force: false });
      const tableExists = await sequelize.getQueryInterface().showAllTables();
      return tableExists.includes('crm_dictionaries');
    });

    // 测试本地字典表
    await this.runTest('database', '本地字典表检查', async () => {
      await Dictionary.sync({ force: false });
      const tableExists = await sequelize.getQueryInterface().showAllTables();
      return tableExists.includes('dictionaries');
    });

    console.log('✅ 数据库设置测试完成\n');
  }

  /**
   * 测试CRM字典同步
   */
  async testCrmDictionarySync() {
    console.log('🔄 测试CRM字典同步...');

    const crmService = new CrmDictionaryService();

    // 测试CRM连接（如果配置了CRM）
    await this.runTest('crmSync', 'CRM连接测试', async () => {
      try {
        // 这里只是测试服务实例化，不进行实际连接
        return crmService instanceof CrmDictionaryService;
      } catch (error) {
        console.log('⚠️ CRM连接测试跳过（可能未配置CRM）');
        return true; // 跳过测试
      }
    });

    // 测试字典数据解析
    await this.runTest('crmSync', '字典数据解析', async () => {
      const mockField = {
        fieldName: '测试字段',
        fieldCode: 'test_field',
        fieldDomType: 'SELECT',
        dicData: {
          datas: [
            { dataId: '1', dataName: '选项1', dataValue: 'option1', dataOrder: 1 },
            { dataId: '2', dataName: '选项2', dataValue: 'option2', dataOrder: 2 }
          ]
        }
      };

      const result = crmService.parseDictionaryData(mockField, 'customer');
      return result.length === 2 && result[0].dataName === '选项1';
    });

    // 测试数据库同步
    await this.runTest('crmSync', '数据库同步测试', async () => {
      const testData = {
        deployId: 'test',
        fieldName: '测试字段',
        fieldCode: 'test_field',
        fieldDomType: 'SELECT',
        dataId: 'test_1',
        dataName: '测试选项',
        dataValue: 'test_option',
        dataOrder: 1,
        rawData: { test: true },
        syncStatus: 'pending',
        isActive: true,
        isDeleted: false
      };

      const result = await crmService.syncDictionaryItem(testData);
      return result && (result.action === 'created' || result.action === 'updated');
    });

    console.log('✅ CRM字典同步测试完成\n');
  }

  /**
   * 测试本地字典操作
   */
  async testLocalDictionaryOperations() {
    console.log('📝 测试本地字典操作...');

    const dictionaryService = new DictionaryService();

    // 测试创建字典项
    await this.runTest('api', '创建字典项', async () => {
      const testDict = await Dictionary.create({
        category: 'test_category',
        dictKey: 'test_key',
        dictLabel: '测试标签',
        dictValue: 'test_value',
        sortOrder: 1,
        status: 'active',
        description: '测试字典项',
        createdBy: 1,
        updatedBy: 1
      });

      return testDict && testDict.id;
    });

    // 测试获取字典分类
    await this.runTest('api', '获取字典分类', async () => {
      const categories = await dictionaryService.getAllCategories();
      return Array.isArray(categories) && categories.includes('test_category');
    });

    // 测试按分类获取字典项
    await this.runTest('api', '按分类获取字典项', async () => {
      const dictionaries = await dictionaryService.getDictionariesByCategory('test_category');
      return Array.isArray(dictionaries) && dictionaries.length > 0;
    });

    // 测试更新字典项
    await this.runTest('api', '更新字典项', async () => {
      const dict = await Dictionary.findOne({ where: { category: 'test_category' } });
      if (dict) {
        await dictionaryService.updateDictionary(dict.id, {
          dictLabel: '更新后的标签'
        }, 1);
        
        const updated = await Dictionary.findByPk(dict.id);
        return updated.dictLabel === '更新后的标签';
      }
      return false;
    });

    console.log('✅ 本地字典操作测试完成\n');
  }

  /**
   * 测试系统集成
   */
  async testIntegration() {
    console.log('🔗 测试系统集成...');

    // 测试字典数据统计
    await this.runTest('integration', '字典数据统计', async () => {
      const crmService = new CrmDictionaryService();
      const stats = await crmService.getDictionaryStats();
      return Array.isArray(stats);
    });

    // 测试数据清理
    await this.runTest('integration', '数据清理功能', async () => {
      const crmService = new CrmDictionaryService();
      const result = await crmService.cleanupOldDictionaries(365); // 清理一年前的数据
      return typeof result.cleanedRecords === 'number';
    });

    // 清理测试数据
    await this.runTest('integration', '清理测试数据', async () => {
      await Dictionary.destroy({ where: { category: 'test_category' } });
      await CrmDictionary.destroy({ where: { deployId: 'test' } });
      return true;
    });

    console.log('✅ 系统集成测试完成\n');
  }

  /**
   * 运行单个测试
   */
  async runTest(category, testName, testFunction) {
    try {
      const result = await testFunction();
      if (result) {
        this.testResults[category].passed++;
        this.testResults[category].tests.push({ name: testName, status: 'PASSED' });
        console.log(`  ✅ ${testName}`);
      } else {
        this.testResults[category].failed++;
        this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: 'Test returned false' });
        console.log(`  ❌ ${testName} - 测试返回false`);
      }
    } catch (error) {
      this.testResults[category].failed++;
      this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: error.message });
      console.log(`  ❌ ${testName} - ${error.message}`);
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('📋 测试结果汇总:');
    console.log('=' * 50);

    let totalPassed = 0;
    let totalFailed = 0;

    for (const [category, results] of Object.entries(this.testResults)) {
      const categoryName = {
        database: '数据库设置',
        crmSync: 'CRM字典同步',
        api: '本地字典API',
        integration: '系统集成'
      }[category];

      console.log(`\n${categoryName}:`);
      console.log(`  通过: ${results.passed}`);
      console.log(`  失败: ${results.failed}`);

      if (results.failed > 0) {
        console.log('  失败的测试:');
        results.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`    - ${test.name}: ${test.error}`);
          });
      }

      totalPassed += results.passed;
      totalFailed += results.failed;
    }

    console.log('\n' + '=' * 50);
    console.log(`总计: ${totalPassed + totalFailed} 个测试`);
    console.log(`通过: ${totalPassed}`);
    console.log(`失败: ${totalFailed}`);
    console.log(`成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 所有测试通过！字典管理系统运行正常。');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关功能。');
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new DictionarySystemTester();
  tester.runAllTests().catch(console.error);
}

module.exports = DictionarySystemTester;
