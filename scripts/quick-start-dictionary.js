/**
 * 字典管理系统快速启动脚本
 * 
 * 功能说明：
 * - 一键完成字典管理系统的初始化和启动
 * - 检查环境配置
 * - 初始化数据库
 * - 验证系统功能
 * - 提供使用指南
 * 
 * 使用方法：
 * node scripts/quick-start-dictionary.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const DictionaryDatabaseInitializer = require('./init-dictionary-database');
const { sequelize } = require('../src/models');

class QuickStartDictionary {
  constructor() {
    this.steps = [
      { name: '环境检查', status: 'pending' },
      { name: '数据库连接', status: 'pending' },
      { name: '数据库初始化', status: 'pending' },
      { name: '系统验证', status: 'pending' },
      { name: '完成配置', status: 'pending' }
    ];
  }

  /**
   * 运行快速启动流程
   */
  async quickStart() {
    console.log('🚀 字典管理系统快速启动');
    console.log('=' * 50);
    console.log('这个脚本将帮助您快速设置和启动字典管理系统\n');

    try {
      await this.checkEnvironment();
      await this.testDatabaseConnection();
      await this.initializeDatabase();
      await this.verifySystem();
      await this.showCompletionGuide();
      
    } catch (error) {
      console.error('\n❌ 快速启动过程中发生错误:', error.message);
      console.log('\n🔧 故障排除建议:');
      console.log('1. 检查数据库连接配置');
      console.log('2. 确保数据库服务正在运行');
      console.log('3. 验证数据库用户权限');
      console.log('4. 查看详细错误日志');
      process.exit(1);
    }
  }

  /**
   * 检查环境配置
   */
  async checkEnvironment() {
    this.updateStepStatus('环境检查', 'running');
    console.log('🔍 检查环境配置...');

    const requiredEnvVars = [
      'DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      this.updateStepStatus('环境检查', 'failed');
      throw new Error(`缺少必要的环境变量: ${missingVars.join(', ')}`);
    }

    console.log('✅ 环境配置检查通过');
    console.log(`   数据库主机: ${process.env.DB_HOST}:${process.env.DB_PORT}`);
    console.log(`   数据库名称: ${process.env.DB_NAME}`);
    console.log(`   数据库用户: ${process.env.DB_USER}`);

    // 检查CRM配置
    if (process.env.CRM_BASE_URL && process.env.CRM_CORP_ACCESS_TOKEN) {
      console.log('✅ CRM配置已设置');
    } else {
      console.log('⚠️ CRM配置未完整设置（可稍后配置）');
    }

    this.updateStepStatus('环境检查', 'completed');
  }

  /**
   * 测试数据库连接
   */
  async testDatabaseConnection() {
    this.updateStepStatus('数据库连接', 'running');
    console.log('\n🔗 测试数据库连接...');

    try {
      await sequelize.authenticate();
      console.log('✅ 数据库连接成功');
      this.updateStepStatus('数据库连接', 'completed');
    } catch (error) {
      this.updateStepStatus('数据库连接', 'failed');
      throw new Error(`数据库连接失败: ${error.message}`);
    }
  }

  /**
   * 初始化数据库
   */
  async initializeDatabase() {
    this.updateStepStatus('数据库初始化', 'running');
    console.log('\n📊 初始化数据库...');

    try {
      const initializer = new DictionaryDatabaseInitializer();
      
      // 重新实现初始化逻辑，避免重复连接
      await initializer.createTables();
      await initializer.createIndexes();
      await initializer.initializeBaseData();
      
      console.log('✅ 数据库初始化完成');
      this.updateStepStatus('数据库初始化', 'completed');
    } catch (error) {
      this.updateStepStatus('数据库初始化', 'failed');
      throw new Error(`数据库初始化失败: ${error.message}`);
    }
  }

  /**
   * 验证系统功能
   */
  async verifySystem() {
    this.updateStepStatus('系统验证', 'running');
    console.log('\n🔍 验证系统功能...');

    try {
      const { Dictionary, CrmDictionary } = require('../src/models');
      const DictionaryService = require('../src/services/DictionaryService');
      
      // 验证表是否存在
      const tables = await sequelize.getQueryInterface().showAllTables();
      if (!tables.includes('dictionaries') || !tables.includes('crm_dictionaries')) {
        throw new Error('数据表创建不完整');
      }

      // 验证数据
      const dictCount = await Dictionary.count();
      if (dictCount === 0) {
        throw new Error('基础字典数据未创建');
      }

      // 验证服务
      const dictionaryService = new DictionaryService();
      const categories = await dictionaryService.getAllCategories();
      if (categories.length === 0) {
        throw new Error('字典服务异常');
      }

      console.log('✅ 系统验证通过');
      console.log(`   字典数据: ${dictCount} 条记录`);
      console.log(`   字典分类: ${categories.length} 个分类`);
      
      this.updateStepStatus('系统验证', 'completed');
    } catch (error) {
      this.updateStepStatus('系统验证', 'failed');
      throw new Error(`系统验证失败: ${error.message}`);
    }
  }

  /**
   * 显示完成指南
   */
  async showCompletionGuide() {
    this.updateStepStatus('完成配置', 'completed');
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 字典管理系统快速启动完成！');
    console.log('='.repeat(60));

    console.log('\n📌 系统已就绪，您可以：');
    console.log('');
    
    console.log('1️⃣ 启动应用服务:');
    console.log('   npm start');
    console.log('');
    
    console.log('2️⃣ 访问字典管理界面:');
    console.log(`   http://${process.env.DB_HOST}:${process.env.PORT || 3001}/dictionaries`);
    console.log('');
    
    console.log('3️⃣ 配置CRM集成（可选）:');
    console.log('   编辑 .env 文件中的 CRM_CORP_ACCESS_TOKEN 和 CRM_CORP_ID');
    console.log('   然后在字典管理界面中测试CRM连接和同步数据');
    console.log('');
    
    console.log('4️⃣ 测试表单集成:');
    console.log('   访问合作对接管理页面，查看下拉选项是否正常加载');
    console.log('');

    console.log('📚 更多信息:');
    console.log('   查看文档: docs/dictionary-management-system.md');
    console.log('   运行测试: node scripts/test-dictionary-system.js');
    console.log('   功能演示: node scripts/demo-dictionary-system.js');
    console.log('');

    console.log('🔧 如果遇到问题:');
    console.log('   1. 检查数据库连接和权限');
    console.log('   2. 查看应用日志文件');
    console.log('   3. 运行系统测试脚本');
    console.log('   4. 参考故障排除文档');

    console.log('\n' + '='.repeat(60));
    
    // 显示步骤完成状态
    console.log('📋 启动步骤完成状态:');
    this.steps.forEach(step => {
      const statusIcon = step.status === 'completed' ? '✅' : 
                        step.status === 'failed' ? '❌' : 
                        step.status === 'running' ? '🔄' : '⏳';
      console.log(`   ${statusIcon} ${step.name}`);
    });

    await sequelize.close();
  }

  /**
   * 更新步骤状态
   */
  updateStepStatus(stepName, status) {
    const step = this.steps.find(s => s.name === stepName);
    if (step) {
      step.status = status;
    }
  }

  /**
   * 显示进度
   */
  showProgress() {
    const completed = this.steps.filter(s => s.status === 'completed').length;
    const total = this.steps.length;
    const percentage = Math.round((completed / total) * 100);
    
    console.log(`\n📊 进度: ${completed}/${total} (${percentage}%)`);
    
    this.steps.forEach(step => {
      const statusIcon = step.status === 'completed' ? '✅' : 
                        step.status === 'failed' ? '❌' : 
                        step.status === 'running' ? '🔄' : '⏳';
      console.log(`   ${statusIcon} ${step.name}`);
    });
  }
}

// 运行快速启动
if (require.main === module) {
  const quickStart = new QuickStartDictionary();
  quickStart.quickStart().catch(error => {
    console.error('💥 快速启动失败:', error.message);
    process.exit(1);
  });
}

module.exports = QuickStartDictionary;
