/**
 * 种草平台字段映射测试脚本
 * 
 * 功能说明：
 * - 验证达人提报数据到合作对接记录的种草平台字段映射
 * - 检查平台映射逻辑的正确性
 * - 确保字段名称与数据库模型一致
 */

const fs = require('fs');

function testSeedingPlatformMapping() {
  console.log('🔄 测试种草平台字段映射...\n');
  
  let hasErrors = false;
  
  try {
    // 检查InfluencerReportView.vue的修改
    console.log('📋 检查数据预填充逻辑:');
    const reportViewContent = fs.readFileSync('frontend/src/views/InfluencerReportView.vue', 'utf8');
    
    // 检查种草平台字段映射
    if (reportViewContent.includes('seedingPlatform: platformMap[reportData.platform] || reportData.platform')) {
      console.log('✅ 种草平台字段映射已正确添加');
    } else {
      console.log('❌ 种草平台字段映射缺失或不正确');
      hasErrors = true;
    }
    
    // 检查平台映射逻辑
    const platformMapRegex = /const platformMap = \{[\s\S]*?\}/;
    const platformMapMatch = reportViewContent.match(platformMapRegex);
    
    if (platformMapMatch) {
      const platformMapCode = platformMapMatch[0];
      console.log('✅ 平台映射对象已定义');
      
      // 检查具体的平台映射
      if (platformMapCode.includes('xiaohongshu: \'小红书\'')) {
        console.log('✅ 小红书平台映射正确');
      } else {
        console.log('❌ 小红书平台映射缺失');
        hasErrors = true;
      }
      
      if (platformMapCode.includes('douyin: \'抖音\'')) {
        console.log('✅ 抖音平台映射正确');
      } else {
        console.log('❌ 抖音平台映射缺失');
        hasErrors = true;
      }
    } else {
      console.log('❌ 平台映射对象未找到');
      hasErrors = true;
    }
    
    // 检查CooperationForm.vue中的字段定义
    console.log('\n📋 检查表单组件字段定义:');
    const cooperationFormContent = fs.readFileSync('frontend/src/components/CooperationForm.vue', 'utf8');
    
    // 检查表单字段
    if (cooperationFormContent.includes('seedingPlatform: \'\'')) {
      console.log('✅ 表单中种草平台字段已定义');
    } else {
      console.log('❌ 表单中种草平台字段缺失');
      hasErrors = true;
    }
    
    // 检查表单输入组件
    if (cooperationFormContent.includes('v-model="form.seedingPlatform"')) {
      console.log('✅ 表单输入组件正确绑定种草平台字段');
    } else {
      console.log('❌ 表单输入组件未正确绑定种草平台字段');
      hasErrors = true;
    }
    
    // 检查数据库模型
    console.log('\n📋 检查数据库模型字段定义:');
    const modelContent = fs.readFileSync('src/models/CooperationManagement.js', 'utf8');
    
    // 检查模型字段定义
    if (modelContent.includes('seedingPlatform:') && modelContent.includes('field: \'seeding_platform\'')) {
      console.log('✅ 数据库模型中种草平台字段定义正确');
    } else {
      console.log('❌ 数据库模型中种草平台字段定义有问题');
      hasErrors = true;
    }
    
    // 模拟测试数据映射
    console.log('\n📋 模拟数据映射测试:');
    
    const testCases = [
      {
        name: '小红书达人提报',
        input: { platform: 'xiaohongshu', influencerName: '测试达人1' },
        expected: '小红书'
      },
      {
        name: '抖音达人提报',
        input: { platform: 'douyin', influencerName: '测试达人2' },
        expected: '抖音'
      },
      {
        name: '未知平台达人提报',
        input: { platform: 'weibo', influencerName: '测试达人3' },
        expected: 'weibo'
      }
    ];
    
    testCases.forEach(testCase => {
      // 模拟平台映射逻辑
      const platformMap = { xiaohongshu: '小红书', douyin: '抖音' };
      const result = platformMap[testCase.input.platform] || testCase.input.platform;
      
      if (result === testCase.expected) {
        console.log(`✅ ${testCase.name}: ${testCase.input.platform} → ${result}`);
      } else {
        console.log(`❌ ${testCase.name}: 期望 ${testCase.expected}，实际 ${result}`);
        hasErrors = true;
      }
    });
    
    // 检查字段在预填充数据中的位置
    console.log('\n📋 检查字段映射位置:');
    
    const seedingPlatformRegex = /seedingPlatform:\s*platformMap\[reportData\.platform\]\s*\|\|\s*reportData\.platform/;
    if (reportViewContent.match(seedingPlatformRegex)) {
      console.log('✅ 种草平台字段在客户信息模块中正确映射');
      
      // 检查字段位置是否合理（应该在客户信息模块中）
      const customerInfoStart = reportViewContent.indexOf('// 客户信息模块');
      const cooperationInfoStart = reportViewContent.indexOf('// 协议信息模块');
      const seedingPlatformPos = reportViewContent.indexOf('seedingPlatform:');
      
      if (customerInfoStart < seedingPlatformPos && seedingPlatformPos < cooperationInfoStart) {
        console.log('✅ 种草平台字段位置正确（在客户信息模块中）');
      } else {
        console.log('❌ 种草平台字段位置不正确');
        hasErrors = true;
      }
    } else {
      console.log('❌ 种草平台字段映射语法不正确');
      hasErrors = true;
    }
    
  } catch (error) {
    console.log(`❌ 测试过程中发生错误: ${error.message}`);
    hasErrors = true;
  }
  
  console.log('\n🎯 测试结果:');
  if (hasErrors) {
    console.log('❌ 发现问题，请检查修改');
    return false;
  } else {
    console.log('✅ 所有检查通过！');
    console.log('\n💡 映射规则说明:');
    console.log('• xiaohongshu → 小红书');
    console.log('• douyin → 抖音');
    console.log('• 其他平台 → 保持原值');
    console.log('\n📝 字段映射路径:');
    console.log('达人提报.platform → 合作对接记录.seedingPlatform');
    console.log('数据库字段: seeding_platform');
    console.log('\n🎊 种草平台字段映射功能已完成！');
    return true;
  }
}

testSeedingPlatformMapping();
