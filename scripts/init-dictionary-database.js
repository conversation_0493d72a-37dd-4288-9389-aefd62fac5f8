/**
 * 字典管理系统数据库初始化脚本
 * 
 * 功能说明：
 * - 安全地在线上数据库中初始化字典管理系统
 * - 创建CRM字典数据表
 * - 检查并创建必要的索引
 * - 初始化基础字典数据
 * - 验证系统完整性
 * 
 * 使用方法：
 * node scripts/init-dictionary-database.js
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { sequelize, Dictionary, CrmDictionary } = require('../src/models');
const dictionaryService = require('../src/services/DictionaryService');

class DictionaryDatabaseInitializer {
  constructor() {
    this.dictionaryService = dictionaryService;
    this.results = {
      tables: { created: 0, existing: 0, errors: [] },
      indexes: { created: 0, existing: 0, errors: [] },
      data: { created: 0, existing: 0, errors: [] },
      validation: { passed: 0, failed: 0, errors: [] }
    };
  }

  /**
   * 运行完整初始化流程
   */
  async initialize() {
    console.log('🚀 开始初始化字典管理系统数据库...');
    console.log('📊 数据库信息:');
    console.log(`   主机: ${process.env.DB_HOST}`);
    console.log(`   端口: ${process.env.DB_PORT}`);
    console.log(`   数据库: ${process.env.DB_NAME}`);
    console.log(`   用户: ${process.env.DB_USER}`);
    console.log('');

    try {
      // 1. 测试数据库连接
      await this.testConnection();
      
      // 2. 创建数据表
      await this.createTables();
      
      // 3. 创建索引
      await this.createIndexes();
      
      // 4. 初始化基础数据
      await this.initializeBaseData();
      
      // 5. 验证系统
      await this.validateSystem();
      
      // 6. 打印结果
      this.printResults();
      
    } catch (error) {
      console.error('❌ 初始化过程中发生错误:', error);
      throw error;
    } finally {
      await sequelize.close();
    }
  }

  /**
   * 测试数据库连接
   */
  async testConnection() {
    console.log('🔍 测试数据库连接...');
    
    try {
      await sequelize.authenticate();
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  /**
   * 创建数据表
   */
  async createTables() {
    console.log('\n📊 创建数据表...');

    // 创建本地字典表
    try {
      await Dictionary.sync({ alter: true });
      console.log('✅ 本地字典表 (dictionaries) 创建/更新成功');
      this.results.tables.created++;
    } catch (error) {
      console.error('❌ 本地字典表创建失败:', error.message);
      this.results.tables.errors.push(`dictionaries: ${error.message}`);
    }

    // 创建CRM字典表
    try {
      await CrmDictionary.sync({ alter: true });
      console.log('✅ CRM字典表 (crm_dictionaries) 创建/更新成功');
      this.results.tables.created++;
    } catch (error) {
      console.error('❌ CRM字典表创建失败:', error.message);
      this.results.tables.errors.push(`crm_dictionaries: ${error.message}`);
    }
  }

  /**
   * 创建索引
   */
  async createIndexes() {
    console.log('\n🔗 创建数据库索引...');

    const queryInterface = sequelize.getQueryInterface();

    // CRM字典表索引
    const crmIndexes = [
      {
        name: 'idx_crm_deploy_id',
        table: 'crm_dictionaries',
        fields: ['deploy_id']
      },
      {
        name: 'idx_crm_field_code',
        table: 'crm_dictionaries',
        fields: ['field_code']
      },
      {
        name: 'idx_crm_sync_status',
        table: 'crm_dictionaries',
        fields: ['sync_status']
      },
      {
        name: 'idx_crm_local_mapping',
        table: 'crm_dictionaries',
        fields: ['local_category', 'local_key']
      }
    ];

    for (const index of crmIndexes) {
      try {
        await queryInterface.addIndex(index.table, index.fields, {
          name: index.name,
          concurrently: true
        });
        console.log(`✅ 索引创建成功: ${index.name}`);
        this.results.indexes.created++;
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`⚠️ 索引已存在: ${index.name}`);
          this.results.indexes.existing++;
        } else {
          console.error(`❌ 索引创建失败 ${index.name}:`, error.message);
          this.results.indexes.errors.push(`${index.name}: ${error.message}`);
        }
      }
    }
  }

  /**
   * 初始化基础字典数据
   */
  async initializeBaseData() {
    console.log('\n📝 初始化基础字典数据...');

    const baseData = [
      // 合作形式
      { category: 'cooperation_form', dictKey: 'image_text', dictLabel: '图文', sortOrder: 1, description: '图文合作形式' },
      { category: 'cooperation_form', dictKey: 'video', dictLabel: '视频', sortOrder: 2, description: '视频合作形式' },
      { category: 'cooperation_form', dictKey: 'live_stream', dictLabel: '直播', sortOrder: 3, description: '直播合作形式' },
      
      // 合作品牌
      { category: 'cooperation_brand', dictKey: 'brand_a', dictLabel: '品牌A', sortOrder: 1, description: '合作品牌A' },
      { category: 'cooperation_brand', dictKey: 'brand_b', dictLabel: '品牌B', sortOrder: 2, description: '合作品牌B' },
      
      // 返点状态
      { category: 'rebate_status', dictKey: 'completed', dictLabel: '已完成', sortOrder: 1, description: '返点已完成' },
      { category: 'rebate_status', dictKey: 'pending', dictLabel: '进行中', sortOrder: 2, description: '返点进行中' },
      { category: 'rebate_status', dictKey: 'not_applicable', dictLabel: '不适用', sortOrder: 3, description: '不适用返点' },
      
      // 发布平台
      { category: 'publish_platform', dictKey: 'xiaohongshu', dictLabel: '小红书', sortOrder: 1, description: '小红书平台' },
      { category: 'publish_platform', dictKey: 'douyin', dictLabel: '抖音', sortOrder: 2, description: '抖音平台' },
      { category: 'publish_platform', dictKey: 'weibo', dictLabel: '微博', sortOrder: 3, description: '微博平台' },
      
      // 内容植入系数
      { category: 'content_implant_coefficient', dictKey: 'high', dictLabel: '高', sortOrder: 1, description: '高植入系数' },
      { category: 'content_implant_coefficient', dictKey: 'medium', dictLabel: '中', sortOrder: 2, description: '中植入系数' },
      { category: 'content_implant_coefficient', dictKey: 'low', dictLabel: '低', sortOrder: 3, description: '低植入系数' },
      
      // 评论维护系数
      { category: 'comment_maintenance_coefficient', dictKey: 'high', dictLabel: '高', sortOrder: 1, description: '高维护系数' },
      { category: 'comment_maintenance_coefficient', dictKey: 'medium', dictLabel: '中', sortOrder: 2, description: '中维护系数' },
      { category: 'comment_maintenance_coefficient', dictKey: 'low', dictLabel: '低', sortOrder: 3, description: '低维护系数' },
      
      // 品牌话题包含
      { category: 'brand_topic_included', dictKey: 'yes', dictLabel: '是', sortOrder: 1, description: '包含品牌话题' },
      { category: 'brand_topic_included', dictKey: 'no', dictLabel: '否', sortOrder: 2, description: '不包含品牌话题' },
      
      // 自我评价
      { category: 'self_evaluation', dictKey: 'excellent', dictLabel: '优秀', sortOrder: 1, description: '优秀评价' },
      { category: 'self_evaluation', dictKey: 'good', dictLabel: '良好', sortOrder: 2, description: '良好评价' },
      { category: 'self_evaluation', dictKey: 'average', dictLabel: '一般', sortOrder: 3, description: '一般评价' }
    ];

    for (const data of baseData) {
      try {
        // 检查是否已存在
        const existing = await Dictionary.findOne({
          where: {
            category: data.category,
            dictKey: data.dictKey
          }
        });

        if (existing) {
          console.log(`⚠️ 字典项已存在: ${data.category}.${data.dictKey}`);
          this.results.data.existing++;
        } else {
          await this.dictionaryService.createDictionary({
            ...data,
            status: 'active'
          }, 1); // 使用系统用户ID 1
          
          console.log(`✅ 创建字典项: ${data.category}.${data.dictKey} = ${data.dictLabel}`);
          this.results.data.created++;
        }
      } catch (error) {
        console.error(`❌ 创建字典项失败 ${data.category}.${data.dictKey}:`, error.message);
        this.results.data.errors.push(`${data.category}.${data.dictKey}: ${error.message}`);
      }
    }
  }

  /**
   * 验证系统完整性
   */
  async validateSystem() {
    console.log('\n🔍 验证系统完整性...');

    // 验证表是否存在
    try {
      const tables = await sequelize.getQueryInterface().showAllTables();
      
      if (tables.includes('dictionaries')) {
        console.log('✅ 本地字典表验证通过');
        this.results.validation.passed++;
      } else {
        console.log('❌ 本地字典表不存在');
        this.results.validation.failed++;
      }

      if (tables.includes('crm_dictionaries')) {
        console.log('✅ CRM字典表验证通过');
        this.results.validation.passed++;
      } else {
        console.log('❌ CRM字典表不存在');
        this.results.validation.failed++;
      }
    } catch (error) {
      console.error('❌ 表验证失败:', error.message);
      this.results.validation.errors.push(`表验证: ${error.message}`);
    }

    // 验证数据
    try {
      const dictCount = await Dictionary.count();
      console.log(`✅ 本地字典数据验证通过: ${dictCount} 条记录`);
      this.results.validation.passed++;
    } catch (error) {
      console.error('❌ 字典数据验证失败:', error.message);
      this.results.validation.errors.push(`数据验证: ${error.message}`);
    }

    // 验证服务
    try {
      const categories = await this.dictionaryService.getAllCategories();
      console.log(`✅ 字典服务验证通过: ${categories.length} 个分类`);
      this.results.validation.passed++;
    } catch (error) {
      console.error('❌ 字典服务验证失败:', error.message);
      this.results.validation.errors.push(`服务验证: ${error.message}`);
    }
  }

  /**
   * 打印初始化结果
   */
  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 数据库初始化结果汇总');
    console.log('='.repeat(60));

    console.log('\n📊 数据表:');
    console.log(`   创建/更新: ${this.results.tables.created}`);
    console.log(`   错误: ${this.results.tables.errors.length}`);
    if (this.results.tables.errors.length > 0) {
      this.results.tables.errors.forEach(error => console.log(`     - ${error}`));
    }

    console.log('\n🔗 索引:');
    console.log(`   创建: ${this.results.indexes.created}`);
    console.log(`   已存在: ${this.results.indexes.existing}`);
    console.log(`   错误: ${this.results.indexes.errors.length}`);
    if (this.results.indexes.errors.length > 0) {
      this.results.indexes.errors.forEach(error => console.log(`     - ${error}`));
    }

    console.log('\n📝 基础数据:');
    console.log(`   创建: ${this.results.data.created}`);
    console.log(`   已存在: ${this.results.data.existing}`);
    console.log(`   错误: ${this.results.data.errors.length}`);
    if (this.results.data.errors.length > 0) {
      this.results.data.errors.forEach(error => console.log(`     - ${error}`));
    }

    console.log('\n🔍 系统验证:');
    console.log(`   通过: ${this.results.validation.passed}`);
    console.log(`   失败: ${this.results.validation.failed}`);
    if (this.results.validation.errors.length > 0) {
      this.results.validation.errors.forEach(error => console.log(`     - ${error}`));
    }

    const totalErrors = this.results.tables.errors.length + 
                       this.results.indexes.errors.length + 
                       this.results.data.errors.length + 
                       this.results.validation.errors.length;

    console.log('\n' + '='.repeat(60));
    if (totalErrors === 0) {
      console.log('🎉 字典管理系统初始化完成！所有组件运行正常。');
      console.log('\n📌 下一步操作:');
      console.log('   1. 启动应用服务: npm start');
      console.log('   2. 访问字典管理: http://60.205.165.41:3001/dictionaries');
      console.log('   3. 测试CRM连接和数据同步');
    } else {
      console.log(`⚠️ 初始化完成，但有 ${totalErrors} 个错误需要处理。`);
      console.log('请检查上述错误信息并进行相应修复。');
    }
    console.log('='.repeat(60));
  }
}

// 运行初始化
if (require.main === module) {
  const initializer = new DictionaryDatabaseInitializer();
  initializer.initialize().catch(error => {
    console.error('💥 初始化失败:', error);
    process.exit(1);
  });
}

module.exports = DictionaryDatabaseInitializer;
