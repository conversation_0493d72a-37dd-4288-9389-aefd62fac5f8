/**
 * Excel服务测试脚本
 * 测试Excel文件生成功能的各种场景
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

// 配置
const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api/excel`;

class ExcelServiceTester {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始Excel服务功能测试...\n');

    try {
      // 测试服务状态
      await this.testServiceStatus();
      
      // 测试生成示例文件
      await this.testGenerateSample();
      
      // 测试生成简单Excel文件
      await this.testGenerateSimpleExcel();
      
      // 测试生成多工作表Excel文件
      await this.testGenerateMultiSheetExcel();
      
      // 测试文件检查功能
      await this.testFileCheck();
      
      // 测试错误处理
      await this.testErrorHandling();
      
      // 显示测试结果
      this.showTestResults();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error.message);
    }
  }

  /**
   * 测试服务状态
   */
  async testServiceStatus() {
    console.log('📋 测试1: 检查服务状态...');
    
    try {
      const response = await axios.get(`${API_BASE}/status`);
      
      if (response.data.success) {
        console.log('✅ 服务状态正常');
        console.log(`   - 导出目录: ${response.data.data.exportDirectory}`);
        console.log(`   - 文件数量: ${response.data.data.fileCount}`);
        console.log(`   - 总大小: ${response.data.data.totalSizeFormatted}`);
        this.addTestResult('服务状态检查', true);
      } else {
        throw new Error('服务状态异常');
      }
    } catch (error) {
      console.log('❌ 服务状态检查失败:', error.message);
      this.addTestResult('服务状态检查', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试生成示例文件
   */
  async testGenerateSample() {
    console.log('📋 测试2: 生成示例Excel文件...');
    
    try {
      const response = await axios.post(`${API_BASE}/generate-sample`);
      
      if (response.data.success) {
        console.log('✅ 示例文件生成成功');
        console.log(`   - 文件名: ${response.data.data.fileName}`);
        console.log(`   - 下载链接: ${response.data.data.downloadUrl}`);
        console.log(`   - 文件大小: ${response.data.data.fileSizeFormatted}`);
        console.log(`   - 记录数量: ${response.data.data.recordCount}`);
        this.addTestResult('示例文件生成', true);
        
        // 验证文件是否真的存在
        await this.verifyFileExists(response.data.data.downloadUrl);
      } else {
        throw new Error('示例文件生成失败');
      }
    } catch (error) {
      console.log('❌ 示例文件生成失败:', error.message);
      this.addTestResult('示例文件生成', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试生成简单Excel文件
   */
  async testGenerateSimpleExcel() {
    console.log('📋 测试3: 生成简单Excel文件...');
    
    const testData = [
      {
        'ID': 1,
        '产品名称': 'iPhone 15',
        '价格': 5999,
        '库存': 100,
        '分类': '手机',
        '上架时间': new Date().toLocaleString('zh-CN')
      },
      {
        'ID': 2,
        '产品名称': 'MacBook Pro',
        '价格': 12999,
        '库存': 50,
        '分类': '电脑',
        '上架时间': new Date().toLocaleString('zh-CN')
      },
      {
        'ID': 3,
        '产品名称': 'AirPods Pro',
        '价格': 1999,
        '库存': 200,
        '分类': '耳机',
        '上架时间': new Date().toLocaleString('zh-CN')
      }
    ];
    
    try {
      const response = await axios.post(`${API_BASE}/generate`, {
        data: testData,
        fileName: '产品列表测试',
        formatting: {
          headerStyle: true
        }
      });
      
      if (response.data.success) {
        console.log('✅ 简单Excel文件生成成功');
        console.log(`   - 文件名: ${response.data.data.fileName}`);
        console.log(`   - 下载链接: ${response.data.data.downloadUrl}`);
        console.log(`   - 文件大小: ${response.data.data.fileSizeFormatted}`);
        this.addTestResult('简单Excel文件生成', true);
        
        // 验证文件是否真的存在
        await this.verifyFileExists(response.data.data.downloadUrl);
      } else {
        throw new Error('简单Excel文件生成失败');
      }
    } catch (error) {
      console.log('❌ 简单Excel文件生成失败:', error.message);
      this.addTestResult('简单Excel文件生成', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试生成多工作表Excel文件
   */
  async testGenerateMultiSheetExcel() {
    console.log('📋 测试4: 生成多工作表Excel文件...');
    
    const sheetsData = [
      {
        name: '用户信息',
        data: [
          { 'ID': 1, '姓名': '张三', '年龄': 25, '部门': '技术部' },
          { 'ID': 2, '姓名': '李四', '年龄': 30, '部门': '产品部' },
          { 'ID': 3, '姓名': '王五', '年龄': 28, '部门': '设计部' }
        ]
      },
      {
        name: '销售数据',
        data: [
          { '月份': '2024-01', '销售额': 100000, '订单数': 150 },
          { '月份': '2024-02', '销售额': 120000, '订单数': 180 },
          { '月份': '2024-03', '销售额': 110000, '订单数': 165 }
        ]
      },
      {
        name: '库存统计',
        data: [
          { '商品': 'iPhone', '库存': 100, '预警线': 50 },
          { '商品': 'iPad', '库存': 80, '预警线': 30 },
          { '商品': 'MacBook', '库存': 25, '预警线': 20 }
        ]
      }
    ];
    
    try {
      const response = await axios.post(`${API_BASE}/generate-multi`, {
        sheets: sheetsData,
        fileName: '综合报表测试',
        formatting: {
          headerStyle: true
        }
      });
      
      if (response.data.success) {
        console.log('✅ 多工作表Excel文件生成成功');
        console.log(`   - 文件名: ${response.data.data.fileName}`);
        console.log(`   - 下载链接: ${response.data.data.downloadUrl}`);
        console.log(`   - 文件大小: ${response.data.data.fileSizeFormatted}`);
        console.log(`   - 工作表数量: ${response.data.data.sheetsCount}`);
        this.addTestResult('多工作表Excel文件生成', true);
        
        // 验证文件是否真的存在
        await this.verifyFileExists(response.data.data.downloadUrl);
      } else {
        throw new Error('多工作表Excel文件生成失败');
      }
    } catch (error) {
      console.log('❌ 多工作表Excel文件生成失败:', error.message);
      this.addTestResult('多工作表Excel文件生成', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试文件检查功能
   */
  async testFileCheck() {
    console.log('📋 测试5: 文件检查功能...');
    
    try {
      // 检查存在的文件
      const existingFileName = 'sample_data_20241215_12345678.xlsx'; // 假设的文件名
      const response1 = await axios.get(`${API_BASE}/check/${existingFileName}`);
      
      // 检查不存在的文件
      const nonExistentFileName = 'non_existent_file.xlsx';
      const response2 = await axios.get(`${API_BASE}/check/${nonExistentFileName}`);
      
      console.log('✅ 文件检查功能正常');
      console.log(`   - 不存在文件检查: ${!response2.data.data.exists ? '正确' : '错误'}`);
      this.addTestResult('文件检查功能', true);
      
    } catch (error) {
      console.log('❌ 文件检查功能失败:', error.message);
      this.addTestResult('文件检查功能', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log('📋 测试6: 错误处理...');
    
    try {
      // 测试空数据
      try {
        await axios.post(`${API_BASE}/generate`, { data: [] });
        console.log('❌ 空数据应该返回错误');
        this.addTestResult('错误处理-空数据', false, '应该返回错误但没有');
      } catch (error) {
        if (error.response && error.response.status === 400) {
          console.log('✅ 空数据错误处理正确');
          this.addTestResult('错误处理-空数据', true);
        } else {
          throw error;
        }
      }
      
      // 测试无效数据格式
      try {
        await axios.post(`${API_BASE}/generate`, { data: "invalid data" });
        console.log('❌ 无效数据格式应该返回错误');
        this.addTestResult('错误处理-无效格式', false, '应该返回错误但没有');
      } catch (error) {
        if (error.response && error.response.status === 400) {
          console.log('✅ 无效数据格式错误处理正确');
          this.addTestResult('错误处理-无效格式', true);
        } else {
          throw error;
        }
      }
      
    } catch (error) {
      console.log('❌ 错误处理测试失败:', error.message);
      this.addTestResult('错误处理', false, error.message);
    }
    
    console.log('');
  }

  /**
   * 验证文件是否存在
   */
  async verifyFileExists(downloadUrl) {
    try {
      const response = await axios.head(downloadUrl);
      if (response.status === 200) {
        console.log('   ✅ 文件确实存在且可访问');
        return true;
      }
    } catch (error) {
      console.log('   ⚠️ 文件验证失败:', error.message);
      return false;
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, error = null) {
    this.testResults.push({
      name: testName,
      success,
      error
    });
  }

  /**
   * 显示测试结果
   */
  showTestResults() {
    console.log('📊 测试结果汇总:');
    console.log('='.repeat(50));
    
    let passedCount = 0;
    let failedCount = 0;
    
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅ 通过' : '❌ 失败';
      console.log(`${index + 1}. ${result.name}: ${status}`);
      
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
      
      if (result.success) {
        passedCount++;
      } else {
        failedCount++;
      }
    });
    
    console.log('='.repeat(50));
    console.log(`总计: ${this.testResults.length} 个测试`);
    console.log(`通过: ${passedCount} 个`);
    console.log(`失败: ${failedCount} 个`);
    console.log(`成功率: ${((passedCount / this.testResults.length) * 100).toFixed(1)}%`);
    
    if (failedCount === 0) {
      console.log('\n🎉 所有测试都通过了！Excel服务功能正常！');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关功能。');
    }
  }
}

// 运行测试
async function runTests() {
  const tester = new ExcelServiceTester();
  await tester.runAllTests();
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = ExcelServiceTester;
