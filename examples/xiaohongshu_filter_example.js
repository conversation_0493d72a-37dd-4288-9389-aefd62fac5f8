/**
 * 小红书爬虫首个帖子播放量过滤功能使用示例
 * 
 * 本示例展示如何使用新增的minFirstNotePlayCount参数来过滤达人
 */

const XiaohongshuCrawler = require('../src/services/crawler/crawlers/XiaohongshuCrawler');

/**
 * 示例1: 基础使用 - 设置播放量阈值
 */
async function basicFilterExample() {
  console.log('📋 示例1: 基础播放量过滤\n');

  const crawler = new XiaohongshuCrawler();
  await crawler.initialize();

  const config = {
    keywords: '美妆',
    maxPages: 2,
    pageSize: 10,
    minFirstNotePlayCount: 5000, // 首个帖子最低播放量5000
    saveVideos: false
  };

  const callbacks = {
    onProgress: async (progress) => {
      console.log(`进度: ${progress.percentage}% (成功:${progress.successCount}, 失败:${progress.failedCount})`);
    },
    onResult: async (result) => {
      console.log(`✅ 获取达人: ${result.nickname} - 粉丝:${result.followersCount}`);
    }
  };

  const results = await crawler.crawl(config, callbacks);
  console.log(`\n结果: 总计${results.totalCount}, 成功${results.successCount}, 失败${results.failedCount}\n`);
}

/**
 * 示例2: 高质量过滤 - 设置较高阈值
 */
async function highQualityFilterExample() {
  console.log('📋 示例2: 高质量内容过滤\n');

  const crawler = new XiaohongshuCrawler();
  await crawler.initialize();

  const config = {
    keywords: '护肤',
    maxPages: 1,
    pageSize: 20,
    minFirstNotePlayCount: 20000, // 首个帖子最低播放量2万
    saveVideos: false
  };

  const callbacks = {
    onProgress: async (progress) => {
      console.log(`高质量过滤进度: ${progress.percentage}%`);
    },
    onResult: async (result) => {
      console.log(`🌟 高质量达人: ${result.nickname}`);
      console.log(`   粉丝数: ${result.followersCount}`);
      console.log(`   城市: ${result.city}`);
    }
  };

  const results = await crawler.crawl(config, callbacks);
  console.log(`\n高质量过滤结果: ${results.successCount}/${results.totalCount} 达人通过筛选\n`);
}

/**
 * 示例3: 对比测试 - 不同阈值的效果对比
 */
async function comparisonExample() {
  console.log('📋 示例3: 不同阈值效果对比\n');

  const crawler = new XiaohongshuCrawler();
  await crawler.initialize();

  const baseConfig = {
    keywords: '时尚',
    maxPages: 1,
    pageSize: 10,
    saveVideos: false
  };

  const thresholds = [1000, 5000, 10000, 20000];
  
  for (const threshold of thresholds) {
    console.log(`🎯 测试阈值: ${threshold}`);
    
    const config = {
      ...baseConfig,
      minFirstNotePlayCount: threshold
    };

    const results = await crawler.crawl(config, {
      onProgress: async () => {}, // 静默处理
      onResult: async () => {},   // 静默处理
    });

    console.log(`   结果: ${results.successCount}/${results.totalCount} 达人通过 (${Math.round(results.successCount/results.totalCount*100)}%)`);
    
    // 短暂延迟避免请求过频
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  console.log('');
}

/**
 * 示例4: 单独测试检查方法
 */
async function checkMethodExample() {
  console.log('📋 示例4: 单独测试检查方法\n');

  const crawler = new XiaohongshuCrawler();
  await crawler.initialize();

  // 注意: 这里需要替换为实际的达人ID
  const testAuthorIds = [
    '5f8a9b2c3d4e5f6789012345', // 示例ID1
    '6a9b8c3d4e5f6789012346'   // 示例ID2
  ];

  const minPlayCount = 8000;

  for (const authorId of testAuthorIds) {
    console.log(`🔍 检查达人: ${authorId}`);
    
    try {
      const result = await crawler.checkFirstNotePlayCount(authorId, minPlayCount);
      
      console.log(`   结果: ${result.passed ? '✅ 通过' : '❌ 未通过'}`);
      console.log(`   播放量: ${result.firstNotePlayCount}`);
      console.log(`   帖子ID: ${result.noteId || '无'}`);
      console.log(`   原因: ${result.reason}`);
    } catch (error) {
      console.log(`   ❌ 检查失败: ${error.message}`);
    }
    
    console.log('');
  }
}

/**
 * 示例5: 完整的爬取流程配置
 */
async function fullConfigExample() {
  console.log('📋 示例5: 完整配置示例\n');

  const crawler = new XiaohongshuCrawler();
  await crawler.initialize();

  const config = {
    // 基础搜索配置
    keywords: '美食',
    maxPages: 3,
    pageSize: 15,
    
    // 新增：首个帖子播放量过滤
    minFirstNotePlayCount: 8000,
    
    // 其他配置
    saveVideos: true,
    crawlTaskId: 'example_task_001'
  };

  const callbacks = {
    onProgress: async (progress) => {
      console.log(`📊 爬取进度: ${progress.currentPage}/${progress.totalPages} (${progress.percentage}%)`);
      console.log(`   当前统计: 成功 ${progress.successCount}, 失败 ${progress.failedCount}`);
    },
    
    onResult: async (result) => {
      console.log(`🎉 新增达人: ${result.nickname}`);
      console.log(`   平台ID: ${result.platformUserId}`);
      console.log(`   粉丝数: ${result.followersCount}`);
      console.log(`   视频数: ${result.videoStats.videoCount}`);
      console.log(`   平均播放: ${result.videoStats.averagePlay}`);
    },
    
    onError: async (error) => {
      console.log(`❌ 处理错误: ${error.message}`);
    }
  };

  console.log('🚀 开始完整爬取流程...\n');
  const results = await crawler.crawl(config, callbacks);

  console.log('\n📊 最终统计:');
  console.log(`   搜索关键词: ${config.keywords}`);
  console.log(`   播放量阈值: ${config.minFirstNotePlayCount}`);
  console.log(`   总计达人: ${results.totalCount}`);
  console.log(`   成功获取: ${results.successCount}`);
  console.log(`   失败/跳过: ${results.failedCount}`);
  console.log(`   成功率: ${Math.round(results.successCount/results.totalCount*100)}%`);
  
  if (results.data.length > 0) {
    console.log('\n✅ 成功获取的达人列表:');
    results.data.forEach((author, index) => {
      console.log(`   ${index + 1}. ${author.nickname} (粉丝: ${author.followersCount})`);
    });
  }
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
  console.log('🎯 小红书爬虫首个帖子播放量过滤功能示例\n');
  console.log('=' * 60 + '\n');

  try {
    // 示例1: 基础使用
    await basicFilterExample();
    console.log('-' * 40 + '\n');

    // 示例2: 高质量过滤
    await highQualityFilterExample();
    console.log('-' * 40 + '\n');

    // 示例3: 对比测试
    await comparisonExample();
    console.log('-' * 40 + '\n');

    // 示例4: 单独测试
    await checkMethodExample();
    console.log('-' * 40 + '\n');

    // 示例5: 完整配置
    await fullConfigExample();

    console.log('\n🏁 所有示例运行完成！');

  } catch (error) {
    console.error('❌ 示例运行失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 如果直接运行此文件，则执行所有示例
if (require.main === module) {
  runAllExamples().catch(console.error);
}

module.exports = {
  basicFilterExample,
  highQualityFilterExample,
  comparisonExample,
  checkMethodExample,
  fullConfigExample,
  runAllExamples
};
