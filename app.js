const Koa = require('koa');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const serve = require('koa-static');
const path = require('path');
require('dotenv').config();

// 导入中间件
const errorHandler = require('./src/middleware/errorHandler');
const logger = require('./src/middleware/logger');
// 导入路由
const routes = require('./src/routes');
const { swaggerMiddleware, swaggerJsonMiddleware } = require('./src/middleware/swagger');

// 导入数据库
const { testConnection, initializeAndSyncDatabase } = require('./src/models');

// 导入爬虫服务
const crawlerService = require('./src/services/crawler');
// 导入合作对接定时任务服务
const cooperationScheduleService = require('./src/services/CooperationScheduleService');

const app = new Koa();

// 全局错误处理中间件（必须在最前面）
app.use(errorHandler);

// 请求日志
app.use(logger);

// 简单的CORS处理
app.use(async (ctx, next) => {
  ctx.set('Access-Control-Allow-Origin', '*');
  ctx.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  ctx.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (ctx.method === 'OPTIONS') {
    ctx.status = 200;
    return;
  }

  await next();
});

// 请求体解析
app.use(bodyParser({
  enableTypes: ['json', 'form'],
  jsonLimit: '10mb',
  formLimit: '10mb',
  textLimit: '10mb'
}));

// 静态文件服务
app.use(serve(path.join(__dirname, 'public')));

// Swagger API文档
app.use(swaggerJsonMiddleware);
app.use(swaggerMiddleware);

// 注册路由
app.use(routes.routes());
app.use(routes.allowedMethods());

// 404处理
app.use(async (ctx) => {
  ctx.status = 404;
  ctx.body = {
    success: false,
    message: '接口不存在'
  };
});

// 错误事件监听
app.on('error', (err, ctx) => {
  console.error('服务器错误:', err);
});

// 启动服务器
const PORT = process.env.PORT || 3000;

const startServer = async () => {
  try {
    // 测试数据库连接
    await testConnection();

    // 智能数据库初始化和同步
    await initializeAndSyncDatabase();

    // 初始化爬虫服务
    await crawlerService.initialize();

    // 初始化合作对接定时任务服务
    await cooperationScheduleService.initialize();

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`🚀 服务器启动成功！`);
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 API信息: http://localhost:${PORT}/api`);
      console.log(`📖 API文档: http://localhost:${PORT}/api-docs`);
      console.log(`📋 OpenAPI规范: http://localhost:${PORT}/api-docs.json`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，正在关闭服务器...');
  process.exit(0);
});

startServer();

module.exports = app;
