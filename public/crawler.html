<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>爬虫管理 - 达人信息管理系统</title>
    <link rel="stylesheet" href="css/style.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <style>
      .crawler-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
      }

      .task-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #667eea;
      }

      .task-status {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
      }

      .status-pending {
        background: #ffeaa7;
        color: #2d3436;
      }
      .status-running {
        background: #74b9ff;
        color: white;
      }
      .status-completed {
        background: #00b894;
        color: white;
      }
      .status-failed {
        background: #e17055;
        color: white;
      }
      .status-paused {
        background: #a29bfe;
        color: white;
      }
      .status-cancelled {
        background: #636e72;
        color: white;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: #f1f2f6;
        border-radius: 4px;
        overflow: hidden;
        margin: 10px 0;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transition: width 0.3s ease;
      }

      .task-actions {
        display: flex;
        gap: 8px;
        margin-top: 15px;
      }

      .task-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-size: 14px;
        color: #666;
      }

      .platform-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
      }

      .platform-juxingtu {
        background: #ff6b6b;
        color: white;
      }
      .platform-xiaohongshu {
        background: #ff4757;
        color: white;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }

      .stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #667eea;
      }

      .stat-label {
        color: #666;
        font-size: 14px;
        margin-top: 5px;
      }

      .create-task-form {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
      }

      .form-group {
        margin-bottom: 15px;
      }

      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #333;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }

      .form-group textarea {
        resize: vertical;
        min-height: 80px;
      }

      .task-filters {
        background: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
      }

      .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .filter-group label {
        font-size: 14px;
        color: #666;
      }

      .filter-group select {
        padding: 6px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }

      @media (max-width: 768px) {
        .form-row {
          grid-template-columns: 1fr;
        }

        .task-filters {
          flex-direction: column;
          align-items: stretch;
        }

        .filter-group {
          justify-content: space-between;
        }

        .stats-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 顶部导航 -->
      <nav class="navbar">
        <div class="nav-brand">
          <i class="fas fa-robot"></i>
          <span>爬虫管理</span>
        </div>
        <div class="nav-menu">
          <a href="index.html" class="nav-link"> <i class="fas fa-users"></i> 达人管理 </a>
          <a href="crawler.html" class="nav-link active"> <i class="fas fa-robot"></i> 爬虫管理 </a>
          <div class="nav-user">
            <span id="userWelcome">欢迎，用户</span>
            <button id="logoutBtn" class="btn btn-outline">
              <i class="fas fa-sign-out-alt"></i> 退出
            </button>
          </div>
        </div>
      </nav>

      <!-- 页面头部 -->
      <div class="crawler-header">
        <h1><i class="fas fa-robot"></i> 爬虫任务管理</h1>
        <p>创建和管理达人信息爬虫任务，支持巨量星图和小红书平台</p>
      </div>

      <!-- 统计信息 -->
      <div class="stats-grid" id="statsGrid">
        <div class="stat-card">
          <div class="stat-number" id="totalTasks">0</div>
          <div class="stat-label">总任务数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="runningTasks">0</div>
          <div class="stat-label">运行中</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="completedTasks">0</div>
          <div class="stat-label">已完成</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="totalResults">0</div>
          <div class="stat-label">爬取结果</div>
        </div>
      </div>

      <!-- 创建任务表单 -->
      <div class="create-task-form" id="createTaskForm" style="display: none">
        <h3><i class="fas fa-plus"></i> 创建新任务</h3>
        <form id="taskForm">
          <div class="form-row">
            <div class="form-group">
              <label for="taskName">任务名称 *</label>
              <input
                type="text"
                id="taskName"
                name="taskName"
                required
                placeholder="请输入任务名称"
              />
            </div>
            <div class="form-group">
              <label for="platform">爬取平台 *</label>
              <select id="platform" name="platform" required>
                <option value="">请选择平台</option>
                <option value="juxingtu">巨量星图</option>
                <option value="xiaohongshu">小红书</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="keywords">搜索关键词 *</label>
              <input
                type="text"
                id="keywords"
                name="keywords"
                required
                placeholder="请输入搜索关键词"
              />
            </div>
            <div class="form-group">
              <label for="maxPages">最大页数</label>
              <input
                type="number"
                id="maxPages"
                name="maxPages"
                min="1"
                max="50"
                value="5"
                placeholder="1-50"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="priority">优先级</label>
              <select id="priority" name="priority">
                <option value="0">普通</option>
                <option value="1">高</option>
                <option value="2">紧急</option>
              </select>
            </div>
            <div class="form-group">
              <label for="pageSize">每页数量</label>
              <input
                type="number"
                id="pageSize"
                name="pageSize"
                min="10"
                max="100"
                value="20"
                placeholder="10-100"
              />
            </div>
          </div>

          <div class="form-group">
            <label for="description">任务描述</label>
            <textarea
              id="description"
              name="description"
              placeholder="请输入任务描述（可选）"
            ></textarea>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-plus"></i> 创建任务
            </button>
            <button type="button" class="btn btn-outline" id="cancelCreateBtn">
              <i class="fas fa-times"></i> 取消
            </button>
          </div>
        </form>
      </div>

      <!-- 任务筛选 -->
      <div class="task-filters">
        <div class="filter-group">
          <label>状态:</label>
          <select id="statusFilter">
            <option value="">全部状态</option>
            <option value="pending">待执行</option>
            <option value="running">运行中</option>
            <option value="completed">已完成</option>
            <option value="failed">失败</option>
            <option value="paused">已暂停</option>
          </select>
        </div>
        <div class="filter-group">
          <label>平台:</label>
          <select id="platformFilter">
            <option value="">全部平台</option>
            <option value="juxingtu">巨量星图</option>
            <option value="xiaohongshu">小红书</option>
          </select>
        </div>
        <div class="filter-group">
          <button class="btn btn-primary" id="createTaskBtn">
            <i class="fas fa-plus"></i> 创建任务
          </button>
          <button class="btn btn-warning" id="batchRetryBtn" style="display: none">
            <i class="fas fa-redo"></i> 批量重试失败任务
          </button>
          <button class="btn btn-outline" id="refreshTasksBtn">
            <i class="fas fa-sync-alt"></i> 刷新
          </button>
        </div>
      </div>

      <!-- 任务列表 -->
      <div id="tasksList">
        <!-- 任务卡片将通过JavaScript动态生成 -->
      </div>

      <!-- 分页 -->
      <div class="pagination" id="pagination" style="display: none">
        <button class="btn btn-outline" id="prevPageBtn">
          <i class="fas fa-chevron-left"></i> 上一页
        </button>
        <div class="page-numbers" id="pageNumbers"></div>
        <button class="btn btn-outline" id="nextPageBtn">
          下一页 <i class="fas fa-chevron-right"></i>
        </button>
        <div class="pagination-info" id="paginationInfo"></div>
      </div>
    </div>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>加载中...</p>
      </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer" class="message-container"></div>

    <!-- JavaScript -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/crawler.js"></script>
  </body>
</html>
