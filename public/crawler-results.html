<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>爬虫结果详情 - AgentX</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <!-- 自定义样式 -->
    <link href="css/style.css" rel="stylesheet" />

    <style>
      .result-card {
        transition: all 0.3s ease;
        border: 1px solid #e0e0e0;
      }
      .result-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      .avatar-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
      }
      .status-badge {
        font-size: 0.75rem;
      }
      .contact-info {
        font-size: 0.85rem;
      }
      .video-stats {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 8px;
        margin: 4px 0;
      }
      .search-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
      }
      .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
      }
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <a class="navbar-brand" href="index.html"> <i class="fas fa-robot me-2"></i>AgentX </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link" href="index.html"> <i class="fas fa-home me-1"></i>首页 </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="influencer-management.html">
                <i class="fas fa-users me-1"></i>达人管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="crawler.html">
                <i class="fas fa-spider me-1"></i>爬虫管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="cookies.html">
                <i class="fas fa-cookie-bite me-1"></i>Cookie管理
              </a>
            </li>
          </ul>

          <ul class="navbar-nav">
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="navbarDropdown"
                role="button"
                data-bs-toggle="dropdown"
              >
                <i class="fas fa-user me-1"></i><span id="userWelcome">用户</span>
              </a>
              <ul class="dropdown-menu">
                <li>
                  <a class="dropdown-item" href="#" id="logoutBtn">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- 面包屑导航 -->
    <div class="container mt-3">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="index.html">首页</a></li>
          <li class="breadcrumb-item"><a href="crawler.html">爬虫管理</a></li>
          <li class="breadcrumb-item active">任务结果详情</li>
        </ol>
      </nav>
    </div>

    <!-- 搜索和统计区域 -->
    <div class="search-section">
      <div class="container">
        <div class="row">
          <div class="col-md-8">
            <h2 class="mb-3">
              <i class="fas fa-chart-line me-2"></i>
              任务结果详情
              <small class="fs-6" id="taskTitle">加载中...</small>
            </h2>

            <!-- 搜索表单 -->
            <div class="row g-3">
              <div class="col-md-6">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input
                    type="text"
                    class="form-control"
                    id="searchKeyword"
                    placeholder="搜索达人昵称、平台ID..."
                  />
                </div>
              </div>
              <div class="col-md-3">
                <select class="form-select" id="platformFilter">
                  <option value="">全部平台</option>
                  <option value="xiaohongshu">小红书</option>
                  <option value="juxingtu">巨量星图</option>
                </select>
              </div>
              <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                  <option value="">全部状态</option>
                  <option value="pending">待处理</option>
                  <option value="processed">已处理</option>
                  <option value="imported">已导入</option>
                  <option value="failed">失败</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="col-md-4">
            <div class="stats-card">
              <div class="row text-center">
                <div class="col-4">
                  <div class="h4 mb-0 text-primary" id="totalResults">0</div>
                  <small>总记录</small>
                </div>
                <div class="col-4">
                  <div class="h4 mb-0 text-success" id="validResults">0</div>
                  <small>有效记录</small>
                </div>
                <div class="col-4">
                  <div class="h4 mb-0 text-danger" id="failedResults">0</div>
                  <small>失败记录</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container">
      <!-- 工具栏 -->
      <div class="row mb-3">
        <div class="col-md-6">
          <button class="btn btn-outline-primary" id="refreshBtn">
            <i class="fas fa-sync-alt me-1"></i>刷新
          </button>
          <button class="btn btn-outline-secondary" id="backBtn">
            <i class="fas fa-arrow-left me-1"></i>返回任务列表
          </button>
        </div>
        <div class="col-md-6 text-end">
          <button class="btn btn-success" id="exportBtn">
            <i class="fas fa-download me-1"></i>导出Excel
          </button>
          <button class="btn btn-primary" id="importSelectedBtn" disabled>
            <i class="fas fa-plus me-1"></i>导入选中到达人库
          </button>
        </div>
      </div>

      <!-- 结果列表 -->
      <div class="row" id="resultsList">
        <!-- 加载状态 -->
        <div class="col-12 text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <div class="mt-2">正在加载爬虫结果...</div>
        </div>
      </div>

      <!-- 分页 -->
      <nav aria-label="分页导航" class="mt-4">
        <div class="row align-items-center">
          <div class="col-md-6">
            <div id="paginationInfo" class="text-muted">显示 0 - 0 条，共 0 条记录</div>
          </div>
          <div class="col-md-6">
            <ul class="pagination justify-content-end mb-0">
              <li class="page-item">
                <button class="page-link" id="prevPageBtn" disabled>
                  <i class="fas fa-chevron-left"></i>
                </button>
              </li>
              <div id="pageNumbers" class="d-flex">
                <!-- 页码按钮将在这里动态生成 -->
              </div>
              <li class="page-item">
                <button class="page-link" id="nextPageBtn" disabled>
                  <i class="fas fa-chevron-right"></i>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
      <div class="modal-dialog modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>详细信息</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body" id="detailContent"></div>
        </div>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
      <div class="text-center">
        <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem"></div>
        <div>处理中...</div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div
      id="message"
      class="alert position-fixed top-0 start-50 translate-middle-x mt-3"
      style="z-index: 9999; display: none"
    ></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 简化的爬虫结果页面脚本 -->
    <script>
      // 全局变量
      let currentPage = 1;
      let pageSize = 20;
      let totalCount = 0;
      let selectedIds = new Set();
      let taskId = null;
      let searchParams = {};

      // API基础配置
      const API_BASE = "http://localhost:3001/api";
      const TOKEN_KEY = "daren_token";

      // 工具函数
      function getToken() {
        return localStorage.getItem(TOKEN_KEY);
      }

      function showMessage(message, type = "success") {
        const messageEl = document.getElementById("message");
        messageEl.className = `alert alert-${
          type === "error" ? "danger" : type
        } position-fixed top-0 start-50 translate-middle-x mt-3`;
        messageEl.style.zIndex = "9999";
        messageEl.style.display = "block";
        messageEl.textContent = message;

        setTimeout(() => {
          messageEl.style.display = "none";
        }, 3000);
      }

      function showLoading(show = true) {
        const overlay = document.getElementById("loadingOverlay");
        overlay.style.display = show ? "flex" : "none";
      }

      // API调用函数
      async function apiCall(url, options = {}) {
        const token = getToken();
        const config = {
          headers: {
            "Content-Type": "application/json",
            ...(token && { Authorization: `Bearer ${token}` }),
          },
          ...options,
        };

        try {
          const response = await fetch(`${API_BASE}${url}`, config);
          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.message || "请求失败");
          }

          return data;
        } catch (error) {
          console.error("API调用失败:", error);
          throw error;
        }
      }

      // 获取URL参数
      function getUrlParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
      }

      // 格式化数字
      function formatNumber(num) {
        if (!num) return "0";
        if (num >= 10000) {
          return (num / 10000).toFixed(1) + "w";
        }
        return num.toLocaleString();
      }

      // 获取平台名称
      function getPlatformName(platform) {
        const platformMap = {
          xiaohongshu: "小红书",
          juxingtu: "巨量星图",
        };
        return platformMap[platform] || platform;
      }

      // 获取状态信息
      function getStatusInfo(status) {
        const statusMap = {
          pending: { class: "bg-secondary", text: "待处理" },
          processed: { class: "bg-success", text: "已处理" },
          imported: { class: "bg-info", text: "已导入" },
          failed: { class: "bg-danger", text: "失败" },
        };
        return statusMap[status] || { class: "bg-secondary", text: status };
      }

      // 获取头像URL，处理默认头像
      function getAvatarUrl(avatarUrl) {
        // 如果没有头像URL，返回默认头像
        if (!avatarUrl) {
          return getDefaultAvatar();
        }
        return avatarUrl;
      }

      // 生成默认头像（使用SVG）
      function getDefaultAvatar() {
        // 使用SVG生成简单的默认头像
        const svg = `
          <svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
            <circle cx="25" cy="25" r="25" fill="#e9ecef"/>
            <circle cx="25" cy="20" r="8" fill="#6c757d"/>
            <path d="M10 40 Q25 30 40 40 L40 50 L10 50 Z" fill="#6c757d"/>
          </svg>
        `;
        return `data:image/svg+xml;base64,${btoa(svg)}`;
      }

      // 处理头像加载错误
      function handleAvatarError(imgElement) {
        imgElement.src = getDefaultAvatar();
        imgElement.onerror = null; // 防止无限循环
      }

      // 强制关闭所有模态框
      function closeAllModals() {
        // 关闭Bootstrap模态框
        const modals = document.querySelectorAll(".modal");
        modals.forEach((modalEl) => {
          const modal = bootstrap.Modal.getInstance(modalEl);
          if (modal) {
            modal.hide();
          }
        });

        // 移除可能残留的backdrop
        const backdrops = document.querySelectorAll(".modal-backdrop");
        backdrops.forEach((backdrop) => {
          backdrop.remove();
        });

        // 恢复body的样式
        document.body.classList.remove("modal-open");
        document.body.style.overflow = "";
        document.body.style.paddingRight = "";

        console.log("已强制关闭所有模态框");
      }

      // 页面初始化
      document.addEventListener("DOMContentLoaded", function () {
        console.log("页面初始化开始");

        taskId = getUrlParam("taskId");
        if (!taskId) {
          showMessage("缺少任务ID参数", "error");
          setTimeout(() => {
            window.location.href = "crawler.html";
          }, 2000);
          return;
        }

        try {
          // 先清理可能残留的模态框
          closeAllModals();

          // 绑定事件
          bindEvents();
          console.log("事件绑定完成");

          // 加载数据
          loadTaskInfo();
          loadResults();
        } catch (error) {
          console.error("页面初始化失败:", error);
          showMessage("页面初始化失败: " + error.message, "error");
        }
      });

      // 绑定事件
      function bindEvents() {
        console.log("开始绑定事件");

        try {
          // 搜索
          let searchTimeout;
          const searchKeyword = document.getElementById("searchKeyword");
          if (searchKeyword) {
            searchKeyword.addEventListener("input", function () {
              console.log("搜索输入事件触发");
              clearTimeout(searchTimeout);
              searchTimeout = setTimeout(handleSearch, 500);
            });
          }

          // 筛选
          const platformFilter = document.getElementById("platformFilter");
          const statusFilter = document.getElementById("statusFilter");
          if (platformFilter) {
            platformFilter.addEventListener("change", handleSearch);
          }
          if (statusFilter) {
            statusFilter.addEventListener("change", handleSearch);
          }

          // 工具栏按钮
          const refreshBtn = document.getElementById("refreshBtn");
          if (refreshBtn) {
            refreshBtn.addEventListener("click", () => {
              console.log("刷新按钮点击");
              showMessage("正在刷新数据...", "info");
              loadResults();
            });
          }

          const backBtn = document.getElementById("backBtn");
          if (backBtn) {
            backBtn.addEventListener("click", () => {
              console.log("返回按钮点击");
              window.location.href = "crawler.html";
            });
          }

          const exportBtn = document.getElementById("exportBtn");
          if (exportBtn) {
            exportBtn.addEventListener("click", () => {
              console.log("导出按钮点击");
              handleExport();
            });
          }

          const importSelectedBtn = document.getElementById("importSelectedBtn");
          if (importSelectedBtn) {
            importSelectedBtn.addEventListener("click", () => {
              console.log("批量导入按钮点击");
              handleImportSelected();
            });
          }

          // 分页按钮
          const prevPageBtn = document.getElementById("prevPageBtn");
          if (prevPageBtn) {
            prevPageBtn.addEventListener("click", () => {
              console.log("上一页按钮点击");
              if (currentPage > 1) {
                loadPage(currentPage - 1);
              }
            });
          }

          const nextPageBtn = document.getElementById("nextPageBtn");
          if (nextPageBtn) {
            nextPageBtn.addEventListener("click", () => {
              console.log("下一页按钮点击");
              const totalPages = Math.ceil(totalCount / pageSize);
              if (currentPage < totalPages) {
                loadPage(currentPage + 1);
              }
            });
          }

          // 退出登录
          const logoutBtn = document.getElementById("logoutBtn");
          if (logoutBtn) {
            logoutBtn.addEventListener("click", function () {
              console.log("退出登录按钮点击");
              localStorage.removeItem(TOKEN_KEY);
              localStorage.removeItem("daren_user");
              window.location.href = "login.html";
            });
          }

          // 添加ESC键关闭模态框
          document.addEventListener("keydown", function (e) {
            if (e.key === "Escape") {
              closeAllModals();
            }
          });

          // 添加双击页面关闭模态框（紧急情况下）
          document.addEventListener("dblclick", function (e) {
            if (e.target === document.body || e.target.classList.contains("container")) {
              closeAllModals();
            }
          });

          console.log("事件绑定完成");
        } catch (error) {
          console.error("事件绑定失败:", error);
          showMessage("事件绑定失败: " + error.message, "error");
        }
      }

      // 加载任务信息
      async function loadTaskInfo() {
        try {
          const data = await apiCall(`/crawler/tasks/${taskId}`);
          if (data.success) {
            const task = data.data;
            document.getElementById("taskTitle").textContent = `- ${task.name} (${getPlatformName(
              task.platform
            )})`;
          }
        } catch (error) {
          console.error("加载任务信息失败:", error);
        }
      }

      // 加载结果数据
      async function loadResults() {
        try {
          showLoading(true);

          const params = new URLSearchParams({
            page: currentPage,
            pageSize: pageSize,
            ...searchParams,
          });

          const data = await apiCall(`/crawler/tasks/${taskId}/results?${params}`);

          if (data.success) {
            renderResults(data.data.list || []);
            updatePagination(data.data.pagination || {});
            updateStats(data.data.stats || {});
          } else {
            throw new Error(data.message || "加载结果失败");
          }
        } catch (error) {
          console.error("加载爬虫结果失败:", error);
          showMessage("加载数据失败：" + error.message, "error");
          renderErrorState();
        } finally {
          showLoading(false);
        }
      }

      // 渲染结果列表
      function renderResults(results) {
        const resultsList = document.getElementById("resultsList");

        if (!results || results.length === 0) {
          resultsList.innerHTML = `
            <div class="col-12 text-center py-5">
              <i class="fas fa-search fa-3x text-muted mb-3"></i>
              <div>暂无爬虫结果数据</div>
            </div>
          `;
          return;
        }

        const html = results.map((result) => renderResultCard(result)).join("");
        resultsList.innerHTML = html;

        // 绑定动态生成的事件
        bindDynamicEvents();
      }

      // 渲染结果卡片
      function renderResultCard(result) {
        const statusInfo = getStatusInfo(result.status);
        const followersText = formatNumber(result.followersCount);
        const contactInfo = formatContactInfo(result.contactInfo);

        return `
          <div class="col-md-6 col-lg-4 mb-3">
            <div class="card result-card h-100">
              <div class="card-header d-flex justify-content-between align-items-center">
                <div class="form-check">
                  <input class="form-check-input result-checkbox" type="checkbox"
                         value="${result.id}" ${selectedIds.has(result.id) ? "checked" : ""}>
                  <span class="badge ${statusInfo.class} status-badge">${statusInfo.text}</span>
                </div>
                <span class="badge bg-secondary">${getPlatformName(result.platform)}</span>
              </div>

              <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                  <img src="${getAvatarUrl(result.avatarUrl)}"
                       alt="头像" class="avatar-img me-3"
                       onerror="this.src='${getDefaultAvatar()}'; this.onerror=null;">
                  <div class="flex-grow-1">
                    <h6 class="card-title mb-1">${result.nickname}</h6>
                    <small class="text-muted">
                      ${
                        result.uniqueId
                          ? `ID: ${result.uniqueId}`
                          : `平台ID: ${result.platformUserId}`
                      }
                    </small>
                  </div>
                </div>

                <div class="mb-2">
                  <i class="fas fa-users text-primary me-1"></i>
                  <strong>${followersText}</strong> 粉丝
                  ${
                    result.city
                      ? `<span class="ms-2"><i class="fas fa-map-marker-alt text-muted me-1"></i>${result.city}</span>`
                      : ""
                  }
                </div>

                ${contactInfo ? `<div class="contact-info mb-2">${contactInfo}</div>` : ""}

                ${
                  result.errorMessage
                    ? `
                  <div class="alert alert-danger alert-sm mt-2 mb-0">
                    <small><i class="fas fa-exclamation-triangle me-1"></i>${result.errorMessage}</small>
                  </div>
                `
                    : ""
                }
              </div>

              <div class="card-footer bg-transparent">
                <div class="btn-group w-100" role="group">
                  <button class="btn btn-outline-primary btn-sm view-detail-btn"
                          data-result-id="${result.id}">
                    <i class="fas fa-eye"></i> 详情
                  </button>
                  ${
                    result.status === "processed"
                      ? `
                    <button class="btn btn-outline-success btn-sm import-single-btn"
                            data-result-id="${result.id}">
                      <i class="fas fa-plus"></i> 导入
                    </button>
                  `
                      : ""
                  }
                </div>
              </div>
            </div>
          </div>
        `;
      }

      // 格式化联系信息
      function formatContactInfo(contactInfo) {
        if (!contactInfo || typeof contactInfo !== "object") return "";

        const contacts = [];
        if (contactInfo.wechat) {
          contacts.push(`<i class="fab fa-weixin text-success"></i> ${contactInfo.wechat}`);
        }
        if (contactInfo.phone) {
          contacts.push(`<i class="fas fa-phone text-primary"></i> ${contactInfo.phone}`);
        }
        if (contactInfo.email) {
          contacts.push(`<i class="fas fa-envelope text-info"></i> ${contactInfo.email}`);
        }

        return contacts.length > 0 ? contacts.join("<br>") : "";
      }

      // 绑定动态生成的事件
      function bindDynamicEvents() {
        console.log("开始绑定动态事件");

        try {
          // 复选框事件
          const checkboxes = document.querySelectorAll(".result-checkbox");
          console.log(`找到 ${checkboxes.length} 个复选框`);
          checkboxes.forEach((checkbox) => {
            checkbox.addEventListener("change", function () {
              console.log("复选框状态改变:", this.value, this.checked);
              handleSelectOne(this.value, this.checked);
            });
          });

          // 查看详情按钮
          const detailBtns = document.querySelectorAll(".view-detail-btn");
          console.log(`找到 ${detailBtns.length} 个详情按钮`);
          detailBtns.forEach((btn) => {
            btn.addEventListener("click", function (e) {
              e.preventDefault();
              const resultId = this.getAttribute("data-result-id");
              console.log("查看详情按钮点击:", resultId);
              showDetailModal(resultId);
            });
          });

          // 单个导入按钮
          const importBtns = document.querySelectorAll(".import-single-btn");
          console.log(`找到 ${importBtns.length} 个导入按钮`);
          importBtns.forEach((btn) => {
            btn.addEventListener("click", function (e) {
              e.preventDefault();
              const resultId = this.getAttribute("data-result-id");
              console.log("单个导入按钮点击:", resultId);
              importSingleResult(resultId);
            });
          });

          // 分页按钮
          const pageBtns = document.querySelectorAll(".page-btn");
          console.log(`找到 ${pageBtns.length} 个分页按钮`);
          pageBtns.forEach((btn) => {
            btn.addEventListener("click", function (e) {
              e.preventDefault();
              const page = parseInt(this.getAttribute("data-page"));
              console.log("分页按钮点击:", page);
              loadPage(page);
            });
          });

          console.log("动态事件绑定完成");
        } catch (error) {
          console.error("动态事件绑定失败:", error);
        }
      }

      // 处理搜索
      function handleSearch() {
        searchParams = {
          keyword: document.getElementById("searchKeyword").value.trim(),
          platform: document.getElementById("platformFilter").value,
          status: document.getElementById("statusFilter").value,
        };

        currentPage = 1;
        loadResults();
      }

      // 处理单选
      function handleSelectOne(id, checked) {
        // 确保ID为数字类型
        const numericId = parseInt(id);
        if (isNaN(numericId)) {
          console.error("无效的ID:", id);
          return;
        }

        if (checked) {
          selectedIds.add(numericId);
        } else {
          selectedIds.delete(numericId);
        }

        console.log("选中的IDs:", Array.from(selectedIds));
        updateBatchButtons();
      }

      // 更新批量操作按钮状态
      function updateBatchButtons() {
        const hasSelected = selectedIds.size > 0;
        document.getElementById("importSelectedBtn").disabled = !hasSelected;
      }

      // 更新分页
      function updatePagination(pagination) {
        totalCount = pagination.total || 0;
        currentPage = pagination.page || 1;

        // 更新分页信息
        const start = (currentPage - 1) * pageSize + 1;
        const end = Math.min(currentPage * pageSize, totalCount);
        document.getElementById(
          "paginationInfo"
        ).textContent = `显示 ${start} - ${end} 条，共 ${totalCount} 条记录`;

        // 更新分页按钮
        renderPaginationButtons(pagination);
      }

      // 渲染分页按钮
      function renderPaginationButtons(pagination) {
        const pageNumbers = document.getElementById("pageNumbers");
        const totalPages = Math.ceil(pagination.total / pageSize);

        let buttons = "";

        // 计算显示的页码范围
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
          buttons += `
            <li class="page-item ${i === currentPage ? "active" : ""}">
              <button class="page-link page-btn" data-page="${i}">${i}</button>
            </li>
          `;
        }

        pageNumbers.innerHTML = buttons;

        // 更新上一页/下一页按钮状态
        document.getElementById("prevPageBtn").disabled = currentPage <= 1;
        document.getElementById("nextPageBtn").disabled = currentPage >= totalPages;

        // 重新绑定分页按钮事件
        document.querySelectorAll(".page-btn").forEach((btn) => {
          btn.addEventListener("click", function () {
            const page = parseInt(this.getAttribute("data-page"));
            loadPage(page);
          });
        });
      }

      // 更新统计信息
      function updateStats(stats) {
        if (stats) {
          document.getElementById("totalResults").textContent = stats.total || 0;
          document.getElementById("validResults").textContent = stats.processed || 0;
          document.getElementById("failedResults").textContent = stats.failed || 0;
        }
      }

      // 加载指定页
      function loadPage(page) {
        currentPage = page;
        loadResults();
      }

      // 渲染错误状态
      function renderErrorState() {
        document.getElementById("resultsList").innerHTML = `
          <div class="col-12 text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <div>数据加载失败</div>
            <button class="btn btn-primary mt-3" onclick="location.reload()">
              <i class="fas fa-refresh me-1"></i>重新加载
            </button>
          </div>
        `;
      }

      // 显示详情模态框
      async function showDetailModal(resultId) {
        try {
          showLoading(true);

          const data = await apiCall(`/crawler/results/${resultId}`);
          if (data.success) {
            const result = data.data;
            renderDetailModal(result);

            // 确保先关闭任何已打开的模态框
            const existingModal = bootstrap.Modal.getInstance(
              document.getElementById("detailModal")
            );
            if (existingModal) {
              existingModal.hide();
            }

            // 使用Bootstrap的模态框API
            const modal = new bootstrap.Modal(document.getElementById("detailModal"), {
              backdrop: true, // 允许点击背景关闭
              keyboard: true, // 允许ESC键关闭
            });
            modal.show();

            console.log("模态框已显示");
          } else {
            throw new Error(data.message || "获取详情失败");
          }
        } catch (error) {
          console.error("获取结果详情失败:", error);
          showMessage("获取详情失败：" + error.message, "error");
        } finally {
          showLoading(false);
        }
      }

      // 渲染详情模态框
      function renderDetailModal(result) {
        const statusInfo = getStatusInfo(result.status);
        const content = `
          <div class="row">
            <div class="col-md-4 text-center">
              <img src="${getAvatarUrl(result.avatarUrl)}"
                   alt="头像" class="img-fluid rounded-circle mb-3" style="max-width: 150px;"
                   onerror="this.src='${getDefaultAvatar()}'; this.onerror=null;">
              <h5>${result.nickname}</h5>
              <p class="text-muted">${getPlatformName(result.platform)}</p>
            </div>
            <div class="col-md-8">
              <table class="table table-borderless">
                <tr><td><strong>平台用户ID:</strong></td><td>${result.platformUserId}</td></tr>
                <tr><td><strong>唯一标识:</strong></td><td>${result.uniqueId || "-"}</td></tr>
                <tr><td><strong>粉丝数量:</strong></td><td>${formatNumber(
                  result.followersCount
                )}</td></tr>
                <tr><td><strong>所在城市:</strong></td><td>${result.city || "-"}</td></tr>
                <tr><td><strong>处理状态:</strong></td><td><span class="badge ${
                  statusInfo.class
                }">${statusInfo.text}</span></td></tr>
              </table>

              ${
                result.contactInfo
                  ? `
                <h6>联系信息</h6>
                <div class="mb-3">${formatContactInfo(result.contactInfo)}</div>
              `
                  : ""
              }

              ${
                result.errorMessage
                  ? `
                <h6>错误信息</h6>
                <div class="alert alert-danger">${result.errorMessage}</div>
              `
                  : ""
              }
            </div>
          </div>

          ${
            result.rawData
              ? `
            <hr>
            <h6>原始数据</h6>
            <pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
              <code>${JSON.stringify(result.rawData, null, 2)}</code>
            </pre>
          `
              : ""
          }
        `;

        document.getElementById("detailContent").innerHTML = content;
      }

      // 导入单个结果
      async function importSingleResult(resultId) {
        if (!confirm("确定要将此结果导入到达人库吗？")) {
          return;
        }

        try {
          showLoading(true);

          const data = await apiCall(`/crawler/results/${resultId}/import`, {
            method: "POST",
          });

          if (data.success) {
            showMessage("导入成功", "success");
            loadResults(); // 刷新列表
          } else {
            throw new Error(data.message || "导入失败");
          }
        } catch (error) {
          console.error("导入结果失败:", error);
          showMessage("导入失败：" + error.message, "error");
        } finally {
          showLoading(false);
        }
      }

      // 处理批量导入
      async function handleImportSelected() {
        if (selectedIds.size === 0) {
          showMessage("请先选择要导入的结果", "warning");
          return;
        }

        if (!confirm(`确定要导入选中的 ${selectedIds.size} 个结果到达人库吗？`)) {
          return;
        }

        try {
          showLoading(true);

          const ids = Array.from(selectedIds);
          console.log("批量导入 - 发送的IDs:", ids);
          console.log(
            "批量导入 - IDs类型:",
            ids.map((id) => typeof id)
          );

          const data = await apiCall("/crawler/results/batch-import", {
            method: "POST",
            body: JSON.stringify({ ids }),
          });

          if (data.success) {
            const count = data.data?.count || ids.length;
            showMessage(`成功导入 ${count} 个结果`, "success");
            selectedIds.clear();
            loadResults(); // 刷新列表
          } else {
            throw new Error(data.message || "批量导入失败");
          }
        } catch (error) {
          console.error("批量导入失败:", error);
          showMessage("批量导入失败：" + error.message, "error");
        } finally {
          showLoading(false);
        }
      }

      // 处理导出
      async function handleExport() {
        try {
          showLoading(true);
          showMessage("正在导出数据...", "info");
          debugger;

          const params = new URLSearchParams({
            taskId: taskId,
            ...searchParams,
          });

          // 创建下载链接
          const url = `${API_BASE}/crawler/tasks/${taskId}/export?${params}`;
          const token = getToken();

          // 使用fetch下载文件
          const response = await fetch(url, {
            headers: {
              ...(token && { Authorization: `Bearer ${token}` }),
            },
          });

          if (response.ok) {
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = downloadUrl;
            a.download = `crawler-results-${taskId}-${Date.now()}.xlsx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(downloadUrl);

            showMessage("导出成功", "success");
          } else {
            throw new Error("导出失败");
          }
        } catch (error) {
          console.error("导出失败:", error);
          showMessage("导出失败：" + error.message, "error");
        } finally {
          showLoading(false);
        }
      }
    </script>
  </body>
</html>
