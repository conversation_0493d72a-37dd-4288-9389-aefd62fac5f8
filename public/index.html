<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>达人信息管理系统</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
  </head>
  <body>
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <h1 class="logo">达人信息管理系统</h1>
          <div class="user-info">
            <span id="userWelcome">欢迎，用户</span>
            <button id="logoutBtn" class="btn btn-outline">退出登录</button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main">
      <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
              <h3 id="totalCount">0</h3>
              <p>达人总数</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-heart"></i>
            </div>
            <div class="stat-content">
              <h3 id="xiaohongshuCount">0</h3>
              <p>小红书达人</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-star"></i>
            </div>
            <div class="stat-content">
              <h3 id="juxingtuCount">0</h3>
              <p>巨量星图达人</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-tags"></i>
            </div>
            <div class="stat-content">
              <h3 id="categoryCount">0</h3>
              <p>分类数量</p>
            </div>
          </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <button id="addInfluencerBtn" class="btn btn-primary"><i class="fas fa-plus"></i> 添加达人</button>
            <button id="batchDeleteBtn" class="btn btn-danger" disabled><i class="fas fa-trash"></i> 批量删除</button>
            <button id="exportBtn" class="btn btn-success"><i class="fas fa-download"></i> 导出Excel</button>
          </div>
          <div class="toolbar-right">
            <button id="refreshBtn" class="btn btn-outline"><i class="fas fa-refresh"></i> 刷新</button>
          </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-panel">
          <div class="search-row">
            <div class="search-group">
              <label>关键词搜索</label>
              <input type="text" id="searchKeyword" placeholder="搜索达人昵称..." />
            </div>
            <div class="search-group">
              <label>平台筛选</label>
              <select id="platformFilter">
                <option value="">全部平台</option>
                <option value="xiaohongshu">小红书</option>
                <option value="juxingtu">巨量星图</option>
              </select>
            </div>
            <div class="search-group">
              <label>分类筛选</label>
              <select id="categoryFilter">
                <option value="">全部分类</option>
              </select>
            </div>
            <div class="search-group">
              <button id="searchBtn" class="btn btn-primary">搜索</button>
              <button id="resetBtn" class="btn btn-outline">重置</button>
            </div>
          </div>
        </div>

        <!-- 达人列表 -->
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>
                  <input type="checkbox" id="selectAll" />
                </th>
                <th>头像</th>
                <th>昵称</th>
                <th>平台</th>
                <th>粉丝数</th>
                <th>分类</th>
                <th>联系方式</th>
                <th>扩展信息</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="influencerTableBody">
              <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <div class="pagination-info">
            <span id="paginationInfo">显示 0 - 0 条，共 0 条记录</span>
          </div>
          <div class="pagination">
            <button id="prevPageBtn" class="btn btn-outline" disabled>上一页</button>
            <span id="pageNumbers"></span>
            <button id="nextPageBtn" class="btn btn-outline" disabled>下一页</button>
          </div>
        </div>
      </div>
    </main>

    <!-- 添加/编辑达人模态框 -->
    <div id="influencerModal" class="modal" style="display: none">
      <div class="modal-content">
        <div class="modal-header">
          <h3 id="modalTitle">添加达人</h3>
          <button class="modal-close" id="closeModal">&times;</button>
        </div>
        <div class="modal-body">
          <form id="influencerForm">
            <input type="hidden" id="influencerId" />
            <div class="form-row">
              <div class="form-group">
                <label for="nickname">达人昵称 *</label>
                <input type="text" id="nickname" name="nickname" required />
              </div>
              <div class="form-group">
                <label for="platform">平台 *</label>
                <select id="platform" name="platform" required>
                  <option value="">请选择平台</option>
                  <option value="xiaohongshu">小红书</option>
                  <option value="juxingtu">巨量星图</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="platformId">平台ID</label>
                <input type="text" id="platformId" name="platformId" />
              </div>
              <div class="form-group">
                <label for="followersCount">粉丝数量</label>
                <input type="number" id="followersCount" name="followersCount" min="0" />
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="category">分类</label>
                <input type="text" id="category" name="category" />
              </div>
              <div class="form-group">
                <label for="avatarUrl">头像链接</label>
                <input type="url" id="avatarUrl" name="avatarUrl" />
              </div>
            </div>
            <div class="form-group">
              <label for="tags">标签（用逗号分隔）</label>
              <input type="text" id="tags" name="tags" placeholder="例如：美妆,护肤,种草" />
            </div>
            <div class="form-group">
              <label for="contactPhone">联系电话</label>
              <input type="tel" id="contactPhone" name="contactPhone" />
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="contactEmail">联系邮箱</label>
                <input type="email" id="contactEmail" name="contactEmail" />
              </div>
              <div class="form-group">
                <label for="contactWechat">微信号</label>
                <input type="text" id="contactWechat" name="contactWechat" />
              </div>
            </div>
            <div class="form-group">
              <label for="notes">备注</label>
              <textarea id="notes" name="notes" rows="3"></textarea>
            </div>
            <div class="form-group">
              <label for="authorExtInfo">扩展信息</label>
              <textarea
                id="authorExtInfo"
                name="authorExtInfo"
                rows="4"
                placeholder="JSON格式的扩展信息（只读）"
                readonly
              ></textarea>
              <small class="form-text">此字段由爬虫系统自动填充，包含来自巨量星图的动态附加信息</small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline" id="cancelBtn">取消</button>
          <button type="submit" form="influencerForm" class="btn btn-primary" id="saveBtn">保存</button>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div id="message" class="message" style="display: none"></div>

    <!-- 加载中遮罩 -->
    <div id="loading" class="loading" style="display: none">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/main.js"></script>
  </body>
</html>
