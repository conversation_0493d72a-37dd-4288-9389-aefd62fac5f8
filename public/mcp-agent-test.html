<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Agent接入测试</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-danger {
            background-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .status-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .status-info h4 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .session-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            display: none;
        }
        
        .session-info h5 {
            margin-top: 0;
            color: #856404;
        }
        
        .events-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .connection-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .connection-status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        
        .connection-status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .connection-status.connecting {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MCP Agent接入测试</h1>
        
        <!-- 服务状态 -->
        <div class="status-info">
            <h4>MCP Agent服务状态</h4>
            <div id="serviceStatus">正在检查服务状态...</div>
            <button class="btn" onclick="checkServiceStatus()">刷新状态</button>
        </div>
        
        <!-- 当前会话信息 -->
        <div id="sessionInfo" class="session-info">
            <h5>当前Agent会话</h5>
            <div id="sessionDetails"></div>
            <div>
                <strong>SSE连接状态:</strong> 
                <span id="connectionStatus" class="connection-status disconnected">未连接</span>
            </div>
        </div>
        
        <!-- Agent连接 -->
        <div class="test-section">
            <h3>1. Agent连接</h3>
            <div class="form-group">
                <label for="agentId">Agent ID:</label>
                <input type="text" id="agentId" value="web_test_agent" placeholder="输入Agent唯一标识">
            </div>
            <div class="form-group">
                <label for="agentName">Agent名称:</label>
                <input type="text" id="agentName" value="Web测试Agent" placeholder="输入Agent名称">
            </div>
            <div class="form-group">
                <label for="agentVersion">版本:</label>
                <input type="text" id="agentVersion" value="1.0.0" placeholder="Agent版本">
            </div>
            <button class="btn btn-success" onclick="connectAgent()">连接Agent</button>
            <button class="btn btn-danger" onclick="disconnectAgent()">断开连接</button>
            <div id="connectResult" class="result"></div>
        </div>
        
        <!-- 工具执行 -->
        <div class="test-section">
            <h3>2. 工具执行</h3>
            <div class="form-group">
                <label for="toolName">选择工具:</label>
                <select id="toolName" onchange="updateToolArguments()">
                    <option value="">请选择工具</option>
                    <option value="create_crawler_task">create_crawler_task</option>
                    <option value="get_task_status">get_task_status</option>
                    <option value="get_task_results">get_task_results</option>
                    <option value="export_results_to_excel">export_results_to_excel</option>
                </select>
            </div>
            <div class="form-group">
                <label for="toolArguments">工具参数 (JSON):</label>
                <textarea id="toolArguments" rows="6" placeholder="输入JSON格式的工具参数"></textarea>
            </div>
            <button class="btn" onclick="executeTool()">执行工具</button>
            <div id="toolResult" class="result"></div>
        </div>
        
        <!-- 任务订阅 -->
        <div class="test-section">
            <h3>3. 任务订阅</h3>
            <div class="form-group">
                <label for="taskId">任务ID:</label>
                <input type="number" id="taskId" placeholder="输入要订阅的任务ID">
            </div>
            <button class="btn" onclick="subscribeTask()">订阅任务</button>
            <button class="btn" onclick="unsubscribeTask()">取消订阅</button>
            <div id="subscribeResult" class="result"></div>
        </div>
        
        <!-- SSE事件日志 -->
        <div class="test-section">
            <h3>4. SSE事件日志</h3>
            <button class="btn" onclick="clearEventLog()">清空日志</button>
            <div id="eventLog" class="events-log">等待SSE事件...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let sessionId = null;
        let eventSource = null;
        let eventCount = 0;
        
        // API基础URL
        const AGENT_API_BASE = '/api/mcp/agent';
        
        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch(`${AGENT_API_BASE}/status`);
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('serviceStatus').innerHTML = `
                        <strong>✅ 服务运行正常</strong><br>
                        版本: ${result.data.version}<br>
                        活跃会话数: ${result.data.sessions.total}<br>
                        活跃连接数: ${result.data.eventService.activeConnections}<br>
                        任务订阅数: ${result.data.eventService.taskSubscriptions}<br>
                        运行时间: ${Math.floor(result.data.eventService.uptime)}秒
                    `;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                document.getElementById('serviceStatus').innerHTML = `
                    <strong>❌ 服务状态异常</strong><br>
                    错误: ${error.message}
                `;
            }
        }
        
        // 连接Agent
        async function connectAgent() {
            const resultDiv = document.getElementById('connectResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在连接Agent...';
            
            const agentData = {
                agentId: document.getElementById('agentId').value,
                agentName: document.getElementById('agentName').value,
                version: document.getElementById('agentVersion').value,
                capabilities: ['crawler', 'excel_export', 'real_time_monitoring'],
                metadata: {
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                }
            };
            
            try {
                const response = await fetch(`${AGENT_API_BASE}/connect`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(agentData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    sessionId = result.data.sessionId;
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Agent连接成功</strong><br>
                        会话ID: ${sessionId}<br>
                        可用工具数量: ${result.data.tools.length}
                    `;
                    
                    // 显示会话信息
                    showSessionInfo(result.data);
                    
                    // 建立SSE连接
                    connectSSE();
                    
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Agent连接失败: ${error.message}`;
            }
        }
        
        // 显示会话信息
        function showSessionInfo(data) {
            const sessionInfoDiv = document.getElementById('sessionInfo');
            const sessionDetailsDiv = document.getElementById('sessionDetails');
            
            sessionDetailsDiv.innerHTML = `
                <strong>会话ID:</strong> ${data.sessionId}<br>
                <strong>Agent ID:</strong> ${data.agentId}<br>
                <strong>Agent名称:</strong> ${data.agentName}<br>
                <strong>服务器:</strong> ${data.serverInfo.name} v${data.serverInfo.version}<br>
                <strong>SSE端点:</strong> ${data.sseEndpoint}
            `;
            
            sessionInfoDiv.style.display = 'block';
        }
        
        // 建立SSE连接
        function connectSSE() {
            if (!sessionId) {
                logEvent('❌ 无法建立SSE连接：没有有效的会话ID');
                return;
            }
            
            updateConnectionStatus('connecting');
            logEvent('🔄 正在建立SSE连接...');
            
            const sseUrl = `${AGENT_API_BASE}/events/${sessionId}`;
            eventSource = new EventSource(sseUrl);
            
            eventSource.onopen = function(event) {
                updateConnectionStatus('connected');
                logEvent('✅ SSE连接建立成功');
            };
            
            eventSource.onerror = function(event) {
                updateConnectionStatus('disconnected');
                logEvent('❌ SSE连接错误: ' + JSON.stringify(event));
            };
            
            eventSource.onmessage = function(event) {
                logEvent(`📡 收到消息: ${event.data}`);
            };
            
            // 监听特定事件类型
            const eventTypes = ['connected', 'welcome', 'task_status_update', 'task_completed', 'tool_execution_result', 'heartbeat', 'task_subscribed'];
            
            eventTypes.forEach(eventType => {
                eventSource.addEventListener(eventType, function(event) {
                    logEvent(`📡 [${eventType}] ${event.data}`);
                });
            });
        }
        
        // 更新连接状态
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `connection-status ${status}`;
            
            const statusText = {
                'connected': '已连接',
                'disconnected': '未连接',
                'connecting': '连接中'
            };
            
            statusElement.textContent = statusText[status] || status;
        }
        
        // 记录事件日志
        function logEvent(message) {
            const eventLog = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            eventCount++;
            
            eventLog.textContent += `[${timestamp}] ${message}\n`;
            eventLog.scrollTop = eventLog.scrollHeight;
        }
        
        // 清空事件日志
        function clearEventLog() {
            document.getElementById('eventLog').textContent = '事件日志已清空...\n';
            eventCount = 0;
        }
        
        // 更新工具参数
        function updateToolArguments() {
            const tool = document.getElementById('toolName').value;
            const argumentsTextarea = document.getElementById('toolArguments');
            
            const examples = {
                'create_crawler_task': {
                    keywords: 'Web测试达人',
                    platform: 'xiaohongshu',
                    taskName: 'Web Agent测试任务',
                    maxPages: 2,
                    config: {
                        pageSize: 10,
                        delay: { min: 500, max: 1000 }
                    }
                },
                'get_task_status': {
                    taskId: 123
                },
                'get_task_results': {
                    taskId: 123,
                    page: 1,
                    limit: 10
                },
                'export_results_to_excel': {
                    taskId: 123,
                    fileName: 'Web测试导出'
                }
            };
            
            if (examples[tool]) {
                argumentsTextarea.value = JSON.stringify(examples[tool], null, 2);
            } else {
                argumentsTextarea.value = '';
            }
        }
        
        // 执行工具
        async function executeTool() {
            if (!sessionId) {
                showError('请先连接Agent', 'toolResult');
                return;
            }
            
            const tool = document.getElementById('toolName').value;
            const argumentsText = document.getElementById('toolArguments').value;
            const resultDiv = document.getElementById('toolResult');
            
            if (!tool) {
                showError('请选择工具', 'toolResult');
                return;
            }
            
            if (!argumentsText.trim()) {
                showError('请输入工具参数', 'toolResult');
                return;
            }
            
            let args;
            try {
                args = JSON.parse(argumentsText);
            } catch (e) {
                showError('参数JSON格式错误: ' + e.message, 'toolResult');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在执行工具...';
            
            try {
                const response = await fetch(`${AGENT_API_BASE}/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        tool: tool,
                        arguments: args,
                        requestId: `web_req_${Date.now()}`
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ 工具执行成功<br>工具: ${result.data.tool}`;
                    
                    // 如果创建了任务，自动填入任务ID
                    if (tool === 'create_crawler_task' && result.data.result.success) {
                        document.getElementById('taskId').value = result.data.result.data.taskId;
                    }
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 工具执行失败: ${error.message}`;
            }
        }
        
        // 订阅任务
        async function subscribeTask() {
            if (!sessionId) {
                showError('请先连接Agent', 'subscribeResult');
                return;
            }
            
            const taskId = document.getElementById('taskId').value;
            if (!taskId) {
                showError('请输入任务ID', 'subscribeResult');
                return;
            }
            
            try {
                const response = await fetch(`${AGENT_API_BASE}/subscribe`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        taskId: parseInt(taskId),
                        action: 'subscribe'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess(`任务 ${taskId} 订阅成功`, 'subscribeResult');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('订阅失败: ' + error.message, 'subscribeResult');
            }
        }
        
        // 取消订阅任务
        async function unsubscribeTask() {
            if (!sessionId) {
                showError('请先连接Agent', 'subscribeResult');
                return;
            }
            
            const taskId = document.getElementById('taskId').value;
            if (!taskId) {
                showError('请输入任务ID', 'subscribeResult');
                return;
            }
            
            try {
                const response = await fetch(`${AGENT_API_BASE}/subscribe`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        taskId: parseInt(taskId),
                        action: 'unsubscribe'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess(`任务 ${taskId} 取消订阅成功`, 'subscribeResult');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('取消订阅失败: ' + error.message, 'subscribeResult');
            }
        }
        
        // 断开Agent连接
        async function disconnectAgent() {
            if (!sessionId) {
                showError('没有活跃的Agent连接', 'connectResult');
                return;
            }
            
            try {
                // 关闭SSE连接
                if (eventSource) {
                    eventSource.close();
                    eventSource = null;
                    updateConnectionStatus('disconnected');
                    logEvent('🔌 SSE连接已关闭');
                }
                
                // 断开Agent连接
                const response = await fetch(`${AGENT_API_BASE}/disconnect`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('Agent连接已断开', 'connectResult');
                    sessionId = null;
                    document.getElementById('sessionInfo').style.display = 'none';
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('断开连接失败: ' + error.message, 'connectResult');
            }
        }
        
        // 显示成功消息
        function showSuccess(message, elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result success';
            element.innerHTML = '✅ ' + message;
        }
        
        // 显示错误消息
        function showError(message, elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result error';
            element.innerHTML = '❌ ' + message;
        }
        
        // 页面加载时检查服务状态
        document.addEventListener('DOMContentLoaded', function() {
            checkServiceStatus();
        });
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>
