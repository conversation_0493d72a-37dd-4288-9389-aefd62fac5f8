// 登录页面逻辑

document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已登录
    if (isLoggedIn()) {
        redirect('/index.html');
        return;
    }

    const loginForm = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.getElementById('loginBtn');
    const btnText = loginBtn.querySelector('.btn-text');
    const btnLoading = loginBtn.querySelector('.btn-loading');

    // 表单提交处理
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();

        // 基础验证
        if (!username) {
            showMessage('请输入用户名', 'error');
            usernameInput.focus();
            return;
        }

        if (!password) {
            showMessage('请输入密码', 'error');
            passwordInput.focus();
            return;
        }

        // 显示加载状态
        setLoginLoading(true);

        try {
            // 调用登录API
            const response = await authAPI.login(username, password);
            
            if (response.success) {
                // 保存token和用户信息
                setToken(response.data.token);
                setUser(response.data.user);
                
                showMessage('登录成功，正在跳转...', 'success');
                
                // 延迟跳转，让用户看到成功消息
                setTimeout(() => {
                    redirect('/index.html');
                }, 1000);
            } else {
                showMessage(response.message || '登录失败', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            showMessage(error.message || '登录失败，请检查网络连接', 'error');
        } finally {
            setLoginLoading(false);
        }
    });

    // 设置登录按钮加载状态
    function setLoginLoading(loading) {
        loginBtn.disabled = loading;
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline';
        } else {
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
        }
    }

    // 回车键快捷登录
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !loginBtn.disabled) {
            loginForm.dispatchEvent(new Event('submit'));
        }
    });

    // 演示账户快速填充
    const demoInfo = document.querySelector('.demo-info');
    if (demoInfo) {
        demoInfo.addEventListener('click', function() {
            usernameInput.value = 'admin';
            passwordInput.value = 'admin123456';
            usernameInput.focus();
        });
    }

    // 输入框焦点处理
    usernameInput.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
    });

    usernameInput.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
    });

    passwordInput.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
    });

    passwordInput.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
    });

    // 自动聚焦到用户名输入框
    usernameInput.focus();
});
