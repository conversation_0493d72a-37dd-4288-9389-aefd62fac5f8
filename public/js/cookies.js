/**
 * Cookie管理页面JavaScript
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;
let searchTimeout;
let editingCookieId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadCookieStats();
    loadCookies();
    setupEventListeners();
});

/**
 * 检查用户认证状态
 */
async function checkAuth() {
    try {
        // 检查本地是否有token
        if (!isLoggedIn()) {
            showError('请先登录');
            setTimeout(() => {
                redirect('/login.html');
            }, 1500);
            return;
        }

        // 验证token有效性
        const response = await api.get('/auth/me');
        if (response.success) {
            // 更新用户信息显示
            const user = response.data;
            setUser(user);

            // 更新页面用户信息显示
            const userNameEl = document.getElementById('userName');
            if (userNameEl) {
                userNameEl.textContent = user.username || '用户';
            }
        } else {
            // token无效，清除本地存储并跳转登录
            removeToken();
            removeUser();
            showError('登录已过期，请重新登录');
            setTimeout(() => {
                redirect('/login.html');
            }, 1500);
        }
    } catch (error) {
        console.error('认证检查失败:', error);
        // 网络错误或其他错误，清除认证信息
        removeToken();
        removeUser();
        showError('认证验证失败，请重新登录');
        setTimeout(() => {
            redirect('/login.html');
        }, 1500);
    }
}

/**
 * API请求封装函数
 */
async function apiRequest(url, method = 'GET', data = null) {
    try {
        let response;
        switch (method.toUpperCase()) {
            case 'GET':
                response = await api.get(url);
                break;
            case 'POST':
                response = await api.post(url, data);
                break;
            case 'PUT':
                response = await api.put(url, data);
                break;
            case 'DELETE':
                response = await api.delete(url);
                break;
            default:
                throw new Error(`不支持的请求方法: ${method}`);
        }
        return response;
    } catch (error) {
        console.error('API请求失败:', error);

        // 处理认证错误
        if (error.message && (error.message.includes('401') || error.message.includes('认证'))) {
            removeToken();
            removeUser();
            showError('登录已过期，请重新登录');
            setTimeout(() => {
                redirect('/login.html');
            }, 1500);
        }

        throw error;
    }
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
    showMessage(message, 'success');
}

/**
 * 显示错误消息
 */
function showError(message) {
    showMessage(message, 'error');
}

/**
 * 显示警告消息
 */
function showWarning(message) {
    showMessage(message, 'warning');
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // Cookie表单提交
    const cookieForm = document.getElementById('cookieForm');
    if (cookieForm) {
        cookieForm.addEventListener('submit', handleCookieSubmit);
    }

    // 文件导入
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }

    // 拖拽导入
    const importArea = document.getElementById('importArea');
    if (importArea && fileInput) {
        importArea.addEventListener('dragover', handleDragOver);
        importArea.addEventListener('drop', handleFileDrop);
        importArea.addEventListener('click', () => fileInput.click());
    }

    // 搜索输入框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            currentPage = 1; // 重置到第一页
            loadCookies(currentPage);
        }, 500));
    }

    // 平台筛选器
    const platformFilter = document.getElementById('platformFilter');
    if (platformFilter) {
        platformFilter.addEventListener('change', function() {
            currentPage = 1; // 重置到第一页
            loadCookies(currentPage);
            loadCookieStats(); // 重新加载统计信息
        });
    }

    // 状态筛选器
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            currentPage = 1; // 重置到第一页
            loadCookies(currentPage);
        });
    }

    // 刷新按钮
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            loadCookies(currentPage);
            loadCookieStats();
            showSuccess('数据已刷新');
        });
    }

    // 添加Cookie按钮
    const addCookieBtn = document.getElementById('addCookieBtn');
    if (addCookieBtn) {
        addCookieBtn.addEventListener('click', showAddModal);
    }

    // 导入按钮
    const importBtn = document.getElementById('importBtn');
    if (importBtn) {
        importBtn.addEventListener('click', showImportModal);
    }

    // 导出按钮
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportCookies);
    }

    // 模态框关闭按钮
    const closeModalBtns = document.querySelectorAll('.close-modal, .btn-cancel');
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            closeModal();
            closeImportModal();
        });
    });

    // 点击模态框背景关闭
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
                closeImportModal();
            }
        });
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
            closeImportModal();
        }
    });

    // 页面大小选择器
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
        pageSizeSelect.addEventListener('change', function() {
            pageSize = parseInt(this.value);
            currentPage = 1; // 重置到第一页
            loadCookies(currentPage);
        });
    }

    // 全选/取消全选
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.cookie-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // 批量操作按钮
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', handleBatchDelete);
    }

    const batchValidateBtn = document.getElementById('batchValidateBtn');
    if (batchValidateBtn) {
        batchValidateBtn.addEventListener('click', handleBatchValidate);
    }

    console.log('事件监听器设置完成');
}

/**
 * 加载Cookie统计信息
 */
async function loadCookieStats() {
    try {
        const platformFilter = document.getElementById('platformFilter');
        const platform = platformFilter ? platformFilter.value : '';
        const url = platform ? `/cookies/stats?platform=${platform}` : '/cookies/stats';

        const response = await apiRequest(url);
        if (response.success) {
            const stats = response.data;

            // 更新统计数据显示
            const totalCountEl = document.getElementById('totalCount');
            const activeCountEl = document.getElementById('activeCount');
            const expiredCountEl = document.getElementById('expiredCount');
            const bannedCountEl = document.getElementById('bannedCount');

            if (totalCountEl) totalCountEl.textContent = formatNumber(stats.total || 0);
            if (activeCountEl) activeCountEl.textContent = formatNumber(stats.active || 0);
            if (expiredCountEl) expiredCountEl.textContent = formatNumber(stats.expired || 0);
            if (bannedCountEl) bannedCountEl.textContent = formatNumber(stats.banned || 0);

            console.log('Cookie统计信息加载成功:', stats);
        } else {
            console.error('获取统计信息失败:', response.message);
            showError('获取统计信息失败: ' + response.message);

            // 显示默认值
            resetStatsDisplay();
        }
    } catch (error) {
        console.error('加载统计信息失败:', error);
        showError('加载统计信息失败，请检查网络连接');

        // 显示默认值
        resetStatsDisplay();
    }
}

/**
 * 重置统计显示为默认值
 */
function resetStatsDisplay() {
    const totalCountEl = document.getElementById('totalCount');
    const activeCountEl = document.getElementById('activeCount');
    const expiredCountEl = document.getElementById('expiredCount');
    const bannedCountEl = document.getElementById('bannedCount');

    if (totalCountEl) totalCountEl.textContent = '0';
    if (activeCountEl) activeCountEl.textContent = '0';
    if (expiredCountEl) expiredCountEl.textContent = '0';
    if (bannedCountEl) bannedCountEl.textContent = '0';
}

/**
 * 加载Cookie列表
 */
async function loadCookies(page = 1) {
    try {
        showLoading();
        
        const platform = document.getElementById('platformFilter').value;
        const status = document.getElementById('statusFilter').value;
        const search = document.getElementById('searchInput').value;
        
        const params = new URLSearchParams({
            page: page.toString(),
            limit: pageSize.toString()
        });
        
        if (platform) params.append('platform', platform);
        if (status) params.append('status', status);
        if (search) params.append('accountName', search);
        
        const response = await apiRequest(`/cookies?${params}`);
        
        if (response.success) {
            renderCookieTable(response.data);
            renderPagination(response.pagination);
            currentPage = page;
        } else {
            showError('加载Cookie列表失败: ' + response.message);
        }
    } catch (error) {
        console.error('加载Cookie列表失败:', error);
        showError('加载Cookie列表失败');
    } finally {
        hideLoading();
    }
}

/**
 * 渲染Cookie表格
 */
function renderCookieTable(cookies) {
    const tbody = document.getElementById('cookieTableBody');
    
    if (cookies.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="empty-state">
                    <div>
                        <p>📭 暂无Cookie数据</p>
                        <button class="btn btn-primary" onclick="showAddModal()">添加第一个Cookie</button>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = cookies.map(cookie => `
        <tr>
            <td>
                <strong>${escapeHtml(cookie.accountName)}</strong>
                ${cookie.notes ? `<br><small class="text-muted">${escapeHtml(cookie.notes)}</small>` : ''}
            </td>
            <td>
                <span class="platform-badge platform-${cookie.platform}">
                    ${getPlatformName(cookie.platform)}
                </span>
            </td>
            <td>
                <span class="status-badge status-${cookie.status}">
                    ${getStatusText(cookie.status)}
                </span>
            </td>
            <td>
                <span class="priority-badge">${cookie.priority}</span>
            </td>
            <td>
                <div>
                    <strong>${cookie.useCount}</strong>
                    <br><small>今日: ${cookie.dailyUseCount}/${cookie.maxDailyUse}</small>
                </div>
            </td>
            <td>
                ${cookie.lastUsedAt ? formatDateTime(cookie.lastUsedAt) : '未使用'}
            </td>
            <td>
                ${cookie.creator?.username || '未知'}
            </td>
            <td>
                <div class="cookie-actions">
                    <button class="btn-icon btn-primary" onclick="validateCookie(${cookie.id})" 
                            title="验证Cookie">
                        🔍
                    </button>
                    <button class="btn-icon btn-secondary" onclick="editCookie(${cookie.id})" 
                            title="编辑">
                        ✏️
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteCookie(${cookie.id}, '${escapeHtml(cookie.accountName)}')" 
                            title="删除">
                        🗑️
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * 渲染分页
 */
function renderPagination(pagination) {
    const container = document.getElementById('pagination');
    
    if (pagination.pages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页
    if (pagination.page > 1) {
        html += `<button class="btn btn-outline" onclick="loadCookies(${pagination.page - 1})">上一页</button>`;
    }
    
    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === pagination.page ? 'btn-primary' : 'btn-outline';
        html += `<button class="btn ${isActive}" onclick="loadCookies(${i})">${i}</button>`;
    }
    
    // 下一页
    if (pagination.page < pagination.pages) {
        html += `<button class="btn btn-outline" onclick="loadCookies(${pagination.page + 1})">下一页</button>`;
    }
    
    // 总数信息
    html += `<span class="pagination-info">共 ${pagination.total} 条记录</span>`;
    
    container.innerHTML = html;
}

/**
 * 显示添加Cookie模态框
 */
function showAddModal() {
    editingCookieId = null;
    document.getElementById('modalTitle').textContent = '添加Cookie';
    document.getElementById('cookieForm').reset();
    document.getElementById('cookieModal').style.display = 'block';
}

/**
 * 编辑Cookie
 */
async function editCookie(id) {
    try {
        const response = await apiRequest(`/cookies/${id}`);
        if (response.success) {
            const cookie = response.data;
            editingCookieId = id;
            
            document.getElementById('modalTitle').textContent = '编辑Cookie';
            document.getElementById('accountName').value = cookie.accountName;
            document.getElementById('platform').value = cookie.platform;
            document.getElementById('userAgent').value = cookie.userAgent || '';
            document.getElementById('priority').value = cookie.priority;
            document.getElementById('maxDailyUse').value = cookie.maxDailyUse;
            document.getElementById('notes').value = cookie.notes || '';
            
            // 明文模式：直接显示Cookie数据
            document.getElementById('cookieString').value = cookie.cookieValue || cookie.cookieData || '';
            document.getElementById('cookieString').placeholder = '请输入Cookie数据';
            
            document.getElementById('cookieModal').style.display = 'block';
        }
    } catch (error) {
        showError('获取Cookie信息失败');
    }
}

/**
 * 处理Cookie表单提交
 */
async function handleCookieSubmit(e) {
    e.preventDefault();
    
    try {
        const formData = {
            accountName: document.getElementById('accountName').value.trim(),
            platform: document.getElementById('platform').value,
            cookieString: document.getElementById('cookieString').value.trim(),
            userAgent: document.getElementById('userAgent').value.trim(),
            priority: parseInt(document.getElementById('priority').value),
            maxDailyUse: parseInt(document.getElementById('maxDailyUse').value),
            notes: document.getElementById('notes').value.trim()
        };
        
        // 验证必填字段
        if (!formData.accountName || !formData.platform) {
            showError('账户名和平台为必填项');
            return;
        }
        
        let response;
        if (editingCookieId) {
            // 编辑模式：Cookie数据为必填
            if (!formData.cookieString) {
                showError('Cookie数据不能为空');
                return;
            }
            response = await apiRequest(`/cookies/${editingCookieId}`, 'PUT', formData);
        } else {
            // 添加模式：Cookie数据为必填
            if (!formData.cookieString) {
                showError('Cookie数据不能为空');
                return;
            }
            response = await apiRequest('/cookies', 'POST', formData);
        }
        
        if (response.success) {
            showSuccess(editingCookieId ? 'Cookie更新成功' : 'Cookie添加成功');
            closeModal();
            loadCookies(currentPage);
            loadCookieStats();
        } else {
            showError(response.message);
        }
    } catch (error) {
        showError('操作失败: ' + error.message);
    }
}

/**
 * 验证Cookie
 */
async function validateCookie(id) {
    try {
        showLoading('验证中...');
        const response = await apiRequest(`/cookies/${id}/validate`, 'POST');
        
        if (response.success) {
            const result = response.data;
            if (result.isValid) {
                showSuccess(`Cookie验证成功: ${result.accountName}`);
            } else {
                showWarning(`Cookie验证失败: ${result.accountName}`);
            }
            loadCookies(currentPage);
            loadCookieStats();
        } else {
            showError('验证失败: ' + response.message);
        }
    } catch (error) {
        showError('验证失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

/**
 * 删除Cookie
 */
async function deleteCookie(id, accountName) {
    if (!window.confirm(`确定要删除Cookie "${accountName}" 吗？此操作不可恢复。`)) {
        return;
    }
    
    try {
        const response = await apiRequest(`/cookies/${id}`, 'DELETE');
        
        if (response.success) {
            showSuccess('Cookie删除成功');
            loadCookies(currentPage);
            loadCookieStats();
        } else {
            showError('删除失败: ' + response.message);
        }
    } catch (error) {
        showError('删除失败: ' + error.message);
    }
}

/**
 * 显示导入模态框
 */
function showImportModal() {
    document.getElementById('importData').value = '';
    document.getElementById('importModal').style.display = 'block';
}

/**
 * 关闭模态框
 */
function closeModal() {
    const cookieModal = document.getElementById('cookieModal');
    if (cookieModal) {
        cookieModal.style.display = 'none';
    }
    editingCookieId = null;
}

/**
 * 关闭导入模态框
 */
function closeImportModal() {
    const importModal = document.getElementById('importModal');
    if (importModal) {
        importModal.style.display = 'none';
    }
}

/**
 * 批量删除Cookie
 */
async function handleBatchDelete() {
    const selectedCheckboxes = document.querySelectorAll('.cookie-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showWarning('请选择要删除的Cookie');
        return;
    }

    if (!window.confirm(`确定要删除选中的 ${selectedCheckboxes.length} 个Cookie吗？此操作不可恢复。`)) {
        return;
    }

    try {
        showLoading('批量删除中...');
        const deletePromises = Array.from(selectedCheckboxes).map(checkbox => {
            const cookieId = checkbox.value;
            return apiRequest(`/cookies/${cookieId}`, 'DELETE');
        });

        const results = await Promise.allSettled(deletePromises);
        const successCount = results.filter(result => result.status === 'fulfilled' && result.value.success).length;
        const failCount = results.length - successCount;

        if (successCount > 0) {
            showSuccess(`成功删除 ${successCount} 个Cookie${failCount > 0 ? `，失败 ${failCount} 个` : ''}`);
            loadCookies(currentPage);
            loadCookieStats();
        } else {
            showError('批量删除失败');
        }
    } catch (error) {
        showError('批量删除失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

/**
 * 批量验证Cookie
 */
async function handleBatchValidate() {
    const selectedCheckboxes = document.querySelectorAll('.cookie-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showWarning('请选择要验证的Cookie');
        return;
    }

    try {
        showLoading('批量验证中...');
        const validatePromises = Array.from(selectedCheckboxes).map(checkbox => {
            const cookieId = checkbox.value;
            return apiRequest(`/cookies/${cookieId}/validate`, 'POST');
        });

        const results = await Promise.allSettled(validatePromises);
        const successCount = results.filter(result =>
            result.status === 'fulfilled' &&
            result.value.success &&
            result.value.data.isValid
        ).length;
        const failCount = results.length - successCount;

        showSuccess(`验证完成：有效 ${successCount} 个，无效 ${failCount} 个`);
        loadCookies(currentPage);
        loadCookieStats();
    } catch (error) {
        showError('批量验证失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

function closeImportModal() {
    document.getElementById('importModal').style.display = 'none';
}

/**
 * 搜索防抖
 */
function debounceSearch() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        currentPage = 1;
        loadCookies();
    }, 500);
}

/**
 * 导出Cookie
 */
async function exportCookies() {
    try {
        const platform = document.getElementById('platformFilter').value;
        const url = platform ? `/cookies/export?platform=${platform}` : '/cookies/export';
        
        const response = await apiRequest(url);
        if (response.success) {
            const dataStr = JSON.stringify(response.data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `cookies_${platform || 'all'}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            showSuccess('Cookie数据导出成功');
        }
    } catch (error) {
        showError('导出失败: ' + error.message);
    }
}

// 工具函数
function getPlatformName(platform) {
    const names = {
        'juxingtu': '巨量星图',
        'xiaohongshu': '小红书'
    };
    return names[platform] || platform;
}

function getStatusText(status) {
    const texts = {
        'active': '可用',
        'expired': '已过期',
        'banned': '已封禁',
        'inactive': '未激活'
    };
    return texts[status] || status;
}

function formatDateTime(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 处理文件选择
 */
function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        readFile(file);
    }
}

/**
 * 处理拖拽
 */
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        readFile(files[0]);
    }
}

/**
 * 读取文件内容
 */
function readFile(file) {
    if (!file.type.includes('json') && !file.name.endsWith('.json')) {
        showError('请选择JSON格式的文件');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const content = e.target.result;
            JSON.parse(content); // 验证JSON格式
            document.getElementById('importData').value = content;
            showSuccess('文件读取成功，请检查数据后点击导入');
        } catch (error) {
            showError('文件格式错误，请确保是有效的JSON文件');
        }
    };
    reader.readAsText(file);
}

/**
 * 导入Cookie
 */
async function importCookies() {
    try {
        const importData = document.getElementById('importData').value.trim();
        if (!importData) {
            showError('请输入或选择要导入的数据');
            return;
        }

        let cookies;
        try {
            cookies = JSON.parse(importData);
        } catch (error) {
            showError('数据格式错误，请确保是有效的JSON格式');
            return;
        }

        if (!Array.isArray(cookies)) {
            showError('数据格式错误，应该是Cookie对象的数组');
            return;
        }

        if (cookies.length === 0) {
            showError('没有要导入的Cookie数据');
            return;
        }

        // 验证数据格式
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i];
            if (!cookie.accountName || !cookie.platform || !cookie.cookieString) {
                showError(`第 ${i + 1} 条记录缺少必填字段 (accountName, platform, cookieString)`);
                return;
            }
        }

        showLoading('导入中...');

        const response = await apiRequest('/cookies/batch-import', 'POST', { cookies });

        if (response.success) {
            const result = response.data;
            let message = `导入完成！成功: ${result.success} 条`;
            if (result.failed > 0) {
                message += `，失败: ${result.failed} 条`;
                if (result.errors.length > 0) {
                    message += `\n失败详情:\n${result.errors.map(e => `- ${e.accountName}: ${e.error}`).join('\n')}`;
                }
            }

            if (result.failed > 0) {
                showWarning(message);
            } else {
                showSuccess(message);
            }

            closeImportModal();
            loadCookies(currentPage);
            loadCookieStats();
        } else {
            showError('导入失败: ' + response.message);
        }
    } catch (error) {
        showError('导入失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// 移除拖拽样式
document.addEventListener('dragleave', function(e) {
    if (e.target.id === 'importArea') {
        e.target.classList.remove('dragover');
    }
});
