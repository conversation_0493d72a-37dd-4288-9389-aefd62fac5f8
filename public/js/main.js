// 主页面逻辑

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;
let totalCount = 0;
let currentFilters = {};
let selectedInfluencers = new Set();
let allCategories = new Set();

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    if (!isLoggedIn()) {
        redirect('/login.html');
        return;
    }

    // 初始化页面
    initializePage();
    
    // 绑定事件
    bindEvents();
    
    // 加载数据
    loadInitialData();
});

// 初始化页面
function initializePage() {
    // 显示用户信息
    const user = getUser();
    if (user) {
        document.getElementById('userWelcome').textContent = `欢迎，${user.username}`;
    }
}

// 绑定事件
function bindEvents() {
    // 退出登录
    document.getElementById('logoutBtn').addEventListener('click', logout);
    
    // 工具栏按钮
    document.getElementById('addInfluencerBtn').addEventListener('click', () => openInfluencerModal());
    document.getElementById('batchDeleteBtn').addEventListener('click', batchDeleteInfluencers);
    document.getElementById('exportBtn').addEventListener('click', exportInfluencers);
    document.getElementById('refreshBtn').addEventListener('click', refreshData);
    
    // 搜索和筛选
    document.getElementById('searchBtn').addEventListener('click', performSearch);
    document.getElementById('resetBtn').addEventListener('click', resetFilters);
    
    // 搜索框回车
    document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // 全选复选框
    document.getElementById('selectAll').addEventListener('change', toggleSelectAll);
    
    // 分页按钮
    document.getElementById('prevPageBtn').addEventListener('click', () => changePage(currentPage - 1));
    document.getElementById('nextPageBtn').addEventListener('click', () => changePage(currentPage + 1));
    
    // 模态框事件
    document.getElementById('closeModal').addEventListener('click', closeInfluencerModal);
    document.getElementById('cancelBtn').addEventListener('click', closeInfluencerModal);
    document.getElementById('influencerForm').addEventListener('submit', saveInfluencer);
    
    // 点击模态框外部关闭
    document.getElementById('influencerModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeInfluencerModal();
        }
    });
}

// 加载初始数据
async function loadInitialData() {
    try {
        showLoading();
        
        // 并行加载统计数据和达人列表
        await Promise.all([
            loadStats(),
            loadInfluencers()
        ]);
        
    } catch (error) {
        console.error('Failed to load initial data:', error);
        showMessage('加载数据失败：' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 加载统计数据
async function loadStats() {
    try {
        const response = await influencerAPI.getStats();
        if (response.success) {
            const stats = response.data;
            
            // 更新统计卡片
            document.getElementById('totalCount').textContent = formatNumber(stats.totalCount);
            
            // 处理平台统计
            let xiaohongshuCount = 0;
            let juxingtuCount = 0;
            
            if (stats.platformStats) {
                stats.platformStats.forEach(item => {
                    if (item.platform === 'xiaohongshu') {
                        xiaohongshuCount = item.count;
                    } else if (item.platform === 'juxingtu') {
                        juxingtuCount = item.count;
                    }
                });
            }
            
            document.getElementById('xiaohongshuCount').textContent = formatNumber(xiaohongshuCount);
            document.getElementById('juxingtuCount').textContent = formatNumber(juxingtuCount);
            
            // 更新分类统计
            const categoryCount = stats.categoryStats ? stats.categoryStats.length : 0;
            document.getElementById('categoryCount').textContent = formatNumber(categoryCount);
            
            // 收集所有分类用于筛选
            if (stats.categoryStats) {
                stats.categoryStats.forEach(item => {
                    if (item.category) {
                        allCategories.add(item.category);
                    }
                });
                updateCategoryFilter();
            }
        }
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
}

// 更新分类筛选下拉框
function updateCategoryFilter() {
    const categoryFilter = document.getElementById('categoryFilter');
    const currentValue = categoryFilter.value;
    
    // 清空现有选项（保留"全部分类"）
    categoryFilter.innerHTML = '<option value="">全部分类</option>';
    
    // 添加分类选项
    Array.from(allCategories).sort().forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categoryFilter.appendChild(option);
    });
    
    // 恢复之前的选择
    categoryFilter.value = currentValue;
}

// 加载达人列表
async function loadInfluencers() {
    try {
        const params = {
            page: currentPage,
            limit: pageSize,
            sortBy: 'updatedAt',
            sortOrder: 'DESC',
            ...currentFilters
        };
        
        const response = await influencerAPI.getInfluencers(params);
        if (response.success) {
            const { data, pagination } = response;
            
            // 更新分页信息
            currentPage = pagination.page;
            totalPages = pagination.pages;
            totalCount = pagination.total;
            
            // 渲染表格
            renderInfluencerTable(data);
            
            // 更新分页控件
            updatePagination();
            
            // 清空选择
            selectedInfluencers.clear();
            updateBatchDeleteButton();
        }
    } catch (error) {
        console.error('Failed to load influencers:', error);
        showMessage('加载达人列表失败：' + error.message, 'error');
    }
}

// 渲染达人表格
function renderInfluencerTable(influencers) {
    const tbody = document.getElementById('influencerTableBody');
    
    if (influencers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" style="text-align: center; padding: 40px; color: #999;">
                    暂无数据
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = influencers.map(influencer => `
        <tr>
            <td>
                <input type="checkbox" class="influencer-checkbox" value="${influencer.id}">
            </td>
            <td>
                ${influencer.avatarUrl ? 
                    `<img src="${influencer.avatarUrl}" alt="${escapeHtml(influencer.nickname)}" class="avatar" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOTk5Ii8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzIDcgMTUuMzMgNyAxOFYyMEgxN1YxOEMxNyAxNS4zMyAxNC42NyAxMyAxMiAxNFoiIGZpbGw9IiM5OTkiLz4KPC9zdmc+Cjwvc3ZnPgo='">` : 
                    `<div class="avatar" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999; font-size: 12px;">头像</div>`
                }
            </td>
            <td>
                <strong>${escapeHtml(influencer.nickname)}</strong>
                ${influencer.platformId ? `<br><small style="color: #666;">ID: ${escapeHtml(influencer.platformId)}</small>` : ''}
            </td>
            <td>
                <span class="platform-badge platform-${influencer.platform}">
                    ${getPlatformName(influencer.platform)}
                </span>
            </td>
            <td>${formatNumber(influencer.followersCount)}</td>
            <td>${influencer.category ? escapeHtml(influencer.category) : '-'}</td>
            <td>
                ${influencer.contactInfo ? formatContactInfo(influencer.contactInfo) : '-'}
            </td>
            <td>
                ${influencer.authorExtInfo ?
                    `<span class="ext-info-badge" title="包含扩展信息">
                        <i class="fas fa-info-circle"></i> ${Object.keys(influencer.authorExtInfo).length} 项
                    </span>` :
                    '<span style="color: #999;">-</span>'
                }
            </td>
            <td>
                <span class="status-badge status-${influencer.status}">
                    ${getStatusName(influencer.status)}
                </span>
            </td>
            <td>
                <button class="btn btn-outline" style="padding: 4px 8px; font-size: 12px;" onclick="editInfluencer(${influencer.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-danger" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;" onclick="deleteInfluencer(${influencer.id})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </td>
        </tr>
    `).join('');
    
    // 绑定复选框事件
    const checkboxes = tbody.querySelectorAll('.influencer-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const id = parseInt(this.value);
            if (this.checked) {
                selectedInfluencers.add(id);
            } else {
                selectedInfluencers.delete(id);
            }
            updateBatchDeleteButton();
            updateSelectAllCheckbox();
        });
    });
}

// 格式化联系方式
function formatContactInfo(contactInfo) {
    if (!contactInfo || typeof contactInfo !== 'object') return '-';
    
    const parts = [];
    if (contactInfo.phone) parts.push(`📞 ${contactInfo.phone}`);
    if (contactInfo.email) parts.push(`📧 ${contactInfo.email}`);
    if (contactInfo.wechat) parts.push(`💬 ${contactInfo.wechat}`);
    
    return parts.length > 0 ? parts.join('<br>') : '-';
}

// 更新分页控件
function updatePagination() {
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');
    const pageNumbers = document.getElementById('pageNumbers');
    const paginationInfo = document.getElementById('paginationInfo');
    
    // 更新按钮状态
    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
    
    // 更新页码
    pageNumbers.innerHTML = generatePageNumbers();
    
    // 更新分页信息
    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, totalCount);
    paginationInfo.textContent = `显示 ${start} - ${end} 条，共 ${totalCount} 条记录`;
}

// 生成页码
function generatePageNumbers() {
    if (totalPages <= 1) return '';
    
    const pages = [];
    const maxVisible = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let endPage = Math.min(totalPages, startPage + maxVisible - 1);
    
    if (endPage - startPage + 1 < maxVisible) {
        startPage = Math.max(1, endPage - maxVisible + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pages.push(`
            <button class="btn ${i === currentPage ? 'btn-primary' : 'btn-outline'}" 
                    style="padding: 4px 8px; font-size: 12px; margin: 0 2px;" 
                    onclick="changePage(${i})">${i}</button>
        `);
    }
    
    return pages.join('');
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) return;
    
    currentPage = page;
    loadInfluencers();
}

// 执行搜索
function performSearch() {
    const keyword = document.getElementById('searchKeyword').value.trim();
    const platform = document.getElementById('platformFilter').value;
    const category = document.getElementById('categoryFilter').value;
    
    currentFilters = {};
    if (keyword) currentFilters.keyword = keyword;
    if (platform) currentFilters.platform = platform;
    if (category) currentFilters.category = category;
    
    currentPage = 1;
    loadInfluencers();
}

// 重置筛选
function resetFilters() {
    document.getElementById('searchKeyword').value = '';
    document.getElementById('platformFilter').value = '';
    document.getElementById('categoryFilter').value = '';
    
    currentFilters = {};
    currentPage = 1;
    loadInfluencers();
}

// 刷新数据
function refreshData() {
    loadInitialData();
}

// 退出登录
function logout() {
    if (window.confirm('确定要退出登录吗？')) {
        removeToken();
        removeUser();
        redirect('/login.html');
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.influencer-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        const id = parseInt(checkbox.value);
        if (selectAll.checked) {
            selectedInfluencers.add(id);
        } else {
            selectedInfluencers.delete(id);
        }
    });
    
    updateBatchDeleteButton();
}

// 更新全选复选框状态
function updateSelectAllCheckbox() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.influencer-checkbox');
    const checkedCount = document.querySelectorAll('.influencer-checkbox:checked').length;
    
    selectAll.checked = checkboxes.length > 0 && checkedCount === checkboxes.length;
    selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
}

// 更新批量删除按钮状态
function updateBatchDeleteButton() {
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    batchDeleteBtn.disabled = selectedInfluencers.size === 0;
    
    if (selectedInfluencers.size > 0) {
        batchDeleteBtn.innerHTML = `<i class="fas fa-trash"></i> 批量删除 (${selectedInfluencers.size})`;
    } else {
        batchDeleteBtn.innerHTML = `<i class="fas fa-trash"></i> 批量删除`;
    }
}

// 打开达人模态框
function openInfluencerModal(influencer = null) {
    const modal = document.getElementById('influencerModal');
    const form = document.getElementById('influencerForm');
    const title = document.getElementById('modalTitle');

    // 重置表单
    form.reset();
    document.getElementById('influencerId').value = '';

    if (influencer) {
        // 编辑模式
        title.textContent = '编辑达人';
        fillInfluencerForm(influencer);
    } else {
        // 添加模式
        title.textContent = '添加达人';
    }

    modal.style.display = 'flex';
}

// 关闭达人模态框
function closeInfluencerModal() {
    const modal = document.getElementById('influencerModal');
    modal.style.display = 'none';
}

// 填充达人表单
function fillInfluencerForm(influencer) {
    document.getElementById('influencerId').value = influencer.id;
    document.getElementById('nickname').value = influencer.nickname || '';
    document.getElementById('platform').value = influencer.platform || '';
    document.getElementById('platformId').value = influencer.platformId || '';
    document.getElementById('followersCount').value = influencer.followersCount || '';
    document.getElementById('category').value = influencer.category || '';
    document.getElementById('avatarUrl').value = influencer.avatarUrl || '';
    document.getElementById('notes').value = influencer.notes || '';

    // 处理标签
    if (influencer.tags && Array.isArray(influencer.tags)) {
        document.getElementById('tags').value = influencer.tags.join(', ');
    }

    // 处理联系方式
    if (influencer.contactInfo) {
        document.getElementById('contactPhone').value = influencer.contactInfo.phone || '';
        document.getElementById('contactEmail').value = influencer.contactInfo.email || '';
        document.getElementById('contactWechat').value = influencer.contactInfo.wechat || '';
    }

    // 处理扩展信息
    if (influencer.authorExtInfo) {
        document.getElementById('authorExtInfo').value = JSON.stringify(influencer.authorExtInfo, null, 2);
    } else {
        document.getElementById('authorExtInfo').value = '';
    }
}

// 保存达人
async function saveInfluencer(e) {
    e.preventDefault();

    const form = e.target;
    const formData = new FormData(form);
    const influencerId = document.getElementById('influencerId').value;

    // 构建数据对象
    const data = {
        nickname: formData.get('nickname').trim(),
        platform: formData.get('platform'),
        platformId: formData.get('platformId').trim(),
        followersCount: parseInt(formData.get('followersCount')) || 0,
        category: formData.get('category').trim(),
        avatarUrl: formData.get('avatarUrl').trim(),
        notes: formData.get('notes').trim()
    };

    // 处理标签
    const tagsStr = formData.get('tags').trim();
    if (tagsStr) {
        data.tags = tagsStr.split(',').map(tag => tag.trim()).filter(tag => tag);
    }

    // 处理联系方式
    const contactInfo = {};
    const phone = formData.get('contactPhone').trim();
    const email = formData.get('contactEmail').trim();
    const wechat = formData.get('contactWechat').trim();

    if (phone) contactInfo.phone = phone;
    if (email) contactInfo.email = email;
    if (wechat) contactInfo.wechat = wechat;

    if (Object.keys(contactInfo).length > 0) {
        data.contactInfo = contactInfo;
    }

    // 验证必填字段
    if (!data.nickname) {
        showMessage('请输入达人昵称', 'error');
        return;
    }

    if (!data.platform) {
        showMessage('请选择平台', 'error');
        return;
    }

    // 验证邮箱格式
    if (email && !isValidEmail(email)) {
        showMessage('邮箱格式不正确', 'error');
        return;
    }

    // 验证手机号格式
    if (phone && !isValidPhone(phone)) {
        showMessage('手机号格式不正确', 'error');
        return;
    }

    // 验证URL格式
    if (data.avatarUrl && !isValidUrl(data.avatarUrl)) {
        showMessage('头像链接格式不正确', 'error');
        return;
    }

    try {
        showLoading();

        let response;
        if (influencerId) {
            // 更新达人
            response = await influencerAPI.updateInfluencer(influencerId, data);
        } else {
            // 创建达人
            response = await influencerAPI.createInfluencer(data);
        }

        if (response.success) {
            showMessage(influencerId ? '更新成功' : '添加成功', 'success');
            closeInfluencerModal();
            loadInfluencers();
            loadStats(); // 重新加载统计数据
        } else {
            showMessage(response.message || '操作失败', 'error');
        }
    } catch (error) {
        console.error('Save influencer error:', error);
        showMessage(error.message || '操作失败', 'error');
    } finally {
        hideLoading();
    }
}

// 编辑达人
async function editInfluencer(id) {
    try {
        showLoading();
        const response = await influencerAPI.getInfluencer(id);
        if (response.success) {
            openInfluencerModal(response.data);
        } else {
            showMessage('获取达人信息失败', 'error');
        }
    } catch (error) {
        console.error('Edit influencer error:', error);
        showMessage('获取达人信息失败：' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 删除达人
async function deleteInfluencer(id) {
    if (!window.confirm('确定要删除这个达人吗？此操作不可恢复。')) {
        return;
    }

    try {
        showLoading();
        const response = await influencerAPI.deleteInfluencer(id);
        if (response.success) {
            showMessage('删除成功', 'success');
            loadInfluencers();
            loadStats(); // 重新加载统计数据
        } else {
            showMessage(response.message || '删除失败', 'error');
        }
    } catch (error) {
        console.error('Delete influencer error:', error);
        showMessage(error.message || '删除失败', 'error');
    } finally {
        hideLoading();
    }
}

// 批量删除达人
async function batchDeleteInfluencers() {
    if (selectedInfluencers.size === 0) {
        showMessage('请选择要删除的达人', 'warning');
        return;
    }

    if (!window.confirm(`确定要删除选中的 ${selectedInfluencers.size} 个达人吗？此操作不可恢复。`)) {
        return;
    }

    try {
        showLoading();
        const ids = Array.from(selectedInfluencers);
        const response = await influencerAPI.batchDeleteInfluencers(ids);
        if (response.success) {
            showMessage(`成功删除 ${response.data.deletedCount} 个达人`, 'success');
            selectedInfluencers.clear();
            loadInfluencers();
            loadStats(); // 重新加载统计数据
        } else {
            showMessage(response.message || '批量删除失败', 'error');
        }
    } catch (error) {
        console.error('Batch delete error:', error);
        showMessage(error.message || '批量删除失败', 'error');
    } finally {
        hideLoading();
    }
}

// 导出Excel
async function exportInfluencers() {
    try {
        showLoading();

        // 使用当前的筛选条件
        const params = { ...currentFilters };

        const blob = await influencerAPI.exportExcel(params);

        // 生成文件名
        const now = new Date();
        const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '');
        const filename = `达人信息_${timestamp}.xlsx`;

        // 下载文件
        downloadFile(blob, filename);

        showMessage('导出成功', 'success');
    } catch (error) {
        console.error('Export error:', error);
        showMessage(error.message || '导出失败', 'error');
    } finally {
        hideLoading();
    }
}

// 全局函数，供HTML调用
window.editInfluencer = editInfluencer;
window.deleteInfluencer = deleteInfluencer;
window.changePage = changePage;
