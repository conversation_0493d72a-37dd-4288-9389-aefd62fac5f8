// API交互模块

class ApiClient {
    constructor(baseURL = API_BASE_URL) {
        this.baseURL = baseURL;
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const token = getToken();
        
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // 添加认证头
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        try {
            const response = await fetch(url, config);
            
            // 处理特殊响应类型（如文件下载）
            if (options.responseType === 'blob') {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.blob();
            }
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // GET请求
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url, { method: 'GET' });
    }

    // POST请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE请求
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // 文件下载
    async download(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url, { 
            method: 'GET',
            responseType: 'blob'
        });
    }
}

// 创建API客户端实例
const api = new ApiClient();

// 认证相关API
const authAPI = {
    // 用户登录
    async login(username, password) {
        return api.post('/auth/login', { username, password });
    },

    // 获取当前用户信息
    async getCurrentUser() {
        return api.get('/auth/me');
    },

    // 修改密码
    async changePassword(oldPassword, newPassword) {
        return api.put('/auth/change-password', { oldPassword, newPassword });
    }
};

// 达人管理相关API
const influencerAPI = {
    // 获取达人列表
    async getInfluencers(params = {}) {
        return api.get('/influencers', params);
    },

    // 获取达人详情
    async getInfluencer(id) {
        return api.get(`/influencers/${id}`);
    },

    // 创建达人
    async createInfluencer(data) {
        return api.post('/influencers', data);
    },

    // 更新达人
    async updateInfluencer(id, data) {
        return api.put(`/influencers/${id}`, data);
    },

    // 删除达人
    async deleteInfluencer(id) {
        return api.delete(`/influencers/${id}`);
    },

    // 批量删除达人
    async batchDeleteInfluencers(ids) {
        return api.post('/influencers/batch-delete', { ids });
    },

    // 获取统计信息
    async getStats() {
        return api.get('/influencers/stats');
    },

    // 导出Excel
    async exportExcel(params = {}) {
        return api.download('/influencers/export', params);
    }
};

// 错误处理
window.addEventListener('unhandledrejection', event => {
    console.error('Unhandled promise rejection:', event.reason);

    // 如果是认证错误，跳转到登录页
    if (event.reason.message && (event.reason.message.includes('认证') || event.reason.message.includes('401'))) {
        removeToken();
        removeUser();
        if (window.location.pathname !== '/login.html') {
            redirect('/login.html');
        }
    }
});

// 爬虫相关API
const crawlerAPI = {
    // 获取任务列表
    async getTasks(params = {}) {
        return api.get('/crawler/tasks', params);
    },

    // 获取任务详情
    async getTask(id) {
        return api.get(`/crawler/tasks/${id}`);
    },

    // 创建任务
    async createTask(data) {
        return api.post('/crawler/tasks', data);
    },

    // 启动任务
    async startTask(id) {
        return api.post(`/crawler/tasks/${id}/start`);
    },

    // 停止任务（暂停）
    async stopTask(id) {
        return api.post(`/crawler/tasks/${id}/stop`);
    },

    // 恢复任务
    async resumeTask(id) {
        return api.post(`/crawler/tasks/${id}/resume`);
    },

    // 重试任务
    async retryTask(id) {
        return api.post(`/crawler/tasks/${id}/retry`);
    },

    // 删除任务
    async deleteTask(id) {
        return api.delete(`/crawler/tasks/${id}`);
    },

    // 批量重试任务
    async batchRetryTasks(ids) {
        return api.post('/crawler/tasks/batch-retry', { ids });
    },

    // 批量删除任务
    async batchDeleteTasks(ids) {
        return api.post('/crawler/tasks/batch-delete', { ids });
    },

    // 获取任务结果
    async getTaskResults(id, params = {}) {
        return api.get(`/crawler/tasks/${id}/results`, params);
    },

    // 获取任务日志
    async getTaskLogs(id, params = {}) {
        return api.get(`/crawler/tasks/${id}/logs`, params);
    },

    // 导入结果到达人表
    async importResults(id, resultIds) {
        return api.post(`/crawler/tasks/${id}/import`, { resultIds });
    },

    // 获取服务状态
    async getServiceStatus() {
        return api.get('/crawler/status');
    },

    // 获取统计信息
    async getStats() {
        return api.get('/crawler/stats');
    }
};

// 导出API对象
window.authAPI = authAPI;
window.influencerAPI = influencerAPI;
window.crawlerAPI = crawlerAPI;
