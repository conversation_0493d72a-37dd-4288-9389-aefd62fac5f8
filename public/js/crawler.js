// 爬虫管理页面逻辑

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;
let totalCount = 0;
let currentFilters = {};
let refreshInterval = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    if (!isLoggedIn()) {
        redirect('/login.html');
        return;
    }

    // 初始化页面
    initializePage();
    
    // 绑定事件
    bindEvents();
    
    // 加载数据
    loadInitialData();
    
    // 启动自动刷新
    startAutoRefresh();
});

// 初始化页面
function initializePage() {
    // 显示用户信息
    const user = getUser();
    if (user) {
        document.getElementById('userWelcome').textContent = `欢迎，${user.username}`;
    }
}

// 绑定事件
function bindEvents() {
    // 退出登录
    document.getElementById('logoutBtn').addEventListener('click', logout);
    
    // 创建任务相关
    document.getElementById('createTaskBtn').addEventListener('click', showCreateTaskForm);
    document.getElementById('cancelCreateBtn').addEventListener('click', hideCreateTaskForm);
    document.getElementById('taskForm').addEventListener('submit', createTask);
    
    // 筛选和刷新
    document.getElementById('statusFilter').addEventListener('change', applyFilters);
    document.getElementById('platformFilter').addEventListener('change', applyFilters);
    document.getElementById('refreshTasksBtn').addEventListener('click', refreshTasks);
    document.getElementById('batchRetryBtn').addEventListener('click', batchRetryFailedTasks);
    
    // 分页
    document.getElementById('prevPageBtn').addEventListener('click', () => changePage(currentPage - 1));
    document.getElementById('nextPageBtn').addEventListener('click', () => changePage(currentPage + 1));
}

// 加载初始数据
async function loadInitialData() {
    try {
        showLoading();
        
        // 并行加载统计数据和任务列表
        await Promise.all([
            loadStats(),
            loadTasks()
        ]);
        
    } catch (error) {
        console.error('Failed to load initial data:', error);
        showMessage('加载数据失败：' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 加载统计数据
async function loadStats() {
    try {
        const response = await crawlerAPI.getStats();
        if (response.success) {
            const stats = response.data;
            
            // 更新统计卡片
            let totalTasks = 0;
            let runningTasks = 0;
            let completedTasks = 0;
            
            if (stats.taskStats) {
                stats.taskStats.forEach(item => {
                    totalTasks += item.count;
                    if (item.status === 'running') {
                        runningTasks = item.count;
                    } else if (item.status === 'completed') {
                        completedTasks = item.count;
                    }
                });
            }
            
            let totalResults = 0;
            if (stats.resultStats) {
                totalResults = stats.resultStats.reduce((sum, item) => sum + item.count, 0);
            }
            
            document.getElementById('totalTasks').textContent = formatNumber(totalTasks);
            document.getElementById('runningTasks').textContent = formatNumber(runningTasks);
            document.getElementById('completedTasks').textContent = formatNumber(completedTasks);
            document.getElementById('totalResults').textContent = formatNumber(totalResults);
        }
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
}

// 加载任务列表
async function loadTasks() {
    try {
        const params = {
            page: currentPage,
            limit: pageSize,
            ...currentFilters
        };
        
        const response = await crawlerAPI.getTasks(params);
        if (response.success) {
            const { data, pagination } = response;
            
            // 更新分页信息
            currentPage = pagination.page;
            totalPages = pagination.pages;
            totalCount = pagination.total;
            
            // 渲染任务列表
            renderTasksList(data);

            // 更新分页控件
            updatePagination();

            // 检查是否有失败的任务，决定是否显示批量重试按钮
            updateBatchRetryButton(data);
        }
    } catch (error) {
        console.error('Failed to load tasks:', error);
        showMessage('加载任务列表失败：' + error.message, 'error');
    }
}

// 渲染任务列表
function renderTasksList(tasks) {
    const container = document.getElementById('tasksList');
    
    if (tasks.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #999;">
                <i class="fas fa-robot" style="font-size: 48px; margin-bottom: 15px;"></i>
                <p>暂无爬虫任务</p>
                <button class="btn btn-primary" onclick="showCreateTaskForm()">
                    <i class="fas fa-plus"></i> 创建第一个任务
                </button>
            </div>
        `;
        return;
    }
    
    container.innerHTML = tasks.map(task => `
        <div class="task-card">
            <div class="task-meta">
                <div>
                    <strong>${escapeHtml(task.taskName)}</strong>
                    <span class="platform-badge platform-${task.platform}">
                        ${getPlatformName(task.platform)}
                    </span>
                </div>
                <div>
                    <span class="task-status status-${task.status}">
                        ${getTaskStatusName(task.status)}
                    </span>
                </div>
            </div>
            
            <div style="margin: 10px 0;">
                <strong>关键词:</strong> ${escapeHtml(task.keywords)}
            </div>
            
            <div style="margin: 10px 0; font-size: 14px; color: #666;">
                <span><i class="fas fa-calendar"></i> 创建时间: ${formatDateTime(task.createdAt)}</span>
                ${task.startedAt ? `<span style="margin-left: 15px;"><i class="fas fa-play"></i> 开始时间: ${formatDateTime(task.startedAt)}</span>` : ''}
                ${task.completedAt ? `<span style="margin-left: 15px;"><i class="fas fa-check"></i> 完成时间: ${formatDateTime(task.completedAt)}</span>` : ''}
            </div>
            
            ${task.status === 'running' ? `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${task.progress || 0}%"></div>
                </div>
                <div style="font-size: 12px; color: #666;">
                    进度: ${task.progress || 0}% | 当前页: ${task.currentPage || 1}/${task.maxPages || 5} | 
                    成功: ${task.successCount || 0} | 失败: ${task.failedCount || 0}
                </div>
            ` : ''}
            
            ${task.resultSummary ? `
                <div style="margin: 10px 0; font-size: 14px; color: #666;">
                    <span><i class="fas fa-chart-bar"></i> 结果统计: 总计 ${task.resultSummary.totalResults || 0} 个，成功 ${task.resultSummary.successCount || 0} 个</span>
                </div>
            ` : ''}
            
            ${task.errorMessage ? `
                <div style="margin: 10px 0; padding: 10px; background: #ffe6e6; border-radius: 4px; color: #d63031; font-size: 14px;">
                    <i class="fas fa-exclamation-triangle"></i> ${escapeHtml(task.errorMessage)}
                </div>
            ` : ''}

            ${task.retryCount > 0 ? `
                <div style="margin: 10px 0; font-size: 14px; color: #666;">
                    <i class="fas fa-redo"></i> 重试次数: ${task.retryCount}/${task.maxRetries || 3}
                    ${task.lastRetryAt ? `| 最后重试: ${formatDateTime(task.lastRetryAt)}` : ''}
                </div>
            ` : ''}
            
            <div class="task-actions">
                ${getTaskActionButtons(task)}
            </div>
        </div>
    `).join('');
}

// 获取任务操作按钮
function getTaskActionButtons(task) {
    const buttons = [];
    
    // 查看详情按钮
    buttons.push(`
        <button class="btn btn-outline" onclick="viewTaskDetail(${task.id})">
            <i class="fas fa-eye"></i> 详情
        </button>
    `);
    
    // 根据状态显示不同的操作按钮
    switch (task.status) {
        case 'pending':
            buttons.push(`
                <button class="btn btn-primary" onclick="startTask(${task.id})">
                    <i class="fas fa-play"></i> 启动
                </button>
            `);
            break;
        case 'paused':
            buttons.push(`
                <button class="btn btn-success" onclick="resumeTask(${task.id})">
                    <i class="fas fa-play"></i> 恢复
                </button>
            `);
            break;
        case 'running':
            buttons.push(`
                <button class="btn btn-warning" onclick="stopTask(${task.id})">
                    <i class="fas fa-pause"></i> 暂停
                </button>
            `);
            break;
        case 'completed':
            buttons.push(`
                <button class="btn btn-success" onclick="viewTaskResults(${task.id})">
                    <i class="fas fa-download"></i> 查看结果
                </button>
            `);
            break;
        case 'failed':
            // 检查是否可以重试
            const canRetry = (task.retryCount || 0) < (task.maxRetries || 3);
            if (canRetry) {
                buttons.push(`
                    <button class="btn btn-warning" onclick="retryTask(${task.id})">
                        <i class="fas fa-redo"></i> 重试
                    </button>
                `);
            }
            break;
    }
    
    // 删除按钮（运行中的任务不能删除）
    if (task.status !== 'running') {
        buttons.push(`
            <button class="btn btn-danger" onclick="deleteTask(${task.id})">
                <i class="fas fa-trash"></i> 删除
            </button>
        `);
    }
    
    return buttons.join('');
}

// 获取任务状态名称
function getTaskStatusName(status) {
    const statusNames = {
        'pending': '待执行',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败',
        'paused': '已暂停',
        'cancelled': '已取消'
    };
    return statusNames[status] || status;
}

// 显示创建任务表单
function showCreateTaskForm() {
    document.getElementById('createTaskForm').style.display = 'block';
    document.getElementById('taskName').focus();
}

// 隐藏创建任务表单
function hideCreateTaskForm() {
    document.getElementById('createTaskForm').style.display = 'none';
    document.getElementById('taskForm').reset();
}

// 创建任务
async function createTask(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const taskData = {
        taskName: formData.get('taskName').trim(),
        platform: formData.get('platform'),
        keywords: formData.get('keywords').trim(),
        maxPages: parseInt(formData.get('maxPages')) || 5,
        priority: parseInt(formData.get('priority')) || 0,
        config: {
            pageSize: parseInt(formData.get('pageSize')) || 20,
            description: formData.get('description').trim()
        }
    };
    
    // 验证必填字段
    if (!taskData.taskName) {
        showMessage('请输入任务名称', 'error');
        return;
    }
    if (!taskData.platform) {
        showMessage('请选择爬取平台', 'error');
        return;
    }
    if (!taskData.keywords) {
        showMessage('请输入搜索关键词', 'error');
        return;
    }
    
    try {
        showLoading();
        
        const response = await crawlerAPI.createTask(taskData);
        if (response.success) {
            showMessage('任务创建成功', 'success');
            hideCreateTaskForm();
            loadTasks();
            loadStats();
        } else {
            showMessage(response.message || '创建任务失败', 'error');
        }
    } catch (error) {
        console.error('Create task error:', error);
        showMessage(error.message || '创建任务失败', 'error');
    } finally {
        hideLoading();
    }
}

// 启动任务
async function startTask(taskId) {
    if (!window.confirm('确定要启动这个任务吗？')) {
        return;
    }
    
    try {
        showLoading();
        
        const response = await crawlerAPI.startTask(taskId);
        if (response.success) {
            showMessage('任务启动成功', 'success');
            loadTasks();
            loadStats();
        } else {
            showMessage(response.message || '启动任务失败', 'error');
        }
    } catch (error) {
        console.error('Start task error:', error);
        showMessage(error.message || '启动任务失败', 'error');
    } finally {
        hideLoading();
    }
}

// 停止任务
async function stopTask(taskId) {
    if (!window.confirm('确定要停止这个任务吗？')) {
        return;
    }

    try {
        showLoading();

        const response = await crawlerAPI.stopTask(taskId);
        if (response.success) {
            showMessage('任务停止成功', 'success');
            loadTasks();
            loadStats();
        } else {
            showMessage(response.message || '停止任务失败', 'error');
        }
    } catch (error) {
        console.error('Stop task error:', error);
        showMessage(error.message || '停止任务失败', 'error');
    } finally {
        hideLoading();
    }
}

// 恢复任务
async function resumeTask(taskId) {
    if (!window.confirm('确定要恢复这个任务吗？')) {
        return;
    }

    try {
        showLoading();

        const response = await crawlerAPI.resumeTask(taskId);
        if (response.success) {
            showMessage('任务恢复成功', 'success');
            loadTasks();
            loadStats();
        } else {
            showMessage(response.message || '恢复任务失败', 'error');
        }
    } catch (error) {
        console.error('Resume task error:', error);
        showMessage(error.message || '恢复任务失败', 'error');
    } finally {
        hideLoading();
    }
}

// 重试任务
async function retryTask(taskId) {
    if (!window.confirm('确定要重试这个任务吗？')) {
        return;
    }

    try {
        showLoading();

        const response = await crawlerAPI.retryTask(taskId);
        if (response.success) {
            showMessage('任务重试成功', 'success');
            loadTasks();
            loadStats();
        } else {
            showMessage(response.message || '重试任务失败', 'error');
        }
    } catch (error) {
        console.error('Retry task error:', error);
        showMessage(error.message || '重试任务失败', 'error');
    } finally {
        hideLoading();
    }
}

// 删除任务
async function deleteTask(taskId) {
    if (!window.confirm('确定要删除这个任务吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        showLoading();
        
        const response = await crawlerAPI.deleteTask(taskId);
        if (response.success) {
            showMessage('任务删除成功', 'success');
            loadTasks();
            loadStats();
        } else {
            showMessage(response.message || '删除任务失败', 'error');
        }
    } catch (error) {
        console.error('Delete task error:', error);
        showMessage(error.message || '删除任务失败', 'error');
    } finally {
        hideLoading();
    }
}

// 查看任务详情
function viewTaskDetail(taskId) {
    // 这里可以打开一个模态框显示任务详情
    // 或者跳转到详情页面
    window.open(`crawler-detail.html?id=${taskId}`, '_blank');
}

// 查看任务结果
function viewTaskResults(taskId) {
    // 跳转到结果页面
    window.location.href = `crawler-results.html?taskId=${taskId}`;
}

// 应用筛选
function applyFilters() {
    const status = document.getElementById('statusFilter').value;
    const platform = document.getElementById('platformFilter').value;
    
    currentFilters = {};
    if (status) currentFilters.status = status;
    if (platform) currentFilters.platform = platform;
    
    currentPage = 1;
    loadTasks();
}

// 刷新任务列表
function refreshTasks() {
    loadTasks();
    loadStats();
}

// 启动自动刷新
function startAutoRefresh() {
    // 每30秒自动刷新一次
    refreshInterval = setInterval(() => {
        loadTasks();
        loadStats();
    }, 30000);
}

// 停止自动刷新
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

// 更新分页控件
function updatePagination() {
    const pagination = document.getElementById('pagination');
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');
    const pageNumbers = document.getElementById('pageNumbers');
    const paginationInfo = document.getElementById('paginationInfo');
    
    if (totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }
    
    pagination.style.display = 'flex';
    
    // 更新按钮状态
    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
    
    // 更新页码
    pageNumbers.innerHTML = generatePageNumbers();
    
    // 更新分页信息
    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, totalCount);
    paginationInfo.textContent = `显示 ${start} - ${end} 条，共 ${totalCount} 条记录`;
}

// 生成页码
function generatePageNumbers() {
    if (totalPages <= 1) return '';
    
    const pages = [];
    const maxVisible = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let endPage = Math.min(totalPages, startPage + maxVisible - 1);
    
    if (endPage - startPage + 1 < maxVisible) {
        startPage = Math.max(1, endPage - maxVisible + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pages.push(`
            <button class="btn ${i === currentPage ? 'btn-primary' : 'btn-outline'}" 
                    style="padding: 4px 8px; font-size: 12px; margin: 0 2px;" 
                    onclick="changePage(${i})">${i}</button>
        `);
    }
    
    return pages.join('');
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) return;

    currentPage = page;
    loadTasks();
}

// 更新批量重试按钮显示状态
function updateBatchRetryButton(tasks) {
    const batchRetryBtn = document.getElementById('batchRetryBtn');

    // 检查是否有可重试的失败任务
    const retryableTasks = tasks.filter(task =>
        task.status === 'failed' &&
        (task.retryCount || 0) < (task.maxRetries || 3)
    );

    if (retryableTasks.length > 0) {
        batchRetryBtn.style.display = 'inline-block';
        batchRetryBtn.textContent = `批量重试失败任务 (${retryableTasks.length})`;
    } else {
        batchRetryBtn.style.display = 'none';
    }
}

// 批量重试失败任务
async function batchRetryFailedTasks() {
    try {
        // 获取当前页面的所有失败且可重试的任务
        const response = await crawlerAPI.getTasks({
            page: 1,
            limit: 1000, // 获取所有任务
            status: 'failed'
        });

        if (!response.success) {
            showMessage('获取失败任务列表失败', 'error');
            return;
        }

        const failedTasks = response.data.filter(task =>
            (task.retryCount || 0) < (task.maxRetries || 3)
        );

        if (failedTasks.length === 0) {
            showMessage('没有可重试的失败任务', 'info');
            return;
        }

        const confirmMessage = `确定要重试 ${failedTasks.length} 个失败任务吗？`;
        if (!window.confirm(confirmMessage)) {
            return;
        }

        showLoading();

        const taskIds = failedTasks.map(task => task.id);
        const retryResponse = await crawlerAPI.batchRetryTasks(taskIds);

        if (retryResponse.success) {
            const { successCount, errors } = retryResponse.data;
            let message = `成功重试 ${successCount} 个任务`;

            if (errors.length > 0) {
                message += `，${errors.length} 个任务重试失败`;
            }

            showMessage(message, successCount > 0 ? 'success' : 'warning');
            loadTasks();
            loadStats();
        } else {
            showMessage(retryResponse.message || '批量重试失败', 'error');
        }
    } catch (error) {
        console.error('Batch retry error:', error);
        showMessage(error.message || '批量重试失败', 'error');
    } finally {
        hideLoading();
    }
}

// 退出登录
function logout() {
    if (window.confirm('确定要退出登录吗？')) {
        stopAutoRefresh();
        removeToken();
        removeUser();
        redirect('/login.html');
    }
}

// 页面卸载时停止自动刷新
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});

// 全局函数，供HTML调用
window.startTask = startTask;
window.stopTask = stopTask;
window.resumeTask = resumeTask;
window.retryTask = retryTask;
window.deleteTask = deleteTask;
window.viewTaskDetail = viewTaskDetail;
window.viewTaskResults = viewTaskResults;
window.changePage = changePage;
window.showCreateTaskForm = showCreateTaskForm;
window.batchRetryFailedTasks = batchRetryFailedTasks;
