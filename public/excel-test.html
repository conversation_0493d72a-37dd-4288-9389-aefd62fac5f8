<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel文件生成服务测试</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .download-link {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background-color: #17a2b8;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .download-link:hover {
            background-color: #138496;
        }
        
        .status-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .status-info h4 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Excel文件生成服务测试</h1>
        
        <!-- 服务状态 -->
        <div class="status-info">
            <h4>服务状态</h4>
            <div id="serviceStatus">正在检查服务状态...</div>
            <button class="btn" onclick="checkServiceStatus()">刷新状态</button>
        </div>
        
        <!-- 生成示例文件 -->
        <div class="test-section">
            <h3>1. 生成示例Excel文件</h3>
            <p>生成包含示例数据的Excel文件，用于快速测试功能。</p>
            <button class="btn btn-success" onclick="generateSample()">生成示例文件</button>
            <div id="sampleResult" class="result"></div>
        </div>
        
        <!-- 生成自定义Excel文件 -->
        <div class="test-section">
            <h3>2. 生成自定义Excel文件</h3>
            <div class="form-group">
                <label for="fileName">文件名（可选）:</label>
                <input type="text" id="fileName" placeholder="例如：用户数据">
            </div>
            <div class="form-group">
                <label for="jsonData">JSON数据:</label>
                <textarea id="jsonData" placeholder='请输入JSON格式的数据，例如：
[
  {"姓名": "张三", "年龄": 25, "城市": "北京"},
  {"姓名": "李四", "年龄": 30, "城市": "上海"}
]'></textarea>
            </div>
            <button class="btn" onclick="generateCustomExcel()">生成Excel文件</button>
            <div id="customResult" class="result"></div>
        </div>
        
        <!-- 生成多工作表Excel文件 -->
        <div class="test-section">
            <h3>3. 生成多工作表Excel文件</h3>
            <div class="form-group">
                <label for="multiFileName">文件名（可选）:</label>
                <input type="text" id="multiFileName" placeholder="例如：综合报表">
            </div>
            <div class="form-group">
                <label for="sheetsData">工作表配置:</label>
                <textarea id="sheetsData" placeholder='请输入工作表配置，例如：
[
  {
    "name": "用户信息",
    "data": [
      {"ID": 1, "姓名": "张三", "部门": "技术部"},
      {"ID": 2, "姓名": "李四", "部门": "产品部"}
    ]
  },
  {
    "name": "销售数据",
    "data": [
      {"月份": "2024-01", "销售额": 100000},
      {"月份": "2024-02", "销售额": 120000}
    ]
  }
]'></textarea>
            </div>
            <button class="btn" onclick="generateMultiSheetExcel()">生成多工作表文件</button>
            <div id="multiResult" class="result"></div>
        </div>
        
        <!-- 文件管理 -->
        <div class="test-section">
            <h3>4. 文件管理</h3>
            <div class="form-group">
                <label for="checkFileName">检查文件名:</label>
                <input type="text" id="checkFileName" placeholder="输入要检查的文件名">
            </div>
            <button class="btn" onclick="checkFile()">检查文件</button>
            <button class="btn" onclick="cleanupFiles()">清理过期文件</button>
            <div id="fileManageResult" class="result"></div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = '/api/excel';
        
        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch(`${API_BASE}/status`);
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('serviceStatus').innerHTML = `
                        <strong>✅ 服务运行正常</strong><br>
                        导出目录: ${result.data.exportDirectory}<br>
                        文件数量: ${result.data.fileCount}<br>
                        总大小: ${result.data.totalSizeFormatted}<br>
                        文件保留时间: ${result.data.maxFileAge}
                    `;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                document.getElementById('serviceStatus').innerHTML = `
                    <strong>❌ 服务状态异常</strong><br>
                    错误: ${error.message}
                `;
            }
        }
        
        // 生成示例文件
        async function generateSample() {
            const resultDiv = document.getElementById('sampleResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '正在生成示例文件...';
            
            try {
                const response = await fetch(`${API_BASE}/generate-sample`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ 示例文件生成成功</strong><br>
                        文件名: ${result.data.fileName}<br>
                        文件大小: ${result.data.fileSizeFormatted}<br>
                        记录数量: ${result.data.recordCount}<br>
                        <a href="${result.data.downloadUrl}" class="download-link" target="_blank">下载文件</a>
                    `;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 生成失败: ${error.message}`;
            }
        }
        
        // 生成自定义Excel文件
        async function generateCustomExcel() {
            const fileName = document.getElementById('fileName').value;
            const jsonDataText = document.getElementById('jsonData').value;
            const resultDiv = document.getElementById('customResult');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '正在生成Excel文件...';
            
            try {
                if (!jsonDataText.trim()) {
                    throw new Error('请输入JSON数据');
                }
                
                let jsonData;
                try {
                    jsonData = JSON.parse(jsonDataText);
                } catch (e) {
                    throw new Error('JSON数据格式错误: ' + e.message);
                }
                
                const requestBody = {
                    data: jsonData,
                    formatting: { headerStyle: true }
                };
                
                if (fileName.trim()) {
                    requestBody.fileName = fileName.trim();
                }
                
                const response = await fetch(`${API_BASE}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Excel文件生成成功</strong><br>
                        文件名: ${result.data.fileName}<br>
                        文件大小: ${result.data.fileSizeFormatted}<br>
                        <a href="${result.data.downloadUrl}" class="download-link" target="_blank">下载文件</a>
                    `;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 生成失败: ${error.message}`;
            }
        }
        
        // 生成多工作表Excel文件
        async function generateMultiSheetExcel() {
            const fileName = document.getElementById('multiFileName').value;
            const sheetsDataText = document.getElementById('sheetsData').value;
            const resultDiv = document.getElementById('multiResult');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '正在生成多工作表Excel文件...';
            
            try {
                if (!sheetsDataText.trim()) {
                    throw new Error('请输入工作表配置数据');
                }
                
                let sheetsData;
                try {
                    sheetsData = JSON.parse(sheetsDataText);
                } catch (e) {
                    throw new Error('工作表配置JSON格式错误: ' + e.message);
                }
                
                const requestBody = {
                    sheets: sheetsData,
                    formatting: { headerStyle: true }
                };
                
                if (fileName.trim()) {
                    requestBody.fileName = fileName.trim();
                }
                
                const response = await fetch(`${API_BASE}/generate-multi`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ 多工作表Excel文件生成成功</strong><br>
                        文件名: ${result.data.fileName}<br>
                        文件大小: ${result.data.fileSizeFormatted}<br>
                        工作表数量: ${result.data.sheetsCount}<br>
                        <a href="${result.data.downloadUrl}" class="download-link" target="_blank">下载文件</a>
                    `;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 生成失败: ${error.message}`;
            }
        }
        
        // 检查文件
        async function checkFile() {
            const fileName = document.getElementById('checkFileName').value;
            const resultDiv = document.getElementById('fileManageResult');
            
            if (!fileName.trim()) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ 请输入文件名';
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '正在检查文件...';
            
            try {
                const response = await fetch(`${API_BASE}/check/${encodeURIComponent(fileName)}`);
                const result = await response.json();
                
                if (result.success) {
                    if (result.data.exists) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <strong>✅ 文件存在</strong><br>
                            文件名: ${result.data.fileName}<br>
                            <a href="${result.data.downloadUrl}" class="download-link" target="_blank">下载文件</a>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `❌ 文件不存在: ${result.data.fileName}`;
                    }
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 检查失败: ${error.message}`;
            }
        }
        
        // 清理过期文件
        async function cleanupFiles() {
            const resultDiv = document.getElementById('fileManageResult');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '正在清理过期文件...';
            
            try {
                const response = await fetch(`${API_BASE}/cleanup`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✅ 过期文件清理完成';
                    // 刷新服务状态
                    checkServiceStatus();
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 清理失败: ${error.message}`;
            }
        }
        
        // 页面加载时检查服务状态
        document.addEventListener('DOMContentLoaded', function() {
            checkServiceStatus();
            
            // 设置默认的JSON数据示例
            document.getElementById('jsonData').value = JSON.stringify([
                {"姓名": "张三", "年龄": 25, "城市": "北京", "职业": "工程师"},
                {"姓名": "李四", "年龄": 30, "城市": "上海", "职业": "设计师"},
                {"姓名": "王五", "年龄": 28, "城市": "广州", "职业": "产品经理"}
            ], null, 2);
            
            // 设置默认的多工作表数据示例
            document.getElementById('sheetsData').value = JSON.stringify([
                {
                    "name": "用户信息",
                    "data": [
                        {"ID": 1, "姓名": "张三", "部门": "技术部", "薪资": 15000},
                        {"ID": 2, "姓名": "李四", "部门": "产品部", "薪资": 12000}
                    ]
                },
                {
                    "name": "销售数据",
                    "data": [
                        {"月份": "2024-01", "销售额": 100000, "订单数": 150},
                        {"月份": "2024-02", "销售额": 120000, "订单数": 180}
                    ]
                }
            ], null, 2);
        });
    </script>
</body>
</html>
