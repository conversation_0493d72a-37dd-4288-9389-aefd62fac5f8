<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cookie管理 - 达人信息管理系统</title>
    <link rel="stylesheet" href="/css/style.css">
    <style>
        .cookie-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .cookie-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }

        .cookie-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-card.success { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }
        .stat-card.warning { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
        .stat-card.danger { background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); }

        .cookie-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .cookie-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .cookie-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .cookie-table th,
        .cookie-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .cookie-table th {
            background: #f5f5f5;
            font-weight: 600;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-active { background: #e8f5e8; color: #2e7d32; }
        .status-expired { background: #ffebee; color: #c62828; }
        .status-banned { background: #fce4ec; color: #ad1457; }

        .priority-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            background: #e3f2fd;
            color: #1976d2;
        }

        .cookie-actions {
            display: flex;
            gap: 5px;
        }

        .btn-icon {
            padding: 5px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .cookie-preview {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }

        .import-area {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            margin: 15px 0;
        }

        .import-area.dragover {
            border-color: #007bff;
            background: #f8f9fa;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state img {
            width: 120px;
            opacity: 0.5;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .cookie-container {
                padding: 10px;
            }
            
            .cookie-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .cookie-controls {
                justify-content: center;
            }
            
            .cookie-table {
                overflow-x: auto;
            }
            
            .modal-content {
                margin: 10% auto;
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="cookie-container">
        <!-- 页面头部 -->
        <div class="cookie-header">
            <div>
                <h1>🍪 Cookie管理</h1>
                <p>管理爬虫账户Cookie，支持多账户轮换和自动失效检测</p>
            </div>
            <div>
                <button class="btn btn-primary" onclick="showAddModal()">
                    ➕ 添加Cookie
                </button>
                <button class="btn btn-secondary" onclick="showImportModal()">
                    📥 批量导入
                </button>
                <a href="/crawler.html" class="btn btn-outline">
                    🔙 返回爬虫管理
                </a>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="cookie-stats" id="cookieStats">
            <div class="stat-card">
                <h3 id="totalCount">0</h3>
                <p>总Cookie数</p>
            </div>
            <div class="stat-card success">
                <h3 id="activeCount">0</h3>
                <p>可用Cookie</p>
            </div>
            <div class="stat-card warning">
                <h3 id="expiredCount">0</h3>
                <p>已过期</p>
            </div>
            <div class="stat-card danger">
                <h3 id="bannedCount">0</h3>
                <p>已封禁</p>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="cookie-controls">
            <select id="platformFilter" onchange="loadCookies()">
                <option value="">所有平台</option>
                <option value="juxingtu">巨量星图</option>
                <option value="xiaohongshu">小红书</option>
            </select>
            
            <select id="statusFilter" onchange="loadCookies()">
                <option value="">所有状态</option>
                <option value="active">可用</option>
                <option value="expired">已过期</option>
                <option value="banned">已封禁</option>
            </select>
            
            <input type="text" id="searchInput" placeholder="搜索账户名..." 
                   onkeyup="debounceSearch()" style="flex: 1; min-width: 200px;">
            
            <button class="btn btn-outline" onclick="loadCookies()">
                🔄 刷新
            </button>
            
            <button class="btn btn-outline" onclick="exportCookies()">
                📤 导出
            </button>
        </div>

        <!-- Cookie列表 -->
        <div class="cookie-table">
            <table>
                <thead>
                    <tr>
                        <th>账户名</th>
                        <th>平台</th>
                        <th>状态</th>
                        <th>优先级</th>
                        <th>使用次数</th>
                        <th>最后使用</th>
                        <th>创建者</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="cookieTableBody">
                    <!-- 动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div class="pagination" id="pagination">
            <!-- 动态生成 -->
        </div>
    </div>

    <!-- 添加/编辑Cookie模态框 -->
    <div id="cookieModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加Cookie</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <form id="cookieForm">
                <div class="form-group">
                    <label for="accountName">账户名 *</label>
                    <input type="text" id="accountName" required>
                </div>
                
                <div class="form-group">
                    <label for="platform">平台 *</label>
                    <select id="platform" required>
                        <option value="">请选择平台</option>
                        <option value="juxingtu">巨量星图</option>
                        <option value="xiaohongshu">小红书</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="cookieString">Cookie数据 *</label>
                    <textarea id="cookieString" placeholder="粘贴完整的Cookie字符串..." required></textarea>
                    <small>从浏览器开发者工具中复制完整的Cookie字符串</small>
                </div>
                
                <div class="form-group">
                    <label for="userAgent">User-Agent</label>
                    <input type="text" id="userAgent" placeholder="可选，留空将使用默认值">
                </div>
                
                <div class="form-group">
                    <label for="priority">优先级</label>
                    <input type="number" id="priority" value="1" min="1" max="10">
                    <small>数字越大优先级越高</small>
                </div>
                
                <div class="form-group">
                    <label for="maxDailyUse">每日最大使用次数</label>
                    <input type="number" id="maxDailyUse" value="100" min="1">
                </div>
                
                <div class="form-group">
                    <label for="notes">备注</label>
                    <textarea id="notes" placeholder="可选的备注信息..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="importModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量导入Cookie</h3>
                <span class="close" onclick="closeImportModal()">&times;</span>
            </div>
            
            <div class="import-area" id="importArea">
                <p>📁 拖拽JSON文件到此处，或点击选择文件</p>
                <input type="file" id="fileInput" accept=".json" style="display: none;">
                <button type="button" class="btn btn-outline" onclick="document.getElementById('fileInput').click()">
                    选择文件
                </button>
            </div>
            
            <div class="form-group">
                <label>或直接粘贴JSON数据：</label>
                <textarea id="importData" placeholder='[{"accountName":"账户1","platform":"juxingtu","cookieString":"cookie数据..."}]' rows="8"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeImportModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="importCookies()">导入</button>
            </div>
        </div>
    </div>

    <script src="/js/utils.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/cookies.js"></script>
</body>
</html>
