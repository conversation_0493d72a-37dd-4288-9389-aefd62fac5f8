<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP服务测试页面</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .download-link {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background-color: #17a2b8;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .download-link:hover {
            background-color: #138496;
        }
        
        .status-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .status-info h4 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .task-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .task-info h5 {
            margin-top: 0;
            color: #856404;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
        
        .json-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MCP (Model Context Protocol) 服务测试</h1>
        
        <!-- 服务状态 -->
        <div class="status-info">
            <h4>MCP服务状态</h4>
            <div id="serviceStatus">正在检查服务状态...</div>
            <button class="btn" onclick="checkServiceStatus()">刷新状态</button>
            <button class="btn" onclick="getToolsList()">获取工具列表</button>
        </div>
        
        <!-- 当前任务信息 -->
        <div id="currentTaskInfo" class="task-info" style="display: none;">
            <h5>当前任务信息</h5>
            <div id="taskDetails"></div>
            <button class="btn btn-warning" onclick="refreshTaskStatus()">刷新状态</button>
            <button class="btn" onclick="getTaskResults()">获取结果</button>
            <button class="btn btn-success" onclick="exportToExcel()">导出Excel</button>
        </div>
        
        <!-- 创建爬虫任务 -->
        <div class="test-section">
            <h3>1. 创建爬虫任务</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="taskName">任务名称:</label>
                    <input type="text" id="taskName" value="MCP测试任务" placeholder="输入任务名称">
                </div>
                <div class="form-group">
                    <label for="keywords">搜索关键词:</label>
                    <input type="text" id="keywords" value="美妆博主" placeholder="输入搜索关键词">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="platform">目标平台:</label>
                    <select id="platform">
                        <option value="xiaohongshu">小红书</option>
                        <option value="juxingtu">巨量星图</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="maxPages">最大页数:</label>
                    <input type="number" id="maxPages" value="3" min="1" max="20">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="pageSize">每页数量:</label>
                    <input type="number" id="pageSize" value="20" min="10" max="50">
                </div>
                <div class="form-group">
                    <label for="priority">任务优先级:</label>
                    <input type="number" id="priority" value="1" min="0" max="10">
                </div>
            </div>
            <button class="btn btn-success" onclick="createCrawlerTask()">创建并启动任务</button>
            <div id="createTaskResult" class="result"></div>
        </div>
        
        <!-- 任务管理 -->
        <div class="test-section">
            <h3>2. 任务管理</h3>
            <div class="form-group">
                <label for="taskId">任务ID:</label>
                <input type="number" id="taskId" placeholder="输入要查询的任务ID">
            </div>
            <button class="btn" onclick="getTaskStatusById()">获取任务状态</button>
            <button class="btn" onclick="getTaskResultsById()">获取任务结果</button>
            <button class="btn btn-success" onclick="exportTaskById()">导出Excel</button>
            <div id="taskManageResult" class="result"></div>
        </div>
        
        <!-- MCP工具执行 -->
        <div class="test-section">
            <h3>3. MCP工具执行</h3>
            <div class="form-group">
                <label for="mcpTool">选择工具:</label>
                <select id="mcpTool" onchange="updateMCPArguments()">
                    <option value="">请选择工具</option>
                    <option value="create_crawler_task">create_crawler_task</option>
                    <option value="get_task_status">get_task_status</option>
                    <option value="get_task_results">get_task_results</option>
                    <option value="export_results_to_excel">export_results_to_excel</option>
                </select>
            </div>
            <div class="form-group">
                <label for="mcpArguments">工具参数 (JSON格式):</label>
                <textarea id="mcpArguments" rows="8" placeholder="输入JSON格式的工具参数"></textarea>
            </div>
            <button class="btn" onclick="executeMCPTool()">执行MCP工具</button>
            <div id="mcpResult" class="result"></div>
        </div>
        
        <!-- 结果显示 -->
        <div class="test-section">
            <h3>4. 详细结果显示</h3>
            <div id="detailedResults" class="json-display">暂无数据</div>
        </div>
    </div>

    <script>
        // API基础URL
        const MCP_API_BASE = '/api/mcp';
        
        // 全局变量
        let currentTaskId = null;
        let statusCheckInterval = null;
        
        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch(`${MCP_API_BASE}/status`);
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('serviceStatus').innerHTML = `
                        <strong>✅ 服务运行正常</strong><br>
                        版本: ${result.data.version}<br>
                        初始化状态: ${result.data.initialized ? '已初始化' : '未初始化'}<br>
                        可用工具数量: ${result.data.tools.count}<br>
                        工具列表: ${result.data.tools.available.join(', ')}
                    `;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                document.getElementById('serviceStatus').innerHTML = `
                    <strong>❌ 服务状态异常</strong><br>
                    错误: ${error.message}
                `;
            }
        }
        
        // 获取工具列表
        async function getToolsList() {
            try {
                const response = await fetch(`${MCP_API_BASE}/tools`);
                const result = await response.json();
                
                if (result.success) {
                    showDetailedResults('工具列表', result.data);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('获取工具列表失败: ' + error.message);
            }
        }
        
        // 创建爬虫任务
        async function createCrawlerTask() {
            const resultDiv = document.getElementById('createTaskResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在创建爬虫任务...';
            
            const taskData = {
                taskName: document.getElementById('taskName').value,
                keywords: document.getElementById('keywords').value,
                platform: document.getElementById('platform').value,
                maxPages: parseInt(document.getElementById('maxPages').value),
                config: {
                    pageSize: parseInt(document.getElementById('pageSize').value),
                    delay: { min: 1000, max: 2000 }
                },
                priority: parseInt(document.getElementById('priority').value)
            };
            
            try {
                const response = await fetch(`${MCP_API_BASE}/crawler/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(taskData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentTaskId = result.data.taskId;
                    document.getElementById('taskId').value = currentTaskId;
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ 爬虫任务创建成功</strong><br>
                        任务ID: ${result.data.taskId}<br>
                        任务名称: ${result.data.taskName}<br>
                        平台: ${result.data.platform}<br>
                        关键词: ${result.data.keywords}<br>
                        状态: ${result.data.status}<br>
                        预估时长: ${result.data.estimatedDuration}
                    `;
                    
                    // 显示当前任务信息
                    showCurrentTaskInfo(result.data);
                    
                    // 开始定期检查任务状态
                    startStatusCheck();
                    
                    showDetailedResults('创建任务结果', result.data);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 创建任务失败: ${error.message}`;
            }
        }
        
        // 显示当前任务信息
        function showCurrentTaskInfo(taskData) {
            const taskInfoDiv = document.getElementById('currentTaskInfo');
            const taskDetailsDiv = document.getElementById('taskDetails');
            
            taskDetailsDiv.innerHTML = `
                <strong>任务ID:</strong> ${taskData.taskId}<br>
                <strong>任务名称:</strong> ${taskData.taskName}<br>
                <strong>平台:</strong> ${taskData.platform}<br>
                <strong>关键词:</strong> ${taskData.keywords}<br>
                <strong>状态:</strong> <span id="currentStatus">${taskData.status}</span><br>
                <strong>进度:</strong> <span id="currentProgress">0</span>%<br>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
            `;
            
            taskInfoDiv.style.display = 'block';
        }
        
        // 开始状态检查
        function startStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
            
            statusCheckInterval = setInterval(async () => {
                if (currentTaskId) {
                    await refreshTaskStatus();
                }
            }, 5000); // 每5秒检查一次
        }
        
        // 刷新任务状态
        async function refreshTaskStatus() {
            if (!currentTaskId) return;
            
            try {
                const response = await fetch(`${MCP_API_BASE}/crawler/tasks/${currentTaskId}/status`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    document.getElementById('currentStatus').textContent = data.status;
                    document.getElementById('currentProgress').textContent = data.progress;
                    document.getElementById('progressFill').style.width = data.progress + '%';
                    
                    // 如果任务完成，停止状态检查
                    if (data.status === 'completed' || data.status === 'failed') {
                        clearInterval(statusCheckInterval);
                        statusCheckInterval = null;
                    }
                }
            } catch (error) {
                console.error('刷新任务状态失败:', error);
            }
        }
        
        // 获取任务结果
        async function getTaskResults() {
            if (!currentTaskId) {
                showError('请先创建任务或输入任务ID');
                return;
            }
            
            try {
                const response = await fetch(`${MCP_API_BASE}/crawler/tasks/${currentTaskId}/results?page=1&limit=20`);
                const result = await response.json();
                
                if (result.success) {
                    showDetailedResults('任务结果', result.data);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('获取任务结果失败: ' + error.message);
            }
        }
        
        // 导出Excel
        async function exportToExcel() {
            if (!currentTaskId) {
                showError('请先创建任务或输入任务ID');
                return;
            }
            
            try {
                const response = await fetch(`${MCP_API_BASE}/crawler/tasks/${currentTaskId}/export`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fileName: 'MCP测试导出',
                        includeFields: ['nickname', 'platform', 'followersCount', 'city', 'status']
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess(`Excel导出成功！<br>
                        文件名: ${result.data.fileName}<br>
                        记录数量: ${result.data.recordCount}<br>
                        <a href="${result.data.downloadUrl}" class="download-link" target="_blank">下载文件</a>
                    `);
                    showDetailedResults('导出结果', result.data);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('导出Excel失败: ' + error.message);
            }
        }
        
        // 根据ID获取任务状态
        async function getTaskStatusById() {
            const taskId = document.getElementById('taskId').value;
            if (!taskId) {
                showError('请输入任务ID');
                return;
            }
            
            try {
                const response = await fetch(`${MCP_API_BASE}/crawler/tasks/${taskId}/status`);
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('任务状态获取成功', 'taskManageResult');
                    showDetailedResults('任务状态', result.data);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('获取任务状态失败: ' + error.message, 'taskManageResult');
            }
        }
        
        // 根据ID获取任务结果
        async function getTaskResultsById() {
            const taskId = document.getElementById('taskId').value;
            if (!taskId) {
                showError('请输入任务ID');
                return;
            }
            
            try {
                const response = await fetch(`${MCP_API_BASE}/crawler/tasks/${taskId}/results?page=1&limit=20`);
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('任务结果获取成功', 'taskManageResult');
                    showDetailedResults('任务结果', result.data);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('获取任务结果失败: ' + error.message, 'taskManageResult');
            }
        }
        
        // 根据ID导出任务
        async function exportTaskById() {
            const taskId = document.getElementById('taskId').value;
            if (!taskId) {
                showError('请输入任务ID');
                return;
            }
            
            try {
                const response = await fetch(`${MCP_API_BASE}/crawler/tasks/${taskId}/export`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fileName: `任务${taskId}_导出结果`
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess(`Excel导出成功！<br>
                        <a href="${result.data.downloadUrl}" class="download-link" target="_blank">下载文件</a>
                    `, 'taskManageResult');
                    showDetailedResults('导出结果', result.data);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('导出Excel失败: ' + error.message, 'taskManageResult');
            }
        }
        
        // 更新MCP参数
        function updateMCPArguments() {
            const tool = document.getElementById('mcpTool').value;
            const argumentsTextarea = document.getElementById('mcpArguments');
            
            const examples = {
                'create_crawler_task': {
                    keywords: '美妆博主',
                    platform: 'xiaohongshu',
                    taskName: 'MCP工具测试任务',
                    maxPages: 2,
                    config: {
                        pageSize: 20,
                        delay: { min: 1000, max: 2000 }
                    },
                    priority: 1
                },
                'get_task_status': {
                    taskId: currentTaskId || 123
                },
                'get_task_results': {
                    taskId: currentTaskId || 123,
                    page: 1,
                    limit: 20
                },
                'export_results_to_excel': {
                    taskId: currentTaskId || 123,
                    fileName: 'MCP工具导出',
                    includeFields: ['nickname', 'platform', 'followersCount', 'city']
                }
            };
            
            if (examples[tool]) {
                argumentsTextarea.value = JSON.stringify(examples[tool], null, 2);
            } else {
                argumentsTextarea.value = '';
            }
        }
        
        // 执行MCP工具
        async function executeMCPTool() {
            const tool = document.getElementById('mcpTool').value;
            const argumentsText = document.getElementById('mcpArguments').value;
            const resultDiv = document.getElementById('mcpResult');
            
            if (!tool) {
                showError('请选择工具', 'mcpResult');
                return;
            }
            
            if (!argumentsText.trim()) {
                showError('请输入工具参数', 'mcpResult');
                return;
            }
            
            let args;
            try {
                args = JSON.parse(argumentsText);
            } catch (e) {
                showError('参数JSON格式错误: ' + e.message, 'mcpResult');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在执行MCP工具...';
            
            try {
                const response = await fetch(`${MCP_API_BASE}/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool: tool,
                        arguments: args
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ MCP工具执行成功<br>工具: ${tool}`;
                    showDetailedResults('MCP工具执行结果', result.data);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ MCP工具执行失败: ${error.message}`;
            }
        }
        
        // 显示详细结果
        function showDetailedResults(title, data) {
            const detailedDiv = document.getElementById('detailedResults');
            detailedDiv.innerHTML = `=== ${title} ===\n\n${JSON.stringify(data, null, 2)}`;
        }
        
        // 显示成功消息
        function showSuccess(message, elementId = null) {
            const element = elementId ? document.getElementById(elementId) : document.getElementById('createTaskResult');
            element.style.display = 'block';
            element.className = 'result success';
            element.innerHTML = '✅ ' + message;
        }
        
        // 显示错误消息
        function showError(message, elementId = null) {
            const element = elementId ? document.getElementById(elementId) : document.getElementById('createTaskResult');
            element.style.display = 'block';
            element.className = 'result error';
            element.innerHTML = '❌ ' + message;
        }
        
        // 页面加载时检查服务状态
        document.addEventListener('DOMContentLoaded', function() {
            checkServiceStatus();
        });
    </script>
</body>
</html>
