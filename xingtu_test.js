var axios = require("axios");
var xlsx = require("xlsx"); // 引入 xlsx 库
var fs = require("fs"); // 引入文件系统模块

// 查询参数配置
var data = {
  "scene_param": {
      "platform_source": 1,
      "search_scene": 1,
      "display_scene": 1,
      "task_category": 1,
      "marketing_target": 1,
      "first_industry_id": 0
  },
  "page_param": {
      "page": "1",
      "limit": "20"
  },
  "sort_param": {
      "sort_field": {
          "field_name": "score"
      },
      "sort_type": 2
  },
  "attribute_filter": [
      {
          "field": {
              "field_name": "price_by_video_type__ge",
              "rel_id": "2"
          },
          "field_value": "0"
      }
  ],
  "search_param": {
      "seach_type": 3,
      "keyword": "送女生礼物",
      "time_range_days": 180,
      "is_new_content_query": true
  }
}

// 防爬虫配置
const ANTI_CRAWLER_CONFIG = {
  MIN_DELAY: 1000,        // 最小延迟时间（毫秒）
  MAX_DELAY: 3000,        // 最大延迟时间（毫秒）
  MAX_RETRIES: 3,         // 最大重试次数
  RETRY_DELAY: 2000,      // 重试延迟时间（毫秒）
  REQUEST_TIMEOUT: 10000  // 请求超时时间（毫秒）
};

// 用户代理池，模拟不同浏览器
const USER_AGENTS = [
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
];

/**
 * 生成随机延迟时间
 * @param {number} min 最小延迟时间（毫秒）
 * @param {number} max 最大延迟时间（毫秒）
 * @returns {number} 随机延迟时间
 */
function getRandomDelay(min = ANTI_CRAWLER_CONFIG.MIN_DELAY, max = ANTI_CRAWLER_CONFIG.MAX_DELAY) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 随机获取用户代理
 * @returns {string} 随机用户代理字符串
 */
function getRandomUserAgent() {
  return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}

/**
 * 延迟执行函数
 * @param {number} ms 延迟时间（毫秒）
 * @returns {Promise} Promise对象
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 创建请求配置，包含防爬虫策略
 * @param {string} method HTTP方法
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @returns {Object} axios请求配置对象
 */
function createConfig(method, url, data = null) {
  return {
    method: method,
    url: url,
    headers: {
      "Agw-Js-Conv": "str",
      Cookie: "sessionid=6749d88295a332b819b4a37ef267702e",
      "User-Agent": getRandomUserAgent(), // 使用随机用户代理
      "Content-Type": "application/json",
      Accept: "*/*",
      Host: "www.xingtu.cn",
      Connection: "keep-alive",
      // 添加更多真实浏览器头部
      "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
      "Accept-Encoding": "gzip, deflate, br",
      "Cache-Control": "no-cache",
      "Pragma": "no-cache",
      "Sec-Fetch-Dest": "empty",
      "Sec-Fetch-Mode": "cors",
      "Sec-Fetch-Site": "same-origin"
    },
    data: data,
    timeout: ANTI_CRAWLER_CONFIG.REQUEST_TIMEOUT, // 设置请求超时
    validateStatus: function (status) {
      return status >= 200 && status < 300; // 只接受2xx状态码
    }
  };
}

/**
 * 带重试机制的请求函数
 * @param {string} url 请求URL
 * @param {Object} config axios配置对象
 * @param {number} retryCount 当前重试次数
 * @returns {Promise} 请求结果Promise
 */
async function fetchWithRetry(url, config, retryCount = 0) {
  try {
    console.log(`[请求] 发起请求: ${url} (重试次数: ${retryCount})`);

    // 添加随机延迟，模拟真实用户行为
    const delayTime = getRandomDelay();
    console.log(`[延迟] 等待 ${delayTime}ms 后发起请求...`);
    await delay(delayTime);

    const response = await axios(config);
    console.log(`[成功] 请求成功: ${url}`);
    return response.data;

  } catch (error) {
    console.error(`[错误] 请求失败: ${url}, 错误信息:`, error.message);

    // 如果还有重试次数，则进行重试
    if (retryCount < ANTI_CRAWLER_CONFIG.MAX_RETRIES) {
      const retryDelay = ANTI_CRAWLER_CONFIG.RETRY_DELAY * (retryCount + 1); // 递增延迟
      console.log(`[重试] ${retryDelay}ms 后进行第 ${retryCount + 1} 次重试...`);
      await delay(retryDelay);
      return fetchWithRetry(url, config, retryCount + 1);
    }

    // 重试次数用完，抛出错误
    console.error(`[失败] 请求最终失败: ${url}, 已重试 ${ANTI_CRAWLER_CONFIG.MAX_RETRIES} 次`);
    throw error;
  }
}

// 获取达人列表并写入 Excel
let page = 1;
let authorDetails = [];
const maxPages = 5; // 设置最大页数

/**
 * 获取达人列表的主函数（异步版本）
 */
async function getAuthorList() {
  try {
    if (page > maxPages) {
      console.log(`[完成] 已达到最大页数 ${maxPages}，停止获取数据。`);
      await writeToExcel(authorDetails);
      return;
    }

    console.log(`[开始] 获取第 ${page} 页的达人列表...`);

    const url = "https://www.xingtu.cn/gw/api/gsearch/search_for_author_square";
    const requestData = { ...data };
    requestData.page_param.page = page.toString();

    const config = createConfig("post", url, requestData);

    const response = await fetchWithRetry(url, config);

    if (!response || !response.authors) {
      console.error(`[错误] 第 ${page} 页数据格式异常`);
      return;
    }

    const { pagination, authors } = response;
    console.log(`[信息] 第 ${page} 页获取成功，本页达人数: ${authors.length}, 总数: ${pagination.total_count}`);

    // 批量处理达人详细信息，添加并发控制
    const batchSize = 3; // 每批处理3个达人，避免请求过于频繁
    for (let i = 0; i < authors.length; i += batchSize) {
      const batch = authors.slice(i, i + batchSize);
      console.log(`[处理] 正在处理第 ${Math.floor(i / batchSize) + 1} 批达人 (${i + 1}-${Math.min(i + batchSize, authors.length)}/${authors.length})`);

      const batchPromises = batch.map(async (author) => {
        try {
          console.log(`[获取] 正在获取达人 ${author.star_id} 的详细信息...`);
          const detail = await getAuthorDetail(author.star_id);

          if (detail && detail.昵称 && detail.昵称 !== "暂无") {
            authorDetails.push(detail);
            console.log(`[成功] 达人 ${author.star_id} (${detail.昵称}) 信息获取成功`);
            return detail;
          } else {
            console.log(`[跳过] 达人 ${author.star_id} 信息不完整，跳过该达人`);
            return null;
          }
        } catch (error) {
          console.error(`[错误] 获取达人 ${author.star_id} 信息失败:`, error.message);
          return null;
        }
      });

      // 等待当前批次完成
      await Promise.all(batchPromises);

      // 批次间添加延迟
      if (i + batchSize < authors.length) {
        const batchDelay = getRandomDelay(2000, 4000);
        console.log(`[延迟] 批次间等待 ${batchDelay}ms...`);
        await delay(batchDelay);
      }
    }

    console.log(`[完成] 第 ${page} 页处理完成，已获取 ${authorDetails.length} 个有效达人信息`);

    // 判断是否继续请求下一页
    if (pagination.has_more && page < maxPages) {
      page = page + 1;

      // 页面间添加较长延迟
      const pageDelay = getRandomDelay(3000, 5000);
      console.log(`[延迟] 页面间等待 ${pageDelay}ms 后获取下一页...`);
      await delay(pageDelay);

      await getAuthorList(); // 递归调用获取下一页
    } else {
      console.log(`[完成] 所有页面处理完成，共获取 ${authorDetails.length} 个达人信息，开始写入 Excel 文件...`);
      await writeToExcel(authorDetails);
    }

  } catch (error) {
    console.error(`[错误] 获取第 ${page} 页达人列表时发生错误:`, error.message);
    console.log(`[备份] 当前已获取 ${authorDetails.length} 个达人信息，尝试写入 Excel 文件...`);

    if (authorDetails.length > 0) {
      await writeToExcel(authorDetails);
    }
  }
}

/**
 * 获取达人详细信息（包含视频数据）
 * @param {string} authorId 达人ID
 * @returns {Promise<Object>} 达人详细信息对象
 */
async function getAuthorDetail(authorId) {
  try {
    console.log(`[开始] 获取达人 ${authorId} 的详细信息...`);

    // 获取达人基本信息
    const url = `https://www.xingtu.cn/gw/api/gauthor/author_get_business_card_info?o_author_id=${authorId}`;
    const config = createConfig("get", url);

    const response = await fetchWithRetry(url, config);

    if (!response || !response.card_info) {
      console.warn(`[警告] 达人 ${authorId} 基本信息获取失败`);
      return null;
    }

    const card_info = response.card_info;
    console.log(`[成功] 达人 ${authorId} 基本信息获取成功: ${card_info.nick_name || '无昵称'}`);

    // 获取达人视频信息
    console.log(`[开始] 获取达人 ${authorId} 的视频信息...`);
    const videoInfo = await getLast10VideoInfo(authorId);

    // 构建完整的达人信息对象
    const authorDetail = {
      id: authorId,
      昵称: card_info?.nick_name || "暂无",
      城市: card_info?.city || "暂无",
      粉丝数: card_info?.follower || "暂无",
      抖音号: card_info?.unique_id || "暂无",
      微信号: card_info?.wechat || "暂无",
      手机号: card_info?.phone || "暂无",
      // 视频相关统计数据
      视频数量: videoInfo.videoCount || 0,
      平均播放量: videoInfo.averagePlay || 0,
      总播放量: videoInfo.totalPlay || 0,
      总点赞数: videoInfo.totalLike || 0,
      总评论数: videoInfo.totalComment || 0,
      总分享数: videoInfo.totalShare || 0
    };

    // 如果需要详细的视频列表，可以添加到对象中
    if (videoInfo.videos && videoInfo.videos.length > 0) {
      // 将前3个视频的详细信息添加到达人信息中
      videoInfo.videos.slice(0, 10).forEach((video, index) => {
        const videoPrefix = `视频${index + 1}`;
        authorDetail[`${videoPrefix}_标题`] = video.title;
        authorDetail[`${videoPrefix}_播放量`] = video.play;
        authorDetail[`${videoPrefix}_点赞数`] = video.like;
        authorDetail[`${videoPrefix}_评论数`] = video.comment;
        authorDetail[`${videoPrefix}_发布时间`] = video.item_date;
        authorDetail[`${videoPrefix}_视频ID`] = video.video_id;
      });
    }

    console.log(`[完成] 达人 ${authorId} 完整信息获取成功`);
    return authorDetail;

  } catch (error) {
    console.error(`[错误] 获取达人 ${authorId} 的详细信息时发生错误:`, error.message);
    return null; // 返回null而不是抛出错误，避免整个流程中断
  }
}

/**
 * 获取达人最近10个视频的详细信息
 * @param {string} authorId 达人ID
 * @returns {Promise<Object>} 包含视频详情的对象
 */
async function getLast10VideoInfo(authorId) {
  try {
      console.log(`[开始] 获取作者 ${authorId} 的最近10个视频详细信息`);

      const url = `https://www.xingtu.cn/gw/api/author/get_author_show_items_v2?o_author_id=${authorId}&platform_channel=1&platform_source=1&limit=15&only_assign=true&flow_type=0`;
      const config = createConfig("get", url);

      const data = await fetchWithRetry(url, config);

      // 检查返回数据的有效性
      if (!data || (!data.latest_item_info && !data.latest_star_item_info)) {
          console.warn(`[警告] 作者 ${authorId} 没有视频数据`);
          return {
              id: authorId,
              videoCount: 0,
              videos: [],
              averagePlay: 0,
              totalPlay: 0,
              totalLike: 0,
              totalComment: 0,
              totalShare: 0
          };
      }

      // 合并两个数组的视频信息
      const latest_item_info = data.latest_item_info || [];
      const latest_star_item_info = data.latest_star_item_info || [];
      const combinedInfo = latest_item_info.concat(latest_star_item_info);

      console.log(`[信息] 作者 ${authorId} 共获取到 ${combinedInfo.length} 个视频`);

      // 按照发布时间排序（最新的在前）
      combinedInfo.sort((a, b) => new Date(b.item_date) - new Date(a.item_date));

      // 取最新的10个视频
      const recentVideos = combinedInfo.slice(0, 10);

      // 处理视频详情信息
      const videoDetails = recentVideos.map((video, index) => {
          // 格式化播放量（去除单位，转换为数字）
          const playCount = formatNumberFromString(video.play || '0');
          const likeCount = formatNumberFromString(video.like || '0');
          const commentCount = formatNumberFromString(video.comment || '0');
          const shareCount = formatNumberFromString(video.share || '0');

          console.log(`[视频${index + 1}] 标题: ${video.title || '无标题'}, 播放量: ${playCount}`);

          return {
              title: video.title || '无标题',                    // 视频标题
              play: playCount,                                   // 播放量（数字）
              like: likeCount,                                   // 点赞数（数字）
              comment: commentCount,                             // 评论数（数字）
              share: shareCount,                                 // 分享数（数字）
              item_date: video.item_date || '',                 // 发布时间
              url: video.url || '',                             // 视频地址
              video_id: video.item_id || '',                    // 视频ID
              cover_url: video.cover || '',                     // 视频封面
              duration: video.duration || 0                     // 视频时长
          };
      });

      // 计算统计数据
      const totalPlay = videoDetails.reduce((sum, video) => sum + video.play, 0);
      const totalLike = videoDetails.reduce((sum, video) => sum + video.like, 0);
      const totalComment = videoDetails.reduce((sum, video) => sum + video.comment, 0);
      const totalShare = videoDetails.reduce((sum, video) => sum + video.share, 0);
      const averagePlay = videoDetails.length > 0 ? Math.round(totalPlay / videoDetails.length) : 0;

      console.log(`[完成] 作者 ${authorId} 最近10个视频信息获取成功，平均播放量: ${averagePlay}`);

      return {
          id: authorId,
          videoCount: videoDetails.length,
          videos: videoDetails,
          averagePlay: averagePlay,
          totalPlay: totalPlay,
          totalLike: totalLike,
          totalComment: totalComment,
          totalShare: totalShare
      };

  } catch (error) {
      console.error(`[错误] 获取作者 ${authorId} 的视频信息时发生错误:`, error.message);
      // 返回空数据而不是抛出错误，避免整个流程中断
      return {
          id: authorId,
          videoCount: 0,
          videos: [],
          averagePlay: 0,
          totalPlay: 0,
          totalLike: 0,
          totalComment: 0,
          totalShare: 0,
          error: error.message
      };
  }
}

/**
 * 格式化数字字符串，处理万、千等单位
 * @param {string} str 包含单位的数字字符串
 * @returns {number} 转换后的数字
 */
function formatNumberFromString(str) {
  if (!str || str === '0' || str === '-') return 0;

  // 移除所有空格
  str = str.toString().replace(/\s/g, '');

  // 处理万单位
  if (str.includes('万')) {
      const num = parseFloat(str.replace('万', ''));
      return Math.round(num * 10000);
  }

  // 处理千单位
  if (str.includes('千')) {
      const num = parseFloat(str.replace('千', ''));
      return Math.round(num * 1000);
  }

  // 处理w单位（万的英文缩写）
  if (str.toLowerCase().includes('w')) {
      const num = parseFloat(str.toLowerCase().replace('w', ''));
      return Math.round(num * 10000);
  }

  // 处理k单位（千的英文缩写）
  if (str.toLowerCase().includes('k')) {
      const num = parseFloat(str.toLowerCase().replace('k', ''));
      return Math.round(num * 1000);
  }

  // 直接转换数字
  const num = parseFloat(str);
  return isNaN(num) ? 0 : Math.round(num);
}


/**
 * 写入 Excel 文件
 * @param {Array} authorDetails 达人详细信息数组
 */
function writeToExcel(authorDetails) {
  try {
    if (!authorDetails || authorDetails.length === 0) {
      console.warn("[警告] 没有数据可写入 Excel 文件");
      return;
    }

    console.log(`[开始] 写入 Excel 文件，共 ${authorDetails.length} 条达人信息...`);

    // 创建工作表
    const worksheet = xlsx.utils.json_to_sheet(authorDetails);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, "达人信息");

    // 自适应列宽
    if (authorDetails.length > 0) {
      const columnKeys = Object.keys(authorDetails[0]);
      const colWidths = columnKeys.map(key => {
        const maxLength = authorDetails.reduce((max, item) => {
          const cellValue = item[key] ? item[key].toString() : '';
          return Math.max(max, cellValue.length);
        }, key.length); // 至少要容纳列名的长度

        return { wch: Math.min(maxLength + 2, 50) }; // 限制最大宽度为50
      });

      worksheet["!cols"] = colWidths;
    }

    // 生成文件名
    const now = new Date();
    const timestamp = now.toISOString().replace(/[-:.]/g, "").slice(0, 15);
    const filename = `./达人信息_${timestamp}.xlsx`;

    // 写入文件
    xlsx.writeFile(workbook, filename);

    console.log(`[成功] Excel 文件已成功写入: ${filename}`);
    console.log(`[统计] 共写入 ${authorDetails.length} 条达人信息`);

    // 输出一些统计信息
    const validVideoCount = authorDetails.filter(author => author.视频数量 > 0).length;
    const avgPlayCount = authorDetails.reduce((sum, author) => sum + (author.平均播放量 || 0), 0) / authorDetails.length;

    console.log(`[统计] 有视频数据的达人: ${validVideoCount}/${authorDetails.length}`);
    console.log(`[统计] 平均播放量: ${Math.round(avgPlayCount)}`);

  } catch (error) {
    console.error("[错误] 写入 Excel 文件时发生错误:", error.message);

    // 尝试写入备份文件
    try {
      const backupFilename = `./达人信息_备份_${Date.now()}.json`;
      fs.writeFileSync(backupFilename, JSON.stringify(authorDetails, null, 2));
      console.log(`[备份] 数据已保存到备份文件: ${backupFilename}`);
    } catch (backupError) {
      console.error("[错误] 创建备份文件失败:", backupError.message);
    }
  }
}

/**
 * 启动爬虫程序
 */
async function startCrawler() {
  console.log("=".repeat(60));
  console.log("🚀 星图达人信息爬虫启动");
  console.log("=".repeat(60));
  console.log(`📊 配置信息:`);
  console.log(`   - 搜索关键词: ${data.search_param.keyword}`);
  console.log(`   - 最大页数: ${maxPages}`);
  console.log(`   - 每页数量: ${data.page_param.limit}`);
  console.log(`   - 防爬虫延迟: ${ANTI_CRAWLER_CONFIG.MIN_DELAY}-${ANTI_CRAWLER_CONFIG.MAX_DELAY}ms`);
  console.log(`   - 最大重试次数: ${ANTI_CRAWLER_CONFIG.MAX_RETRIES}`);
  console.log("=".repeat(60));

  const startTime = Date.now();

  try {
    await getAuthorList();

    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log("=".repeat(60));
    console.log("✅ 爬虫任务完成");
    console.log(`⏱️  总耗时: ${duration} 秒`);
    console.log(`📈 获取达人数: ${authorDetails.length}`);
    console.log("=".repeat(60));

  } catch (error) {
    console.error("❌ 爬虫任务失败:", error.message);
  }
}

// 启动爬虫
startCrawler();
