/**
 * 检查Cookie状态的脚本
 */

const { sequelize } = require('./src/config/database');
const CrawlerCookie = require('./src/models/CrawlerCookie');

async function checkCookies() {
  try {
    console.log('🔍 检查Cookie状态...\n');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功\n');
    
    // 查询所有Cookie
    const allCookies = await CrawlerCookie.findAll({
      attributes: ['id', 'platform', 'accountName', 'status', 'useCount', 'dailyUseCount', 'maxDailyUse', 'lastUsedAt', 'createdAt'],
      order: [['platform', 'ASC'], ['createdAt', 'DESC']]
    });
    
    console.log(`📊 总共找到 ${allCookies.length} 个Cookie记录\n`);
    
    if (allCookies.length === 0) {
      console.log('❌ 没有找到任何Cookie记录');
      console.log('💡 请先在Cookie管理界面添加小红书Cookie');
      return;
    }
    
    // 按平台分组显示
    const cookiesByPlatform = {};
    allCookies.forEach(cookie => {
      if (!cookiesByPlatform[cookie.platform]) {
        cookiesByPlatform[cookie.platform] = [];
      }
      cookiesByPlatform[cookie.platform].push(cookie);
    });
    
    Object.keys(cookiesByPlatform).forEach(platform => {
      const cookies = cookiesByPlatform[platform];
      console.log(`🏷️  ${platform.toUpperCase()} 平台 (${cookies.length} 个):`);
      console.log('─'.repeat(80));
      
      cookies.forEach((cookie, index) => {
        const statusIcon = cookie.status === 'active' ? '✅' : '❌';
        const usageInfo = `${cookie.useCount} 次使用 (今日: ${cookie.dailyUseCount}/${cookie.maxDailyUse})`;
        const lastUsed = cookie.lastUsedAt ? new Date(cookie.lastUsedAt).toLocaleString('zh-CN') : '从未使用';
        
        console.log(`  ${index + 1}. ${statusIcon} ${cookie.accountName}`);
        console.log(`     状态: ${cookie.status}`);
        console.log(`     使用情况: ${usageInfo}`);
        console.log(`     最后使用: ${lastUsed}`);
        console.log(`     创建时间: ${new Date(cookie.createdAt).toLocaleString('zh-CN')}`);
        console.log('');
      });
    });
    
    // 检查小红书可用Cookie
    const xiaohongshuCookies = cookiesByPlatform['xiaohongshu'] || [];
    const activeCookies = xiaohongshuCookies.filter(cookie => 
      cookie.status === 'active' && cookie.dailyUseCount < cookie.maxDailyUse
    );
    
    console.log('🎯 小红书Cookie状态分析:');
    console.log(`   总数: ${xiaohongshuCookies.length}`);
    console.log(`   可用: ${activeCookies.length}`);
    console.log(`   不可用: ${xiaohongshuCookies.length - activeCookies.length}`);
    
    if (activeCookies.length === 0) {
      console.log('\n❌ 没有可用的小红书Cookie！');
      console.log('💡 可能的原因:');
      console.log('   1. 没有添加小红书Cookie');
      console.log('   2. 所有Cookie都已过期或被禁用');
      console.log('   3. 今日使用次数已达上限');
      console.log('\n🔧 解决方案:');
      console.log('   1. 访问 Cookie管理页面 添加新的小红书Cookie');
      console.log('   2. 检查现有Cookie的状态并重新激活');
      console.log('   3. 增加Cookie的每日使用限制');
    } else {
      console.log('\n✅ 小红书Cookie状态正常，可以使用智能提报功能');
    }
    
  } catch (error) {
    console.error('❌ 检查Cookie时发生错误:', error.message);
  } finally {
    await sequelize.close();
  }
}

// 运行检查
checkCookies();
