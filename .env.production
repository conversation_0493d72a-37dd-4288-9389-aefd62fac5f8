# 服务器配置
PORT=3001
NODE_ENV=production
BASE_URL=http://*************:3001

# 生产环境数据库配置（线上服务器）
DB_HOST=*************
DB_PORT=3306
DB_NAME=daren_db
DB_USER=daren_db
DB_PASSWORD=daren_db_2025!
DB_LOGGING=false

# 自动初始化配置
DB_AUTO_INIT=true

# JWT配置
JWT_SECRET=daren_server_jwt_secret_key_2024
JWT_EXPIRES_IN=7d

# Cookie加密配置
COOKIE_ENCRYPTION_KEY=daren_cookie_encryption_key_32_chars

# 日志配置
LOG_LEVEL=info

# 跨域配置
CORS_ORIGIN=*