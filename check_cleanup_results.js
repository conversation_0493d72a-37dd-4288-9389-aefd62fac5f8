/**
 * 检查清理重复记录后的结果
 */

const { sequelize, MyInfluencer, PublicInfluencer } = require('./src/models');

async function checkCleanupResults() {
  try {
    console.log('📊 检查清理重复记录后的结果...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 检查我的达人库
    console.log('\n📋 我的达人库统计:');
    await checkMyInfluencerStats();

    // 2. 检查公海达人库
    console.log('\n📋 公海达人库统计:');
    await checkPublicInfluencerStats();

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await sequelize.close();
    console.log('\n🔌 数据库连接已关闭');
  }
}

/**
 * 检查我的达人库统计
 */
async function checkMyInfluencerStats() {
  // 总记录数
  const totalCount = await MyInfluencer.count();
  console.log(`   总记录数: ${totalCount}`);

  // 有平台ID的记录数
  const withPlatformIdCount = await MyInfluencer.count({
    where: {
      platformId: { [require('sequelize').Op.ne]: null }
    }
  });
  console.log(`   有平台ID的记录: ${withPlatformIdCount}`);

  // 检查是否还有重复记录
  const duplicateQuery = `
    SELECT platform, platform_id, COUNT(*) as count
    FROM my_influencers 
    WHERE platform_id IS NOT NULL AND platform_id != ''
    GROUP BY platform, platform_id 
    HAVING COUNT(*) > 1
  `;

  const [duplicates] = await sequelize.query(duplicateQuery);
  console.log(`   重复的平台ID组数: ${duplicates.length}`);

  if (duplicates.length > 0) {
    console.log('   ⚠️ 仍存在重复记录:');
    duplicates.forEach(dup => {
      console.log(`     ${dup.platform} - ${dup.platform_id}: ${dup.count}条`);
    });
  } else {
    console.log('   ✅ 没有重复记录');
  }

  // 按平台统计
  const platformStats = await MyInfluencer.findAll({
    attributes: [
      'platform',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    group: ['platform']
  });

  console.log('   按平台统计:');
  platformStats.forEach(stat => {
    console.log(`     ${stat.platform}: ${stat.dataValues.count}条`);
  });
}

/**
 * 检查公海达人库统计
 */
async function checkPublicInfluencerStats() {
  // 总记录数
  const totalCount = await PublicInfluencer.count();
  console.log(`   总记录数: ${totalCount}`);

  // 有平台用户ID的记录数
  const withPlatformUserIdCount = await PublicInfluencer.count({
    where: {
      platformUserId: { [require('sequelize').Op.ne]: null }
    }
  });
  console.log(`   有平台用户ID的记录: ${withPlatformUserIdCount}`);

  // 检查是否还有重复记录
  const duplicateQuery = `
    SELECT platform, platform_user_id, COUNT(*) as count
    FROM public_influencers 
    WHERE platform_user_id IS NOT NULL AND platform_user_id != ''
    GROUP BY platform, platform_user_id 
    HAVING COUNT(*) > 1
  `;

  const [duplicates] = await sequelize.query(duplicateQuery);
  console.log(`   重复的平台用户ID组数: ${duplicates.length}`);

  if (duplicates.length > 0) {
    console.log('   ⚠️ 仍存在重复记录:');
    duplicates.slice(0, 10).forEach(dup => {
      console.log(`     ${dup.platform} - ${dup.platform_user_id}: ${dup.count}条`);
    });
    if (duplicates.length > 10) {
      console.log(`     ... 还有 ${duplicates.length - 10} 组重复记录`);
    }
  } else {
    console.log('   ✅ 没有重复记录');
  }

  // 按平台统计
  const platformStats = await PublicInfluencer.findAll({
    attributes: [
      'platform',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    group: ['platform']
  });

  console.log('   按平台统计:');
  platformStats.forEach(stat => {
    console.log(`     ${stat.platform}: ${stat.dataValues.count}条`);
  });

  // 按状态统计
  const statusStats = await PublicInfluencer.findAll({
    attributes: [
      'status',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    group: ['status']
  });

  console.log('   按状态统计:');
  statusStats.forEach(stat => {
    console.log(`     ${stat.status}: ${stat.dataValues.count}条`);
  });
}

// 运行检查
if (require.main === module) {
  checkCleanupResults()
    .then(() => {
      console.log('🎉 检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 检查失败:', error);
      process.exit(1);
    });
}

module.exports = checkCleanupResults;
