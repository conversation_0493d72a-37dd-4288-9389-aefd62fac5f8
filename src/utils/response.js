/**
 * 统一响应格式工具类
 *
 * 功能说明：
 * - 提供标准化的API响应格式
 * - 统一成功和错误响应的数据结构
 * - 支持分页数据的响应格式
 * - 自动格式化响应数据中的时间字段
 * - 简化控制器中的响应代码编写
 *
 * 响应格式规范：
 * {
 *   "success": boolean,    // 操作是否成功
 *   "message": string,     // 响应消息
 *   "data": any,          // 响应数据（可选）
 *   "pagination": object, // 分页信息（可选）
 *   "errors": object      // 验证错误信息（可选）
 * }
 *
 * 使用示例：
 * ResponseUtil.success(ctx, userData, '获取用户信息成功');
 * ResponseUtil.error(ctx, '用户不存在', 404);
 * ResponseUtil.validationError(ctx, {name: '姓名不能为空'});
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const DateFormatter = require('./dateFormatter');
class ResponseUtil {
  /**
   * 成功响应
   *
   * @param {Object} ctx - Koa上下文对象
   * @param {any} data - 响应数据，默认null
   * @param {string} message - 成功消息，默认'操作成功'
   * @param {number} status - HTTP状态码，默认200
   * @param {Object} pagination - 分页信息，默认null
   * @param {Object} extra - 额外数据，默认null
   *
   * 使用示例：
   * ResponseUtil.success(ctx, user, '用户创建成功');
   * ResponseUtil.success(ctx, user, '审核通过', 200, null, {canCreateCooperation: true});
   */
  static success(ctx, data = null, message = '操作成功', status = 200, pagination = null, extra = null) {
    ctx.status = status;
    const responseBody = {
      success: true,
      message,
      data: DateFormatter.formatDeep(data)
    };

    if (pagination) {
      responseBody.pagination = pagination;
    }

    if (extra) {
      responseBody.extra = extra;
    }

    ctx.body = responseBody;
  }

  /**
   * 分页成功响应
   *
   * @param {Object} ctx - Koa上下文对象
   * @param {Array} data - 分页数据列表
   * @param {Object} pagination - 分页信息对象
   * @param {string} message - 成功消息，默认'获取成功'
   *
   * 分页信息格式：
   * {
   *   page: number,     // 当前页码
   *   limit: number,    // 每页数量
   *   total: number,    // 总记录数
   *   pages: number     // 总页数
   * }
   *
   * 使用示例：
   * ResponseUtil.successWithPagination(ctx, users, {page: 1, limit: 10, total: 100, pages: 10});
   */
  static successWithPagination(ctx, data, pagination, message = '获取成功') {
    ctx.status = 200;
    ctx.body = {
      success: true,
      message,
      data: DateFormatter.formatArray(data),
      pagination
    };
  }

  /**
   * 错误响应
   *
   * @param {Object} ctx - Koa上下文对象
   * @param {string} message - 错误消息，默认'操作失败'
   * @param {number} status - HTTP状态码，默认400
   * @param {any} data - 错误相关数据，默认null
   *
   * 使用示例：
   * ResponseUtil.error(ctx, '用户不存在', 404);
   * ResponseUtil.error(ctx, '参数错误', 400, {field: 'email'});
   */
  static error(ctx, message = '操作失败', status = 400, data = null) {
    ctx.status = status;
    ctx.body = {
      success: false,
      message,
      data
    };
  }

  // 验证错误响应
  static validationError(ctx, errors) {
    ctx.status = 422;
    ctx.body = {
      success: false,
      message: '数据验证失败',
      errors
    };
  }

  // 未找到响应
  static notFound(ctx, message = '资源未找到') {
    ctx.status = 404;
    ctx.body = {
      success: false,
      message
    };
  }

  // 未授权响应
  static unauthorized(ctx, message = '未授权访问') {
    ctx.status = 401;
    ctx.body = {
      success: false,
      message
    };
  }

  // 禁止访问响应
  static forbidden(ctx, message = '禁止访问') {
    ctx.status = 403;
    ctx.body = {
      success: false,
      message
    };
  }

  /**
   * Sequelize分页查询结果响应
   * 专门处理Sequelize的findAndCountAll结果
   *
   * @param {Object} ctx - Koa上下文对象
   * @param {Object} sequelizeResult - Sequelize查询结果 {count, rows}
   * @param {Object} paginationInfo - 分页信息 {page, limit}
   * @param {string} message - 成功消息，默认'获取成功'
   *
   * 使用示例：
   * const result = await User.findAndCountAll({...});
   * ResponseUtil.sequelizePagination(ctx, result, {page: 1, limit: 10});
   */
  static sequelizePagination(ctx, sequelizeResult, paginationInfo, message = '获取成功') {
    const { count, rows } = sequelizeResult;
    const { page, limit } = paginationInfo;

    ctx.status = 200;
    ctx.body = {
      success: true,
      message,
      data: DateFormatter.formatArray(rows),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    };
  }

  /**
   * 格式化单个Sequelize实例响应
   *
   * @param {Object} ctx - Koa上下文对象
   * @param {Object} instance - Sequelize实例
   * @param {string} message - 成功消息，默认'获取成功'
   *
   * 使用示例：
   * const user = await User.findByPk(id);
   * ResponseUtil.sequelizeInstance(ctx, user, '获取用户成功');
   */
  static sequelizeInstance(ctx, instance, message = '获取成功') {
    ctx.status = 200;
    ctx.body = {
      success: true,
      message,
      data: DateFormatter.formatObject(instance)
    };
  }

  /**
   * 格式化Sequelize实例数组响应
   *
   * @param {Object} ctx - Koa上下文对象
   * @param {Array} instances - Sequelize实例数组
   * @param {string} message - 成功消息，默认'获取成功'
   *
   * 使用示例：
   * const users = await User.findAll();
   * ResponseUtil.sequelizeArray(ctx, users, '获取用户列表成功');
   */
  static sequelizeArray(ctx, instances, message = '获取成功') {
    ctx.status = 200;
    ctx.body = {
      success: true,
      message,
      data: DateFormatter.formatArray(instances)
    };
  }
}

module.exports = ResponseUtil;
