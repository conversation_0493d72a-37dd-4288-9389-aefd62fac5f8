const jwt = require('jsonwebtoken');

class JWTUtil {
  // 生成JWT令牌
  static generateToken(payload) {
    return jwt.sign(
      payload,
      process.env.JWT_SECRET,
      {
        expiresIn: process.env.JWT_EXPIRES_IN || '7d'
      }
    );
  }

  // 验证JWT令牌
  static verifyToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      throw new Error('无效的令牌');
    }
  }

  // 解码JWT令牌（不验证）
  static decodeToken(token) {
    return jwt.decode(token);
  }
}

module.exports = JWTUtil;
