/**
 * 数据验证工具类
 *
 * 功能说明：
 * - 提供常用的数据格式验证功能
 * - 支持邮箱、密码、用户名、手机号等格式验证
 * - 提供必填字段和长度验证功能
 * - 统一验证规则和错误消息格式
 *
 * 验证规则说明：
 * - 邮箱：标准邮箱格式验证
 * - 密码：至少8位，包含字母和数字
 * - 用户名：3-20位，只能包含字母、数字、下划线
 * - 手机号：中国大陆手机号格式（1开头11位）
 * - URL：标准URL格式验证
 *
 * 使用示例：
 * const errors = ValidationUtil.validateRequired(data, ['name', 'email']);
 * const isValid = ValidationUtil.isValidEmail('<EMAIL>');
 *
 * <AUTHOR>
 * @version 1.0.0
 */
class ValidationUtil {
  /**
   * 验证邮箱格式
   *
   * @param {string} email - 待验证的邮箱地址
   * @returns {boolean} 是否为有效邮箱格式
   *
   * 验证规则：标准邮箱格式，包含@符号和域名
   *
   * 使用示例：
   * ValidationUtil.isValidEmail('<EMAIL>'); // true
   * ValidationUtil.isValidEmail('invalid-email'); // false
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证密码强度
   *
   * @param {string} password - 待验证的密码
   * @returns {boolean} 是否符合密码强度要求
   *
   * 验证规则：至少8位，必须包含字母和数字，可包含特殊字符
   *
   * 使用示例：
   * ValidationUtil.isValidPassword('password123'); // true
   * ValidationUtil.isValidPassword('123456'); // false
   */
  static isValidPassword(password) {
    // 至少8位，包含字母和数字
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
  }

  /**
   * 验证用户名格式
   *
   * @param {string} username - 待验证的用户名
   * @returns {boolean} 是否为有效用户名格式
   *
   * 验证规则：3-20位，只能包含字母、数字、下划线
   *
   * 使用示例：
   * ValidationUtil.isValidUsername('user_123'); // true
   * ValidationUtil.isValidUsername('u'); // false (太短)
   */
  static isValidUsername(username) {
    // 3-20位，只能包含字母、数字、下划线
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
  }

  /**
   * 验证手机号格式
   *
   * @param {string} phone - 待验证的手机号
   * @returns {boolean} 是否为有效手机号格式
   *
   * 验证规则：中国大陆手机号，1开头的11位数字
   *
   * 使用示例：
   * ValidationUtil.isValidPhone('13812345678'); // true
   * ValidationUtil.isValidPhone('12345678901'); // false
   */
  static isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 验证URL格式
   *
   * @param {string} url - 待验证的URL
   * @returns {boolean} 是否为有效URL格式
   *
   * 验证规则：标准URL格式，包含协议和域名
   *
   * 使用示例：
   * ValidationUtil.isValidUrl('https://example.com'); // true
   * ValidationUtil.isValidUrl('invalid-url'); // false
   */
  static isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // 验证必填字段
  static validateRequired(data, requiredFields) {
    const errors = {};
    
    requiredFields.forEach(field => {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
        errors[field] = `${field} 是必填字段`;
      }
    });
    
    return Object.keys(errors).length > 0 ? errors : null;
  }

  // 验证字段长度
  static validateLength(data, lengthRules) {
    const errors = {};
    
    Object.keys(lengthRules).forEach(field => {
      const value = data[field];
      const rules = lengthRules[field];
      
      if (value && typeof value === 'string') {
        if (rules.min && value.length < rules.min) {
          errors[field] = `${field} 长度不能少于 ${rules.min} 个字符`;
        }
        if (rules.max && value.length > rules.max) {
          errors[field] = `${field} 长度不能超过 ${rules.max} 个字符`;
        }
      }
    });
    
    return Object.keys(errors).length > 0 ? errors : null;
  }
}

module.exports = ValidationUtil;
