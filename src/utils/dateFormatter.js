/**
 * 统一时间格式化工具类
 *
 * 功能说明：
 * - 提供统一的时间格式化方法
 * - 支持标准的 'YYYY-MM-DD HH:mm:ss' 格式
 * - 处理各种时间输入类型（Date对象、时间戳、字符串）
 * - 为API响应提供一致的时间格式
 * - 支持批量格式化对象中的时间字段
 *
 * 使用示例：
 * DateFormatter.format(new Date()) // '2024-01-15 14:30:25'
 * DateFormatter.formatObject(user) // 自动格式化对象中的时间字段
 * DateFormatter.formatArray(users) // 批量格式化数组中的时间字段
 *
 * <AUTHOR>
 * @version 1.0.0
 */
class DateFormatter {
  /**
   * 标准时间格式
   */
  static FORMAT = 'YYYY/MM/DD HH:mm:ss';

  /**
   * 常见的时间字段名称
   */
  static TIME_FIELDS = [
    'createdAt',
    'updatedAt',
    'deletedAt',
    'publishTime',
    'startedAt',
    'completedAt',
    'lastLoginAt',
    'lastRetryAt',
    'lastUpdated',
    'expiredAt',
    'validUntil',
    'time',
    'lastUsedAt',
    'lastValidatedAt',
    'cooldownUntil',
    'created_at',
    'updated_at',
    'last_used_at',
    'last_validated_at',
    'cooldown_until',
    // 合作对接相关时间字段
    'scheduledPublishTime',
    'actualPublishDate',
    'dataRegistrationDate',
    'scheduledFetchTime',
    'lastSubmittedAt',
    'reviewedAt'
  ];

  /**
   * 格式化单个时间值
   * @param {Date|string|number} date 时间值
   * @returns {string|null} 格式化后的时间字符串，无效时间返回null
   */
  static format(date) {
    if (!date) return null;

    try {
      let dateObj;

      // 处理不同类型的输入
      if (date instanceof Date) {
        dateObj = date;
      } else if (typeof date === 'string') {
        dateObj = new Date(date);
      } else if (typeof date === 'number') {
        dateObj = new Date(date);
      } else {
        return null;
      }

      // 检查日期是否有效
      if (isNaN(dateObj.getTime())) {
        return null;
      }

      // 格式化为 YYYY/MM/DD HH:mm:ss
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getDate()).padStart(2, '0');
      const hours = String(dateObj.getHours()).padStart(2, '0');
      const minutes = String(dateObj.getMinutes()).padStart(2, '0');
      const seconds = String(dateObj.getSeconds()).padStart(2, '0');

      return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      console.warn('时间格式化失败:', error.message, '原始值:', date);
      return null;
    }
  }

  /**
   * 格式化对象中的时间字段
   * @param {Object} obj 要格式化的对象
   * @param {Array} customFields 自定义时间字段名称（可选）
   * @returns {Object} 格式化后的对象
   */
  static formatObject(obj, customFields = []) {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    // 如果是Sequelize实例，先转换为普通对象
    const plainObj = obj.toJSON ? obj.toJSON() : obj;
    const result = { ...plainObj };

    // 合并默认字段和自定义字段
    const fieldsToFormat = [...this.TIME_FIELDS, ...customFields];

    // 格式化时间字段
    fieldsToFormat.forEach(field => {
      if (result[field]) {
        const formatted = this.format(result[field]);
        if (formatted !== null) {
          result[field] = formatted;
        }
      }
    });

    return result;
  }

  /**
   * 格式化数组中的时间字段
   * @param {Array} array 要格式化的数组
   * @param {Array} customFields 自定义时间字段名称（可选）
   * @returns {Array} 格式化后的数组
   */
  static formatArray(array, customFields = []) {
    if (!Array.isArray(array)) {
      return array;
    }

    return array.map(item => this.formatObject(item, customFields));
  }

  /**
   * 格式化分页响应数据
   * @param {Object} paginationResult Sequelize分页查询结果
   * @param {Array} customFields 自定义时间字段名称（可选）
   * @returns {Object} 格式化后的分页结果
   */
  static formatPaginationResult(paginationResult, customFields = []) {
    if (!paginationResult || !paginationResult.rows) {
      return paginationResult;
    }

    return {
      ...paginationResult,
      rows: this.formatArray(paginationResult.rows, customFields)
    };
  }

  /**
   * 深度格式化对象（处理嵌套对象和数组）
   * @param {any} data 要格式化的数据
   * @param {Array} customFields 自定义时间字段名称（可选）
   * @returns {any} 格式化后的数据
   */
  static formatDeep(data, customFields = []) {
    if (!data) {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.formatDeep(item, customFields));
    }

    if (typeof data === 'object') {
      // 如果是Sequelize实例，先转换为普通对象
      const plainObj = data.toJSON ? data.toJSON() : data;
      const result = {};

      // 合并默认字段和自定义字段
      const fieldsToFormat = [...this.TIME_FIELDS, ...customFields];

      Object.keys(plainObj).forEach(key => {
        const value = plainObj[key];

        // 如果是时间字段，进行格式化
        if (fieldsToFormat.includes(key)) {
          const formatted = this.format(value);
          result[key] = formatted !== null ? formatted : value;
        }
        // 如果是嵌套对象或数组，递归处理
        else if (value && (typeof value === 'object' || Array.isArray(value))) {
          result[key] = this.formatDeep(value, customFields);
        }
        // 其他字段直接复制
        else {
          result[key] = value;
        }
      });

      return result;
    }

    return data;
  }

  /**
   * 检查是否为时间字段
   * @param {string} fieldName 字段名称
   * @param {Array} customFields 自定义时间字段名称（可选）
   * @returns {boolean} 是否为时间字段
   */
  static isTimeField(fieldName, customFields = []) {
    const allFields = [...this.TIME_FIELDS, ...customFields];
    return allFields.includes(fieldName);
  }

  /**
   * 获取当前时间的格式化字符串
   * @returns {string} 当前时间的格式化字符串
   */
  static now() {
    return this.format(new Date());
  }
}

module.exports = DateFormatter;
