const XLSX = require('xlsx');

class ExcelUtil {
  // 导出达人信息到Excel
  static exportInfluencers(influencers) {
    // 准备数据
    const data = influencers.map(influencer => ({
      ID: influencer.id,
      达人昵称: influencer.nickname,
      平台: influencer.platform === 'xiaohongshu' ? '小红书' : '巨量星图',
      平台ID: influencer.platformId || influencer.platformUserId || '',
      粉丝数量: influencer.followersCount,
      分类: influencer.category || '',
      标签: Array.isArray(influencer.tags) ? influencer.tags.join(', ') : '',
      联系方式: this.formatContactInfo(influencer.contactInfo),
      报价信息: this.formatPriceInfo(influencer.priceInfo),
      备注: influencer.notes || '',
      状态: influencer.status === 'active' ? '活跃' : '非活跃',
      创建时间: this.formatDate(influencer.createdAt),
      更新时间: this.formatDate(influencer.updatedAt)
    }));

    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(data);

    // 设置列宽
    const colWidths = [
      { wch: 8 }, // ID
      { wch: 20 }, // 达人昵称
      { wch: 12 }, // 平台
      { wch: 15 }, // 平台ID
      { wch: 12 }, // 粉丝数量
      { wch: 15 }, // 分类
      { wch: 30 }, // 标签
      { wch: 30 }, // 联系方式
      { wch: 30 }, // 报价信息
      { wch: 40 }, // 备注
      { wch: 10 }, // 状态
      { wch: 20 }, // 创建时间
      { wch: 20 } // 更新时间
    ];
    worksheet['!cols'] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '达人信息');

    // 生成Excel文件缓冲区
    const buffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx'
    });

    return buffer;
  }

  // 格式化联系方式
  static formatContactInfo(contactInfo) {
    if (!contactInfo || typeof contactInfo !== 'object') {
      return '';
    }

    const parts = [];
    if (contactInfo.phone) parts.push(`电话: ${contactInfo.phone}`);
    if (contactInfo.email) parts.push(`邮箱: ${contactInfo.email}`);
    if (contactInfo.wechat) parts.push(`微信: ${contactInfo.wechat}`);
    if (contactInfo.qq) parts.push(`QQ: ${contactInfo.qq}`);

    return parts.join('; ');
  }

  // 格式化报价信息
  static formatPriceInfo(priceInfo) {
    if (!priceInfo || typeof priceInfo !== 'object') {
      return '';
    }

    const parts = [];
    if (priceInfo.post) parts.push(`图文: ${priceInfo.post}元`);
    if (priceInfo.video) parts.push(`视频: ${priceInfo.video}元`);
    if (priceInfo.live) parts.push(`直播: ${priceInfo.live}元`);
    if (priceInfo.story) parts.push(`故事: ${priceInfo.story}元`);

    return parts.join('; ');
  }

  // 格式化日期
  static formatDate(date) {
    if (!date) return '';

    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  }

  // 生成文件名
  static generateFileName(prefix = 'influencers') {
    const now = new Date();
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `${prefix}_${timestamp}.xlsx`;
  }
}

module.exports = ExcelUtil;
