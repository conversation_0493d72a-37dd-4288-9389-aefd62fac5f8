/**
 * 自定义错误类定义
 * 
 * 功能说明：
 * - 定义应用中使用的各种自定义错误类型
 * - 提供统一的错误创建和处理机制
 * - 支持错误码、错误消息和额外数据
 * - 便于错误分类和处理
 * 
 * 错误类型：
 * - AppError: 应用基础错误类
 * - ValidationError: 数据验证错误
 * - AuthenticationError: 认证错误
 * - AuthorizationError: 授权错误
 * - NotFoundError: 资源不存在错误
 * - ConflictError: 资源冲突错误
 * - BusinessError: 业务逻辑错误
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { HTTP_STATUS, ERROR_MESSAGES } = require('../config/constants');

/**
 * 应用基础错误类
 * 所有自定义错误的基类
 */
class AppError extends Error {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   * @param {number} statusCode - HTTP状态码
   * @param {string} errorCode - 错误码
   * @param {any} data - 额外数据
   */
  constructor(message, statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR, errorCode = 'UNKNOWN_ERROR', data = null) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.status = statusCode; // Koa兼容
    this.errorCode = errorCode;
    this.data = data;
    this.isOperational = true; // 标识这是一个可预期的错误
    
    // 捕获堆栈跟踪
    Error.captureStackTrace(this, this.constructor);
  }
  
  /**
   * 转换为JSON格式
   * @returns {Object} JSON格式的错误信息
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      errorCode: this.errorCode,
      data: this.data
    };
  }
}

/**
 * 数据验证错误
 * 用于表单验证、参数验证等场景
 */
class ValidationError extends AppError {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   * @param {Object} errors - 验证错误详情
   */
  constructor(message = '数据验证失败', errors = {}) {
    super(message, HTTP_STATUS.UNPROCESSABLE_ENTITY, 'VALIDATION_ERROR', errors);
    this.errors = errors;
  }
}

/**
 * 认证错误
 * 用于登录失败、令牌无效等场景
 */
class AuthenticationError extends AppError {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   */
  constructor(message = ERROR_MESSAGES.UNAUTHORIZED) {
    super(message, HTTP_STATUS.UNAUTHORIZED, 'AUTHENTICATION_ERROR');
  }
}

/**
 * 授权错误
 * 用于权限不足等场景
 */
class AuthorizationError extends AppError {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   */
  constructor(message = ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS) {
    super(message, HTTP_STATUS.FORBIDDEN, 'AUTHORIZATION_ERROR');
  }
}

/**
 * 资源不存在错误
 * 用于查找资源失败等场景
 */
class NotFoundError extends AppError {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   * @param {string} resource - 资源类型
   */
  constructor(message = ERROR_MESSAGES.RESOURCE_NOT_FOUND, resource = null) {
    super(message, HTTP_STATUS.NOT_FOUND, 'NOT_FOUND_ERROR', { resource });
  }
}

/**
 * 资源冲突错误
 * 用于资源已存在、状态冲突等场景
 */
class ConflictError extends AppError {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   * @param {string} resource - 资源类型
   */
  constructor(message = '资源冲突', resource = null) {
    super(message, HTTP_STATUS.CONFLICT, 'CONFLICT_ERROR', { resource });
  }
}

/**
 * 业务逻辑错误
 * 用于业务规则违反等场景
 */
class BusinessError extends AppError {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   * @param {string} businessCode - 业务错误码
   */
  constructor(message = '业务逻辑错误', businessCode = 'BUSINESS_ERROR') {
    super(message, HTTP_STATUS.BAD_REQUEST, businessCode);
  }
}

/**
 * 外部服务错误
 * 用于第三方API调用失败等场景
 */
class ExternalServiceError extends AppError {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   * @param {string} service - 服务名称
   * @param {any} originalError - 原始错误
   */
  constructor(message = '外部服务错误', service = 'unknown', originalError = null) {
    super(message, HTTP_STATUS.BAD_REQUEST, 'EXTERNAL_SERVICE_ERROR', { service, originalError });
  }
}

/**
 * 速率限制错误
 * 用于API调用频率超限等场景
 */
class RateLimitError extends AppError {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   * @param {number} retryAfter - 重试等待时间（秒）
   */
  constructor(message = '请求频率过高，请稍后重试', retryAfter = 60) {
    super(message, 429, 'RATE_LIMIT_ERROR', { retryAfter });
  }
}

/**
 * 错误工厂函数
 * 根据错误类型创建相应的错误实例
 */
class ErrorFactory {
  /**
   * 创建验证错误
   * @param {Object} errors - 验证错误详情
   * @param {string} message - 自定义消息
   * @returns {ValidationError} 验证错误实例
   */
  static createValidationError(errors, message) {
    return new ValidationError(message, errors);
  }
  
  /**
   * 创建认证错误
   * @param {string} message - 自定义消息
   * @returns {AuthenticationError} 认证错误实例
   */
  static createAuthenticationError(message) {
    return new AuthenticationError(message);
  }
  
  /**
   * 创建授权错误
   * @param {string} message - 自定义消息
   * @returns {AuthorizationError} 授权错误实例
   */
  static createAuthorizationError(message) {
    return new AuthorizationError(message);
  }
  
  /**
   * 创建资源不存在错误
   * @param {string} resource - 资源类型
   * @param {string} message - 自定义消息
   * @returns {NotFoundError} 资源不存在错误实例
   */
  static createNotFoundError(resource, message) {
    const msg = message || `${resource || '资源'}不存在`;
    return new NotFoundError(msg, resource);
  }
  
  /**
   * 创建冲突错误
   * @param {string} resource - 资源类型
   * @param {string} message - 自定义消息
   * @returns {ConflictError} 冲突错误实例
   */
  static createConflictError(resource, message) {
    const msg = message || `${resource || '资源'}已存在`;
    return new ConflictError(msg, resource);
  }
  
  /**
   * 创建业务错误
   * @param {string} message - 错误消息
   * @param {string} businessCode - 业务错误码
   * @returns {BusinessError} 业务错误实例
   */
  static createBusinessError(message, businessCode) {
    return new BusinessError(message, businessCode);
  }
}

module.exports = {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  BusinessError,
  ExternalServiceError,
  RateLimitError,
  ErrorFactory
};
