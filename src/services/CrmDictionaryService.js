/**
 * CRM字典同步服务
 *
 * 功能说明：
 * - 从CRM系统获取字典数据（客户和协议字段）
 * - 解析SELECT类型字段的字典项数据
 * - 实现本地存储和增量更新机制
 * - 提供字典数据的查询和管理功能
 *
 * 主要功能：
 * - 获取CRM字段列表和字典数据
 * - 数据解析和格式化
 * - 本地数据库同步
 * - 增量更新和版本管理
 * - 错误处理和重试机制
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const { CrmDictionary } = require('../models');
const { Op } = require('sequelize');
const CrmIntegrationService = require('./CrmIntegrationService');

class CrmDictionaryService {
  constructor() {
    // 使用现有的CRM集成服务实例，复用鉴权和配置
    this.crmIntegrationService = new CrmIntegrationService();

    // 从现有服务获取配置
    this.config = {
      baseUrl: this.crmIntegrationService.config.proxyUrl,
      corpId: this.crmIntegrationService.config.corpId,
      timeout: this.crmIntegrationService.config.timeout,
      retryCount: this.crmIntegrationService.config.retryAttempts,
      retryDelay: this.crmIntegrationService.config.retryDelay
    };

    // 复用现有的HTTP客户端和频率限制机制
    this.httpClient = this.crmIntegrationService.httpClient;

    // 请求频率限制（与现有服务保持一致）
    this.lastRequestTime = 0;
    this.minRequestInterval = this.crmIntegrationService.config.rateLimit.requestDelay || 1000;
  }

  /**
   * 获取CRM访问令牌
   * 复用现有CRM集成服务的鉴权机制，确保token的自动刷新和缓存
   * @returns {Promise<string>} 访问令牌
   */
  async getCorpAccessToken() {
    try {
      // 直接使用现有CRM集成服务的token获取方法
      // 这确保了token的自动刷新、缓存和错误处理与现有系统保持一致
      const token = await this.crmIntegrationService.getCorpAccessToken();

      console.log('🔑 使用CRM集成服务获取访问令牌成功');
      return token;

    } catch (error) {
      console.error('❌ 获取CRM访问令牌失败:', error.message);
      throw new Error(`CRM鉴权失败: ${error.message}`);
    }
  }

  /**
   * 检查token是否有效
   * 复用现有CRM集成服务的token验证逻辑
   * @returns {boolean}
   */
  isTokenValid() {
    return this.crmIntegrationService.isTokenValid();
  }

  /**
   * 获取CRM系统状态
   * 复用现有CRM集成服务的状态检查
   * @returns {Object} CRM系统状态信息
   */
  getCrmSystemStatus() {
    return {
      tokenValid: this.crmIntegrationService.isTokenValid(),
      lastTokenRefresh: this.crmIntegrationService.tokenCache.expiresAt ?
        new Date(this.crmIntegrationService.tokenCache.expiresAt - this.crmIntegrationService.config.tokenCache.ttl * 1000).toISOString() :
        null,
      nextTokenRefresh: this.crmIntegrationService.tokenCache.expiresAt ?
        new Date(this.crmIntegrationService.tokenCache.expiresAt).toISOString() :
        null,
      queueLength: this.crmIntegrationService.requestQueue.length,
      isProcessingQueue: this.crmIntegrationService.isProcessingQueue
    };
  }

  /**
   * 手动刷新CRM访问令牌
   * 复用现有CRM集成服务的token刷新机制
   * @returns {Promise<string>} 新的访问令牌
   */
  async refreshToken() {
    try {
      // 清除缓存的token
      this.crmIntegrationService.tokenCache.corpAccessToken = null;
      this.crmIntegrationService.tokenCache.expiresAt = null;

      // 获取新token
      const token = await this.crmIntegrationService.getCorpAccessToken();

      console.log('✅ CRM字典服务token刷新成功');
      return token;

    } catch (error) {
      console.error('❌ CRM字典服务token刷新失败:', error.message);
      throw new Error(`Token刷新失败: ${error.message}`);
    }
  }

  /**
   * 频率限制的请求方法
   * 复用现有CRM集成服务的请求机制，确保频率限制和错误处理一致
   * @param {string} endpoint API端点
   * @param {Object} data 请求数据
   * @returns {Promise<Object>} 响应数据
   */
  async makeRateLimitedRequest(endpoint, data) {
    try {
      // 使用现有CRM集成服务的频率限制请求方法
      // 这确保了与现有系统的频率限制策略保持一致
      const response = await this.crmIntegrationService.makeRateLimitedRequest(endpoint, data);

      console.log(`✅ CRM字典API请求成功: ${endpoint}`);
      return response;

    } catch (error) {
      console.error(`❌ CRM字典API请求失败: ${endpoint}`, error.message);
      throw new Error(`CRM字典API请求失败: ${error.message}`);
    }
  }

  /**
   * 获取CRM字段列表
   * @param {string} deployId 部署ID（customer或contract）
   * @returns {Promise<Array>} 字段列表
   */
  async getCrmFieldList(deployId) {
    try {
      const token = await this.getCorpAccessToken();
      
      console.log(`📋 获取CRM字段列表 - 部署类型: ${deployId}`);

      const requestData = {
        corpAccessToken: token,
        corpId: this.config.corpId,
        deployId: deployId
      };

      const response = await this.makeRateLimitedRequest(
        '/oapi/corp/v1/object/field/list.json',
        requestData
      );

      const data = response.data;

      if (!data.success) {
        throw new Error(`获取字段列表失败: ${data.message} (错误码: ${data.result})`);
      }

      const fields = data.data || [];
      console.log(`✅ 成功获取字段列表 - 部署类型: ${deployId}, 字段数量: ${fields.length}`);

      return fields;

    } catch (error) {
      console.error(`❌ 获取CRM字段列表失败 - 部署类型: ${deployId}`, error.message);
      throw new Error(`获取字段列表失败: ${error.message}`);
    }
  }

  /**
   * 解析字段中的字典数据
   * @param {Object} field CRM字段对象
   * @param {string} deployId 部署ID
   * @returns {Array} 字典项数组
   */
  parseDictionaryData(field, deployId) {
    const dictionaries = [];

    try {
      // 只处理SELECT类型的字段
      if (field.fieldDomType !== 'SELECT' && field.fieldDomType !== 'MULTILEVEL_SELECT' && field.fieldDomType !== 'CHECK_BOX') {
        return dictionaries;
      }

      // 检查是否有字典数据
      if (!field.dicData || !field.dicData.datas || !Array.isArray(field.dicData.datas)) {
        console.log(`⚠️ 字段 ${field.fieldName} 没有字典数据`);
        return dictionaries;
      }

      // 解析每个字典项
      field.dicData.datas.forEach((item, index) => {
        const dictionary = {
          deployId: deployId,
          fieldName: field.fieldName || '',
          fieldCode: field.fieldCode || '',
          fieldDomType: field.fieldDomType,
          dataId: item.dataId || '',
          dataName: item.dataName || '',
          dataValue: item.dataValue || item.dataName || '',
          dataOrder: item.dataOrder || index,
          rawData: {
            field: {
              fieldName: field.fieldName,
              fieldCode: field.fieldCode,
              fieldDomType: field.fieldDomType,
              fieldId: field.fieldId
            },
            item: item
          },
          syncStatus: 'pending',
          isActive: true,
          isDeleted: false
        };

        dictionaries.push(dictionary);
      });

      console.log(`✅ 解析字段字典数据 - 字段: ${field.fieldName}, 字典项: ${dictionaries.length}`);

    } catch (error) {
      console.error(`❌ 解析字段字典数据失败 - 字段: ${field.fieldName}`, error.message);
    }

    return dictionaries;
  }

  /**
   * 同步单个部署类型的字典数据
   * @param {string} deployId 部署ID（customer或contract）
   * @returns {Promise<Object>} 同步结果
   */
  async syncDictionariesByDeployId(deployId) {
    const result = {
      deployId: deployId,
      totalFields: 0,
      selectFields: 0,
      totalDictionaries: 0,
      created: 0,
      updated: 0,
      failed: 0,
      errors: []
    };

    try {
      console.log(`🔄 开始同步字典数据 - 部署类型: ${deployId}`);

      // 1. 获取字段列表
      const fields = await this.getCrmFieldList(deployId);
      result.totalFields = fields.length;

      // 2. 筛选SELECT类型字段
      const selectFields = fields.filter(field => field.fieldDomType === 'SELECT' || field.fieldDomType === 'MULTILEVEL_SELECT' || field.fieldDomType === 'CHECK_BOX');
      result.selectFields = selectFields.length;

      console.log(`📊 字段统计 - 总字段: ${result.totalFields}, SELECT字段: ${result.selectFields}`);

      // 3. 处理每个SELECT字段
      for (const field of selectFields) {
        try {
          // 解析字典数据
          const dictionaries = this.parseDictionaryData(field, deployId);
          result.totalDictionaries += dictionaries.length;

          // 同步到数据库
          for (const dict of dictionaries) {
            try {
              await this.syncDictionaryItem(dict);
              result.created++;
            } catch (error) {
              result.failed++;
              result.errors.push({
                fieldCode: dict.fieldCode,
                dataId: dict.dataId,
                error: error.message
              });
              console.error(`❌ 同步字典项失败: ${dict.fieldCode}.${dict.dataId}`, error.message);
            }
          }

        } catch (error) {
          result.failed++;
          result.errors.push({
            fieldCode: field.fieldCode,
            error: error.message
          });
          console.error(`❌ 处理字段失败: ${field.fieldCode}`, error.message);
        }
      }

      console.log(`✅ 字典数据同步完成 - 部署类型: ${deployId}`);
      console.log(`📊 同步统计: 创建 ${result.created}, 更新 ${result.updated}, 失败 ${result.failed}`);

      return result;

    } catch (error) {
      console.error(`❌ 同步字典数据失败 - 部署类型: ${deployId}`, error.message);
      result.errors.push({
        deployId: deployId,
        error: error.message
      });
      throw new Error(`同步字典数据失败: ${error.message}`);
    }
  }

  /**
   * 同步单个字典项到数据库
   * @param {Object} dictData 字典数据
   * @returns {Promise<Object>} 同步结果
   */
  async syncDictionaryItem(dictData) {
    try {
      // 查找现有记录
      const existing = await CrmDictionary.findOne({
        where: {
          deployId: dictData.deployId,
          fieldCode: dictData.fieldCode,
          dataId: dictData.dataId
        }
      });

      const syncTime = new Date();
      const syncVersion = `${Date.now()}`;

      if (existing) {
        // 更新现有记录
        await existing.update({
          fieldName: dictData.fieldName,
          fieldDomType: dictData.fieldDomType,
          dataName: dictData.dataName,
          dataValue: dictData.dataValue,
          dataOrder: dictData.dataOrder,
          rawData: dictData.rawData,
          syncStatus: 'synced',
          syncTime: syncTime,
          syncVersion: syncVersion,
          syncError: null,
          isActive: dictData.isActive,
          isDeleted: dictData.isDeleted,
          updatedBy: null // 系统同步
        });

        console.log(`✅ 更新字典项: ${dictData.fieldCode}.${dictData.dataId}`);
        return { action: 'updated', record: existing };

      } else {
        // 创建新记录
        const newRecord = await CrmDictionary.create({
          ...dictData,
          syncStatus: 'synced',
          syncTime: syncTime,
          syncVersion: syncVersion,
          createdBy: null, // 系统同步
          updatedBy: null
        });

        console.log(`✅ 创建字典项: ${dictData.fieldCode}.${dictData.dataId}`);
        return { action: 'created', record: newRecord };
      }

    } catch (error) {
      console.error(`❌ 同步字典项失败: ${dictData.fieldCode}.${dictData.dataId}`, error.message);
      
      // 记录同步错误
      try {
        await CrmDictionary.upsert({
          deployId: dictData.deployId,
          fieldCode: dictData.fieldCode,
          dataId: dictData.dataId,
          fieldName: dictData.fieldName,
          fieldDomType: dictData.fieldDomType,
          dataName: dictData.dataName,
          dataValue: dictData.dataValue,
          dataOrder: dictData.dataOrder,
          rawData: dictData.rawData,
          syncStatus: 'failed',
          syncTime: new Date(),
          syncError: error.message,
          isActive: false
        });
      } catch (upsertError) {
        console.error('❌ 记录同步错误失败:', upsertError.message);
      }

      throw error;
    }
  }

  /**
   * 同步所有部署类型的字典数据
   * @returns {Promise<Object>} 完整同步结果
   */
  async syncAllDictionaries() {
    const totalResult = {
      startTime: new Date(),
      endTime: null,
      deployResults: {},
      summary: {
        totalFields: 0,
        totalSelectFields: 0,
        totalDictionaries: 0,
        totalCreated: 0,
        totalUpdated: 0,
        totalFailed: 0,
        totalErrors: []
      }
    };

    try {
      console.log('🚀 开始完整字典数据同步...');

      // 同步客户字典
      try {
        const customerResult = await this.syncDictionariesByDeployId('customer');
        totalResult.deployResults.customer = customerResult;

        // 累计统计
        totalResult.summary.totalFields += customerResult.totalFields;
        totalResult.summary.totalSelectFields += customerResult.selectFields;
        totalResult.summary.totalDictionaries += customerResult.totalDictionaries;
        totalResult.summary.totalCreated += customerResult.created;
        totalResult.summary.totalUpdated += customerResult.updated;
        totalResult.summary.totalFailed += customerResult.failed;
        totalResult.summary.totalErrors.push(...customerResult.errors);

      } catch (error) {
        console.error('❌ 客户字典同步失败:', error.message);
        totalResult.deployResults.customer = {
          deployId: 'customer',
          error: error.message,
          totalFields: 0,
          selectFields: 0,
          totalDictionaries: 0,
          created: 0,
          updated: 0,
          failed: 1,
          errors: [{ deployId: 'customer', error: error.message }]
        };
        totalResult.summary.totalFailed += 1;
        totalResult.summary.totalErrors.push({ deployId: 'customer', error: error.message });
      }

      // 同步协议字典
      try {
        const contractResult = await this.syncDictionariesByDeployId('contract');
        totalResult.deployResults.contract = contractResult;

        // 累计统计
        totalResult.summary.totalFields += contractResult.totalFields;
        totalResult.summary.totalSelectFields += contractResult.selectFields;
        totalResult.summary.totalDictionaries += contractResult.totalDictionaries;
        totalResult.summary.totalCreated += contractResult.created;
        totalResult.summary.totalUpdated += contractResult.updated;
        totalResult.summary.totalFailed += contractResult.failed;
        totalResult.summary.totalErrors.push(...contractResult.errors);

      } catch (error) {
        console.error('❌ 协议字典同步失败:', error.message);
        totalResult.deployResults.contract = {
          deployId: 'contract',
          error: error.message,
          totalFields: 0,
          selectFields: 0,
          totalDictionaries: 0,
          created: 0,
          updated: 0,
          failed: 1,
          errors: [{ deployId: 'contract', error: error.message }]
        };
        totalResult.summary.totalFailed += 1;
        totalResult.summary.totalErrors.push({ deployId: 'contract', error: error.message });
      }

      totalResult.endTime = new Date();
      const duration = totalResult.endTime - totalResult.startTime;

      console.log('✅ 完整字典数据同步完成');
      console.log(`📊 总体统计: 字段 ${totalResult.summary.totalFields}, SELECT字段 ${totalResult.summary.totalSelectFields}, 字典项 ${totalResult.summary.totalDictionaries}`);
      console.log(`📊 同步结果: 创建 ${totalResult.summary.totalCreated}, 更新 ${totalResult.summary.totalUpdated}, 失败 ${totalResult.summary.totalFailed}`);
      console.log(`⏱️ 耗时: ${Math.round(duration/1000)}秒`);

      return totalResult;

    } catch (error) {
      totalResult.endTime = new Date();
      console.error('❌ 完整字典数据同步失败:', error.message);
      throw new Error(`完整字典数据同步失败: ${error.message}`);
    }
  }

  /**
   * 获取字典数据（按部署类型和字段名称/代码）
   * @param {string} deployId 部署ID
   * @param {string} fieldIdentifier 字段标识符（字段名称或字段代码，可选）
   * @param {boolean} activeOnly 是否只获取活跃数据
   * @param {string} identifierType 标识符类型：'name'(字段名称) 或 'code'(字段代码)，默认'name'
   * @returns {Promise<Array>} 字典数据列表
   */
  async getDictionaries(deployId, fieldIdentifier = null, activeOnly = true, identifierType = 'name') {
    try {
      const whereCondition = {
        deployId: deployId,
        isDeleted: false
      };

      if (fieldIdentifier) {
        if (identifierType === 'name') {
          whereCondition.fieldName = fieldIdentifier;
        } else {
          whereCondition.fieldCode = fieldIdentifier;
        }
      }

      if (activeOnly) {
        whereCondition.isActive = true;
        whereCondition.syncStatus = 'synced';
      }

      const dictionaries = await CrmDictionary.findAll({
        where: whereCondition,
        order: [
          ['fieldCode', 'ASC'],
          ['dataOrder', 'ASC'],
          ['dataName', 'ASC']
        ]
      });

      const fieldDesc = fieldIdentifier ?
        `${identifierType === 'name' ? '字段名称' : '字段代码'}: ${fieldIdentifier}` :
        '全部';
      console.log(`✅ 获取字典数据 - 部署类型: ${deployId}, ${fieldDesc}, 数量: ${dictionaries.length}`);
      return dictionaries;

    } catch (error) {
      const fieldDesc = fieldIdentifier ?
        `${identifierType === 'name' ? '字段名称' : '字段代码'}: ${fieldIdentifier}` :
        '全部';
      console.error(`❌ 获取字典数据失败 - 部署类型: ${deployId}, ${fieldDesc}`, error.message);
      throw new Error(`获取字典数据失败: ${error.message}`);
    }
  }

  /**
   * 获取字典统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getDictionaryStats() {
    try {
      const stats = await CrmDictionary.findAll({
        attributes: [
          'deployId',
          'fieldCode',
          'fieldName',
          [CrmDictionary.sequelize.fn('COUNT', '*'), 'totalItems'],
          [CrmDictionary.sequelize.fn('COUNT', CrmDictionary.sequelize.literal("CASE WHEN sync_status = 'synced' THEN 1 END")), 'syncedItems'],
          [CrmDictionary.sequelize.fn('COUNT', CrmDictionary.sequelize.literal("CASE WHEN sync_status = 'failed' THEN 1 END")), 'failedItems'],
          [CrmDictionary.sequelize.fn('COUNT', CrmDictionary.sequelize.literal("CASE WHEN is_active = 1 THEN 1 END")), 'activeItems'],
          [CrmDictionary.sequelize.fn('MAX', CrmDictionary.sequelize.col('sync_time')), 'lastSyncTime'],
          [CrmDictionary.sequelize.fn('MAX', CrmDictionary.sequelize.col('updated_at')), 'lastUpdated']
        ],
        where: {
          isDeleted: false
        },
        group: ['deployId', 'fieldCode', 'fieldName'],
        order: [['deployId', 'ASC'], ['fieldCode', 'ASC']]
      });

      console.log(`✅ 获取字典统计信息 - 字段数量: ${stats.length}`);
      return stats;

    } catch (error) {
      console.error('❌ 获取字典统计信息失败:', error.message);
      throw new Error(`获取字典统计信息失败: ${error.message}`);
    }
  }

  /**
   * 清理过期或无效的字典数据
   * @param {number} daysOld 清理多少天前的数据
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupOldDictionaries(daysOld = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      console.log(`🧹 开始清理字典数据 - 清理 ${daysOld} 天前的数据`);

      // 软删除过期的失败记录
      const [affectedRows] = await CrmDictionary.update(
        {
          isDeleted: true,
          updatedBy: null // 系统清理
        },
        {
          where: {
            syncStatus: 'failed',
            updatedAt: {
              [Op.lt]: cutoffDate
            },
            isDeleted: false
          }
        }
      );

      console.log(`✅ 字典数据清理完成 - 清理记录数: ${affectedRows}`);

      return {
        cleanedRecords: affectedRows,
        cutoffDate: cutoffDate
      };

    } catch (error) {
      console.error('❌ 清理字典数据失败:', error.message);
      throw new Error(`清理字典数据失败: ${error.message}`);
    }
  }
}

module.exports = CrmDictionaryService;
