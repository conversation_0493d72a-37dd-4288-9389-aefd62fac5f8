/**
 * 合作对接管理服务
 *
 * 功能说明：
 * - 处理合作对接记录的业务逻辑
 * - 提供笔记链接解析功能
 * - 管理定时笔记数据拉取
 * - 处理小红书API数据获取
 *
 * 主要功能：
 * - CRUD操作：创建、查询、更新、删除合作记录
 * - 笔记解析：从小红书链接中提取笔记ID
 * - 定时计算：自动计算定时拉取时间
 * - 数据拉取：调用小红书API获取笔记数据
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const { CooperationManagement, sequelize } = require('../models');
const { Op } = require('sequelize');
const CrmIntegrationService = require('./CrmIntegrationService');
const axios = require('axios');
const CookieManager = require('./CookieManager');
const moment = require('moment');

class CooperationService {
  constructor() {
    // 初始化Cookie管理器
    this.cookieManager = new CookieManager();
    // 初始化CRM集成服务
    this.crmIntegrationService = new CrmIntegrationService();
  }

  /**
   * 创建合作记录
   * @param {Object} data 合作记录数据
   * @param {number} userId 创建用户ID
   * @param {Object} syncOptions CRM同步选项
   * @returns {Promise<Object>} 创建的合作记录
   */
  async createCooperation(data, userId, syncOptions = {}) {
    try {
      // 处理笔记链接解析
      if (data.publishLink) {
        const parseResult = this.parseNoteLink(data.publishLink);
        data.noteId = parseResult.noteId;
        data.noteLinkUpdateTime = new Date().toISOString();
        // 使用实际发布日期计算定时拉取时间，如果没有则使用当前时间
        const baseTime = data.actualPublishDate || new Date().toISOString();
        data.scheduledFetchTime = this.calculateScheduledFetchTime(baseTime);
        // 计算数据登记日期（与定时拉取时间保持一致的日期部分）
        data.dataRegistrationDate = this.calculateDataRegistrationDate(baseTime);
      }

      // 设置创建人
      data.createdBy = userId;
      data.updatedBy = userId;

      // 确保customerPublicSea字段有默认值
      if (!data.customerPublicSea) {
        data.customerPublicSea = 'fd18e0d6b1164e7080f0fa91dc43b0d8';
      }

      // 提取CRM同步选项
      const { syncCreateCustomer, syncCreateAgreement, ...cooperationData } = data;
      const crmSyncOptions = {
        syncCreateCustomer: syncCreateCustomer || syncOptions.syncCreateCustomer,
        syncCreateAgreement: syncCreateAgreement || syncOptions.syncCreateAgreement
      };

      // 创建合作记录
      const cooperation = await CooperationManagement.create(cooperationData);
      console.log(`✅ 合作记录创建成功: ${cooperation.id}`);

      // CRM智能同步处理
      if (crmSyncOptions.syncCreateCustomer || crmSyncOptions.syncCreateAgreement) {
        try {
          const syncOptions = {
            forceCustomerSync: crmSyncOptions.syncCreateCustomer,
            forceAgreementSync: crmSyncOptions.syncCreateAgreement
          };

          // 获取当前用户信息以传递CRM用户ID
          const { User } = require('../models');
          const currentUser = await User.findByPk(userId);
          const effectiveUserId = this.crmIntegrationService.getEffectiveUserId(currentUser);

          // 将enableAgreementModule字段添加到cooperation对象中，以便CRM集成服务可以访问
          const cooperationWithModuleFlag = {
            ...cooperation.toJSON(),
            enableAgreementModule: data.enableAgreementModule
          };

          const syncResult = await this.crmIntegrationService.smartSyncCooperationData(
            cooperationWithModuleFlag,
            syncOptions,
            effectiveUserId
          );

          // 添加同步结果到返回数据
          cooperation.dataValues.crmSyncResult = syncResult;

          if (syncResult.errors.length > 0) {
            console.warn('⚠️ CRM同步部分失败:', syncResult.errors);
          }
        } catch (crmError) {
          console.error('❌ CRM同步失败:', crmError.message);
          // CRM同步失败不影响合作记录创建
          cooperation.dataValues.crmSyncError = crmError.message;
        }
      }

      return cooperation;
    } catch (error) {
      console.error('❌ 创建合作记录失败:', error.message);
      throw error;
    }
  }

  /**
   * 重新创建CRM客户
   * @param {number} cooperationId 合作记录ID
   * @param {number} userId 当前用户ID
   * @returns {Promise<Object>} 创建结果
   */
  async recreateCrmCustomer(cooperationId, userId = null) {
    try {
      const cooperation = await CooperationManagement.findByPk(cooperationId);
      if (!cooperation) {
        throw new Error('合作记录不存在');
      }

      const syncOptions = {
        forceCustomerSync: true,
        forceAgreementSync: true
      };

      // 获取当前用户信息以传递CRM用户ID
      let effectiveUserId = null;
      if (userId) {
        const { User } = require('../models');
        const currentUser = await User.findByPk(userId);
        effectiveUserId = this.crmIntegrationService.getEffectiveUserId(currentUser);
      }

      // 根据现有协议数据推断协议模块状态
      const hasAgreementData =
        cooperation.title ||
        cooperation.cooperationForm ||
        cooperation.publishPlatform ||
        cooperation.cooperationBrand ||
        cooperation.cooperationProduct ||
        cooperation.cooperationAmount ||
        cooperation.publishLink ||
        cooperation.actualPublishDate;

      const cooperationWithModuleFlag = {
        ...cooperation.toJSON(),
        enableAgreementModule: hasAgreementData
      };

      const syncResult = await this.crmIntegrationService.smartSyncCooperationData(
        cooperationWithModuleFlag,
        syncOptions,
        effectiveUserId
      );

      return {
        success: syncResult.customerSynced,
        customerId: syncResult.customerId,
        message: syncResult.customerSynced ? 'CRM客户创建成功' : 'CRM客户创建失败',
        errors: syncResult.errors
      };
    } catch (error) {
      console.error('❌ 重新创建CRM客户失败:', error.message);
      throw error;
    }
  }

  /**
   * 重新创建CRM协议
   * @param {number} cooperationId 合作记录ID
   * @param {number} userId 当前用户ID
   * @returns {Promise<Object>} 创建结果
   */
  async recreateCrmAgreement(cooperationId, userId = null) {
    try {
      const cooperation = await CooperationManagement.findByPk(cooperationId);
      if (!cooperation) {
        throw new Error('合作记录不存在');
      }

      if (!cooperation.externalCustomerId) {
        throw new Error('必须先创建CRM客户');
      }

      const syncOptions = {
        forceCustomerSync: false,
        forceAgreementSync: true
      };

      // 获取当前用户信息以传递CRM用户ID
      let effectiveUserId = null;
      if (userId) {
        const { User } = require('../models');
        const currentUser = await User.findByPk(userId);
        effectiveUserId = this.crmIntegrationService.getEffectiveUserId(currentUser);
      }

      // 根据现有协议数据推断协议模块状态
      const hasAgreementData =
        cooperation.title ||
        cooperation.cooperationForm ||
        cooperation.publishPlatform ||
        cooperation.cooperationBrand ||
        cooperation.cooperationProduct ||
        cooperation.cooperationAmount ||
        cooperation.publishLink ||
        cooperation.actualPublishDate;

      const cooperationWithModuleFlag = {
        ...cooperation.toJSON(),
        enableAgreementModule: hasAgreementData
      };

      const syncResult = await this.crmIntegrationService.smartSyncCooperationData(
        cooperationWithModuleFlag,
        syncOptions,
        effectiveUserId
      );

      return {
        success: syncResult.agreementSynced,
        agreementId: syncResult.agreementId,
        message: syncResult.agreementSynced ? 'CRM协议创建成功' : 'CRM协议创建失败',
        errors: syncResult.errors
      };
    } catch (error) {
      console.error('❌ 重新创建CRM协议失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取合作记录列表
   * @param {Object} options 查询选项
   * @returns {Promise<Object>} 分页的合作记录列表
   */
  async getCooperationList(options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        // 排序参数
        sortBy = 'externalCustomerId',
        sortOrder = 'DESC',
        // 新字段筛选
        customerName,
        seedingPlatform,
        cooperationBrand,
        crmLinkStatus,
        // 原有字段筛选（保持兼容）
        platform,
        workProgress,
        cooperationMonth,
        responsiblePerson,
        bloggerName,
        keyword
      } = options;

      // console.log('📋 合作记录筛选参数:', {
      //   customerName,
      //   seedingPlatform,
      //   cooperationBrand,
      //   crmLinkStatus,
      //   platform,
      //   workProgress,
      //   cooperationMonth,
      //   responsiblePerson,
      //   bloggerName,
      //   keyword
      // });

      const where = {};

      // 新字段筛选条件
      if (customerName) {
        where.customerName = { [Op.like]: `%${customerName}%` };
      }
      if (seedingPlatform) {
        where.seedingPlatform = { [Op.like]: `%${seedingPlatform}%` };
      }
      if (cooperationBrand) {
        where.cooperationBrand = { [Op.like]: `%${cooperationBrand}%` };
      }
      if (crmLinkStatus) {
        where.crmLinkStatus = crmLinkStatus;
      }

      // 原有字段筛选条件（保持兼容）
      if (platform) where.platform = platform;
      if (workProgress) where.workProgress = workProgress;
      if (cooperationMonth) where.cooperationMonth = cooperationMonth;
      if (responsiblePerson) where.responsiblePerson = { [Op.like]: `%${responsiblePerson}%` };
      if (bloggerName) where.bloggerName = { [Op.like]: `%${bloggerName}%` };

      // 关键词搜索（扩展到新字段）
      if (keyword) {
        where[Op.or] = [
          // 原有搜索字段
          { bloggerName: { [Op.like]: `%${keyword}%` } },
          { responsiblePerson: { [Op.like]: `%${keyword}%` } },
          { contentDirection: { [Op.like]: `%${keyword}%` } },
          { cooperationProduct: { [Op.like]: `%${keyword}%` } },
          // 新增搜索字段
          { customerName: { [Op.like]: `%${keyword}%` } },
          { title: { [Op.like]: `%${keyword}%` } },
          { cooperationBrand: { [Op.like]: `%${keyword}%` } },
          { seedingPlatform: { [Op.like]: `%${keyword}%` } }
        ];
      }

      console.log('🔍 构建的查询条件:', JSON.stringify(where, null, 2));

      // 构建排序逻辑
      let orderClause;
      if (sortBy === 'externalCustomerId') {
        // 按CRM客户ID排序时，空值（NULL或空字符串）优先显示
        orderClause = [
          [
            sequelize.literal('CASE WHEN external_customer_id IS NULL OR external_customer_id = "" THEN 0 ELSE 1 END'),
            'ASC'
          ],
          // ['externalCustomerId', sortOrder.toUpperCase()],
          ['createdAt', 'DESC'] // 次要排序
        ];
      } else {
        // 其他字段排序
        orderClause = [
          [sortBy, sortOrder.toUpperCase()],
          ['createdAt', 'DESC'] // 次要排序
        ];
      }

      console.log('📊 排序配置:', { sortBy, sortOrder, orderClause });

      const result = await CooperationManagement.findAndCountAll({
        where,
        order: orderClause,
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit)
      });

      return {
        cooperations: result.rows,
        pagination: {
          total: result.count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(result.count / parseInt(limit))
        }
      };
    } catch (error) {
      console.error('❌ 获取合作记录列表失败:', error.message);
      throw error;
    }
  }

  /**
   * 根据ID获取合作记录
   * @param {number} id 合作记录ID
   * @returns {Promise<Object>} 合作记录详情
   */
  async getCooperationById(id) {
    try {
      const cooperation = await CooperationManagement.findByPk(id);
      if (!cooperation) {
        throw new Error('合作记录不存在');
      }
      return cooperation;
    } catch (error) {
      console.error(`❌ 获取合作记录失败: ${id}`, error.message);
      throw error;
    }
  }

  /**
   * 更新合作记录
   * @param {number} id 合作记录ID
   * @param {Object} data 更新数据
   * @param {number} userId 更新用户ID
   * @returns {Promise<Object>} 更新后的合作记录
   */
  async updateCooperation(id, data, userId) {
    try {
      const cooperation = await this.getCooperationById(id);

      // 保存原始数据用于变化检测
      const originalData = cooperation.toJSON();

      // 如果更新了发布链接，重新解析
      if (data.publishLink && data.publishLink !== cooperation.publishLink) {
        const parseResult = this.parseNoteLink(data.publishLink);
        data.noteId = parseResult.noteId;
        data.noteLinkUpdateTime = new Date().toISOString();
        // 使用实际发布日期计算定时拉取时间，如果没有则使用当前时间
        const baseTime = data.actualPublishDate || cooperation.actualPublishDate || new Date().toISOString();
        data.scheduledFetchTime = this.calculateScheduledFetchTime(baseTime);
        // 计算数据登记日期（与定时拉取时间保持一致的日期部分）
        data.dataRegistrationDate = this.calculateDataRegistrationDate(baseTime);
        // 重置数据拉取状态
        data.dataFetchStatus = 'pending';
        data.dataFetchTime = null;
        data.dataFetchError = null;
      }

      // 如果更新了实际发布日期且已有笔记ID，重新计算定时拉取时间和数据登记日期
      if (
        data.actualPublishDate &&
        data.actualPublishDate !== cooperation.actualPublishDate &&
        (cooperation.noteId || data.noteId)
      ) {
        data.scheduledFetchTime = this.calculateScheduledFetchTime(data.actualPublishDate);
        // 重新计算数据登记日期
        data.dataRegistrationDate = this.calculateDataRegistrationDate(data.actualPublishDate);
        // 如果数据还未拉取，重置状态
        if (cooperation.dataFetchStatus === 'pending' || cooperation.dataFetchStatus === 'failed') {
          data.dataFetchStatus = 'pending';
          data.dataFetchTime = null;
          data.dataFetchError = null;
        }
      }

      data.updatedBy = userId;
      await cooperation.update(data);

      console.log(`✅ 合作记录更新成功: ${id}`);

      // CRM智能同步处理
      try {
        const changedFields = this.detectChangedFields(originalData, data);

        if (changedFields.length > 0) {
          console.log(`🔍 检测到字段变化: ${changedFields.join(', ')}`);

          // 异步执行CRM智能同步，不阻塞更新流程
          this.performAsyncCrmSync(cooperation, changedFields).catch(error => {
            console.error(`❌ CRM异步同步失败 (ID: ${id}):`, error.message);
          });
        } else {
          console.log(`📝 未检测到字段变化，跳过CRM同步 (ID: ${id})`);
        }
      } catch (crmError) {
        console.error(`❌ CRM同步检测失败 (ID: ${id}):`, crmError.message);
        // CRM同步失败不影响合作记录更新
      }

      return cooperation;
    } catch (error) {
      console.error(`❌ 更新合作记录失败: ${id}`, error.message);
      throw error;
    }
  }

  /**
   * 删除合作记录
   * @param {number} id 合作记录ID
   * @returns {Promise<boolean>} 删除结果
   */
  async deleteCooperation(id) {
    try {
      const cooperation = await this.getCooperationById(id);
      await cooperation.destroy();
      console.log(`✅ 合作记录删除成功: ${id}`);
      return true;
    } catch (error) {
      console.error(`❌ 删除合作记录失败: ${id}`, error.message);
      throw error;
    }
  }

  /**
   * 解析笔记链接，提取笔记ID
   * @param {string} noteLink 笔记链接
   * @returns {Object} 解析结果
   */
  parseNoteLink(noteLink) {
    try {
      if (!noteLink) {
        throw new Error('笔记链接不能为空');
      }

      // 小红书笔记链接格式：https://www.xiaohongshu.com/explore/686e38b60000000011001e15?xsec_token=...
      const xiaohongshuRegex = /xiaohongshu\.com\/explore\/([a-f0-9]+)/i;
      const match = noteLink.match(xiaohongshuRegex);

      if (match && match[1]) {
        return {
          noteId: match[1],
          platform: 'xiaohongshu',
          isValid: true
        };
      }

      throw new Error('无法解析笔记链接，请确认链接格式正确');
    } catch (error) {
      console.error('❌ 笔记链接解析失败:', error.message);
      throw error;
    }
  }

  /**
   * 计算定时拉取时间（发布日期+10天）
   * @param {string} baseTime 基准时间（实际发布日期或更新时间）
   * @returns {string} 定时拉取时间
   */
  calculateScheduledFetchTime(baseTime) {
    // 使用 moment.js 计算基准日期+10天的开始时间
    const fetchTime = moment(baseTime).add(10, 'days').startOf('day').toDate();
    return fetchTime.toISOString();
  }

  /**
   * 计算数据登记日期（发布日期+10天）
   * @param {string} baseTime 基准时间（实际发布日期或更新时间）
   * @returns {string} 数据登记日期（YYYY/MM/DD格式）
   */
  calculateDataRegistrationDate(baseTime) {
    const registrationDate = new Date(baseTime);
    registrationDate.setDate(registrationDate.getDate() + 10); // 加10天
    // 格式化为 YYYY/MM/DD 格式
    const year = registrationDate.getFullYear();
    const month = String(registrationDate.getMonth() + 1).padStart(2, '0');
    const day = String(registrationDate.getDate()).padStart(2, '0');
    return `${year}/${month}/${day}`;
  }

  /**
   * 获取需要拉取数据的合作记录（当天或之前的记录）
   * @returns {Promise<Array>} 需要拉取数据的记录列表
   */
  async getRecordsForDataFetch() {
    try {
      // 使用 moment.js 获取今天结束时间（23:59:59）
      const endOfToday = moment().endOf('day').toDate();

      const records = await CooperationManagement.findAll({
        where: {
          scheduledFetchTime: {
            [Op.lte]: endOfToday // 只要是今天或之前的日期都可以拉取
          },
          dataFetchStatus: 'pending',
          noteId: {
            [Op.ne]: null
          }
        },
        order: [['scheduledFetchTime', 'ASC']]
      });

      console.log(`📋 找到 ${records.length} 条需要拉取数据的记录`);
      return records;
    } catch (error) {
      console.error('❌ 获取待拉取数据记录失败:', error.message);
      throw error;
    }
  }

  /**
   * 拉取笔记数据
   * @param {Object} cooperation 合作记录
   * @returns {Promise<Object>} 拉取结果
   */
  async fetchNoteData(cooperation) {
    try {
      console.log(`🔄 开始拉取笔记数据: ${cooperation.noteId} (平台: ${cooperation.platform})`);

      // 检查平台是否支持
      if (cooperation.platform !== 'xiaohongshu') {
        const errorMsg = `暂不支持 ${cooperation.platform} 平台的数据拉取`;
        await cooperation.update({
          dataFetchStatus: 'failed',
          dataFetchTime: new Date().toISOString(),
          dataFetchError: errorMsg
        });
        return { success: false, error: errorMsg };
      }

      // 获取可用的Cookie
      const cookieInfo = await this.cookieManager.getAvailableCookie('xiaohongshu');
      if (!cookieInfo) {
        const errorMsg = '没有可用的小红书Cookie，请先添加有效的Cookie后再执行数据拉取';
        console.error(`❌ ${errorMsg}`);
        await cooperation.update({
          dataFetchStatus: 'failed',
          dataFetchTime: new Date().toISOString(),
          dataFetchError: errorMsg
        });
        return { success: false, error: errorMsg };
      }

      console.log(`🍪 使用Cookie: ${cookieInfo.accountName} (优先级: ${cookieInfo.priority})`);

      // 更新状态为拉取中
      await cooperation.update({
        dataFetchStatus: 'fetching',
        dataFetchTime: new Date().toISOString()
      });

      // 使用Cookie轮换机制拉取数据
      const result = await this.fetchNoteDataWithCookieRotation(cooperation, cookieInfo);
      return result;
    } catch (error) {
      console.error(`❌ 拉取笔记数据失败: ${cooperation.noteId}`, error.message);

      // 更新失败状态
      await cooperation.update({
        dataFetchStatus: 'failed',
        dataFetchTime: new Date().toISOString(),
        dataFetchError: error.message
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * 使用Cookie轮换机制拉取笔记数据
   * @param {Object} cooperation 合作记录
   * @param {Object} cookieInfo Cookie信息
   * @returns {Promise<Object>} 拉取结果
   */
  async fetchNoteDataWithCookieRotation(cooperation, cookieInfo) {
    const maxRetries = 3;
    let currentCookie = cookieInfo;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🚀 尝试拉取数据 (第${attempt}次): 使用Cookie ${currentCookie.accountName}`);

        // 调用小红书API
        const apiUrl = `https://pgy.xiaohongshu.com/api/solar/note/${cooperation.noteId}/detail?bizCode=`;
        const response = await axios.get(apiUrl, {
          timeout: 30000,
          headers: {
            'User-Agent':
              currentCookie.userAgent ||
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            Cookie: currentCookie.cookieString,
            Accept: 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            Connection: 'keep-alive',
            Referer: 'https://www.xiaohongshu.com/',
            Origin: 'https://www.xiaohongshu.com'
          }
        });

        // 检查响应状态
        if (response.status === 200 && response.data) {
          // 检查是否是认证错误
          if (this.isAuthError(response.data)) {
            console.warn(`🔄 检测到认证错误 (Cookie: ${currentCookie.accountName}): ${JSON.stringify(response.data)}`);
            await this.handleCookieError(currentCookie.id, '请求返回认证错误');

            // 尝试获取新的Cookie
            const newCookie = await this.cookieManager.getAvailableCookie('xiaohongshu');
            if (newCookie && newCookie.id !== currentCookie.id) {
              currentCookie = newCookie;
              console.log(`🔄 切换到新Cookie: ${currentCookie.accountName}`);
              continue; // 重试
            } else {
              throw new Error('没有更多可用的Cookie');
            }
          }

          // 检查数据格式
          if (response.data.data) {
            const noteData = response.data.data;

            // 提取关键指标
            const updateData = {
              noteData: response.data,
              dataFetchStatus: 'success',
              dataFetchTime: new Date().toISOString(),
              dataFetchError: null
            };

            if (noteData.impNum !== undefined) updateData.impNum = noteData.impNum;
            if (noteData.readNum !== undefined) updateData.viewCount = noteData.readNum; //阅读量
            if (noteData.likeNum !== undefined) updateData.likeCount = noteData.likeNum; //点赞量
            if (noteData.favNum !== undefined) updateData.collectCount = noteData.favNum; //收藏量
            if (noteData.cmtNum !== undefined) updateData.commentCount = noteData.cmtNum; //评论量
            if (noteData.shareNum !== undefined) updateData.shareNum = noteData.shareNum;
            if (noteData.followCnt !== undefined) updateData.followCnt = noteData.followCnt;

            await cooperation.update(updateData);

            console.log(`✅ 笔记数据拉取成功: ${cooperation.noteId} (使用Cookie: ${currentCookie.accountName})`);
            // todo
            // 这里拉取完可能要去客户的CRM同步一下笔记数据 只更新笔记数据
            // 异步执行CRM智能同步，不阻塞更新流程
            this.performAsyncCrmSync(cooperation, ['viewCount', 'likeCount', 'collectCount', 'commentCount']).catch(
              error => {
                console.error(`❌ CRM异步同步失败 (ID: ${id}):`, error.message);
              }
            );

            return { success: true, data: noteData, cookieUsed: currentCookie.accountName };
          } else {
            throw new Error('API返回数据格式异常');
          }
        } else {
          throw new Error(`API返回状态异常: ${response.status}`);
        }
      } catch (error) {
        lastError = error;
        const errorDetails = error.response ? `${error.response.status} ${error.response.statusText}` : error.message;
        console.warn(`请求失败 (尝试 ${attempt}/${maxRetries}): ${errorDetails}`);

        // 检查是否是网络错误或认证错误
        if (this.isNetworkError(error) || this.isHttpAuthError(error)) {
          console.warn(`🔄 检测到网络/认证错误，尝试切换Cookie...`);

          // 如果是认证错误，标记当前Cookie为过期
          if (this.isHttpAuthError(error)) {
            await this.handleCookieError(currentCookie.id, `HTTP认证错误: ${error.response?.status}`);
          }

          // 尝试获取新的Cookie
          const newCookie = await this.cookieManager.getAvailableCookie('xiaohongshu');
          if (newCookie && newCookie.id !== currentCookie.id) {
            currentCookie = newCookie;
            console.log(`🔄 切换到新Cookie: ${currentCookie.accountName}`);
            continue; // 重试
          }
        }

        // 如果还有重试机会，等待后重试
        if (attempt < maxRetries) {
          const retryDelay = 2000 * attempt; // 递增延迟
          console.log(`⏳ ${retryDelay}ms 后重试...`);
          await this.delay(retryDelay);
        }
      }
    }

    // 所有重试都失败了
    const errorMsg = `数据拉取失败，已重试${maxRetries}次: ${lastError.message}`;
    await cooperation.update({
      dataFetchStatus: 'failed',
      dataFetchTime: new Date().toISOString(),
      dataFetchError: errorMsg
    });

    return { success: false, error: errorMsg };
  }

  /**
   * 检查是否是认证错误（API响应级别）
   * @param {Object} responseData 响应数据
   * @returns {boolean} 是否是认证错误
   */
  isAuthError(responseData) {
    // 小红书API的认证错误特征
    const authErrorCodes = [-1, 401, 403, 10001, 10002];
    const authErrorMessages = ['登录', '认证', '权限', 'login', 'auth', 'permission'];

    if (authErrorCodes.includes(responseData?.code) || authErrorCodes.includes(responseData?.status_code)) {
      return true;
    }

    const message = responseData?.message || responseData?.msg || responseData?.status_msg || '';
    return authErrorMessages.some(keyword => message.toLowerCase().includes(keyword.toLowerCase()));
  }

  /**
   * 检查是否是HTTP认证错误
   * @param {Error} error 错误对象
   * @returns {boolean} 是否是HTTP认证错误
   */
  isHttpAuthError(error) {
    return error.response && (error.response.status === 401 || error.response.status === 403);
  }

  /**
   * 检查是否是网络错误
   * @param {Error} error 错误对象
   * @returns {boolean} 是否是网络错误
   */
  isNetworkError(error) {
    return (
      error.code === 'ECONNRESET' ||
      error.code === 'ETIMEDOUT' ||
      error.code === 'ENOTFOUND' ||
      error.message.includes('timeout') ||
      error.message.includes('Network Error') ||
      error.message.includes('connect ECONNREFUSED')
    );
  }

  /**
   * 处理Cookie错误
   * @param {number} cookieId Cookie ID
   * @param {string} reason 错误原因
   */
  async handleCookieError(cookieId, reason) {
    try {
      await this.cookieManager.markCookieAsExpired(cookieId, reason);
      console.warn(`⚠️ Cookie已标记为过期: ID ${cookieId}, 原因: ${reason}`);
    } catch (error) {
      console.error('标记Cookie为过期时出错:', error.message);
    }
  }

  /**
   * 获取笔记数据拉取列表
   * @param {Object} options 查询选项
   * @returns {Promise<Object>} 任务列表和分页信息
   */
  async getDataFetchTasks(options = {}) {
    try {
      const { status, customerName, dateFrom, dateTo, page = 1, limit = 20 } = options;

      // 构建查询条件
      const whereCondition = {
        // 移除noteId限制，显示所有合作记录
        // noteId: {
        //   [Op.ne]: null
        // }
        dataRegistrationDate: {
          [Op.ne]: null
        }
      };

      // 状态筛选
      if (status) {
        whereCondition.dataFetchStatus = status;
      }

      // 客户名称搜索
      if (customerName) {
        whereCondition.customerName = {
          [Op.like]: `%${customerName}%`
        };
      }

      // 数据登记日期范围筛选
      if (dateFrom || dateTo) {
        whereCondition.dataRegistrationDate = {};
        if (dateFrom) {
          whereCondition.dataRegistrationDate[Op.gte] = dateFrom;
        }
        if (dateTo) {
          whereCondition.dataRegistrationDate[Op.lte] = dateTo;
        }
      }

      // 查询任务列表
      const tasks = await CooperationManagement.findAll({
        where: whereCondition,
        attributes: [
          'id',
          'customerName',
          'publishLink',
          'noteId',
          'platform',
          'dataRegistrationDate',
          'dataFetchStatus',
          'dataFetchTime',
          'dataFetchError',
          'scheduledFetchTime',
          'impNum',
          'likeNum',
          'favNum',
          'cmtNum',
          'readNum',
          'shareNum',
          'viewCount',
          'likeCount',
          'collectCount',
          'commentCount',
          'createdAt'
        ],
        order: [
          // 优先显示有noteId的记录
          [sequelize.literal('CASE WHEN noteId IS NOT NULL THEN 0 ELSE 1 END'), 'ASC'],
          ['dataRegistrationDate', 'ASC'],
          ['id', 'DESC'] // 添加id排序确保分页稳定
        ],
        limit: limit,
        offset: (page - 1) * limit,
        raw: true // 返回普通对象而不是模型实例
      });

      // 获取总数
      const totalCount = await CooperationManagement.count({ where: whereCondition });

      return {
        tasks,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      };
    } catch (error) {
      console.error('❌ 获取笔记数据拉取列表失败:', error.message);
      throw error;
    }
  }

  /**
   * 批量拉取笔记数据
   * @param {Array} ids 合作记录ID列表
   * @returns {Promise<Object>} 批量拉取结果
   */
  async batchFetchNoteData(ids) {
    try {
      console.log(`🔄 开始批量拉取笔记数据，共 ${ids.length} 条记录`);

      // 获取记录
      const cooperations = await CooperationManagement.findAll({
        where: {
          id: {
            [Op.in]: ids
          },
          noteId: {
            [Op.ne]: null
          }
        }
      });

      if (cooperations.length === 0) {
        return { success: false, error: '没有找到有效的记录' };
      }

      const results = {
        total: cooperations.length,
        success: 0,
        failed: 0,
        processing: 0,
        details: []
      };

      // 并发拉取数据（限制并发数）
      const concurrency = 3; // 最大并发数
      const chunks = [];
      for (let i = 0; i < cooperations.length; i += concurrency) {
        chunks.push(cooperations.slice(i, i + concurrency));
      }

      for (const chunk of chunks) {
        const promises = chunk.map(async cooperation => {
          try {
            const result = await this.fetchNoteData(cooperation);
            const detail = {
              id: cooperation.id,
              customerName: cooperation.customerName,
              noteId: cooperation.noteId,
              success: result.success,
              error: result.error || null
            };

            if (result.success) {
              results.success++;
            } else {
              results.failed++;
            }

            results.details.push(detail);
            return detail;
          } catch (error) {
            const detail = {
              id: cooperation.id,
              customerName: cooperation.customerName,
              noteId: cooperation.noteId,
              success: false,
              error: error.message
            };
            results.failed++;
            results.details.push(detail);
            return detail;
          }
        });

        await Promise.all(promises);

        // 批次间延迟，避免过于频繁的请求
        if (chunks.indexOf(chunk) < chunks.length - 1) {
          await this.delay(1000);
        }
      }

      console.log(`✅ 批量拉取完成: 成功 ${results.success}, 失败 ${results.failed}`);
      return { success: true, data: results };
    } catch (error) {
      console.error('❌ 批量拉取笔记数据失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取笔记数据拉取统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getDataFetchTaskStats() {
    try {
      const today = moment().format('YYYY-MM-DD');
      const endOfToday = moment().endOf('day').toDate();

      // 基础统计（包含所有合作记录）
      const totalTasks = await CooperationManagement.count();

      const pendingTasks = await CooperationManagement.count({
        where: {
          dataFetchStatus: 'pending'
        }
      });

      const fetchingTasks = await CooperationManagement.count({
        where: {
          dataFetchStatus: 'fetching'
        }
      });

      const successTasks = await CooperationManagement.count({
        where: {
          dataFetchStatus: 'success'
        }
      });

      const failedTasks = await CooperationManagement.count({
        where: {
          dataFetchStatus: 'failed'
        }
      });

      // 今日可执行任务
      const todayExecutableTasks = await CooperationManagement.count({
        where: {
          dataRegistrationDate: { [Op.lte]: today },
          dataFetchStatus: 'pending'
        }
      });

      // 今日已执行任务
      const todayExecutedTasks = await CooperationManagement.count({
        where: {
          dataFetchTime: { [Op.gte]: moment().startOf('day').toISOString() }
        }
      });

      return {
        total: totalTasks,
        pending: pendingTasks,
        fetching: fetchingTasks,
        success: successTasks,
        failed: failedTasks,
        todayExecutable: todayExecutableTasks,
        todayExecuted: todayExecutedTasks,
        successRate: totalTasks > 0 ? ((successTasks / totalTasks) * 100).toFixed(1) : 0
      };
    } catch (error) {
      console.error('❌ 获取任务统计信息失败:', error.message);
      throw error;
    }
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 检测字段变化
   * @param {Object} originalData 原始数据
   * @param {Object} newData 新数据
   * @returns {Array} 变化的字段列表
   */
  detectChangedFields(originalData, newData) {
    const changedFields = [];

    // 客户相关字段
    const customerFields = [
      'customerName',
      'customerHomepage',
      'customerPublicSea',
      'seedingPlatform',
      'bloggerFansCount',
      'influencerPlatformId',
      'bloggerWechatAndNotes',
      'starMapId',
      'receiverAddress'
    ];

    // 协议相关字段
    const agreementFields = [
      'title',
      'cooperationForm',
      'cooperationBrand',
      'cooperationProduct',
      'cooperationAmount',
      'scheduledPublishTime',
      'cooperationNotes',
      'publishPlatform',
      'actualPublishDate',
      'publishLink',
      'responsiblePerson'
    ];

    const allFields = [...customerFields, ...agreementFields];

    for (const field of allFields) {
      if (newData.hasOwnProperty(field) && originalData[field] !== newData[field]) {
        changedFields.push(field);
      }
    }

    return changedFields;
  }

  /**
   * 异步执行CRM智能同步
   * @param {Object} cooperation 合作记录对象
   * @param {Array} changedFields 变化的字段列表
   * @param {number} userId 当前用户ID（可选）
   * @returns {Promise} Promise对象
   */
  async performAsyncCrmSync(cooperation, changedFields, userId = null) {
    try {
      console.log(`🔄 开始CRM智能同步 (ID: ${cooperation.id}):`, changedFields);

      // 获取当前用户信息以传递CRM用户ID
      let effectiveUserId = null;
      if (userId) {
        const { User } = require('../models');
        const currentUser = await User.findByPk(userId);
        effectiveUserId = this.crmIntegrationService.getEffectiveUserId(currentUser);
      }

      // 根据现有协议数据推断协议模块状态
      const cooperationWithModuleFlag = {
        ...cooperation.toJSON()
      };

      const syncResult = await this.crmIntegrationService.smartSyncCooperationData(
        cooperationWithModuleFlag,
        {
          changedFields: changedFields
        },
        effectiveUserId
      );

      console.log(`✅ CRM同步成功 (ID: ${cooperation.id}):`, {
        customerSynced: syncResult.customerSynced,
        agreementSynced: syncResult.agreementSynced,
        customerId: syncResult.customerId,
        agreementId: syncResult.agreementId
      });

      if (syncResult.errors && syncResult.errors.length > 0) {
        console.warn(`⚠️ CRM同步部分失败 (ID: ${cooperation.id}):`, syncResult.errors);
      }

      // 如果有同步成功的数据，可以进行数据回写
      if (syncResult.customerSynced || syncResult.agreementSynced) {
        console.log(`📝 CRM数据同步完成 (ID: ${cooperation.id})`);
      }
    } catch (error) {
      console.error(`❌ CRM智能同步失败 (ID: ${cooperation.id}):`, error.message);
      throw error;
    }
  }
}

module.exports = new CooperationService();
