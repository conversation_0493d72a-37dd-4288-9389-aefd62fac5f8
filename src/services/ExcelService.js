/**
 * Excel文件生成服务
 * 提供通用的Excel文件生成、管理和下载功能
 */

const XLSX = require('xlsx');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class ExcelService {
  constructor() {
    this.exportDir = path.join(process.cwd(), 'public', 'exports');
    this.baseUrl = process.env.BASE_URL || 'http://localhost:3000';
    this.maxFileAge = 24 * 60 * 60 * 1000; // 24小时，毫秒
  }

  /**
   * 初始化服务，确保导出目录存在
   */
  async initialize() {
    try {
      await fs.access(this.exportDir);
    } catch (error) {
      await fs.mkdir(this.exportDir, { recursive: true });
      console.log('✅ Excel导出目录创建成功:', this.exportDir);
    }
  }

  /**
   * 生成Excel文件
   * @param {Object} options 配置选项
   * @param {Array|Object} options.data 数据源
   * @param {string} options.fileName 文件名（可选）
   * @param {Array} options.sheets 工作表配置（可选）
   * @param {Object} options.formatting 格式化选项（可选）
   * @returns {Promise<Object>} 生成结果
   */
  async generateExcel(options) {
    try {
      const { data, fileName, sheets, formatting = {} } = options;

      // 验证数据（多工作表模式时data可以为null）
      if (!data && (!sheets || !Array.isArray(sheets) || sheets.length === 0)) {
        throw new Error('数据源不能为空');
      }

      // 生成唯一文件名
      const finalFileName = this.generateFileName(fileName);
      const filePath = path.join(this.exportDir, finalFileName);

      // 创建工作簿
      const workbook = XLSX.utils.book_new();

      // 处理多工作表或单工作表
      if (sheets && Array.isArray(sheets)) {
        // 多工作表模式
        for (const sheet of sheets) {
          await this.addWorksheet(workbook, sheet, formatting);
        }
      } else {
        // 单工作表模式
        const sheetConfig = {
          name: '数据',
          data: Array.isArray(data) ? data : [data],
          headers: this.extractHeaders(Array.isArray(data) ? data : [data])
        };
        await this.addWorksheet(workbook, sheetConfig, formatting);
      }

      // 写入文件
      await this.writeWorkbook(workbook, filePath);

      // 生成下载链接
      const downloadUrl = `${this.baseUrl}/exports/${finalFileName}`;

      // 记录文件信息用于后续清理
      await this.recordFileInfo(finalFileName);

      return {
        success: true,
        fileName: finalFileName,
        downloadUrl,
        filePath,
        fileSize: await this.getFileSize(filePath)
      };
    } catch (error) {
      console.error('Excel文件生成失败:', error);
      throw new Error(`Excel文件生成失败: ${error.message}`);
    }
  }

  /**
   * 添加工作表到工作簿
   * @param {Object} workbook 工作簿对象
   * @param {Object} sheetConfig 工作表配置
   * @param {Object} formatting 格式化选项
   */
  async addWorksheet(workbook, sheetConfig, formatting) {
    const { name = '数据', data = [], headers, columnWidths } = sheetConfig;

    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(data);

    // 设置列宽
    if (columnWidths) {
      worksheet['!cols'] = columnWidths;
    } else if (headers) {
      // 自动计算列宽
      worksheet['!cols'] = this.calculateColumnWidths(headers, data);
    }

    // 应用格式化
    if (formatting.headerStyle) {
      this.applyHeaderStyle(worksheet, headers);
    }

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, name);
  }

  /**
   * 写入工作簿到文件
   * @param {Object} workbook 工作簿对象
   * @param {string} filePath 文件路径
   */
  async writeWorkbook(workbook, filePath) {
    return new Promise((resolve, reject) => {
      try {
        XLSX.writeFile(workbook, filePath, {
          bookType: 'xlsx',
          type: 'file'
        });
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 提取数据头部信息
   * @param {Array} data 数据数组
   * @returns {Array} 头部字段数组
   */
  extractHeaders(data) {
    if (!Array.isArray(data) || data.length === 0) {
      return [];
    }

    const firstRow = data[0];
    if (typeof firstRow === 'object' && firstRow !== null) {
      return Object.keys(firstRow);
    }

    return [];
  }

  /**
   * 计算列宽
   * @param {Array} headers 头部字段
   * @param {Array} data 数据
   * @returns {Array} 列宽配置
   */
  calculateColumnWidths(headers, data) {
    return headers.map(header => {
      // 基于头部长度和数据内容长度计算
      let maxLength = header.length;

      // 检查前几行数据的长度
      for (let i = 0; i < Math.min(data.length, 10); i++) {
        const cellValue = data[i][header];
        if (cellValue !== null && cellValue !== undefined) {
          const cellLength = String(cellValue).length;
          maxLength = Math.max(maxLength, cellLength);
        }
      }

      // 设置合理的列宽范围
      const width = Math.min(Math.max(maxLength + 2, 8), 50);
      return { wch: width };
    });
  }

  /**
   * 应用头部样式
   * @param {Object} worksheet 工作表对象
   * @param {Array} headers 头部字段
   */
  applyHeaderStyle(worksheet, headers) {
    // 这里可以添加头部样式设置
    // xlsx库的样式功能有限，如需要复杂样式可考虑使用exceljs
  }

  /**
   * 生成唯一文件名
   * @param {string} baseName 基础文件名
   * @returns {string} 唯一文件名
   */
  generateFileName(baseName) {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const uuid = uuidv4().slice(0, 8);
    const name = baseName ? baseName.replace(/\.[^/.]+$/, '') : 'export';
    return `${name}_${timestamp}_${uuid}.xlsx`;
  }

  /**
   * 获取文件大小
   * @param {string} filePath 文件路径
   * @returns {Promise<number>} 文件大小（字节）
   */
  async getFileSize(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 记录文件信息
   * @param {string} fileName 文件名
   */
  async recordFileInfo(fileName) {
    // 这里可以记录到数据库或文件，用于后续清理
    // 简单实现：记录到内存中
    if (!this.fileRecords) {
      this.fileRecords = new Map();
    }
    this.fileRecords.set(fileName, {
      createdAt: new Date(),
      accessed: false
    });
  }

  /**
   * 清理过期文件
   */
  async cleanupExpiredFiles() {
    try {
      const files = await fs.readdir(this.exportDir);
      const now = Date.now();

      for (const file of files) {
        if (!file.endsWith('.xlsx')) continue;

        const filePath = path.join(this.exportDir, file);
        const stats = await fs.stat(filePath);
        const fileAge = now - stats.mtime.getTime();

        if (fileAge > this.maxFileAge) {
          await fs.unlink(filePath);
          console.log(`🗑️ 清理过期Excel文件: ${file}`);

          // 从记录中移除
          if (this.fileRecords) {
            this.fileRecords.delete(file);
          }
        }
      }
    } catch (error) {
      console.error('清理过期文件失败:', error);
    }
  }

  /**
   * 启动定期清理任务
   */
  startCleanupSchedule() {
    // 每小时执行一次清理
    setInterval(() => {
      this.cleanupExpiredFiles();
    }, 60 * 60 * 1000);

    console.log('✅ Excel文件清理调度器已启动');
  }

  /**
   * 格式化数据为Excel友好格式
   * @param {any} value 原始值
   * @returns {any} 格式化后的值
   */
  static formatValue(value) {
    if (value === null || value === undefined) {
      return '';
    }

    if (typeof value === 'object') {
      if (value instanceof Date) {
        const year = value.getFullYear();
        const month = String(value.getMonth() + 1).padStart(2, '0');
        const day = String(value.getDate()).padStart(2, '0');
        const hours = String(value.getHours()).padStart(2, '0');
        const minutes = String(value.getMinutes()).padStart(2, '0');
        const seconds = String(value.getSeconds()).padStart(2, '0');
        return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
      }
      return JSON.stringify(value);
    }

    return value;
  }

  /**
   * 验证文件是否存在
   * @param {string} fileName 文件名
   * @returns {Promise<boolean>} 文件是否存在
   */
  async fileExists(fileName) {
    try {
      const filePath = path.join(this.exportDir, fileName);
      await fs.access(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }
}

module.exports = ExcelService;
