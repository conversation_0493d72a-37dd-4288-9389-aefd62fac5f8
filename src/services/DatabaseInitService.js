/**
 * 数据库自动检测和初始化服务
 * 
 * 功能说明：
 * - 自动检测数据库是否已初始化
 * - 在新环境中自动执行初始化脚本
 * - 提供安全的初始化机制和详细日志
 * - 与现有Sequelize模型同步机制兼容
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const fs = require('fs').promises;
const path = require('path');
const { sequelize } = require('../config/database');
const envConfig = require('../config/env');

class DatabaseInitService {
  constructor() {
    this.requiredTables = [
      'users',
      'my_influencers',      // 我的达人表
      'public_influencers',  // 达人公海表（原爬虫结果表）
      'crawl_tasks',
      'crawl_logs',
      'crawler_cookies',
      'author_videos'
    ];
    this.initSqlPath = path.join(__dirname, '../../sql/init.sql');
    this.isInitialized = false;
  }

  /**
   * 检测数据库是否已初始化
   * @returns {Promise<boolean>} 是否已初始化
   */
  async checkDatabaseInitialized() {
    try {
      console.log('🔍 检测数据库初始化状态...');
      
      // 获取所有表名
      const [results] = await sequelize.query("SHOW TABLES");
      const existingTables = results.map(row => Object.values(row)[0]);
      
      console.log(`📋 当前数据库表: [${existingTables.join(', ')}]`);
      
      // 检查必需的表是否都存在
      const missingTables = this.requiredTables.filter(table => !existingTables.includes(table));
      
      if (missingTables.length === 0) {
        console.log('✅ 数据库已完整初始化');
        this.isInitialized = true;
        return true;
      } else {
        console.log(`⚠️ 缺少必需的表: [${missingTables.join(', ')}]`);
        this.isInitialized = false;
        return false;
      }
    } catch (error) {
      console.error('❌ 检测数据库状态失败:', error.message);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * 检查是否允许自动初始化
   * @returns {boolean} 是否允许自动初始化
   */
  isAutoInitAllowed() {
    const nodeEnv = process.env.NODE_ENV || 'development';
    const autoInit = process.env.DB_AUTO_INIT === 'true';
    
    // 开发环境默认允许，生产环境需要明确配置
    if (nodeEnv === 'development') {
      return true;
    }
    
    if (nodeEnv === 'production' && !autoInit) {
      console.log('🔒 生产环境需要设置 DB_AUTO_INIT=true 才能自动初始化');
      return false;
    }
    
    return autoInit;
  }

  /**
   * 读取并解析初始化SQL脚本
   * @returns {Promise<string[]>} SQL语句数组
   */
  async readInitSql() {
    try {
      console.log(`📖 读取初始化脚本: ${this.initSqlPath}`);
      
      const sqlContent = await fs.readFile(this.initSqlPath, 'utf8');
      
      // 分割SQL语句（以分号分隔，忽略注释）
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
        .map(stmt => stmt.replace(/--.*$/gm, '').trim()) // 移除行内注释
        .filter(stmt => stmt.length > 0);
      
      console.log(`📝 解析到 ${statements.length} 条SQL语句`);
      return statements;
    } catch (error) {
      console.error('❌ 读取初始化脚本失败:', error.message);
      throw new Error(`无法读取初始化脚本: ${error.message}`);
    }
  }

  /**
   * 执行数据库初始化
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initializeDatabase() {
    try {
      console.log('🚀 开始执行数据库初始化...');
      
      const statements = await this.readInitSql();
      let successCount = 0;
      let skipCount = 0;
      
      // 开启事务
      const transaction = await sequelize.transaction();
      
      try {
        for (let i = 0; i < statements.length; i++) {
          const statement = statements[i];
          
          try {
            // 跳过USE语句（Sequelize已经连接到指定数据库）
            if (statement.toUpperCase().startsWith('USE ')) {
              console.log(`⏭️ 跳过USE语句: ${statement.substring(0, 50)}...`);
              skipCount++;
              continue;
            }
            
            console.log(`📝 执行SQL ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);
            await sequelize.query(statement, { transaction });
            successCount++;
            
          } catch (sqlError) {
            // 如果是"表已存在"或"重复键"错误，记录但继续执行
            if (sqlError.message.includes('already exists') || 
                sqlError.message.includes('Duplicate entry')) {
              console.log(`⚠️ SQL ${i + 1} 跳过（已存在）: ${sqlError.message}`);
              skipCount++;
              continue;
            }
            
            // 其他错误则抛出
            throw sqlError;
          }
        }
        
        // 提交事务
        await transaction.commit();
        
        console.log('✅ 数据库初始化完成');
        console.log(`📊 执行统计: 成功 ${successCount} 条，跳过 ${skipCount} 条`);
        
        this.isInitialized = true;
        return true;
        
      } catch (error) {
        // 回滚事务
        await transaction.rollback();
        throw error;
      }
      
    } catch (error) {
      console.error('❌ 数据库初始化失败:', error.message);
      console.error('💡 手动解决方案:');
      console.error('   1. 检查数据库连接配置');
      console.error('   2. 确保数据库用户有足够权限');
      console.error('   3. 手动执行 sql/init.sql 脚本');
      console.error('   4. 检查 sql/init.sql 文件是否存在');
      
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * 验证初始化结果
   * @returns {Promise<boolean>} 验证是否通过
   */
  async validateInitialization() {
    try {
      console.log('🔍 验证数据库初始化结果...');
      
      // 重新检查表结构
      const isInitialized = await this.checkDatabaseInitialized();
      
      if (!isInitialized) {
        console.error('❌ 初始化验证失败：仍有表缺失');
        return false;
      }
      
      // 检查关键表是否有数据（如users表应该有默认管理员）
      try {
        const [userResults] = await sequelize.query("SELECT COUNT(*) as count FROM users");
        const userCount = userResults[0].count;
        
        if (userCount > 0) {
          console.log(`✅ 验证通过：users表有 ${userCount} 条记录`);
        } else {
          console.log('⚠️ 警告：users表为空，可能需要手动添加管理员用户');
        }
      } catch (error) {
        console.log('⚠️ 无法验证users表数据:', error.message);
      }
      
      console.log('✅ 数据库初始化验证完成');
      return true;
      
    } catch (error) {
      console.error('❌ 验证初始化结果失败:', error.message);
      return false;
    }
  }

  /**
   * 主要的自动检测和初始化流程
   * @returns {Promise<boolean>} 是否成功完成检测和初始化
   */
  async autoDetectAndInit() {
    try {
      console.log('🔄 开始数据库自动检测和初始化流程...');
      
      // 1. 检测数据库状态
      const isInitialized = await this.checkDatabaseInitialized();
      
      if (isInitialized) {
        console.log('✅ 数据库已初始化，跳过初始化流程');
        return true;
      }
      
      // 2. 检查是否允许自动初始化
      if (!this.isAutoInitAllowed()) {
        console.error('❌ 数据库未初始化，且不允许自动初始化');
        console.error('💡 解决方案:');
        console.error('   - 开发环境：自动允许初始化');
        console.error('   - 生产环境：设置环境变量 DB_AUTO_INIT=true');
        console.error('   - 或手动执行 sql/init.sql 脚本');
        return false;
      }
      
      // 3. 执行初始化
      console.log('🚀 检测到数据库未初始化，开始自动初始化...');
      const initSuccess = await this.initializeDatabase();
      
      if (!initSuccess) {
        return false;
      }
      
      // 4. 验证初始化结果
      const validateSuccess = await this.validateInitialization();
      
      if (validateSuccess) {
        console.log('🎉 数据库自动初始化流程完成！');
        return true;
      } else {
        console.error('❌ 数据库初始化验证失败');
        return false;
      }
      
    } catch (error) {
      console.error('❌ 数据库自动检测和初始化流程失败:', error.message);
      return false;
    }
  }

  /**
   * 获取初始化状态
   * @returns {boolean} 是否已初始化
   */
  getInitializationStatus() {
    return this.isInitialized;
  }
}

// 创建单例实例
const databaseInitService = new DatabaseInitService();

module.exports = databaseInitService;
