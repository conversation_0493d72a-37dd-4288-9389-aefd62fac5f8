/**
 * 字典管理服务
 *
 * 功能说明：
 * - 提供字典数据的CRUD操作
 * - 支持按分类获取字典项
 * - 缓存机制提高查询性能
 * - 支持批量操作和数据验证
 *
 * 主要方法：
 * - getDictionariesByCategory: 按分类获取字典项
 * - createDictionary: 创建字典项
 * - updateDictionary: 更新字典项
 * - deleteDictionary: 删除字典项
 * - getAllCategories: 获取所有分类
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const { Dictionary } = require('../models');
const { Op } = require('sequelize');

class DictionaryService {
  /**
   * 按分类获取字典项
   * @param {string} category 字典分类
   * @param {boolean} activeOnly 是否只获取启用的字典项
   * @returns {Promise<Array>} 字典项列表
   */
  async getDictionariesByCategory(category, activeOnly = true) {
    try {
      const whereCondition = { category };
      if (activeOnly) {
        whereCondition.status = 'active';
      }

      const dictionaries = await Dictionary.findAll({
        where: whereCondition,
        order: [['sortOrder', 'ASC'], ['id', 'ASC']],
        attributes: ['id', 'dictKey', 'dictLabel', 'dictValue', 'sortOrder', 'status']
      });

      console.log(`✅ 获取字典分类 ${category} 成功，共 ${dictionaries.length} 项`);
      return dictionaries;
    } catch (error) {
      console.error(`❌ 获取字典分类 ${category} 失败:`, error.message);
      throw error;
    }
  }

  /**
   * 获取所有字典分类
   * @returns {Promise<Array>} 分类列表
   */
  async getAllCategories() {
    try {
      const categories = await Dictionary.findAll({
        attributes: ['category'],
        group: ['category'],
        order: [['category', 'ASC']]
      });

      const categoryList = categories.map(item => item.category);
      console.log(`✅ 获取所有字典分类成功，共 ${categoryList.length} 个分类`);
      return categoryList;
    } catch (error) {
      console.error('❌ 获取字典分类失败:', error.message);
      throw error;
    }
  }

  /**
   * 创建字典项
   * @param {Object} data 字典数据
   * @param {number} userId 创建用户ID
   * @returns {Promise<Object>} 创建的字典项
   */
  async createDictionary(data, userId) {
    try {
      const {
        category,
        dictKey,
        dictLabel,
        dictValue,
        sortOrder = 0,
        status = 'active',
        description
      } = data;

      // 检查同一分类下键值是否已存在
      const existing = await Dictionary.findOne({
        where: {
          category,
          dictKey
        }
      });

      if (existing) {
        throw new Error(`分类 ${category} 下的键值 ${dictKey} 已存在`);
      }

      const dictionary = await Dictionary.create({
        category,
        dictKey,
        dictLabel,
        dictValue,
        sortOrder,
        status,
        description,
        createdBy: userId,
        updatedBy: userId
      });

      console.log(`✅ 字典项创建成功: ${category}.${dictKey}`);
      return dictionary;
    } catch (error) {
      console.error('❌ 创建字典项失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新字典项
   * @param {number} id 字典项ID
   * @param {Object} data 更新数据
   * @param {number} userId 更新用户ID
   * @returns {Promise<Object>} 更新后的字典项
   */
  async updateDictionary(id, data, userId) {
    try {
      const dictionary = await Dictionary.findByPk(id);
      if (!dictionary) {
        throw new Error('字典项不存在');
      }

      // 如果更新键值，检查是否冲突
      if (data.dictKey && data.dictKey !== dictionary.dictKey) {
        const existing = await Dictionary.findOne({
          where: {
            category: data.category || dictionary.category,
            dictKey: data.dictKey,
            id: { [Op.ne]: id }
          }
        });

        if (existing) {
          throw new Error(`分类下的键值 ${data.dictKey} 已存在`);
        }
      }

      await dictionary.update({
        ...data,
        updatedBy: userId
      });

      console.log(`✅ 字典项更新成功: ${dictionary.category}.${dictionary.dictKey}`);
      return dictionary;
    } catch (error) {
      console.error('❌ 更新字典项失败:', error.message);
      throw error;
    }
  }

  /**
   * 删除字典项
   * @param {number} id 字典项ID
   * @returns {Promise<boolean>} 删除结果
   */
  async deleteDictionary(id) {
    try {
      const dictionary = await Dictionary.findByPk(id);
      if (!dictionary) {
        throw new Error('字典项不存在');
      }

      await dictionary.destroy();
      console.log(`✅ 字典项删除成功: ${dictionary.category}.${dictionary.dictKey}`);
      return true;
    } catch (error) {
      console.error('❌ 删除字典项失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取字典项列表（分页）
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 分页结果
   */
  async getDictionaryList(params) {
    try {
      const {
        page = 1,
        pageSize = 20,
        category,
        status,
        keyword
      } = params;

      const whereCondition = {};
      
      if (category) {
        whereCondition.category = category;
      }
      
      if (status) {
        whereCondition.status = status;
      }
      
      if (keyword) {
        whereCondition[Op.or] = [
          { dictKey: { [Op.like]: `%${keyword}%` } },
          { dictLabel: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const offset = (page - 1) * pageSize;
      const { count, rows } = await Dictionary.findAndCountAll({
        where: whereCondition,
        order: [['category', 'ASC'], ['sortOrder', 'ASC'], ['id', 'ASC']],
        limit: pageSize,
        offset
      });

      console.log(`✅ 获取字典列表成功，共 ${count} 项`);
      return {
        list: rows,
        total: count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(count / pageSize)
      };
    } catch (error) {
      console.error('❌ 获取字典列表失败:', error.message);
      throw error;
    }
  }

  /**
   * 批量创建字典项
   * @param {Array} dictionaries 字典项数组
   * @param {number} userId 创建用户ID
   * @returns {Promise<Object>} 创建结果
   */
  async batchCreateDictionaries(dictionaries, userId) {
    try {
      const results = {
        success: [],
        failed: [],
        total: dictionaries.length
      };

      for (const dict of dictionaries) {
        try {
          const created = await this.createDictionary(dict, userId);
          results.success.push(created);
        } catch (error) {
          results.failed.push({
            data: dict,
            error: error.message
          });
        }
      }

      console.log(`✅ 批量创建字典项完成: 成功 ${results.success.length}，失败 ${results.failed.length}`);
      return results;
    } catch (error) {
      console.error('❌ 批量创建字典项失败:', error.message);
      throw error;
    }
  }
}

module.exports = new DictionaryService();
