/**
 * 简化的MCP服务
 * 使用官方TypeScript SDK重新实现，专注于核心功能
 */

const { McpServer } = require('@modelcontextprotocol/sdk/server/mcp.js');
const { z } = require('zod');
const crawlerService = require('./crawler');
const ExcelService = require('./ExcelService');

class SimpleMCPService {
  constructor() {
    this.server = null;
    this.isInitialized = false;
    this.excelService = new ExcelService();
  }

  /**
   * 初始化MCP服务
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 创建MCP服务器
      this.server = new McpServer({
        name: "daren-crawler-mcp",
        version: "1.0.0"
      });

      // 注册工具
      this.registerTools();

      // 初始化依赖服务
      await crawlerService.initialize();
      
      this.isInitialized = true;
      console.log('✅ 简化MCP服务初始化成功');
    } catch (error) {
      console.error('❌ 简化MCP服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 注册MCP工具
   */
  registerTools() {
    // 1. 创建爬虫任务工具
    this.server.registerTool(
      "create_crawler_task",
      {
        title: "创建爬虫任务",
        description: "创建达人爬虫任务，支持关键词搜索指定平台的达人信息",
        inputSchema: {
          keywords: z.string().describe("搜索关键词，用于查找相关达人"),
          platform: z.enum(['xiaohongshu', 'juxingtu', 'xhs', 'jlxt']).describe("目标平台：xhs/xiaohongshu(小红书) 或 jlxt/juxingtu(巨量星图)"),
          taskName: z.string().describe("任务名称，用于标识和管理任务"),
          maxPages: z.number().min(1).max(20).default(5).describe("最大爬取页数，控制爬取数据量"),
          config: z.object({
            pageSize: z.number().min(10).max(50).default(20).describe("每页数据量"),
            delay: z.object({
              min: z.number().default(1000).describe("最小延迟时间(毫秒)"),
              max: z.number().default(3000).describe("最大延迟时间(毫秒)")
            }).default({ min: 1000, max: 3000 }).describe("请求延迟配置")
          }).default({}).describe("爬取配置参数"),
          priority: z.number().min(0).max(10).default(1).describe("任务优先级，数字越大优先级越高")
        }
      },
      async (args) => {
        try {
          console.log(`🚀 [MCP] 创建爬虫任务: ${args.taskName}`);
          console.log(`📋 关键词: ${args.keywords}, 平台: ${args.platform}, 页数: ${args.maxPages}`);

          // 平台参数映射
          const platformMapping = {
            'jlxt': 'juxingtu',
            'xhs': 'xiaohongshu',
            'juxingtu': 'juxingtu',
            'xiaohongshu': 'xiaohongshu'
          };
          const mappedPlatform = platformMapping[args.platform] || args.platform;

          // 创建任务
          const task = await crawlerService.createTask({
            taskName: args.taskName,
            platform: mappedPlatform,
            keywords: args.keywords,
            maxPages: args.maxPages,
            config: {
              pageSize: args.config?.pageSize || 20,
              delay: args.config?.delay || { min: 1000, max: 3000 },
              retries: 3,
              filters: {}
            },
            priority: args.priority,
            createdBy: 1 // MCP系统用户
          });

          return {
            content: [{
              type: "text",
              text: `✅ 爬虫任务创建成功！\n\n任务信息：\n- 任务ID: ${task.id}\n- 任务名称: ${task.taskName}\n- 平台: ${task.platform}\n- 关键词: ${task.keywords}\n- 最大页数: ${task.maxPages}\n- 状态: ${task.status}\n- 创建时间: ${task.createdAt}\n\n任务已添加到队列，将自动开始执行。`
            }]
          };
        } catch (error) {
          console.error('❌ MCP创建爬虫任务失败:', error);
          return {
            content: [{
              type: "text",
              text: `❌ 创建爬虫任务失败: ${error.message}`
            }],
            isError: true
          };
        }
      }
    );

    // 2. 获取任务状态工具
    this.server.registerTool(
      "get_task_status",
      {
        title: "获取任务状态",
        description: "根据任务ID获取爬虫任务的执行状态和进度信息",
        inputSchema: {
          taskId: z.number().describe("任务ID")
        }
      },
      async (args) => {
        try {
          console.log(`📊 [MCP] 查询任务状态: ${args.taskId}`);

          const task = await crawlerService.getTaskDetail(args.taskId);
          
          if (!task) {
            return {
              content: [{
                type: "text",
                text: `❌ 任务不存在: ${args.taskId}`
              }],
              isError: true
            };
          }

          const statusText = this.getStatusText(task.status);
          const progressText = task.progress ? `${task.progress}%` : '0%';

          return {
            content: [{
              type: "text",
              text: `📊 任务状态信息\n\n基本信息：\n- 任务ID: ${task.id}\n- 任务名称: ${task.taskName}\n- 平台: ${task.platform}\n- 关键词: ${task.keywords}\n- 状态: ${statusText}\n- 进度: ${progressText}\n\n执行信息：\n- 创建时间: ${task.createdAt}\n- 开始时间: ${task.startedAt || '未开始'}\n- 完成时间: ${task.completedAt || '未完成'}\n- 当前页数: ${task.currentPage || 0}/${task.maxPages}\n\n结果统计：\n- 总计: ${task.resultStats?.total || 0}\n- 已处理: ${task.resultStats?.processed || 0}\n- 已导入: ${task.resultStats?.imported || 0}\n- 失败: ${task.resultStats?.failed || 0}`
            }]
          };
        } catch (error) {
          console.error('❌ MCP获取任务状态失败:', error);
          return {
            content: [{
              type: "text",
              text: `❌ 获取任务状态失败: ${error.message}`
            }],
            isError: true
          };
        }
      }
    );

    // 3. 获取任务结果工具
    this.server.registerTool(
      "get_task_results",
      {
        title: "获取任务结果",
        description: "获取爬虫任务的结果数据，支持分页和筛选",
        inputSchema: {
          taskId: z.number().describe("任务ID"),
          page: z.number().min(1).default(1).describe("页码"),
          limit: z.number().min(1).max(100).default(20).describe("每页数量"),
          status: z.enum(['pending', 'processed', 'imported', 'failed']).optional().describe("结果状态筛选"),
          keyword: z.string().optional().describe("关键词搜索（昵称、用户ID等）")
        }
      },
      async (args) => {
        try {
          console.log(`📋 [MCP] 获取任务结果: ${args.taskId}, 页码: ${args.page}`);

          // 这里需要调用爬虫服务的结果查询方法
          // 由于原有的getTaskResults方法在控制器中，我们需要直接查询数据库
          const { CrawlResult } = require('../../models');
          const { Op } = require('sequelize');

          const where = { taskId: args.taskId };
          if (args.status) where.status = args.status;
          if (args.keyword) {
            where[Op.or] = [
              { nickname: { [Op.like]: `%${args.keyword}%` } },
              { platformUserId: { [Op.like]: `%${args.keyword}%` } },
              { uniqueId: { [Op.like]: `%${args.keyword}%` } }
            ];
          }

          const result = await CrawlResult.findAndCountAll({
            where,
            order: [['createdAt', 'DESC']],
            limit: args.limit,
            offset: (args.page - 1) * args.limit,
            attributes: ['id', 'nickname', 'platformUserId', 'followersCount', 'status', 'createdAt', 'platform']
          });

          const totalPages = Math.ceil(result.count / args.limit);

          let resultText = `📋 任务结果 (任务ID: ${args.taskId})\n\n`;
          resultText += `📊 统计信息：\n- 总数量: ${result.count}\n- 当前页: ${args.page}/${totalPages}\n- 每页显示: ${args.limit}\n\n`;

          if (result.rows.length === 0) {
            resultText += '暂无结果数据';
          } else {
            resultText += '📝 结果列表：\n';
            result.rows.forEach((item, index) => {
              const statusEmoji = this.getResultStatusEmoji(item.status);
              resultText += `${index + 1}. ${statusEmoji} ${item.nickname || '未知'}\n`;
              resultText += `   - 用户ID: ${item.platformUserId || '未知'}\n`;
              resultText += `   - 粉丝数: ${item.followersCount || 0}\n`;
              resultText += `   - 平台: ${item.platform || '未知'}\n`;
              resultText += `   - 状态: ${item.status}\n`;
              resultText += `   - 时间: ${item.createdAt}\n\n`;
            });
          }

          return {
            content: [{
              type: "text",
              text: resultText
            }]
          };
        } catch (error) {
          console.error('❌ MCP获取任务结果失败:', error);
          return {
            content: [{
              type: "text",
              text: `❌ 获取任务结果失败: ${error.message}`
            }],
            isError: true
          };
        }
      }
    );

    // 4. 导出Excel工具
    this.server.registerTool(
      "export_results_to_excel",
      {
        title: "导出结果到Excel",
        description: "将爬虫任务结果导出为Excel文件",
        inputSchema: {
          taskId: z.number().describe("任务ID"),
          filename: z.string().optional().describe("文件名（可选，默认自动生成）"),
          status: z.enum(['pending', 'processed', 'imported', 'failed']).optional().describe("导出指定状态的结果")
        }
      },
      async (args) => {
        try {
          console.log(`📊 [MCP] 导出Excel: 任务${args.taskId}`);

          // 获取任务信息
          const task = await crawlerService.getTaskDetail(args.taskId);
          if (!task) {
            return {
              content: [{
                type: "text",
                text: `❌ 任务不存在: ${args.taskId}`
              }],
              isError: true
            };
          }

          // 生成文件名
          const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
          const filename = args.filename || `crawler_results_${args.taskId}_${timestamp}.xlsx`;

          // 导出Excel
          const exportResult = await this.excelService.exportCrawlResults(args.taskId, {
            filename,
            status: args.status
          });

          return {
            content: [{
              type: "text",
              text: `✅ Excel导出成功！\n\n文件信息：\n- 文件名: ${exportResult.filename}\n- 文件路径: ${exportResult.filePath}\n- 下载链接: ${exportResult.downloadUrl}\n- 数据量: ${exportResult.recordCount} 条\n- 文件大小: ${exportResult.fileSize}\n- 导出时间: ${new Date().toLocaleString()}`
            }]
          };
        } catch (error) {
          console.error('❌ MCP导出Excel失败:', error);
          return {
            content: [{
              type: "text",
              text: `❌ 导出Excel失败: ${error.message}`
            }],
            isError: true
          };
        }
      }
    );
  }

  /**
   * 获取结果状态表情符号
   */
  getResultStatusEmoji(status) {
    const statusMap = {
      'pending': '⏳',
      'processed': '✅',
      'imported': '📥',
      'failed': '❌'
    };
    return statusMap[status] || '❓';
  }

  /**
   * 获取状态文本描述
   */
  getStatusText(status) {
    const statusMap = {
      'pending': '⏳ 等待中',
      'running': '🔄 执行中',
      'completed': '✅ 已完成',
      'failed': '❌ 失败',
      'paused': '⏸️ 已暂停',
      'cancelled': '🚫 已取消'
    };
    return statusMap[status] || status;
  }

  /**
   * 获取MCP服务器实例
   */
  getServer() {
    return this.server;
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      serverName: this.server?.name || 'unknown',
      crawlerService: crawlerService.getStatus()
    };
  }
}

// 创建单例实例
const simpleMCPService = new SimpleMCPService();

module.exports = simpleMCPService;
