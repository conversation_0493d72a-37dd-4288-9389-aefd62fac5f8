/**
 * Cookie管理服务
 *
 * 功能说明：
 * - 提供Cookie的安全存储和管理功能
 * - 实现Cookie的自动轮换和负载均衡
 * - 支持Cookie的有效性验证和状态监控
 * - 提供Cookie的使用统计和分析功能
 *
 * 核心特性：
 * - 🔐 加密存储：Cookie数据加密保存（生产环境）
 * - 🔄 智能轮换：基于优先级和使用频率的自动轮换
 * - 📊 使用统计：详细的使用次数和时间统计
 * - ⚡ 性能优化：连接池和缓存机制
 * - 🛡️ 安全防护：失效检测和异常处理
 *
 * 配置说明：
 * - 测试模式：当前以明文存储Cookie，便于调试
 * - 生产模式：需要启用加密功能保证数据安全
 * - 轮换策略：支持基于时间、次数、优先级的轮换
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const crypto = require('crypto');
const CrawlerCookie = require('../models/CrawlerCookie');
const { Op } = require('sequelize');

class CookieManager {
  /**
   * Cookie管理器构造函数
   *
   * 功能说明：
   * - 初始化Cookie管理器的配置参数
   * - 设置加密密钥和算法（生产环境）
   * - 配置Cookie轮换策略和限制
   * - 显示当前运行模式的安全警告
   *
   * 配置项说明：
   * - encryptionKey: 加密密钥，生产环境从环境变量获取
   * - algorithm: 加密算法，使用AES-256-CBC
   * - isTestMode: 测试模式标识，控制是否加密存储
   * - rotationConfig: 轮换配置，包含使用限制和冷却时间
   *
   * 安全注意事项：
   * - 测试模式下Cookie以明文存储，仅用于开发调试
   * - 生产环境必须设置COOKIE_ENCRYPTION_KEY环境变量
   * - 生产环境必须将isTestMode设置为false
   */
  constructor() {
    // 明文存储模式
    this.isTestMode = true;
    console.log('🔧 [明文模式] Cookie管理器已启用明文存储模式');

    this.encryptionKey = process.env.COOKIE_ENCRYPTION_KEY || 'your-secret-key-32-chars-long!!';
    this.algorithm = 'aes-256-cbc';

    this.rotationConfig = {
      maxDailyUse: 100000,
      cooldownMinutes: 0,
      validationInterval: 60,
      maxConsecutiveFailures: 3
    };
  }

  /**
   * 加密Cookie数据
   * @param {string} cookieString Cookie字符串
   * @returns {string} 加密后的数据
   *
   * ⚠️ 测试阶段临时修改：跳过加密，直接返回明文
   * 🔒 生产环境必须恢复加密功能以保证安全性
   */
  encryptCookie(cookieString) {
    // 明文模式：直接返回原始Cookie字符串
    console.log('🔧 [明文模式] Cookie以明文形式存储');
    return cookieString;
  }

  /**
   * 解密Cookie数据
   * @param {string} encryptedData 加密的数据
   * @returns {string} 解密后的Cookie字符串
   *
   * ⚠️ 测试阶段临时修改：跳过解密，直接返回原数据
   * 🔒 生产环境必须恢复解密功能以保证安全性
   */
  decryptCookie(encryptedData) {
    // 明文模式：直接返回原始数据
    if (!encryptedData) {
      console.error('Cookie数据为空');
      return null;
    }
    console.log('🔧 [明文模式] Cookie以明文形式读取');
    return encryptedData;
  }

  /**
   * 添加Cookie
   * @param {Object} cookieData Cookie数据
   * @returns {Object} 创建的Cookie记录
   */
  async addCookie(cookieData) {
    const {
      accountName,
      platform,
      cookieString,
      userAgent,
      priority = 1,
      maxDailyUse = 100,
      notes,
      createdBy
    } = cookieData;

    // 检查账户名是否已存在
    const existing = await CrawlerCookie.findOne({
      where: {
        accountName,
        platform
      }
    });

    if (existing) {
      throw new Error(`平台 ${platform} 上的账户 ${accountName} 已存在`);
    }

    // 明文存储Cookie数据
    const cookieDataToStore = this.encryptCookie(cookieString);

    const cookie = await CrawlerCookie.create({
      accountName,
      platform,
      cookieData: cookieDataToStore,
      userAgent,
      priority,
      maxDailyUse,
      notes,
      createdBy,
      lastResetDate: new Date().toISOString().split('T')[0]
    });

    console.log(`✅ [明文模式] Cookie已添加: ${accountName} (明文存储)`);
    return cookie;
  }

  /**
   * 获取可用的Cookie
   * @param {string} platform 平台名称
   * @returns {Object|null} Cookie记录和解密后的数据
   */
  async getAvailableCookie(platform) {
    const now = new Date();
    const today = now.toISOString().split('T')[0];

    // 重置每日使用计数
    await this.resetDailyCountIfNeeded(today);

    // 构建查询条件
    const whereConditions = {
      platform,
      status: 'active',
      dailyUseCount: { [Op.lt]: CrawlerCookie.sequelize.col('max_daily_use') }
    };

    // ⚠️ 测试模式：跳过冷却时间检查
    // 🔒 生产环境需要启用冷却机制
    // if (!this.isTestMode) {
    //   whereConditions[Op.or] = [
    //     { cooldownUntil: null },
    //     { cooldownUntil: { [Op.lt]: now } }
    //   ];
    // } else {
    //   console.log('⚠️ [测试模式] 跳过Cookie冷却时间检查');
    // }

    // 查找可用的Cookie
    const availableCookies = await CrawlerCookie.findAll({
      where: whereConditions,
      order: [
        ['priority', 'DESC'],
        ['use_count', 'ASC'],
        ['last_used_at', 'ASC']
      ]
    });

    if (availableCookies.length === 0) {
      console.warn(`⚠️ 没有可用的 ${platform} Cookie`);
      return null;
    }

    const selectedCookie = availableCookies[0];

    // ⚠️ 测试阶段：直接读取明文Cookie数据
    // 🔒 生产环境需要恢复解密功能
    const cookieString = this.decryptCookie(selectedCookie.cookieData); // 测试阶段返回明文
    if (!cookieString) {
      await this.markCookieAsExpired(selectedCookie.id, 'Cookie数据读取失败');
      return null;
    }

    // 更新使用统计
    await this.updateCookieUsage(selectedCookie.id);

    console.log(`🍪 [测试模式] 使用Cookie: ${selectedCookie.accountName} (使用次数: ${selectedCookie.useCount + 1}, 明文读取)`);

    return {
      id: selectedCookie.id,
      accountName: selectedCookie.accountName,
      cookieString,
      userAgent: selectedCookie.userAgent,
      priority: selectedCookie.priority
    };
  }

  /**
   * 更新Cookie使用统计
   * @param {number} cookieId Cookie ID
   */
  async updateCookieUsage(cookieId) {
    const now = new Date();

    // 构建更新数据
    const updateData = {
      useCount: CrawlerCookie.sequelize.literal('use_count + 1'),
      dailyUseCount: CrawlerCookie.sequelize.literal('daily_use_count + 1'),
      lastUsedAt: now
    };

    // ⚠️ 测试模式：跳过冷却时间设置
    // 🔒 生产环境需要启用冷却机制
    // if (!this.isTestMode) {
    //   const cooldownUntil = new Date(now.getTime() + this.rotationConfig.cooldownMinutes * 60000);
    //   updateData.cooldownUntil = cooldownUntil;
    // } else {
    //   console.log('⚠️ [测试模式] 跳过Cookie冷却时间设置');
    // }

    await CrawlerCookie.update(updateData, {
      where: { id: cookieId }
    });
  }

  /**
   * 重置每日使用计数
   * @param {string} today 今日日期
   */
  async resetDailyCountIfNeeded(today) {
    await CrawlerCookie.update({
      dailyUseCount: 0,
      lastResetDate: today
    }, {
      where: {
        [Op.or]: [
          { lastResetDate: null },
          { lastResetDate: { [Op.ne]: today } }
        ]
      }
    });
  }

  /**
   * 验证Cookie有效性
   * @param {number} cookieId Cookie ID
   * @param {Function} validationFunction 验证函数
   * @returns {boolean} 验证结果
   */
  async validateCookie(cookieId, validationFunction) {
    try {
      const cookie = await CrawlerCookie.findByPk(cookieId);
      if (!cookie) {
        return false;
      }

      // ⚠️ 测试阶段：直接读取明文Cookie数据
      // 🔒 生产环境需要恢复解密功能
      const cookieString = this.decryptCookie(cookie.cookieData); // 测试阶段返回明文
      if (!cookieString) {
        await this.markCookieAsExpired(cookieId, 'Cookie数据读取失败');
        return false;
      }

      // 执行验证
      const validationResult = await validationFunction(cookieString, cookie.userAgent);
      
      // 更新验证结果
      await CrawlerCookie.update({
        lastValidatedAt: new Date(),
        validationResult: {
          isValid: validationResult.isValid,
          message: validationResult.message,
          timestamp: new Date().toISOString()
        },
        status: validationResult.isValid ? 'active' : 'expired'
      }, {
        where: { id: cookieId }
      });

      if (!validationResult.isValid) {
        console.warn(`❌ [测试模式] Cookie验证失败: ${cookie.accountName} - ${validationResult.message}`);
      } else {
        console.log(`✅ [测试模式] Cookie验证成功: ${cookie.accountName} (明文验证)`);
      }

      return validationResult.isValid;

    } catch (error) {
      console.error('Cookie验证过程出错:', error.message);
      await this.markCookieAsExpired(cookieId, `验证过程出错: ${error.message}`);
      return false;
    }
  }

  /**
   * 标记Cookie为过期
   * @param {number} cookieId Cookie ID
   * @param {string} reason 过期原因
   */
  async markCookieAsExpired(cookieId, reason) {
    await CrawlerCookie.update({
      status: 'expired',
      validationResult: {
        isValid: false,
        message: reason,
        timestamp: new Date().toISOString()
      }
    }, {
      where: { id: cookieId }
    });

    console.warn(`⚠️ Cookie已标记为过期: ID ${cookieId}, 原因: ${reason}`);
  }

  /**
   * 批量导入Cookie
   * @param {Array} cookiesData Cookie数据数组
   * @param {number} createdBy 创建者ID
   * @returns {Object} 导入结果
   */
  async batchImportCookies(cookiesData, createdBy) {
    const results = {
      success: 0,
      failed: 0,
      errors: []
    };

    for (const cookieData of cookiesData) {
      try {
        await this.addCookie({ ...cookieData, createdBy });
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          accountName: cookieData.accountName,
          error: error.message
        });
      }
    }

    console.log(`📊 批量导入完成: 成功 ${results.success}, 失败 ${results.failed}`);
    return results;
  }

  /**
   * 获取Cookie统计信息
   * @param {string} platform 平台名称
   * @returns {Object} 统计信息
   */
  async getCookieStats(platform) {
    const where = platform ? { platform } : {};

    const [total, active, expired, banned] = await Promise.all([
      CrawlerCookie.count({ where }),
      CrawlerCookie.count({ where: { ...where, status: 'active' } }),
      CrawlerCookie.count({ where: { ...where, status: 'expired' } }),
      CrawlerCookie.count({ where: { ...where, status: 'banned' } })
    ]);

    return {
      total,
      active,
      expired,
      banned,
      available: active
    };
  }
}

module.exports = CookieManager;
