/**
 * 发布链接修复服务
 * 
 * 用于处理publishLink字段缺失的问题，提供手动修复和批量处理功能
 */

const { CooperationManagement } = require('../models');
const { Op } = require('sequelize');

class PublishLinkFixService {
  
  /**
   * 获取缺失publishLink的记录统计
   * @returns {Promise<Object>} 统计信息
   */
  async getMissingPublishLinkStats() {
    try {
      const totalCount = await CooperationManagement.count();
      
      const withPublishLinkCount = await CooperationManagement.count({
        where: {
          publishLink: {
            [Op.not]: null,
            [Op.ne]: ''
          }
        }
      });
      
      const missingPublishLinkCount = await CooperationManagement.count({
        where: {
          [Op.or]: [
            { publishLink: null },
            { publishLink: '' }
          ]
        }
      });
      
      const crmSyncedMissingCount = await CooperationManagement.count({
        where: {
          externalAgreementId: {
            [Op.not]: null,
            [Op.ne]: ''
          },
          [Op.or]: [
            { publishLink: null },
            { publishLink: '' }
          ]
        }
      });
      
      return {
        totalCount,
        withPublishLinkCount,
        missingPublishLinkCount,
        crmSyncedMissingCount,
        publishLinkRate: totalCount > 0 ? (withPublishLinkCount / totalCount * 100).toFixed(2) : 0
      };
      
    } catch (error) {
      console.error('❌ 获取缺失publishLink统计失败:', error.message);
      throw error;
    }
  }
  
  /**
   * 获取缺失publishLink的记录列表
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 记录列表
   */
  async getMissingPublishLinkRecords(options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        onlyCrmSynced = false,
        hasViewData = false
      } = options;
      
      const whereCondition = {
        [Op.or]: [
          { publishLink: null },
          { publishLink: '' }
        ]
      };
      
      // 只查看CRM同步的记录
      if (onlyCrmSynced) {
        whereCondition.externalAgreementId = {
          [Op.not]: null,
          [Op.ne]: ''
        };
      }
      
      // 只查看有观看数据的记录（说明内容已发布）
      if (hasViewData) {
        whereCondition[Op.or] = [
          ...whereCondition[Op.or],
          {
            [Op.and]: [
              whereCondition[Op.or][0],
              {
                [Op.or]: [
                  { viewCount: { [Op.gt]: 0 } },
                  { likeCount: { [Op.gt]: 0 } },
                  { commentCount: { [Op.gt]: 0 } }
                ]
              }
            ]
          }
        ];
      }
      
      const records = await CooperationManagement.findAll({
        where: whereCondition,
        attributes: [
          'id', 'customerName', 'title', 'publishLink', 
          'externalAgreementId', 'viewCount', 'likeCount', 
          'commentCount', 'createdAt', 'updatedAt'
        ],
        order: [['createdAt', 'DESC']],
        limit: limit,
        offset: (page - 1) * limit
      });
      
      const totalCount = await CooperationManagement.count({ where: whereCondition });
      
      return {
        records,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      };
      
    } catch (error) {
      console.error('❌ 获取缺失publishLink记录失败:', error.message);
      throw error;
    }
  }
  
  /**
   * 手动更新单个记录的publishLink
   * @param {number} id 记录ID
   * @param {string} publishLink 发布链接
   * @param {number} userId 操作用户ID
   * @returns {Promise<Object>} 更新结果
   */
  async updatePublishLink(id, publishLink, userId) {
    try {
      const record = await CooperationManagement.findByPk(id);
      if (!record) {
        throw new Error('记录不存在');
      }
      
      // 验证链接格式
      if (publishLink && !this.isValidPublishLink(publishLink)) {
        throw new Error('发布链接格式不正确');
      }
      
      await record.update({
        publishLink: publishLink,
        updatedBy: userId
      });
      
      console.log(`✅ 更新发布链接成功: 记录ID ${id}`);
      
      return {
        success: true,
        message: '发布链接更新成功',
        record: {
          id: record.id,
          customerName: record.customerName,
          title: record.title,
          publishLink: record.publishLink
        }
      };
      
    } catch (error) {
      console.error('❌ 更新发布链接失败:', error.message);
      throw error;
    }
  }
  
  /**
   * 批量更新publishLink
   * @param {Array} updates 更新数据数组 [{id, publishLink}, ...]
   * @param {number} userId 操作用户ID
   * @returns {Promise<Object>} 批量更新结果
   */
  async batchUpdatePublishLink(updates, userId) {
    const result = {
      success: 0,
      failed: 0,
      errors: []
    };
    
    try {
      console.log(`🔄 开始批量更新发布链接 - 总数: ${updates.length}`);
      
      for (const update of updates) {
        try {
          await this.updatePublishLink(update.id, update.publishLink, userId);
          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            id: update.id,
            error: error.message
          });
        }
      }
      
      console.log(`✅ 批量更新完成 - 成功: ${result.success}, 失败: ${result.failed}`);
      return result;
      
    } catch (error) {
      console.error('❌ 批量更新发布链接失败:', error.message);
      throw error;
    }
  }
  
  /**
   * 验证发布链接格式
   * @param {string} link 链接
   * @returns {boolean} 是否有效
   */
  isValidPublishLink(link) {
    if (!link || typeof link !== 'string') {
      return false;
    }
    
    // 支持的平台链接格式
    const validPatterns = [
      /^https:\/\/www\.xiaohongshu\.com\/explore\/[a-zA-Z0-9]+/,  // 小红书
      /^https:\/\/www\.iesdouyin\.com\/share\/video\/[0-9]+/,     // 抖音
      /^https:\/\/weibo\.com\/[0-9]+\/[a-zA-Z0-9]+/,             // 微博
      /^https:\/\/www\.bilibili\.com\/video\/[a-zA-Z0-9]+/       // B站
    ];
    
    return validPatterns.some(pattern => pattern.test(link));
  }
  
  /**
   * 分析publishLink缺失的原因
   * @returns {Promise<Object>} 分析结果
   */
  async analyzePublishLinkIssues() {
    try {
      const stats = await this.getMissingPublishLinkStats();
      
      // 分析有观看数据但没有发布链接的记录
      const hasDataButNoLinkCount = await CooperationManagement.count({
        where: {
          [Op.or]: [
            { publishLink: null },
            { publishLink: '' }
          ],
          [Op.or]: [
            { viewCount: { [Op.gt]: 0 } },
            { likeCount: { [Op.gt]: 0 } },
            { commentCount: { [Op.gt]: 0 } }
          ]
        }
      });
      
      // 分析CRM同步但没有发布链接的记录
      const crmSyncedNoLinkCount = await CooperationManagement.count({
        where: {
          externalAgreementId: {
            [Op.not]: null,
            [Op.ne]: ''
          },
          [Op.or]: [
            { publishLink: null },
            { publishLink: '' }
          ]
        }
      });
      
      return {
        ...stats,
        hasDataButNoLinkCount,
        crmSyncedNoLinkCount,
        analysis: {
          dataIntegrityIssue: hasDataButNoLinkCount > 0,
          crmMappingIssue: crmSyncedNoLinkCount > 0,
          overallHealthy: stats.publishLinkRate > 80
        }
      };
      
    } catch (error) {
      console.error('❌ 分析publishLink问题失败:', error.message);
      throw error;
    }
  }
}

module.exports = PublishLinkFixService;
