/**
 * 巨量星图爬虫
 * 基于 xingtu_test.js 实现的巨量星图平台爬虫
 */

const axios = require('axios');
const BaseCrawler = require('./BaseCrawler');
const CookieManager = require('../../CookieManager');
const AuthorVideoService = require('../../AuthorVideoService');

class XingtuCrawler extends BaseCrawler {
  constructor() {
    super('juxingtu');

    // Cookie管理器
    this.cookieManager = new CookieManager();
    this.currentCookie = null;

    // 防爬虫配置
    this.antiCrawlerConfig = {
      minDelay: 1000,
      maxDelay: 3000,
      maxRetries: 3,
      retryDelay: 2000,
      requestTimeout: 10000
    };

    // 用户代理池
    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
    ];

    this.baseUrl = 'https://www.xingtu.cn';
    this.isRunning = false;
  }

  /**
   * 初始化爬虫
   */
  async initialize() {
    console.log('🌟 巨量星图爬虫初始化...');
    // 获取可用的Cookie
    await this.refreshCookie();
  }

  /**
   * 获取或刷新Cookie
   */
  async refreshCookie() {
    try {
      this.currentCookie = await this.cookieManager.getAvailableCookie('juxingtu');
      if (this.currentCookie) {
        console.log(`🍪 获取到可用Cookie: ${this.currentCookie.accountName}`);
      } else {
        console.error('❌ 没有可用的巨量星图Cookie，无法执行爬取任务');
        throw new Error('没有可用的巨量星图Cookie，请先添加有效的Cookie后再执行爬取任务');
      }
    } catch (error) {
      console.error('获取Cookie失败:', error.message);
      this.currentCookie = null;
      throw error;
    }
  }

  /**
   * 执行爬取任务
   * @param {Object} config 爬取配置
   * @param {Object} callbacks 回调函数
   */
  async crawl(config, callbacks = {}) {
    const taskId = config.crawlTaskId;
    this.isRunning = true;

    // 如果有任务ID，记录任务开始
    if (taskId) {
      this.markTaskStart(taskId, config);
    }

    const results = {
      totalCount: 0,
      successCount: 0,
      failedCount: 0,
      data: []
    };

    try {
      console.log(`🚀 开始爬取巨量星图数据，关键词: ${config.keywords}`);

      // 首先检查是否有可用的Cookie
      await this.refreshCookie();
      if (!this.currentCookie) {
        throw new Error('没有可用的巨量星图Cookie，无法执行爬取任务');
      }

      // 构建搜索参数
      const searchData = this.buildSearchData(config);

      // 分页爬取（支持断点续传）
      const startPage = config.startPage || 1;
      console.log(`🔄 从第 ${startPage} 页开始爬取（共 ${config.maxPages} 页）`);

      for (
        let page = startPage;
        page <= config.maxPages && this.isRunning && (!taskId || this.activeTasks.has(taskId));
        page++
      ) {
        try {
          console.log(`📄 正在爬取第 ${page} 页...`);

          // 更新进度
          const progress = {
            currentPage: page,
            totalPages: config.maxPages,
            percentage: Math.round(((page - 1) / config.maxPages) * 100),
            successCount: results.successCount,
            failedCount: results.failedCount
          };

          if (callbacks.onProgress) {
            await callbacks.onProgress(progress);
          }

          // 获取达人列表
          const pageData = await this.fetchAuthorList(searchData, page);

          if (!pageData || !pageData.authors || pageData.authors.length === 0) {
            console.log(`📄 第 ${page} 页无数据，停止爬取`);
            break;
          }

          console.log(`📄 第 ${page} 页获取到 ${pageData.authors.length} 个达人`);
          results.totalCount += pageData.authors.length;

          // 批量处理达人详细信息
          const crawlOptions = {
            saveVideos: config.saveVideos !== false, // 默认为true，除非明确设置为false
            crawlTaskId: config.crawlTaskId || null
          };
          await this.processAuthorsInBatches(pageData.authors, results, callbacks, crawlOptions);

          // 页面间延迟
          if (page < config.maxPages && this.isRunning && (!taskId || this.activeTasks.has(taskId))) {
            const pageDelay = this.getRandomDelay(3000, 5000);
            console.log(`⏳ 页面间等待 ${pageDelay}ms...`);
            await this.delay(pageDelay);
          }
        } catch (error) {
          console.error(`❌ 第 ${page} 页爬取失败:`, error.message);
          results.failedCount++;

          if (callbacks.onError) {
            await callbacks.onError(error);
          }
        }
      }

      // 最终进度更新
      if (callbacks.onProgress) {
        await callbacks.onProgress({
          currentPage: config.maxPages,
          totalPages: config.maxPages,
          percentage: 100,
          successCount: results.successCount,
          failedCount: results.failedCount
        });
      }

      console.log(`✅ 巨量星图爬取完成，成功: ${results.successCount}, 失败: ${results.failedCount}`);
      return results;
    } catch (error) {
      console.error('❌ 巨量星图爬取失败:', error.message);
      throw error;
    } finally {
      this.isRunning = false;
      // 如果有任务ID，记录任务结束
      if (taskId) {
        this.markTaskEnd(taskId);
      }
    }
  }

  /**
   * 构建搜索数据
   * @param {Object} config 配置
   */
  buildSearchData(config) {
    return {
      scene_param: {
        platform_source: 1,
        search_scene: 1,
        display_scene: 1,
        task_category: 1,
        marketing_target: 1,
        first_industry_id: 0
      },
      page_param: {
        page: '1',
        limit: config.pageSize?.toString() || '20'
      },
      sort_param: {
        sort_field: {
          field_name: 'score'
        },
        sort_type: 2
      },
      attribute_filter: [
        {
          field: {
            field_name: 'price_by_video_type__ge',
            rel_id: '2'
          },
          field_value: '0'
        }
      ],
      search_param: {
        seach_type: 3,
        keyword: config.keywords,
        time_range_days: 180,
        is_new_content_query: true
      }
    };
  }

  /**
   * 获取达人列表
   * @param {Object} searchData 搜索数据
   * @param {number} page 页码
   */
  async fetchAuthorList(searchData, page) {
    const url = `${this.baseUrl}/gw/api/gsearch/search_for_author_square`;
    const requestData = { ...searchData };
    requestData.page_param.page = page.toString();

    const config = this.createRequestConfig('post', url, requestData);
    return await this.fetchWithRetry(url, config);
  }

  /**
   * 批量处理达人信息
   * @param {Array} authors 达人列表
   * @param {Object} results 结果对象
   * @param {Object} callbacks 回调函数
   * @param {Object} options 选项配置
   */
  async processAuthorsInBatches(authors, results, callbacks, options = {}) {
    const batchSize = 1; // 每批只处理1个达人，避免数据错乱
    const taskId = options.crawlTaskId;

    for (let i = 0; i < authors.length && this.isRunning && (!taskId || this.activeTasks.has(taskId)); i += batchSize) {
      const author = authors[i]; // 直接取单个达人
      console.log(`🔄 处理达人 ${i + 1}/${authors.length}: ${author.star_id}`);

      try {
        const detail = await this.getAuthorDetail(author.star_id, author, options);

        if (detail && detail.nickname && detail.nickname !== '暂无') {
          results.successCount++;
          results.data.push(detail);

          if (callbacks.onResult) {
            await callbacks.onResult(detail);
          }

          console.log(`✅ 达人 ${author.star_id} (${detail.nickname}) 信息获取成功`);
          console.log(`   播放量中位数: ${detail.playMid || '无'}`);
        } else {
          console.log(`⚠️ 达人 ${author.star_id} 信息不完整，跳过`);
          results.failedCount++;
        }
      } catch (error) {
        console.error(`❌ 获取达人 ${author.star_id} 信息失败:`, error.message);
        results.failedCount++;

        if (callbacks.onError) {
          await callbacks.onError(error);
        }
      }

      // 达人间延迟
      if (i + 1 < authors.length && this.isRunning && (!taskId || this.activeTasks.has(taskId))) {
        const delay = this.getRandomDelay(3000, 5000);
        console.log(`⏳ 达人间等待 ${delay}ms...`);
        await this.delay(delay);
      }
    }
  }

  /**
   * 获取达人详细信息
   * @param {string} authorId 达人ID
   * @param {Object} author 原始达人对象（可选，用于批量处理时传入）
   * @param {Object} options 选项配置
   */
  async getAuthorDetail(authorId, author = null, options = {}) {
    try {
      // 获取达人基本信息
      const url = `${this.baseUrl}/gw/api/gauthor/author_get_business_card_info?o_author_id=${authorId}`;
      const config = this.createRequestConfig('get', url);
      const response = await this.fetchWithRetry(url, config);

      if (!response || !response.card_info) {
        console.warn(`⚠️ 达人 ${authorId} 基本信息获取失败`);
        return null;
      }

      const cardInfo = response.card_info;
      console.log(`✅ 达人 ${authorId} 基本信息获取成功: ${cardInfo.nick_name || '无昵称'}`);

      // 获取达人视频信息
      const videoInfo = await this.getAuthorVideoInfo(authorId);

      // 获取达人播放量中位数
      const medianPlay = await this.getAuthorMedianPlay(authorId);

      // 构建完整的达人信息对象
      const authorDetail = {
        platform: 'juxingtu',
        platformUserId: authorId,
        nickname: cardInfo?.nick_name || '暂无',
        avatarUrl: cardInfo?.avatar_uri || null,
        followersCount: this.parseFollowerCount(cardInfo?.follower),
        city: cardInfo?.city || '暂无',
        uniqueId: cardInfo?.unique_id || '暂无',
        contactInfo: {
          wechat: cardInfo?.wechat || null,
          phone: cardInfo?.phone || null
        },
        videoStats: {
          videoCount: videoInfo.videoCount || 0,
          averagePlay: videoInfo.averagePlay || 0,
          totalPlay: videoInfo.totalPlay || 0,
          totalLike: videoInfo.totalLike || 0,
          totalComment: videoInfo.totalComment || 0,
          totalShare: videoInfo.totalShare || 0
        },
        rawData: {
          cardInfo
        },
        // 达人扩展信息（来自详情接口的动态附加信息）
        authorExtInfo: this.extractAuthorExtInfoFromDetail(cardInfo),
        // 播放量中位数
        playMid: medianPlay
      };

      // 如果启用了视频保存功能，则保存视频数据到数据库
      if (options.saveVideos && options.crawlTaskId && videoInfo.videos && videoInfo.videos.length > 0) {
        try {
          console.log(`🎬 开始保存达人 ${authorId} 的视频数据到数据库...`);

          const videoSaveResult = await AuthorVideoService.saveVideosFromCrawlTask(
            authorDetail,
            options.crawlTaskId,
            videoInfo.videos
          );

          console.log(`✅ 达人 ${authorId} 视频保存结果:`, {
            total: videoSaveResult.total,
            success: videoSaveResult.success,
            created: videoSaveResult.created,
            updated: videoSaveResult.updated,
            failed: videoSaveResult.failed
          });

          // 将视频保存结果添加到返回数据中
          authorDetail.videoSaveResult = videoSaveResult;
        } catch (videoSaveError) {
          console.error(`❌ 保存达人 ${authorId} 视频数据失败:`, videoSaveError.message);
          // 不影响主流程，只记录错误
          authorDetail.videoSaveError = videoSaveError.message;
        }
      }

      return authorDetail;
    } catch (error) {
      console.error(`❌ 获取达人 ${authorId} 详细信息失败:`, error.message);
      return null;
    }
  }

  /**
   * 获取达人视频信息
   * @param {string} authorId 达人ID
   */
  async getAuthorVideoInfo(authorId) {
    try {
      const url = `${this.baseUrl}/gw/api/author/get_author_show_items_v2?o_author_id=${authorId}&platform_channel=1&platform_source=1&limit=15&only_assign=true&flow_type=0`;
      const config = this.createRequestConfig('get', url);
      const data = await this.fetchWithRetry(url, config);

      if (!data || (!data.latest_item_info && !data.latest_star_item_info)) {
        console.warn(`[警告] 作者 ${authorId} 没有视频数据`);
        return { videoCount: 0, videos: [] };
      }

      // 合并两个数组的视频信息
      const latest_item_info = data.latest_item_info || [];
      const latest_star_item_info = data.latest_star_item_info || [];
      const combinedInfo = latest_item_info.concat(latest_star_item_info);

      console.log(`[信息] 作者 ${authorId} 共获取到 ${combinedInfo.length} 个视频`);

      // 按照发布时间排序（最新的在前）
      combinedInfo.sort((a, b) => new Date(b.item_date) - new Date(a.item_date));

      // 取最新的10个视频
      // const recentVideos = combinedInfo.slice(0, 10);
      const recentVideos = combinedInfo || [];

      console.log(`[完成] 作者 ${authorId} 最近${recentVideos.length}个视频信息获取成功`);

      const videos = recentVideos.map(video => ({
        videoId: video.item_id,
        title: video.title || '无标题',
        playCount: video.play || 0,
        likeCount: video.like || 0,
        commentCount: video.comment || 0,
        shareCount: video.share || 0,
        publishTime: video.item_date,
        duration: video.duration || 0,
        // 播放链接
        videoUrl: video.url,
        // 封面链接
        videoCover: video.item_cover
      }));

      // 计算统计数据
      const totalPlay = videos.reduce((sum, v) => sum + (v.playCount || 0), 0);
      const totalLike = videos.reduce((sum, v) => sum + (v.likeCount || 0), 0);
      const totalComment = videos.reduce((sum, v) => sum + (v.commentCount || 0), 0);
      const totalShare = videos.reduce((sum, v) => sum + (v.shareCount || 0), 0);
      const averagePlay = videos.length > 0 ? Math.round(totalPlay / videos.length) : 0;

      return {
        videoCount: videos.length,
        averagePlay,
        totalPlay,
        totalLike,
        totalComment,
        totalShare,
        videos
      };
    } catch (error) {
      console.error(`获取达人 ${authorId} 视频信息失败:`, error.message);
      return { videoCount: 0, videos: [] };
    }
  }

  /**
   * 获取达人播放量中位数
   * @param {string} authorId 达人ID
   */
  async getAuthorMedianPlay(authorId) {
    try {
      // 添加随机延迟以避免触发反爬虫机制
      const delay = this.getRandomDelay(1000, 3000);
      console.log(`⏳ 获取播放量中位数前等待 ${delay}ms...`);
      await this.delay(delay);

      // 策略1: 先尝试 range=3
      let url = `${this.baseUrl}/gw/api/data_sp/get_author_spread_info?o_author_id=${authorId}&platform_source=1&platform_channel=1&type=2&flow_type=0&only_assign=true&range=3`;
      let config = this.createRequestConfig('get', url);
      let response = await this.fetchWithRetry(url, config);

      if (response && response.play_mid) {
        console.log(`✅ 达人 ${authorId} 播放量中位数获取成功 (range=3): ${response.play_mid}`);
        return response.play_mid;
      }

      // 策略2: 如果 range=3 没有数据，尝试 range=2
      console.log(`🔄 range=3 无数据，尝试 range=2...`);

      // 添加短暂延迟
      await this.delay(1000);

      url = `${this.baseUrl}/gw/api/data_sp/get_author_spread_info?o_author_id=${authorId}&platform_source=1&platform_channel=1&type=1&flow_type=0&only_assign=false&range=2`;
      config = this.createRequestConfig('get', url);
      response = await this.fetchWithRetry(url, config);

      if (response && response.play_mid) {
        console.log(`✅ 达人 ${authorId} 播放量中位数获取成功 (range=2): ${response.play_mid}`);
        return response.play_mid;
      } else {
        console.warn(`⚠️ 达人 ${authorId} 播放量中位数获取失败或无数据 (已尝试 range=3 和 range=2)`);
        return null;
      }
    } catch (error) {
      console.error(`❌ 获取达人 ${authorId} 播放量中位数失败:`, error.message);
      return null;
    }
  }

  /**
   * 解析粉丝数量
   * @param {string} followerStr 粉丝数字符串
   */
  parseFollowerCount(followerStr) {
    if (!followerStr) return 0;

    const str = followerStr.toString().toLowerCase();
    const num = parseFloat(str);

    if (str.includes('万')) {
      return Math.round(num * 10000);
    } else if (str.includes('k')) {
      return Math.round(num * 1000);
    } else {
      return Math.round(num);
    }
  }

  /**
   * 提取达人扩展信息（从详情接口）
   * 从巨量星图详情接口返回的 cardInfo 对象中提取动态附加信息
   * @param {Object} cardInfo 达人详情卡片信息
   * @returns {Object} 扩展信息对象
   */
  extractAuthorExtInfoFromDetail(cardInfo) {
    if (!cardInfo || typeof cardInfo !== 'object') {
      return {};
    }

    // 定义需要排除的基础字段（这些字段已经在其他地方处理）
    const excludeFields = new Set([
      'star_id',
      'nick_name',
      'avatar_uri',
      'follower',
      'city',
      'unique_id',
      'wechat',
      'phone'
    ]);

    // 提取所有非基础字段作为扩展信息
    const extInfo = {};

    for (const [key, value] of Object.entries(cardInfo)) {
      if (!excludeFields.has(key) && value !== null && value !== undefined) {
        extInfo[key] = value;
      }
    }

    // 添加提取时间戳和来源信息
    extInfo._extractedAt = new Date().toISOString();
    extInfo._source = 'juxingtu_author_detail';
    extInfo._version = '1.0';

    console.log(`📋 从详情接口提取达人扩展信息: ${Object.keys(extInfo).length} 个字段`);

    return extInfo;
  }

  /**
   * 提取达人扩展信息（从列表接口）
   * 从巨量星图列表接口返回的 author 对象中提取动态附加信息
   * @param {Object} author 原始达人对象
   * @returns {Object} 扩展信息对象
   */
  extractAuthorExtInfo(author) {
    if (!author || typeof author !== 'object') {
      return {};
    }

    // 定义需要排除的基础字段（这些字段已经在其他地方处理）
    const excludeFields = new Set([
      'star_id',
      'nick_name',
      'avatar_uri',
      'follower',
      'city',
      'unique_id',
      'wechat',
      'phone',
      'last_10_items'
    ]);

    // 提取所有非基础字段作为扩展信息
    const extInfo = {};

    for (const [key, value] of Object.entries(author)) {
      if (!excludeFields.has(key) && value !== null && value !== undefined) {
        extInfo[key] = value;
      }
    }

    // 添加提取时间戳和来源信息
    extInfo._extractedAt = new Date().toISOString();
    extInfo._source = 'juxingtu_author_list';
    extInfo._version = '1.0';

    console.log(`📋 提取达人 ${author.star_id} 扩展信息: ${Object.keys(extInfo).length} 个字段`);

    return extInfo;
  }

  /**
   * 创建请求配置
   * @param {string} method 请求方法
   * @param {string} url 请求URL
   * @param {Object} data 请求数据
   */
  createRequestConfig(method, url, data = null) {
    // 使用Cookie中的User-Agent或随机选择
    const userAgent = this.currentCookie?.userAgent || this.getRandomUserAgent();

    const config = {
      method,
      url,
      headers: {
        'User-Agent': userAgent,
        Accept: 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        Connection: 'keep-alive',
        'Agw-Js-Conv': 'str',
        'Content-Type': 'application/json',
        Accept: '*/*',
        Host: 'www.xingtu.cn',
        Connection: 'keep-alive'
      },
      timeout: this.antiCrawlerConfig.requestTimeout
    };

    // 添加Cookie
    console.log(`currentCookie: ${JSON.stringify(this.currentCookie)}`);
    if (this.currentCookie?.cookieString) {
      config.headers['Cookie'] = this.currentCookie.cookieString;
      console.log(`🍪 使用Cookie: ${this.currentCookie.cookieString.substring(0, 50)}...`);
    } else {
      console.error('❌ 没有可用的Cookie，无法发起请求');
      throw new Error('没有可用的Cookie，无法发起请求');
    }

    if (method.toLowerCase() === 'post' && data) {
      config.data = data;
      // config.headers['Content-Type'] = 'application/json;charset=UTF-8';
      console.log(`📤 请求数据: ${JSON.stringify(data, null, 2)}`);
    }

    return config;
  }

  /**
   * 带重试的请求方法
   * @param {string} url 请求URL
   * @param {Object} config 请求配置
   */
  async fetchWithRetry(url, config) {
    let lastError;
    let cookieRefreshed = false;

    for (let attempt = 1; attempt <= this.antiCrawlerConfig.maxRetries; attempt++) {
      try {
        // 请求前延迟
        const delay = this.getRandomDelay(this.antiCrawlerConfig.minDelay, this.antiCrawlerConfig.maxDelay);
        await this.delay(delay);

        console.log(`🚀 发起请求: ${config.method.toUpperCase()} ${url}`);
        const response = await axios(config);
        // 打断点
        console.log(`✅ 请求成功: ${response.status}`);

        if (response.data && response.status === 200) {
          return response.data;
        } else {
          // 检查是否是认证相关错误
          if (this.isAuthError(response.data) && !cookieRefreshed) {
            console.warn('🔄 检测到认证错误，尝试刷新Cookie...');
            await this.handleCookieError();
            cookieRefreshed = true;

            // 更新请求配置中的Cookie
            if (this.currentCookie?.cookieString) {
              config.headers['Cookie'] = this.currentCookie.cookieString;
            }

            // 重试当前请求
            attempt--; // 不计入重试次数
            continue;
          }

          throw new Error(`API返回错误: ${response.data?.status_msg || JSON.stringify(response.data) || '未知错误'}`);
        }
      } catch (error) {
        lastError = error;

        // 详细的错误信息
        let errorDetails = `${error.message}`;
        if (error.response) {
          errorDetails += ` (状态码: ${error.response.status})`;
          if (error.response.data) {
            errorDetails += ` 响应: ${JSON.stringify(error.response.data).substring(0, 200)}`;
          }
        }

        console.warn(`请求失败 (尝试 ${attempt}/${this.antiCrawlerConfig.maxRetries}): ${errorDetails}`);

        // 如果是网络错误且还有Cookie可用，尝试切换Cookie
        if (this.isNetworkError(error) && !cookieRefreshed && this.currentCookie) {
          console.warn('🔄 网络错误，尝试切换Cookie...');
          await this.refreshCookie();
          cookieRefreshed = true;

          if (this.currentCookie?.cookieString) {
            config.headers['Cookie'] = this.currentCookie.cookieString;
            config.headers['User-Agent'] = this.currentCookie.userAgent || this.getRandomUserAgent();
          }

          attempt--; // 不计入重试次数
          continue;
        }

        if (attempt < this.antiCrawlerConfig.maxRetries) {
          const retryDelay = this.antiCrawlerConfig.retryDelay * attempt;
          console.log(`⏳ ${retryDelay}ms 后重试...`);
          await this.delay(retryDelay);
        }
      }
    }

    throw lastError;
  }

  /**
   * 检查是否是认证错误
   * @param {Object} responseData 响应数据
   */
  isAuthError(responseData) {
    const authErrorCodes = [401, 403, 10001, 10002]; // 常见的认证错误码
    return (
      authErrorCodes.includes(responseData?.status_code) ||
      (responseData?.status_msg && responseData.status_msg.includes('登录'))
    );
  }

  /**
   * 检查是否是网络错误
   * @param {Error} error 错误对象
   */
  isNetworkError(error) {
    return (
      error.code === 'ECONNRESET' ||
      error.code === 'ETIMEDOUT' ||
      error.message.includes('timeout') ||
      error.message.includes('Network Error')
    );
  }

  /**
   * 处理Cookie错误
   */
  async handleCookieError() {
    if (this.currentCookie) {
      // 标记当前Cookie为过期
      await this.cookieManager.markCookieAsExpired(this.currentCookie.id, '请求返回认证错误');
    }

    // 获取新的Cookie
    await this.refreshCookie();
  }

  /**
   * 获取随机用户代理
   */
  getRandomUserAgent() {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  /**
   * 获取随机延迟时间
   * @param {number} min 最小延迟
   * @param {number} max 最大延迟
   */
  getRandomDelay(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 停止爬虫
   * @param {number} taskId 任务ID（可选）
   */
  async stop(taskId = null) {
    // 调用父类的stop方法来处理activeTasks管理
    await super.stop(taskId);

    if (taskId) {
      // 停止特定任务
      console.log(`⏹️ 巨量星图爬虫任务 ${taskId} 已停止`);
    } else {
      // 停止所有任务
      this.isRunning = false;
      console.log('⏹️ 巨量星图爬虫已停止');
    }
  }
}

module.exports = XingtuCrawler;
