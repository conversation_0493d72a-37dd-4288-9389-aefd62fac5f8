/**
 * 基础爬虫类
 * 定义爬虫的通用接口和基础功能
 */

class BaseCrawler {
  constructor(platform) {
    this.platform = platform;
    this.isInitialized = false;
    this.isRunning = false;
    this.activeTasks = new Map();
  }

  /**
   * 初始化爬虫
   * 子类应该重写此方法
   */
  async initialize() {
    throw new Error('initialize() method must be implemented by subclass');
  }

  /**
   * 执行爬取任务
   * 子类应该重写此方法
   * @param {Object} config 爬取配置
   * @param {Object} callbacks 回调函数
   */
  async crawl(config, callbacks = {}) {
    throw new Error('crawl() method must be implemented by subclass');
  }

  /**
   * 停止爬取任务
   * @param {number} taskId 任务ID（可选）
   */
  async stop(taskId = null) {
    if (taskId) {
      // 停止特定任务
      if (this.activeTasks.has(taskId)) {
        this.activeTasks.delete(taskId);
        console.log(`⏹️ 停止任务: ${taskId}`);
      }
    } else {
      // 停止所有任务
      this.isRunning = false;
      this.activeTasks.clear();
      console.log(`⏹️ 停止所有 ${this.platform} 爬虫任务`);
    }
  }

  /**
   * 获取爬虫状态
   */
  getStatus() {
    return {
      platform: this.platform,
      initialized: this.isInitialized,
      running: this.isRunning,
      activeTasks: Array.from(this.activeTasks.keys()),
      activeTaskCount: this.activeTasks.size
    };
  }

  /**
   * 验证配置参数
   * @param {Object} config 配置对象
   */
  validateConfig(config) {
    if (!config) {
      throw new Error('配置参数不能为空');
    }
    
    if (!config.keywords) {
      throw new Error('搜索关键词不能为空');
    }
    
    if (config.maxPages && (config.maxPages < 1 || config.maxPages > 50)) {
      throw new Error('最大页数必须在1-50之间');
    }
    
    if (config.pageSize && (config.pageSize < 1 || config.pageSize > 100)) {
      throw new Error('每页数量必须在1-100之间');
    }
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取随机延迟时间
   * @param {number} min 最小延迟
   * @param {number} max 最大延迟
   */
  getRandomDelay(min = 1000, max = 3000) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 安全的JSON解析
   * @param {string} jsonStr JSON字符串
   * @param {*} defaultValue 默认值
   */
  safeJsonParse(jsonStr, defaultValue = null) {
    try {
      return JSON.parse(jsonStr);
    } catch (error) {
      console.warn('JSON解析失败:', error.message);
      return defaultValue;
    }
  }

  /**
   * 清理文本内容
   * @param {string} text 原始文本
   */
  cleanText(text) {
    if (!text || typeof text !== 'string') {
      return '';
    }
    
    return text
      .replace(/[\r\n\t]/g, ' ')  // 替换换行符和制表符
      .replace(/\s+/g, ' ')       // 合并多个空格
      .trim();                    // 去除首尾空格
  }

  /**
   * 解析数字（支持中文单位）
   * @param {string|number} value 数值字符串
   */
  parseNumber(value) {
    if (typeof value === 'number') {
      return value;
    }
    
    if (!value || typeof value !== 'string') {
      return 0;
    }
    
    const str = value.toString().toLowerCase().trim();
    const num = parseFloat(str);
    
    if (isNaN(num)) {
      return 0;
    }
    
    // 处理中文单位
    if (str.includes('万')) {
      return Math.round(num * 10000);
    } else if (str.includes('千') || str.includes('k')) {
      return Math.round(num * 1000);
    } else if (str.includes('亿')) {
      return Math.round(num * 100000000);
    } else {
      return Math.round(num);
    }
  }

  /**
   * 格式化URL
   * @param {string} url 原始URL
   * @param {string} baseUrl 基础URL
   */
  formatUrl(url, baseUrl = '') {
    if (!url) return null;
    
    // 如果已经是完整URL，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    
    // 如果是相对路径，拼接基础URL
    if (baseUrl && url.startsWith('/')) {
      return baseUrl.replace(/\/$/, '') + url;
    }
    
    return url;
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 记录任务开始
   * @param {string} taskId 任务ID
   * @param {Object} config 任务配置
   */
  markTaskStart(taskId, config) {
    this.activeTasks.set(taskId, {
      startTime: new Date(),
      config,
      status: 'running'
    });
  }

  /**
   * 记录任务结束
   * @param {string} taskId 任务ID
   */
  markTaskEnd(taskId) {
    if (this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId);
      task.endTime = new Date();
      task.status = 'completed';
      task.duration = task.endTime.getTime() - task.startTime.getTime();
      
      // 可以选择保留一段时间的历史记录，或者立即删除
      setTimeout(() => {
        this.activeTasks.delete(taskId);
      }, 60000); // 1分钟后删除
    }
  }

  /**
   * 获取任务信息
   * @param {string} taskId 任务ID
   */
  getTaskInfo(taskId) {
    return this.activeTasks.get(taskId) || null;
  }

  /**
   * 检查任务是否应该停止
   * @param {string} taskId 任务ID
   */
  shouldStopTask(taskId) {
    return !this.isRunning || !this.activeTasks.has(taskId);
  }

  /**
   * 获取平台名称
   */
  getPlatformName() {
    const platformNames = {
      'juxingtu': '巨量星图',
      'xiaohongshu': '小红书',
      'douyin': '抖音',
      'kuaishou': '快手'
    };
    
    return platformNames[this.platform] || this.platform;
  }
}

module.exports = BaseCrawler;
