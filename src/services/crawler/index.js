/**
 * 爬虫服务主入口
 * 提供统一的爬虫服务接口
 */

const CrawlerManager = require('./CrawlerManager');
const TaskQueue = require('./TaskQueue');
const { CrawlTask, PublicInfluencer, CrawlLog, sequelize: dbInstance } = require('../../models');

class CrawlerService {
  constructor() {
    this.manager = new CrawlerManager();
    this.taskQueue = new TaskQueue();
    this.isInitialized = false;
  }

  /**
   * 初始化爬虫服务
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 初始化任务队列
      await this.taskQueue.initialize();

      // 初始化爬虫管理器
      await this.manager.initialize();

      // 恢复未完成的任务
      await this.recoverPendingTasks();

      // 检查和修复状态不一致的任务
      await this.checkAndFixInconsistentTasks();

      this.isInitialized = true;
      console.log('✅ 爬虫服务初始化成功');
    } catch (error) {
      console.error('❌ 爬虫服务初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 创建爬虫任务
   * @param {Object} taskData 任务数据
   * @returns {Promise<Object>} 创建的任务
   */
  async createTask(taskData) {
    try {
      // 验证任务数据
      this.validateTaskData(taskData);

      // 创建数据库记录
      const task = await CrawlTask.create({
        taskName: taskData.taskName,
        platform: taskData.platform,
        keywords: taskData.keywords,
        config: taskData.config || {},
        priority: taskData.priority || 0,
        maxPages: taskData.maxPages || 5,
        createdBy: taskData.createdBy,
        status: 'pending'
      });

      // 添加到任务队列
      await this.taskQueue.addTask(task);

      console.log(`✅ 爬虫任务创建成功: ${task.id} - ${task.taskName}`);
      return task;
    } catch (error) {
      console.error('❌ 创建爬虫任务失败:', error.message);
      throw error;
    }
  }

  /**
   * 启动任务
   * @param {number} taskId 任务ID
   */
  async startTask(taskId) {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      if (task.status !== 'pending' && task.status !== 'paused') {
        throw new Error(`任务状态不允许启动: ${task.status}`);
      }

      // 更新任务状态
      await task.update({
        status: 'running',
        startedAt: new Date()
      });

      // 启动爬虫
      await this.manager.startCrawling(task);

      console.log(`✅ 爬虫任务启动成功: ${taskId}`);
      return task;
    } catch (error) {
      console.error(`❌ 启动爬虫任务失败: ${taskId}`, error.message);
      throw error;
    }
  }

  /**
   * 停止任务（暂停）
   * @param {number} taskId 任务ID
   */
  async stopTask(taskId) {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      // 调试日志：打印任务当前状态
      console.log(`🔍 [CrawlerService] 停止任务 ${taskId}:`);
      console.log(`   当前状态: ${task.status}`);
      console.log(`   任务名称: ${task.taskName}`);
      console.log(`   创建时间: ${task.createdAt}`);
      console.log(`   开始时间: ${task.startedAt}`);

      // 检查是否在内存中运行
      const isInMemory = this.manager.activeTasks.has(taskId);
      console.log(`   内存中是否存在: ${isInMemory}`);

      // 检查任务状态
      if (task.status !== 'running') {
        console.log(`⚠️ [CrawlerService] 任务 ${taskId} 状态不允许停止: ${task.status}`);

        if (isInMemory) {
          console.log(`🔧 [CrawlerService] 任务在内存中运行但数据库状态不正确，使用强制停止`);
          // 如果任务在内存中运行，但数据库状态不对，使用强制停止
          return await this.forceStopTask(taskId);
        } else {
          // 如果任务既不在运行状态，也不在内存中，检查是否可以直接设置为暂停
          if (task.status === 'pending' || task.status === 'failed' || task.status === 'completed') {
            console.log(`🔧 [CrawlerService] 任务状态为 ${task.status}，直接设置为暂停状态`);
            await task.update({
              status: 'paused',
              pausedAt: new Date()
            });
            return task;
          } else {
            throw new Error(`任务状态不允许停止: ${task.status}`);
          }
        }
      } else {
        // 任务状态是running，但检查是否真的在内存中运行
        if (!isInMemory) {
          console.log(`⚠️ [CrawlerService] 任务 ${taskId} 数据库状态为running但不在内存中运行`);
          console.log(`🔧 [CrawlerService] 可能原因: 任务已完成、异常结束或服务重启后状态未同步`);
          console.log(`🔧 [CrawlerService] 将直接更新数据库状态为暂停`);

          // 直接更新数据库状态为暂停
          await task.update({
            status: 'paused',
            pausedAt: new Date()
          });

          console.log(`✅ [CrawlerService] 已将任务 ${taskId} 状态同步为暂停`);
          return task;
        }
      }

      // 停止爬虫
      await this.manager.stopCrawling(taskId);

      // 更新任务状态为暂停，保存当前进度
      await task.update({
        status: 'paused',
        pausedAt: new Date()
      });

      console.log(`✅ 爬虫任务暂停成功: ${taskId}`);
      return task;
    } catch (error) {
      console.error(`❌ 暂停爬虫任务失败: ${taskId}`, error.message);
      throw error;
    }
  }

  /**
   * 强制停止任务（用于处理状态不一致的情况）
   * @param {number} taskId 任务ID
   */
  async forceStopTask(taskId) {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      console.log(`🔧 [CrawlerService] 强制停止任务 ${taskId}:`);
      console.log(`   当前状态: ${task.status}`);

      // 检查是否在内存中运行
      const isInMemory = this.manager.activeTasks.has(taskId);
      console.log(`   内存中是否存在: ${isInMemory}`);

      if (isInMemory) {
        // 如果在内存中，先停止爬虫
        try {
          await this.manager.stopCrawling(taskId);
          console.log(`✅ [CrawlerService] 已从内存中停止任务`);
        } catch (error) {
          console.log(`⚠️ [CrawlerService] 从内存中停止任务失败: ${error.message}`);
          // 强制从活跃任务中移除
          this.manager.activeTasks.delete(taskId);
          console.log(`🗑️ [CrawlerService] 已强制从活跃任务列表中移除`);
        }
      }

      // 更新数据库状态
      await task.update({
        status: 'paused',
        pausedAt: new Date()
      });

      console.log(`✅ 强制停止任务成功: ${taskId}`);
      return task;
    } catch (error) {
      console.error(`❌ 强制停止任务失败: ${taskId}`, error.message);
      throw error;
    }
  }

  /**
   * 恢复任务
   * @param {number} taskId 任务ID
   */
  async resumeTask(taskId) {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      if (task.status !== 'paused') {
        throw new Error(`任务状态不允许恢复: ${task.status}`);
      }

      // 更新任务状态为运行中
      await task.update({
        status: 'running',
        resumedAt: new Date(),
        pausedAt: null
      });

      // 启动爬虫（从断点继续）
      await this.manager.startCrawling(task);

      console.log(`✅ 爬虫任务恢复成功: ${taskId}`);
      return task;
    } catch (error) {
      console.error(`❌ 恢复爬虫任务失败: ${taskId}`, error.message);
      throw error;
    }
  }

  /**
   * 重试任务
   * @param {number} taskId 任务ID
   */
  async retryTask(taskId) {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      // 检查任务状态
      // if (task.status !== 'failed') {
      //   throw new Error('只有失败的任务才能重试');
      // }

      // 检查重试次数限制
      const currentRetryCount = task.retryCount || 0;
      const maxRetries = task.maxRetries || 3;

      if (currentRetryCount >= maxRetries) {
        throw new Error(`任务已达到最大重试次数(${maxRetries})`);
      }

      // 记录重试历史
      const retryHistory = task.retryHistory || [];
      const retryRecord = {
        retryCount: currentRetryCount + 1,
        retryTime: new Date(),
        previousError: task.errorMessage,
        reason: '手动重试'
      };
      retryHistory.push(retryRecord);

      // 重置任务状态
      await task.update({
        status: 'pending',
        progress: 0,
        currentPage: 1,
        successCount: 0,
        failedCount: 0,
        errorMessage: null,
        retryCount: currentRetryCount + 1,
        lastRetryAt: new Date(),
        retryHistory: retryHistory,
        startedAt: null,
        completedAt: null
      });

      // 清除之前的失败结果（可选，保留历史数据）
      // await PublicInfluencer.destroy({ where: { taskId, status: 'failed' } });

      // 重新添加到任务队列
      await this.taskQueue.addTask(task);

      console.log(`🔄 爬虫任务重试成功: ${taskId} (第${currentRetryCount + 1}次重试)`);
      return task;
    } catch (error) {
      console.error(`❌ 重试爬虫任务失败: ${taskId}`, error.message);
      throw error;
    }
  }

  /**
   * 批量重试任务
   * @param {Array<number>} taskIds 任务ID数组
   */
  async batchRetryTasks(taskIds) {
    try {
      if (!Array.isArray(taskIds) || taskIds.length === 0) {
        throw new Error('请选择要重试的任务');
      }

      let successCount = 0;
      const errors = [];

      for (const taskId of taskIds) {
        try {
          await this.retryTask(taskId);
          successCount++;
        } catch (error) {
          errors.push({ taskId, error: error.message });
        }
      }

      console.log(`🔄 批量重试完成: 成功 ${successCount} 个，失败 ${errors.length} 个`);
      return {
        successCount,
        errors
      };
    } catch (error) {
      console.error('❌ 批量重试任务失败:', error.message);
      throw error;
    }
  }

  /**
   * 删除任务
   * @param {number} taskId 任务ID
   */
  async deleteTask(taskId) {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      // 如果任务正在运行，先停止
      if (task.status === 'running') {
        await this.stopTask(taskId);
      }

      // 删除相关数据
      await PublicInfluencer.destroy({ where: { taskId } });
      await CrawlLog.destroy({ where: { taskId } });
      await task.destroy();

      console.log(`✅ 爬虫任务删除成功: ${taskId}`);
      return true;
    } catch (error) {
      console.error(`❌ 删除爬虫任务失败: ${taskId}`, error.message);
      throw error;
    }
  }

  /**
   * 获取任务列表
   * @param {Object} options 查询选项
   */
  async getTasks(options = {}) {
    try {
      const { page = 1, limit = 10, status, platform, createdBy, keyword } = options;

      const where = {};
      if (status) where.status = status;
      if (platform) where.platform = platform;
      if (createdBy) where.createdBy = createdBy;

      // 添加任务名称搜索支持
      if (keyword) {
        const { Op } = require('sequelize');
        where[Op.or] = [{ taskName: { [Op.like]: `%${keyword}%` } }, { keywords: { [Op.like]: `%${keyword}%` } }];
      }

      const result = await CrawlTask.findAndCountAll({
        where,
        include: [
          {
            model: PublicInfluencer,
            as: 'publicInfluencers',
            attributes: ['id', 'status']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        distinct: true // 修复：确保count正确，不受关联表影响
      });

      return {
        tasks: result.rows,
        pagination: {
          total: result.count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(result.count / parseInt(limit))
        }
      };
    } catch (error) {
      console.error('❌ 获取任务列表失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取任务详情
   * @param {number} taskId 任务ID
   */
  async getTaskDetail(taskId) {
    try {
      // 获取任务基本信息
      const task = await CrawlTask.findByPk(taskId);

      if (!task) {
        throw new Error('任务不存在');
      }

      // 获取结果统计信息（不加载具体数据，避免内存问题）
      const resultStats = await PublicInfluencer.findAll({
        where: { taskId },
        attributes: ['status', [dbInstance.fn('COUNT', dbInstance.col('id')), 'count']],
        group: ['status'],
        raw: true
      });

      // 暂时跳过日志查询，避免排序内存问题
      // TODO: 需要优化日志查询或增加数据库排序缓冲区
      const recentLogs = [];

      // 获取少量最新的结果样本（用于预览）
      const sampleResults = await PublicInfluencer.findAll({
        where: { taskId },
        order: [['createdAt', 'DESC']],
        limit: 10, // 只获取最新的10条结果作为样本
        attributes: ['id', 'nickname', 'platformUserId', 'followersCount', 'status', 'createdAt'] // 明确指定字段，排除 authorExtInfo
      });

      // 转换统计数据格式
      const statsMap = resultStats.reduce((acc, stat) => {
        acc[stat.status] = parseInt(stat.count);
        return acc;
      }, {});

      // 组装返回数据
      const taskDetail = {
        ...task.toJSON(),
        resultStats: {
          total: Object.values(statsMap).reduce((sum, count) => sum + count, 0),
          pending: statsMap.pending || 0,
          processed: statsMap.processed || 0,
          imported: statsMap.imported || 0,
          failed: statsMap.failed || 0
        },
        sampleResults,
        logs: recentLogs
      };

      return taskDetail;
    } catch (error) {
      console.error(`❌ 获取任务详情失败: ${taskId}`, error.message);
      throw error;
    }
  }

  /**
   * 检查和修复所有状态不一致的任务
   */
  async checkAndFixInconsistentTasks() {
    try {
      console.log('🔍 [CrawlerService] 开始检查状态不一致的任务...');

      // 获取所有running状态的任务
      const runningTasks = await CrawlTask.findAll({
        where: { status: 'running' }
      });

      console.log(`📊 [CrawlerService] 找到 ${runningTasks.length} 个running状态的任务`);

      const inconsistentTasks = [];
      const activeTaskIds = Array.from(this.manager.activeTasks.keys());

      console.log(`💾 [CrawlerService] 内存中活跃任务: [${activeTaskIds.join(', ')}]`);

      for (const task of runningTasks) {
        const isInMemory = this.manager.activeTasks.has(task.id);

        if (!isInMemory) {
          console.log(`⚠️ [CrawlerService] 发现状态不一致任务 ${task.id}: 数据库running但内存中不存在`);
          inconsistentTasks.push(task);

          // 自动修复：将状态设置为completed或failed
          const now = new Date();
          const shouldMarkAsCompleted = task.startedAt && now - task.startedAt > 60000; // 运行超过1分钟认为可能已完成

          const newStatus = shouldMarkAsCompleted ? 'completed' : 'failed';
          const updateData = {
            status: newStatus,
            [newStatus === 'completed' ? 'completedAt' : 'failedAt']: now
          };

          await task.update(updateData);
          console.log(`🔧 [CrawlerService] 已将任务 ${task.id} 状态修复为 ${newStatus}`);
        }
      }

      console.log(`✅ [CrawlerService] 状态检查完成，修复了 ${inconsistentTasks.length} 个不一致的任务`);

      return {
        totalRunningTasks: runningTasks.length,
        inconsistentTasks: inconsistentTasks.length,
        fixedTasks: inconsistentTasks.map(t => ({
          id: t.id,
          taskName: t.taskName,
          originalStatus: 'running'
        }))
      };
    } catch (error) {
      console.error('❌ [CrawlerService] 检查状态不一致任务失败:', error.message);
      throw error;
    }
  }

  /**
   * 诊断任务状态（用于调试）
   * @param {number} taskId 任务ID
   */
  async diagnoseTaskStatus(taskId) {
    try {
      console.log(`🔍 [诊断] 任务 ${taskId} 状态诊断:`);

      // 检查数据库中的任务状态
      const task = await CrawlTask.findByPk(taskId);
      if (!task) {
        console.log(`❌ [诊断] 任务 ${taskId} 在数据库中不存在`);
        return { exists: false };
      }

      console.log(`📋 [诊断] 数据库中的任务信息:`);
      console.log(`   ID: ${task.id}`);
      console.log(`   名称: ${task.taskName}`);
      console.log(`   平台: ${task.platform}`);
      console.log(`   状态: ${task.status}`);
      console.log(`   创建时间: ${task.createdAt}`);
      console.log(`   开始时间: ${task.startedAt}`);
      console.log(`   暂停时间: ${task.pausedAt}`);
      console.log(`   完成时间: ${task.completedAt}`);
      console.log(`   当前页: ${task.currentPage}`);
      console.log(`   最大页数: ${task.maxPages}`);

      // 检查内存中的任务状态
      const isInMemory = this.manager.activeTasks.has(taskId);
      console.log(`💾 [诊断] 内存中的任务状态:`);
      console.log(`   是否在活跃任务列表: ${isInMemory}`);

      if (isInMemory) {
        const taskContext = this.manager.activeTasks.get(taskId);
        console.log(`   爬虫类型: ${taskContext.crawler.constructor.name}`);
        console.log(`   开始时间: ${taskContext.startTime}`);
        console.log(`   是否正在运行: ${taskContext.crawler.isRunning}`);
      }

      console.log(`🔄 [诊断] 活跃任务总览:`);
      console.log(`   总数: ${this.manager.activeTasks.size}`);
      console.log(`   任务列表: [${Array.from(this.manager.activeTasks.keys()).join(', ')}]`);

      // 状态一致性检查
      const statusConsistent = (task.status === 'running') === isInMemory;
      console.log(`⚖️ [诊断] 状态一致性: ${statusConsistent ? '✅ 一致' : '❌ 不一致'}`);

      if (!statusConsistent) {
        console.log(`⚠️ [诊断] 状态不一致详情:`);
        console.log(`   数据库状态: ${task.status}`);
        console.log(`   内存状态: ${isInMemory ? '运行中' : '未运行'}`);
        console.log(`   建议操作: ${isInMemory ? '同步数据库状态为running' : '清理内存状态或更新数据库'}`);
      }

      return {
        exists: true,
        task: {
          id: task.id,
          status: task.status,
          taskName: task.taskName,
          platform: task.platform,
          createdAt: task.createdAt,
          startedAt: task.startedAt,
          pausedAt: task.pausedAt,
          completedAt: task.completedAt
        },
        memory: {
          isActive: isInMemory,
          totalActiveTasks: this.manager.activeTasks.size,
          activeTaskIds: Array.from(this.manager.activeTasks.keys())
        },
        consistent: statusConsistent
      };
    } catch (error) {
      console.error(`❌ [诊断] 诊断任务 ${taskId} 失败:`, error.message);
      throw error;
    }
  }

  /**
   * 验证任务数据
   * @param {Object} taskData 任务数据
   */
  validateTaskData(taskData) {
    if (!taskData.taskName) {
      throw new Error('任务名称不能为空');
    }
    if (!taskData.platform) {
      throw new Error('平台类型不能为空');
    }
    if (!['xiaohongshu', 'juxingtu'].includes(taskData.platform)) {
      throw new Error('不支持的平台类型');
    }
    if (!taskData.keywords) {
      throw new Error('搜索关键词不能为空');
    }
    if (!taskData.createdBy) {
      throw new Error('创建者ID不能为空');
    }
  }

  /**
   * 恢复未完成的任务
   */
  async recoverPendingTasks() {
    try {
      const pendingTasks = await CrawlTask.findAll({
        where: {
          status: 'running'
        }
      });

      for (const task of pendingTasks) {
        // 将运行中的任务重置为暂停状态
        await task.update({ status: 'paused' });
        console.log(`🔄 任务 ${task.id} 状态已重置为暂停`);
      }

      console.log(`✅ 恢复了 ${pendingTasks.length} 个未完成的任务`);
    } catch (error) {
      console.error('❌ 恢复未完成任务失败:', error.message);
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      queueStatus: this.taskQueue.getStatus(),
      managerStatus: this.manager.getStatus()
    };
  }
}

// 创建单例实例
const crawlerService = new CrawlerService();

// 自动初始化（延迟初始化，避免循环依赖）
process.nextTick(async () => {
  try {
    await crawlerService.initialize();
  } catch (error) {
    console.error('❌ CrawlerService自动初始化失败:', error.message);
  }
});

module.exports = crawlerService;
