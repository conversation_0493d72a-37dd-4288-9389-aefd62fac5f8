/**
 * 时间格式化中间件
 *
 * 功能说明：
 * - 自动格式化API响应中的时间字段
 * - 支持单个对象、数组和分页数据的格式化
 * - 在响应发送前统一处理时间格式
 * - 确保所有API接口返回一致的时间格式
 *
 * 使用方式：
 * 在路由中使用该中间件，它会自动处理响应数据中的时间字段
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const DateFormatter = require('../utils/dateFormatter');

/**
 * 时间格式化中间件
 * 在响应发送前自动格式化时间字段
 */
const dateFormatterMiddleware = async (ctx, next) => {
  await next();

  // 只处理成功的JSON响应
  if (ctx.status >= 200 && ctx.status < 300 && ctx.body && typeof ctx.body === 'object') {
    try {
      // 格式化响应数据中的时间字段
      ctx.body = formatResponseData(ctx.body);
    } catch (error) {
      console.warn('时间格式化中间件处理失败:', error.message);
      // 不影响正常响应，只记录警告
    }
  }
};

/**
 * 格式化响应数据中的时间字段
 * @param {Object} responseData 响应数据
 * @returns {Object} 格式化后的响应数据
 */
function formatResponseData(responseData) {
  if (!responseData || typeof responseData !== 'object') {
    return responseData;
  }

  const result = { ...responseData };

  // 处理标准API响应格式
  if (result.success !== undefined) {
    // 格式化 data 字段
    if (result.data) {
      result.data = formatData(result.data);
    }

    // 格式化分页数据（如果存在）
    if (result.pagination && result.data) {
      // 分页数据已经在上面的 data 字段中处理了
    }
  } else {
    // 直接格式化整个响应对象
    return formatData(responseData);
  }

  return result;
}

/**
 * 格式化数据中的时间字段
 * @param {any} data 要格式化的数据
 * @returns {any} 格式化后的数据
 */
function formatData(data) {
  if (!data) {
    return data;
  }

  // 处理数组
  if (Array.isArray(data)) {
    return DateFormatter.formatArray(data);
  }

  // 处理对象
  if (typeof data === 'object') {
    // 检查是否是分页结果格式 {rows: [], count: number}
    if (data.rows && Array.isArray(data.rows)) {
      return DateFormatter.formatPaginationResult(data);
    }

    // 处理普通对象
    return DateFormatter.formatDeep(data);
  }

  return data;
}

/**
 * 创建带有自定义时间字段的格式化中间件
 * @param {Array} customTimeFields 自定义时间字段名称
 * @returns {Function} 中间件函数
 */
function createDateFormatterMiddleware(customTimeFields = []) {
  return async (ctx, next) => {
    await next();

    // 只处理成功的JSON响应
    if (ctx.status >= 200 && ctx.status < 300 && ctx.body && typeof ctx.body === 'object') {
      try {
        // 格式化响应数据中的时间字段
        ctx.body = formatResponseDataWithCustomFields(ctx.body, customTimeFields);
      } catch (error) {
        console.warn('时间格式化中间件处理失败:', error.message);
        // 不影响正常响应，只记录警告
      }
    }
  };
}

/**
 * 使用自定义时间字段格式化响应数据
 * @param {Object} responseData 响应数据
 * @param {Array} customTimeFields 自定义时间字段
 * @returns {Object} 格式化后的响应数据
 */
function formatResponseDataWithCustomFields(responseData, customTimeFields) {
  if (!responseData || typeof responseData !== 'object') {
    return responseData;
  }

  const result = { ...responseData };

  // 处理标准API响应格式
  if (result.success !== undefined) {
    // 格式化 data 字段
    if (result.data) {
      result.data = formatDataWithCustomFields(result.data, customTimeFields);
    }
  } else {
    // 直接格式化整个响应对象
    return formatDataWithCustomFields(responseData, customTimeFields);
  }

  return result;
}

/**
 * 使用自定义时间字段格式化数据
 * @param {any} data 要格式化的数据
 * @param {Array} customTimeFields 自定义时间字段
 * @returns {any} 格式化后的数据
 */
function formatDataWithCustomFields(data, customTimeFields) {
  if (!data) {
    return data;
  }

  // 处理数组
  if (Array.isArray(data)) {
    return DateFormatter.formatArray(data, customTimeFields);
  }

  // 处理对象
  if (typeof data === 'object') {
    // 检查是否是分页结果格式 {rows: [], count: number}
    if (data.rows && Array.isArray(data.rows)) {
      return DateFormatter.formatPaginationResult(data, customTimeFields);
    }

    // 处理普通对象
    return DateFormatter.formatDeep(data, customTimeFields);
  }

  return data;
}

module.exports = {
  dateFormatterMiddleware,
  createDateFormatterMiddleware,
  formatResponseData,
  formatData
};
