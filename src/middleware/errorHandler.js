/**
 * 全局错误处理中间件
 *
 * 功能说明：
 * - 捕获应用中的所有未处理错误
 * - 统一错误响应格式和状态码
 * - 根据环境提供不同级别的错误信息
 * - 记录详细的错误日志用于调试
 * - 防止敏感信息泄露
 *
 * 错误类型处理：
 * - 验证错误：422状态码，返回详细验证信息
 * - 认证错误：401状态码，返回认证失败信息
 * - 权限错误：403状态码，返回权限不足信息
 * - 资源错误：404状态码，返回资源不存在信息
 * - 业务错误：400状态码，返回业务逻辑错误
 * - 系统错误：500状态码，返回通用错误信息
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const envConfig = require('../config/env');
const { HTTP_STATUS, ERROR_MESSAGES } = require('../config/constants');

/**
 * 错误日志记录函数
 * @param {Error} error - 错误对象
 * @param {Object} ctx - Koa上下文对象
 */
function logError(error, ctx) {
  const logData = {
    timestamp: new Date().toISOString(),
    method: ctx.method,
    url: ctx.url,
    userAgent: ctx.headers['user-agent'],
    ip: ctx.ip,
    userId: ctx.state.user?.id || 'anonymous',
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      status: error.status || error.statusCode
    }
  };

  // 根据错误级别使用不同的日志方法
  if (error.status >= 500) {
    console.error('🚨 [SYSTEM ERROR]', JSON.stringify(logData, null, 2));
  } else if (error.status >= 400) {
    console.warn('⚠️ [CLIENT ERROR]', JSON.stringify(logData, null, 2));
  } else {
    console.info('ℹ️ [INFO]', JSON.stringify(logData, null, 2));
  }
}

/**
 * 获取用户友好的错误消息
 * @param {Error} error - 错误对象
 * @returns {string} 用户友好的错误消息
 */
function getUserFriendlyMessage(error) {
  // 根据错误类型返回用户友好的消息
  switch (error.name) {
    case 'ValidationError':
      return '数据验证失败，请检查输入信息';
    case 'UnauthorizedError':
    case 'JsonWebTokenError':
      return ERROR_MESSAGES.UNAUTHORIZED;
    case 'TokenExpiredError':
      return ERROR_MESSAGES.TOKEN_EXPIRED;
    case 'SequelizeValidationError':
      return '数据格式错误，请检查输入信息';
    case 'SequelizeUniqueConstraintError':
      return '数据已存在，请检查重复信息';
    case 'SequelizeForeignKeyConstraintError':
      return '关联数据不存在，请检查相关信息';
    default:
      return error.message || ERROR_MESSAGES.INTERNAL_ERROR;
  }
}

/**
 * 全局错误处理中间件
 * @param {Object} ctx - Koa上下文对象
 * @param {Function} next - 下一个中间件函数
 */
const errorHandler = async (ctx, next) => {
  try {
    await next();
  } catch (error) {
    // 记录错误日志
    logError(error, ctx);

    // 设置错误状态码
    let status = error.status || error.statusCode || HTTP_STATUS.INTERNAL_SERVER_ERROR;

    // 特殊错误类型的状态码处理
    if (error.name === 'ValidationError') {
      status = HTTP_STATUS.UNPROCESSABLE_ENTITY;
    } else if (error.name === 'UnauthorizedError' || error.name === 'JsonWebTokenError') {
      status = HTTP_STATUS.UNAUTHORIZED;
    } else if (error.name === 'TokenExpiredError') {
      status = HTTP_STATUS.UNAUTHORIZED;
    }

    ctx.status = status;

    // 构建错误响应
    const errorResponse = {
      success: false,
      message: getUserFriendlyMessage(error),
      timestamp: new Date().toISOString(),
      path: ctx.url,
      method: ctx.method
    };

    // 开发环境返回详细错误信息
    if (envConfig.NODE_ENV === 'development') {
      errorResponse.debug = {
        originalMessage: error.message,
        stack: error.stack,
        name: error.name
      };
    }

    // 验证错误返回详细字段信息
    if (error.name === 'ValidationError' && error.errors) {
      errorResponse.errors = error.errors;
    }

    ctx.body = errorResponse;

    // 触发应用级错误事件
    ctx.app.emit('error', error, ctx);
  }
};

module.exports = errorHandler;
