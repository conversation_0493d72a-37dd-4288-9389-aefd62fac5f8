// 请求日志中间件
const logger = async (ctx, next) => {
  const start = Date.now();
  
  console.log(`➡️  ${ctx.method} ${ctx.url}`);
  
  await next();
  
  const ms = Date.now() - start;
  const status = ctx.status;
  
  // 根据状态码设置不同的日志颜色
  let statusColor = '';
  if (status >= 200 && status < 300) {
    statusColor = '\x1b[32m'; // 绿色
  } else if (status >= 300 && status < 400) {
    statusColor = '\x1b[33m'; // 黄色
  } else if (status >= 400) {
    statusColor = '\x1b[31m'; // 红色
  }
  
  console.log(`⬅️  ${statusColor}${status}\x1b[0m ${ctx.method} ${ctx.url} - ${ms}ms`);
};

module.exports = logger;
