const path = require('path');
const fs = require('fs');
const { swaggerSpec } = require('../config/swagger');

/**
 * 简单的Swagger UI中间件
 * 使用本地的swagger-ui-dist资源
 */
const swaggerMiddleware = async (ctx, next) => {
  if (ctx.path === '/api-docs' || ctx.path === '/api-docs/') {
    // 生成自定义的Swagger UI HTML页面
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>达人信息管理系统 - API文档</title>
  <link rel="stylesheet" type="text/css" href="/api-docs/swagger-ui.css" />
  <link rel="icon" type="image/png" href="/api-docs/favicon-32x32.png" sizes="32x32" />
  <link rel="icon" type="image/png" href="/api-docs/favicon-16x16.png" sizes="16x16" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
    }
    .swagger-ui .topbar {
      background-color: #1890ff;
    }
    .swagger-ui .topbar .download-url-wrapper {
      display: none;
    }
    .swagger-ui .info .title {
      color: #1890ff;
    }
    .swagger-ui .opblock-tag {
      font-size: 18px;
      font-weight: 600;
      margin: 20px 0 10px 0;
      color: #1890ff;
      border-bottom: 2px solid #1890ff;
      padding-bottom: 5px;
    }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="/api-docs/swagger-ui-bundle.js" charset="UTF-8"> </script>
  <script src="/api-docs/swagger-ui-standalone-preset.js" charset="UTF-8"> </script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: '/api-docs.json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        docExpansion: 'list',
        defaultModelsExpandDepth: 3,
        defaultModelExpandDepth: 3,
        displayOperationId: false,
        displayRequestDuration: true,
        filter: true,
        showExtensions: false,
        showCommonExtensions: false,
        tryItOutEnabled: true,
        requestInterceptor: function(request) {
          // 自动添加认证头（排除不需要认证的接口）
          const token = localStorage.getItem('api_token');
          if (token &&
              !request.url.includes('/api/auth/') &&
              !request.url.includes('/api/excel/')) {
            request.headers['Authorization'] = 'Bearer ' + token;
          }
          return request;
        },
        responseInterceptor: function(response) {
          // 处理认证失败
          if (response.status === 401) {
            localStorage.removeItem('api_token');
            alert('认证已过期，请重新登录');
          }
          return response;
        }
      });

      // 添加便捷方法到全局
      window.setApiToken = function(token) {
        localStorage.setItem('api_token', token);
        alert('Token已保存，刷新页面后生效');
      };

      window.clearApiToken = function() {
        localStorage.removeItem('api_token');
        alert('Token已清除');
      };

      // 在控制台提供便捷方法说明
      console.log('%c🔧 API文档便捷方法:', 'color: #1890ff; font-weight: bold; font-size: 14px;');
      console.log('%csetApiToken("your-token") - 设置认证token', 'color: #52c41a;');
      console.log('%cclearApiToken() - 清除认证token', 'color: #ff4d4f;');
    };
  </script>
</body>
</html>
    `;

    ctx.type = 'text/html';
    ctx.body = html;
    return;
  }

  // 提供Swagger UI静态资源
  if (ctx.path.startsWith('/api-docs/')) {
    const filePath = ctx.path.replace('/api-docs/', '');
    const swaggerUiAssetPath = path.join(__dirname, '../../node_modules/swagger-ui-dist', filePath);

    if (fs.existsSync(swaggerUiAssetPath)) {
      const stat = fs.statSync(swaggerUiAssetPath);
      if (stat.isFile()) {
        const content = fs.readFileSync(swaggerUiAssetPath);

        // 设置正确的Content-Type
        if (filePath.endsWith('.css')) {
          ctx.type = 'text/css';
        } else if (filePath.endsWith('.js')) {
          ctx.type = 'application/javascript';
        } else if (filePath.endsWith('.png')) {
          ctx.type = 'image/png';
        }

        ctx.body = content;
        return;
      }
    }
  }

  await next();
};

/**
 * API规范JSON端点中间件
 * 提供原始的OpenAPI规范JSON数据
 */
const swaggerJsonMiddleware = async (ctx, next) => {
  if (ctx.path === '/api-docs.json' || ctx.path === '/swagger.json') {
    ctx.type = 'application/json';
    ctx.body = swaggerSpec;
    return;
  }
  await next();
};

module.exports = {
  swaggerMiddleware,
  swaggerJsonMiddleware
};
