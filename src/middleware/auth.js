/**
 * 用户认证中间件
 *
 * 功能说明：
 * - 提供JWT令牌验证功能
 * - 支持用户身份认证和权限控制
 * - 自动解析Authorization头中的Bearer令牌
 * - 验证用户状态和令牌有效性
 *
 * 中间件列表：
 * - authenticate: JWT认证中间件，验证用户身份
 * - requireAdmin: 管理员权限中间件，要求管理员角色
 *
 * 使用方式：
 * - 在需要认证的路由中使用authenticate中间件
 * - 在需要管理员权限的路由中同时使用authenticate和requireAdmin
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const jwt = require('jsonwebtoken');
const { User } = require('../models');

/**
 * JWT认证中间件
 *
 * 功能说明：
 * - 从请求头中提取JWT令牌
 * - 验证令牌的有效性和完整性
 * - 查询用户信息并验证用户状态
 * - 将用户信息存储到ctx.state.user中
 *
 * 请求头格式：
 * Authorization: Bearer <jwt-token>
 *
 * 错误情况：
 * - 401: 未提供令牌、令牌无效、用户不存在或已禁用
 *
 * 使用示例：
 * router.get('/protected', authenticate, async (ctx) => {
 *   const user = ctx.state.user; // 获取当前用户信息
 * });
 *
 * @param {Object} ctx - Koa上下文对象
 * @param {Function} next - 下一个中间件函数
 */
const authenticate = async (ctx, next) => {
  try {
    const token = ctx.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      ctx.status = 401;
      ctx.body = {
        success: false,
        message: '未提供认证令牌'
      };
      return;
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.userId);

    if (!user || user.status !== 'active') {
      ctx.status = 401;
      ctx.body = {
        success: false,
        message: '无效的认证令牌'
      };
      return;
    }

    ctx.state.user = user;
    await next();
  } catch (error) {
    // 只处理JWT相关的认证错误
    if (error.name === 'JsonWebTokenError' ||
        error.name === 'TokenExpiredError' ||
        error.name === 'NotBeforeError') {
      ctx.status = 401;
      ctx.body = {
        success: false,
        message: '认证失败',
        error: error.message
      };
    } else {
      // 其他错误（如数据库错误、业务逻辑错误等）应该传递给下一个错误处理中间件
      throw error;
    }
  }
};

/**
 * 管理员权限中间件
 *
 * 功能说明：
 * - 验证当前用户是否具有管理员权限
 * - 必须在authenticate中间件之后使用
 * - 只允许role为'admin'的用户访问
 *
 * 前置条件：
 * - 必须先通过authenticate中间件验证
 * - ctx.state.user必须存在且包含用户信息
 *
 * 错误情况：
 * - 403: 用户不是管理员角色
 *
 * 使用示例：
 * router.delete('/admin/users/:id', authenticate, requireAdmin, async (ctx) => {
 *   // 只有管理员可以访问此接口
 * });
 *
 * @param {Object} ctx - Koa上下文对象
 * @param {Function} next - 下一个中间件函数
 */
const requireAdmin = async (ctx, next) => {
  if (ctx.state.user.role !== 'admin') {
    ctx.status = 403;
    ctx.body = {
      success: false,
      message: '需要管理员权限'
    };
    return;
  }
  await next();
};

module.exports = {
  authenticate,
  requireAdmin
};
