/**
 * CRM集成路由
 *
 * 功能说明：
 * - 定义CRM数据同步相关的API路由
 * - 包含连接测试、数据拉取、状态查询等接口
 * - 提供完整的Swagger文档注释
 *
 * 路由列表：
 * - GET /test-connection - 测试CRM连接
 * - GET /customers - 获取客户列表
 * - GET /customers/:customerName/agreements - 获取客户协议
 * - GET /status - 获取系统状态
 * - POST /refresh-token - 刷新token
 * - GET /config - 获取配置信息
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const Router = require('koa-router');
const crmIntegrationController = require('../controllers/CrmIntegrationController');
const { authenticate } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/crm-integration'
});

/**
 * @swagger
 * /api/crm-integration/test-connection:
 *   get:
 *     tags:
 *       - CRM集成
 *     summary: 测试CRM连接
 *     description: 测试与钉钉CRM系统的连接状态，验证配置是否正确
 *     responses:
 *       200:
 *         description: 连接测试成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "CRM连接测试成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     tokenValid:
 *                       type: boolean
 *                       description: Token是否有效
 *                     customerCount:
 *                       type: number
 *                       description: 客户总数
 *       500:
 *         description: 连接测试失败
 */
router.get('/test-connection', authenticate, crmIntegrationController.testConnection);

/**
 * @swagger
 * /api/crm-integration/users/search:
 *   get:
 *     tags:
 *       - CRM集成
 *     summary: 根据姓名查询CRM用户
 *     description: 根据用户姓名从钉钉CRM系统查询用户信息，用于用户绑定功能
 *     parameters:
 *       - in: query
 *         name: userName
 *         required: true
 *         schema:
 *           type: string
 *         description: 用户姓名
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "查询到 2 个用户"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       dataId:
 *                         type: integer
 *                         description: CRM数据ID
 *                       department:
 *                         type: string
 *                         description: 部门代码
 *                       departmentName:
 *                         type: string
 *                         description: 部门名称
 *                       userId:
 *                         type: string
 *                         description: 用户ID
 *                       userName:
 *                         type: string
 *                         description: 用户姓名
 *                       userPicUrl:
 *                         type: string
 *                         description: 用户头像URL
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 查询失败
 */
router.get('/users/search', authenticate, crmIntegrationController.getUserByName);

/**
 * @swagger
 * /api/crm-integration/customers:
 *   get:
 *     tags:
 *       - CRM集成
 *     summary: 获取客户列表
 *     description: 从钉钉CRM系统获取客户数据列表，支持分页
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取客户列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取客户列表成功"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: 客户ID
 *                       custom_name:
 *                         type: string
 *                         description: 客户名称
 *                       customer_textarea_11:
 *                         type: string
 *                         description: 星图链接
 *                       customer_check_box_2:
 *                         type: string
 *                         description: 种草平台
 *                       customer_select_28:
 *                         type: string
 *                         description: 博主粉丝量级
 *                       customer_textarea_13:
 *                         type: string
 *                         description: 平台ID
 *                       custom_remark:
 *                         type: string
 *                         description: 备注信息
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 *       500:
 *         description: 获取客户列表失败
 */
router.get('/customers', authenticate, crmIntegrationController.getCustomerList);

/**
 * @swagger
 * /api/crm-integration/customers/{customerName}/agreements:
 *   get:
 *     tags:
 *       - CRM集成
 *     summary: 获取客户协议列表
 *     description: 根据客户名称获取该客户的所有协议信息
 *     parameters:
 *       - in: path
 *         name: customerName
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户名称
 *     responses:
 *       200:
 *         description: 获取协议列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取协议列表成功"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: 协议ID
 *                       contract_title:
 *                         type: string
 *                         description: 协议标题
 *                       custom_name:
 *                         type: string
 *                         description: 关联客户名称
 *                       contract_amount:
 *                         type: string
 *                         description: 合作金额
 *                       contract_date_4:
 *                         type: string
 *                         description: 约定发布时间
 *                       contract_url_0:
 *                         type: string
 *                         description: 发布链接
 *       400:
 *         description: 客户名称不能为空
 *       500:
 *         description: 获取协议列表失败
 */
router.get('/customers/:customerName/agreements', authenticate, crmIntegrationController.getAgreementsByCustomer);

/**
 * @swagger
 * /api/crm-integration/status:
 *   get:
 *     tags:
 *       - CRM集成
 *     summary: 获取CRM系统状态
 *     description: 获取CRM集成服务的运行状态和统计信息
 *     responses:
 *       200:
 *         description: 获取系统状态成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取系统状态成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     tokenValid:
 *                       type: boolean
 *                       description: Token是否有效
 *                     customerCount:
 *                       type: number
 *                       description: 客户总数
 *                     lastTokenRefresh:
 *                       type: string
 *                       format: date-time
 *                       description: 上次Token刷新时间
 *                     nextTokenRefresh:
 *                       type: string
 *                       format: date-time
 *                       description: 下次Token刷新时间
 *                     queueLength:
 *                       type: number
 *                       description: 请求队列长度
 *                     isProcessingQueue:
 *                       type: boolean
 *                       description: 是否正在处理队列
 *       500:
 *         description: 获取系统状态失败
 */
router.get('/status', authenticate, crmIntegrationController.getSystemStatus);

/**
 * @swagger
 * /api/crm-integration/refresh-token:
 *   post:
 *     tags:
 *       - CRM集成
 *     summary: 手动刷新Token
 *     description: 手动刷新CRM系统的访问Token
 *     responses:
 *       200:
 *         description: Token刷新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Token刷新成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     tokenValid:
 *                       type: boolean
 *                       description: Token是否有效
 *                     expiresAt:
 *                       type: string
 *                       format: date-time
 *                       description: Token过期时间
 *       500:
 *         description: Token刷新失败
 */
router.post('/refresh-token', authenticate, crmIntegrationController.refreshToken);

/**
 * @swagger
 * /api/crm-integration/config:
 *   get:
 *     tags:
 *       - CRM集成
 *     summary: 获取CRM配置信息
 *     description: 获取CRM集成的配置信息（敏感信息已脱敏）
 *     responses:
 *       200:
 *         description: 获取配置信息成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取配置信息成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     proxyUrl:
 *                       type: string
 *                       description: 代理服务器地址
 *                     corpId:
 *                       type: string
 *                       description: 企业ID
 *                     appId:
 *                       type: string
 *                       description: 应用ID
 *                     appSecret:
 *                       type: string
 *                       description: 应用密钥（已脱敏）
 *                     timeout:
 *                       type: number
 *                       description: 请求超时时间
 *                     retryAttempts:
 *                       type: number
 *                       description: 重试次数
 *                     retryDelay:
 *                       type: number
 *                       description: 重试延迟
 *                     rateLimit:
 *                       type: object
 *                       description: 频率限制配置
 *                     tokenCache:
 *                       type: object
 *                       description: Token缓存配置
 *       500:
 *         description: 获取配置信息失败
 */
router.get('/config', authenticate, crmIntegrationController.getConfig);

/**
 * @swagger
 * /api/crm-integration/sync/customers:
 *   post:
 *     tags:
 *       - CRM集成
 *     summary: 同步客户数据
 *     description: 从钉钉CRM同步客户数据到本地数据库
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *                 minimum: 1
 *                 default: 1
 *                 description: 页码
 *               limit:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *                 default: 20
 *                 description: 每页数量
 *               customerName:
 *                 type: string
 *                 description: 可选的客户名称过滤
 *     responses:
 *       200:
 *         description: 同步成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "客户数据同步完成"
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       description: 同步结果描述
 *                     result:
 *                       type: object
 *                       properties:
 *                         success:
 *                           type: integer
 *                           description: 成功同步数量
 *                         failed:
 *                           type: integer
 *                           description: 失败数量
 *                         skipped:
 *                           type: integer
 *                           description: 跳过数量
 *                         errors:
 *                           type: array
 *                           description: 错误列表
 *                     totalFound:
 *                       type: integer
 *                       description: 总找到数量
 *       500:
 *         description: 同步失败
 */
router.post('/sync/customers', authenticate, crmIntegrationController.syncCustomerData);

/**
 * @swagger
 * /api/crm-integration/sync/complete:
 *   post:
 *     tags:
 *       - CRM集成
 *     summary: 完整数据同步
 *     description: 同步客户数据和对应的协议数据到本地数据库
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *                 minimum: 1
 *                 default: 1
 *                 description: 页码
 *               limit:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 20
 *                 default: 5
 *                 description: 每页数量（建议不超过20避免超时）
 *               customerName:
 *                 type: string
 *                 description: 可选的客户名称过滤
 *     responses:
 *       200:
 *         description: 同步成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "完整数据同步完成"
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       description: 同步结果描述
 *                     result:
 *                       type: object
 *                       properties:
 *                         customerSync:
 *                           type: object
 *                           description: 客户同步结果
 *                         agreementSync:
 *                           type: object
 *                           description: 协议同步结果
 *                         totalProcessed:
 *                           type: integer
 *                           description: 总处理数量
 *       500:
 *         description: 同步失败
 */
router.post('/sync/complete', authenticate, crmIntegrationController.syncCompleteData);

/**
 * @swagger
 * /api/crm-integration/sync/all:
 *   post:
 *     tags:
 *       - CRM集成
 *     summary: 全量数据同步
 *     description: 分页同步所有客户数据和协议数据，支持预览模式和实际同步模式
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               maxPages:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *                 default: 10
 *                 description: 最大处理页数（防止超时）
 *               pageSize:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 50
 *                 default: 10
 *                 description: 每页数量
 *               startPage:
 *                 type: integer
 *                 minimum: 1
 *                 default: 1
 *                 description: 起始页码
 *               customerName:
 *                 type: string
 *                 description: 可选的客户名称过滤
 *               dryRun:
 *                 type: boolean
 *                 default: false
 *                 description: 预览模式（true=只查看不同步，false=实际同步）
 *     responses:
 *       200:
 *         description: 全量同步成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "全量数据同步完成"
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                       description: 同步结果描述
 *                     result:
 *                       type: object
 *                       properties:
 *                         customerSync:
 *                           type: object
 *                           description: 客户同步统计
 *                         agreementSync:
 *                           type: object
 *                           description: 协议同步统计
 *                         totalProcessed:
 *                           type: integer
 *                           description: 总处理数量
 *                         pagesProcessed:
 *                           type: integer
 *                           description: 处理页数
 *                         processingTime:
 *                           type: integer
 *                           description: 处理时间（毫秒）
 *                     summary:
 *                       type: object
 *                       properties:
 *                         mode:
 *                           type: string
 *                           enum: [preview, sync]
 *                           description: 运行模式
 *                         pagesProcessed:
 *                           type: integer
 *                           description: 处理页数
 *                         totalProcessed:
 *                           type: integer
 *                           description: 总处理数量
 *                         processingTimeSeconds:
 *                           type: integer
 *                           description: 处理时间（秒）
 *       500:
 *         description: 全量同步失败
 */
router.post('/sync/all', authenticate, crmIntegrationController.syncAllData);

/**
 * @swagger
 * /api/crm-integration/smart-sync:
 *   post:
 *     tags:
 *       - CRM集成
 *     summary: 智能同步合作对接数据
 *     description: 智能检测并同步合作对接数据到CRM系统，自动判断需要同步的数据类型
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - cooperationId
 *             properties:
 *               cooperationId:
 *                 type: integer
 *                 description: 合作对接记录ID
 *               options:
 *                 type: object
 *                 properties:
 *                   forceCustomerSync:
 *                     type: boolean
 *                     default: false
 *                     description: 强制同步客户数据
 *                   forceAgreementSync:
 *                     type: boolean
 *                     default: false
 *                     description: 强制同步协议数据
 *                   changedFields:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: 发生变化的字段列表
 *     responses:
 *       200:
 *         description: 智能同步成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "智能同步完成"
 *                 data:
 *                   type: object
 *                   properties:
 *                     customerId:
 *                       type: integer
 *                       description: CRM客户ID
 *                     agreementId:
 *                       type: integer
 *                       description: CRM协议ID
 *                     customerSynced:
 *                       type: boolean
 *                       description: 客户是否已同步
 *                     agreementSynced:
 *                       type: boolean
 *                       description: 协议是否已同步
 *                     errors:
 *                       type: array
 *                       description: 错误列表
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 合作对接记录不存在
 *       500:
 *         description: 智能同步失败
 */
router.post('/smart-sync', authenticate, crmIntegrationController.smartSyncCooperationData);

/**
 * @swagger
 * /api/crm-integration/test-data:
 *   post:
 *     tags:
 *       - CRM集成
 *     summary: 创建测试数据
 *     description: 在CRM系统中创建测试客户和协议数据，用于验证集成功能
 *     responses:
 *       200:
 *         description: 测试数据创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "测试数据创建完成"
 *                 data:
 *                   type: object
 *                   properties:
 *                     customerId:
 *                       type: integer
 *                       description: 创建的测试客户ID
 *                     agreementId:
 *                       type: integer
 *                       description: 创建的测试协议ID
 *                     errors:
 *                       type: array
 *                       description: 错误列表
 *       500:
 *         description: 测试数据创建失败
 */
router.post('/test-data', authenticate, crmIntegrationController.createTestData);

/**
 * @swagger
 * /api/crm-integration/data/create:
 *   post:
 *     tags:
 *       - CRM集成
 *     summary: 创建CRM数据
 *     description: 在CRM系统中创建新的客户或协议数据
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - deployId
 *               - data
 *             properties:
 *               deployId:
 *                 type: string
 *                 enum: [customer, contract]
 *                 description: 数据类型（customer=客户，contract=协议）
 *               data:
 *                 type: object
 *                 description: 要创建的数据内容
 *     responses:
 *       200:
 *         description: CRM数据创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "CRM数据创建成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     dataId:
 *                       type: integer
 *                       description: 创建的数据ID
 *                     message:
 *                       type: string
 *                       description: 创建结果消息
 *       400:
 *         description: 参数错误
 *       500:
 *         description: CRM数据创建失败
 */
router.post('/data/create', authenticate, crmIntegrationController.createCrmData);

/**
 * @swagger
 * /api/crm-integration/data/update:
 *   post:
 *     tags:
 *       - CRM集成
 *     summary: 更新CRM数据
 *     description: 更新CRM系统中的客户或协议数据
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - deployId
 *               - dataId
 *               - data
 *             properties:
 *               deployId:
 *                 type: string
 *                 enum: [customer, contract]
 *                 description: 数据类型（customer=客户，contract=协议）
 *               dataId:
 *                 type: integer
 *                 description: 要更新的数据ID
 *               data:
 *                 type: object
 *                 description: 要更新的数据内容
 *     responses:
 *       200:
 *         description: CRM数据更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "CRM数据更新成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     dataId:
 *                       type: integer
 *                       description: 更新的数据ID
 *                     message:
 *                       type: string
 *                       description: 更新结果消息
 *       400:
 *         description: 参数错误
 *       500:
 *         description: CRM数据更新失败
 */
router.post('/data/update', authenticate, crmIntegrationController.updateCrmData);

/**
 * @swagger
 * /api/crm-integration/test-mapping:
 *   post:
 *     tags:
 *       - CRM集成
 *     summary: 测试字段映射
 *     description: 测试合作对接数据到CRM数据的字段映射功能
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - cooperationData
 *               - mappingType
 *             properties:
 *               cooperationData:
 *                 type: object
 *                 description: 合作对接数据
 *               mappingType:
 *                 type: string
 *                 enum: [customer, agreement]
 *                 description: 映射类型（customer=客户映射，agreement=协议映射）
 *     responses:
 *       200:
 *         description: 字段映射测试成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "字段映射测试完成"
 *                 data:
 *                   type: object
 *                   properties:
 *                     originalData:
 *                       type: object
 *                       description: 原始合作对接数据
 *                     mappedData:
 *                       type: object
 *                       description: 映射后的CRM数据
 *                     mappingType:
 *                       type: string
 *                       description: 映射类型
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 字段映射测试失败
 */
router.post('/test-mapping', authenticate, crmIntegrationController.testFieldMapping);

module.exports = router;
