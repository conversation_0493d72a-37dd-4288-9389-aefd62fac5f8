/**
 * 合作对接管理路由
 *
 * 功能说明：
 * - 定义合作对接管理相关的API路由
 * - 包含CRUD操作和特殊功能接口
 * - 所有接口都需要JWT认证
 * - 支持笔记链接解析和数据拉取功能
 *
 * 路由列表：
 * - POST /api/cooperation - 创建合作记录
 * - GET /api/cooperation - 获取合作记录列表
 * - GET /api/cooperation/:id - 获取合作记录详情
 * - PUT /api/cooperation/:id - 更新合作记录
 * - DELETE /api/cooperation/:id - 删除合作记录
 * - POST /api/cooperation/parse-note-link - 解析笔记链接
 * - POST /api/cooperation/:id/fetch-data - 手动拉取笔记数据
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const Router = require('koa-router');
const CooperationController = require('../controllers/CooperationController');
const { authenticate } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/cooperation'
});

// 应用认证中间件
router.use(authenticate);

/**
 * @swagger
 * tags:
 *   name: 合作对接管理
 *   description: 合作对接管理相关接口
 */

// 创建合作记录
router.post('/', CooperationController.createCooperation);

// 获取合作记录列表
router.get('/', CooperationController.getCooperationList);

// 解析笔记链接（放在动态路由前面）
router.post('/parse-note-link', CooperationController.parseNoteLink);

// 任务管理接口（必须在 /:id 路由之前）
router.get('/tasks', CooperationController.getDataFetchTasks);
router.post('/tasks/batch-fetch', CooperationController.batchFetchNoteData);
router.get('/tasks/stats', CooperationController.getTaskStats);

// 定时任务管理接口
router.get('/schedule/status', CooperationController.getScheduleStatus);
router.post('/schedule/trigger', CooperationController.triggerDataFetch);

// Cookie管理接口
router.get('/cookie/status', CooperationController.checkCookieStatus);
router.get('/cookie/stats', CooperationController.getCookieStats);

// 获取合作记录详情（放在具体路由之后）
router.get('/:id', CooperationController.getCooperationById);

// 更新合作记录
router.put('/:id', CooperationController.updateCooperation);

// 删除合作记录
router.delete('/:id', CooperationController.deleteCooperation);

// 手动拉取笔记数据
router.post('/:id/fetch-data', CooperationController.fetchNoteData);

// CRM集成接口
router.post('/:id/crm/recreate-customer', CooperationController.recreateCrmCustomer);
router.post('/:id/crm/recreate-agreement', CooperationController.recreateCrmAgreement);

module.exports = router;
