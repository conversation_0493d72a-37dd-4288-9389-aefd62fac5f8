/**
 * 字典管理路由
 *
 * 功能说明：
 * - 定义字典管理相关的API路由
 * - 包含字典项的CRUD操作
 * - 支持按分类获取和管理员权限控制
 * - 所有接口都需要JWT认证
 *
 * 路由列表：
 * - GET /api/dictionaries/categories - 获取所有分类
 * - GET /api/dictionaries/category/:category - 按分类获取字典项
 * - GET /api/dictionaries - 获取字典列表（分页）
 * - POST /api/dictionaries - 创建字典项（管理员）
 * - PUT /api/dictionaries/:id - 更新字典项（管理员）
 * - DELETE /api/dictionaries/:id - 删除字典项（管理员）
 * - POST /api/dictionaries/batch - 批量创建字典项（管理员）
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const Router = require('koa-router');
const DictionaryController = require('../controllers/DictionaryController');
const { authenticate, requireAdmin } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/dictionaries'
});

// 应用认证中间件到所有路由
router.use(authenticate);

// ==================== 公共接口（所有用户可访问） ====================

/**
 * @swagger
 * /api/dictionaries/categories:
 *   get:
 *     summary: 获取所有字典分类
 *     tags: [字典管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.get('/categories', DictionaryController.getCategories);

/**
 * @swagger
 * /api/dictionaries/batch:
 *   get:
 *     summary: 批量获取字典数据
 *     tags: [字典管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: categories
 *         required: true
 *         schema:
 *           type: string
 *         description: 字典分类列表，用逗号分隔
 *         example: cooperation_form,cooperation_brand,rebate_status
 *       - in: query
 *         name: activeOnly
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'true'
 *         description: 是否只获取启用的字典项
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     dictionaries:
 *                       type: object
 *                     summary:
 *                       type: object
 */
router.get('/batch', DictionaryController.getBatchDictionaries);

/**
 * @swagger
 * /api/dictionaries/category/{category}:
 *   get:
 *     summary: 按分类获取字典项
 *     tags: [字典管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *         description: 字典分类
 *       - in: query
 *         name: activeOnly
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'true'
 *         description: 是否只获取启用的字典项
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/category/:category', DictionaryController.getDictionariesByCategory);

/**
 * @swagger
 * /api/dictionaries:
 *   get:
 *     summary: 获取字典列表（分页）
 *     tags: [字典管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: ['active', 'inactive']
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/', DictionaryController.getDictionaryList);

// ==================== 管理员接口 ====================

/**
 * @swagger
 * /api/dictionaries:
 *   post:
 *     summary: 创建字典项
 *     tags: [字典管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - category
 *               - dictKey
 *               - dictLabel
 *             properties:
 *               category:
 *                 type: string
 *                 description: 字典分类
 *               dictKey:
 *                 type: string
 *                 description: 字典键值
 *               dictLabel:
 *                 type: string
 *                 description: 字典标签
 *               dictValue:
 *                 type: string
 *                 description: 字典值
 *               sortOrder:
 *                 type: integer
 *                 description: 排序顺序
 *               status:
 *                 type: string
 *                 enum: ['active', 'inactive']
 *                 description: 状态
 *               description:
 *                 type: string
 *                 description: 描述信息
 *     responses:
 *       200:
 *         description: 创建成功
 */
router.post('/', requireAdmin, DictionaryController.createDictionary);

/**
 * @swagger
 * /api/dictionaries/{id}:
 *   put:
 *     summary: 更新字典项
 *     tags: [字典管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 更新成功
 */
router.put('/:id', requireAdmin, DictionaryController.updateDictionary);

/**
 * @swagger
 * /api/dictionaries/{id}:
 *   delete:
 *     summary: 删除字典项
 *     tags: [字典管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 */
router.delete('/:id', requireAdmin, DictionaryController.deleteDictionary);

/**
 * @swagger
 * /api/dictionaries/batch:
 *   post:
 *     summary: 批量创建字典项
 *     tags: [字典管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - dictionaries
 *             properties:
 *               dictionaries:
 *                 type: array
 *                 items:
 *                   type: object
 *     responses:
 *       200:
 *         description: 批量创建完成
 */
router.post('/batch', requireAdmin, DictionaryController.batchCreateDictionaries);

module.exports = router;
