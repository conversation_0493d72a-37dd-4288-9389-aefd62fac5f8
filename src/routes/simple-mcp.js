/**
 * 简化的MCP路由
 * 提供基本的MCP接口，专注于核心功能
 */

const Router = require('koa-router');
const SimpleMCPController = require('../controllers/SimpleMCPController');

const router = new Router();

/**
 * @swagger
 * tags:
 *   name: Simple MCP
 *   description: 简化版Model Context Protocol工具接口
 */

/**
 * @swagger
 * /api/simple-mcp/tools:
 *   get:
 *     summary: 获取MCP工具列表
 *     tags: [Simple MCP]
 *     description: 获取所有可用的MCP工具及其描述信息
 *     responses:
 *       200:
 *         description: 工具列表获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     tools:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           title:
 *                             type: string
 *                           description:
 *                             type: string
 *                     count:
 *                       type: integer
 *                     version:
 *                       type: string
 */
router.get('/tools', SimpleMCPController.getTools.bind(SimpleMCPController));

/**
 * @swagger
 * /api/simple-mcp/execute:
 *   post:
 *     summary: 执行MCP工具
 *     tags: [Simple MCP]
 *     description: 执行指定的MCP工具
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tool
 *               - arguments
 *             properties:
 *               tool:
 *                 type: string
 *                 description: 工具名称
 *                 enum: [create_crawler_task, get_task_status, get_task_results, export_results_to_excel]
 *               arguments:
 *                 type: object
 *                 description: 工具参数
 *           examples:
 *             create_task_xhs:
 *               summary: 创建小红书爬虫任务(新格式)
 *               value:
 *                 tool: "create_crawler_task"
 *                 arguments:
 *                   keywords: "美妆博主"
 *                   platform: "xhs"
 *                   taskName: "美妆达人搜索"
 *                   maxPages: 3
 *             create_task_jlxt:
 *               summary: 创建巨量星图爬虫任务(新格式)
 *               value:
 *                 tool: "create_crawler_task"
 *                 arguments:
 *                   keywords: "科技达人"
 *                   platform: "jlxt"
 *                   taskName: "科技达人搜索"
 *                   maxPages: 5
 *             create_task_legacy:
 *               summary: 创建爬虫任务(兼容格式)
 *               value:
 *                 tool: "create_crawler_task"
 *                 arguments:
 *                   keywords: "美妆博主"
 *                   platform: "xiaohongshu"
 *                   taskName: "美妆达人搜索"
 *                   maxPages: 3
 *             get_status:
 *               summary: 获取任务状态
 *               value:
 *                 tool: "get_task_status"
 *                 arguments:
 *                   taskId: 123
 *             get_results:
 *               summary: 获取任务结果
 *               value:
 *                 tool: "get_task_results"
 *                 arguments:
 *                   taskId: 123
 *                   page: 1
 *                   limit: 20
 *             export_excel:
 *               summary: 导出Excel
 *               value:
 *                 tool: "export_results_to_excel"
 *                 arguments:
 *                   taskId: 123
 *                   filename: "results.xlsx"
 *     responses:
 *       200:
 *         description: 工具执行成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 执行失败
 */
router.post('/execute', SimpleMCPController.executeTool.bind(SimpleMCPController));

/**
 * @swagger
 * /api/simple-mcp/status:
 *   get:
 *     summary: 获取MCP服务状态
 *     tags: [Simple MCP]
 *     description: 获取MCP服务的运行状态和统计信息
 *     responses:
 *       200:
 *         description: 状态获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     service:
 *                       type: string
 *                     version:
 *                       type: string
 *                     status:
 *                       type: string
 *                     initialized:
 *                       type: boolean
 *                     serverName:
 *                       type: string
 *                     crawlerService:
 *                       type: object
 *                     timestamp:
 *                       type: string
 */
router.get('/status', SimpleMCPController.getServiceStatus.bind(SimpleMCPController));

module.exports = router;
