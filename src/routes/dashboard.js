/**
 * Dashboard路由配置
 * 
 * 功能说明：
 * - 提供仪表板相关的API路由
 * - 包括总体统计、平台分布、活动记录、趋势分析等接口
 * - 支持Swagger文档自动生成
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const Router = require('koa-router');
const DashboardController = require('../controllers/DashboardController');
const { authenticate } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件到所有dashboard路由
router.use('/api/dashboard', authenticate);

/**
 * @swagger
 * tags:
 *   name: 仪表板
 *   description: 仪表板统计数据管理
 */

/**
 * @swagger
 * /api/dashboard:
 *   get:
 *     tags: [仪表板]
 *     summary: 获取综合仪表板数据
 *     description: 一次性获取仪表板所需的所有数据，包括总体统计、平台分布、最近活动、趋势分析
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 仪表板数据获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 仪表板数据获取成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     overview:
 *                       type: object
 *                       description: 总体统计数据
 *                       properties:
 *                         totalInfluencers:
 *                           type: integer
 *                           description: 总达人数
 *                           example: 245
 *                         influencerGrowth:
 *                           type: integer
 *                           description: 较昨日新增达人数
 *                           example: 12
 *                         activeTasks:
 *                           type: integer
 *                           description: 活跃任务数
 *                           example: 8
 *                         runningTasks:
 *                           type: integer
 *                           description: 运行中任务数
 *                           example: 3
 *                         totalContent:
 *                           type: integer
 *                           description: 总采集内容数
 *                           example: 15420
 *                         todayContent:
 *                           type: integer
 *                           description: 今日新增内容数
 *                           example: 156
 *                         successRate:
 *                           type: number
 *                           description: 任务成功率
 *                           example: 94
 *                     platformStats:
 *                       type: array
 *                       description: 平台分布统计
 *                       items:
 *                         type: object
 *                         properties:
 *                           platform:
 *                             type: string
 *                             example: xiaohongshu
 *                           name:
 *                             type: string
 *                             example: 小红书
 *                           count:
 *                             type: integer
 *                             example: 89
 *                           percent:
 *                             type: integer
 *                             example: 60
 *                           color:
 *                             type: string
 *                             example: "#ff6b6b"
 *                     recentActivities:
 *                       type: array
 *                       description: 最近活动记录
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                             example: complete
 *                           title:
 *                             type: string
 *                             example: 任务完成
 *                           description:
 *                             type: string
 *                             example: 小红书任务"美食达人采集"执行完成，共采集 234 条内容
 *                           timeDisplay:
 *                             type: string
 *                             example: 2分钟前
 *                     taskTrends:
 *                       type: array
 *                       description: 任务执行趋势
 *                       items:
 *                         type: object
 *                         properties:
 *                           date:
 *                             type: string
 *                             example: "2024-01-01"
 *                           dateDisplay:
 *                             type: string
 *                             example: "1/1"
 *                           total:
 *                             type: integer
 *                             example: 10
 *                           completed:
 *                             type: integer
 *                             example: 8
 *                           successRate:
 *                             type: integer
 *                             example: 80
 *       401:
 *         description: 未授权访问
 *       500:
 *         description: 服务器内部错误
 */
router.get('/api/dashboard', DashboardController.getDashboard);

/**
 * @swagger
 * /api/dashboard/overview:
 *   get:
 *     tags: [仪表板]
 *     summary: 获取总体统计数据
 *     description: 获取达人数量、任务状态、内容统计等总体数据
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 统计数据获取成功
 *       401:
 *         description: 未授权访问
 *       500:
 *         description: 服务器内部错误
 */
router.get('/api/dashboard/overview', DashboardController.getOverview);

/**
 * @swagger
 * /api/dashboard/platform-stats:
 *   get:
 *     tags: [仪表板]
 *     summary: 获取平台分布统计
 *     description: 获取各平台的达人数量分布统计
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 平台统计数据获取成功
 *       401:
 *         description: 未授权访问
 *       500:
 *         description: 服务器内部错误
 */
router.get('/api/dashboard/platform-stats', DashboardController.getPlatformStats);

/**
 * @swagger
 * /api/dashboard/recent-activities:
 *   get:
 *     tags: [仪表板]
 *     summary: 获取最近活动记录
 *     description: 获取系统最近的操作活动记录
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 返回记录数量限制
 *     responses:
 *       200:
 *         description: 活动记录获取成功
 *       401:
 *         description: 未授权访问
 *       500:
 *         description: 服务器内部错误
 */
router.get('/api/dashboard/recent-activities', DashboardController.getRecentActivities);

/**
 * @swagger
 * /api/dashboard/influencer-trends:
 *   get:
 *     tags: [仪表板]
 *     summary: 获取达人采集数量趋势
 *     description: 获取最近指定天数的达人采集数量趋势数据
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 7
 *         description: 统计天数
 *     responses:
 *       200:
 *         description: 达人采集趋势数据获取成功
 *       401:
 *         description: 未授权访问
 *       500:
 *         description: 服务器内部错误
 */
router.get('/api/dashboard/influencer-trends', DashboardController.getInfluencerTrends);

/**
 * @swagger
 * /api/dashboard/task-trends:
 *   get:
 *     tags: [仪表板]
 *     summary: 获取任务执行趋势
 *     description: 获取最近指定天数的任务执行趋势数据
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 7
 *         description: 统计天数
 *     responses:
 *       200:
 *         description: 趋势数据获取成功
 *       401:
 *         description: 未授权访问
 *       500:
 *         description: 服务器内部错误
 */
router.get('/api/dashboard/task-trends', DashboardController.getTaskTrends);

module.exports = router;
