const Router = require('koa-router');
const authRoutes = require('./auth');
const userRoutes = require('./users');  // 新增用户管理路由
const influencerRoutes = require('./influencers');
const publicInfluencerRoutes = require('./publicInfluencers');  // 新增达人公海路由
const influencerReportRoutes = require('./influencerReports');  // 新增达人提报路由
const authorVideoRoutes = require('./author-videos');  // 新增达人作品库路由
const crawlerRoutes = require('./crawler');
const cookieRoutes = require('./cookies');
const excelRoutes = require('./excel');
const simpleMcpRoutes = require('./simple-mcp');
const dashboardRoutes = require('./dashboard');  // 新增仪表板路由
const cooperationRoutes = require('./cooperation');  // 新增合作对接管理路由
const dictionaryRoutes = require('./dictionaries');  // 新增字典管理路由
const crmIntegrationRoutes = require('./crmIntegration');  // 新增CRM集成路由
const crmDictionaryRoutes = require('./crmDictionaries');  // 新增CRM字典管理路由

const router = new Router();

/**
 * @swagger
 * /health:
 *   get:
 *     tags: [系统]
 *     summary: 健康检查
 *     description: 检查服务运行状态
 *     responses:
 *       200:
 *         description: 服务运行正常
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 服务运行正常
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: "2024-01-01T12:00:00.000Z"
 *                 version:
 *                   type: string
 *                   example: "1.0.0"
 */
router.get('/health', async (ctx) => {
  ctx.body = {
    success: true,
    message: '服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  };
});

/**
 * @swagger
 * /api:
 *   get:
 *     tags: [系统]
 *     summary: API信息
 *     description: 获取API基本信息和可用端点
 *     responses:
 *       200:
 *         description: API信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 达人信息管理系统 API
 *                 version:
 *                   type: string
 *                   example: "1.0.0"
 *                 endpoints:
 *                   type: object
 *                   properties:
 *                     auth:
 *                       type: string
 *                       example: /api/auth
 *                     influencers:
 *                       type: string
 *                       example: /api/influencers
 *                     crawler:
 *                       type: string
 *                       example: /api/crawler
 *                     cookies:
 *                       type: string
 *                       example: /api/cookies
 */
router.get('/api', async (ctx) => {
  ctx.body = {
    success: true,
    message: '达人信息管理系统 API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      influencers: '/api/influencers',
      publicInfluencers: '/api/public-influencers',  // 新增达人公海端点
      authorVideos: '/api/author-videos',  // 新增达人作品库端点
      crawler: '/api/crawler',
      cookies: '/api/cookies',
      excel: '/api/excel',
      simpleMcp: '/api/simple-mcp',
      dashboard: '/api/dashboard',  // 新增仪表板端点
      cooperation: '/api/cooperation',  // 新增合作对接管理端点
      dictionaries: '/api/dictionaries',  // 新增字典管理端点
      crmIntegration: '/api/crm-integration',  // 新增CRM集成端点
      crmDictionaries: '/api/crm-dictionaries',  // 新增CRM字典管理端点
      docs: '/api-docs'
    }
  };
});

// 注册子路由
router.use('/api/excel', excelRoutes.routes(), excelRoutes.allowedMethods());
router.use('/api/simple-mcp', simpleMcpRoutes.routes(), simpleMcpRoutes.allowedMethods());
router.use(authRoutes.routes(), authRoutes.allowedMethods());
router.use(userRoutes.routes(), userRoutes.allowedMethods());  // 新增用户管理路由
router.use(influencerRoutes.routes(), influencerRoutes.allowedMethods());
router.use(publicInfluencerRoutes.routes(), publicInfluencerRoutes.allowedMethods());  // 新增达人公海路由
router.use(influencerReportRoutes.routes(), influencerReportRoutes.allowedMethods());  // 新增达人提报路由
router.use(authorVideoRoutes.routes(), authorVideoRoutes.allowedMethods());  // 新增达人作品库路由
router.use(crawlerRoutes.routes(), crawlerRoutes.allowedMethods());
router.use(cookieRoutes.routes(), cookieRoutes.allowedMethods());
router.use(dashboardRoutes.routes(), dashboardRoutes.allowedMethods());  // 新增仪表板路由
router.use(cooperationRoutes.routes(), cooperationRoutes.allowedMethods());  // 新增合作对接管理路由
router.use(dictionaryRoutes.routes(), dictionaryRoutes.allowedMethods());  // 新增字典管理路由
router.use(crmIntegrationRoutes.routes(), crmIntegrationRoutes.allowedMethods());  // 新增CRM集成路由
router.use(crmDictionaryRoutes.routes(), crmDictionaryRoutes.allowedMethods());  // 新增CRM字典管理路由

module.exports = router;
