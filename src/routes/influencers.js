const Router = require('koa-router');
const InfluencerController = require('../controllers/influencerController');
const { authenticate } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/influencers'
});

// 所有路由都需要认证
router.use(authenticate);

// 获取达人列表
router.get('/', InfluencerController.getInfluencers);

// 获取达人统计信息
router.get('/stats', InfluencerController.getInfluencerStats);

// 导出达人信息到Excel
router.get('/export', InfluencerController.exportInfluencers);

// 获取达人详情
router.get('/:id', InfluencerController.getInfluencer);

// 获取达人详细信息面板
router.get('/:id/detail', InfluencerController.getInfluencerDetail);

// 创建达人
router.post('/', InfluencerController.createInfluencer);

// 更新达人
router.put('/:id', InfluencerController.updateInfluencer);

// 删除达人
router.delete('/:id', InfluencerController.deleteInfluencer);

// 批量删除达人
router.post('/batch-delete', InfluencerController.batchDeleteInfluencers);

module.exports = router;
