const Router = require('koa-router');
const AuthorVideoController = require('../controllers/authorVideoController');
const { authenticate } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/author-videos'
});

// 所有路由都需要认证
router.use(authenticate);

// 获取作品库统计信息
router.get('/stats', AuthorVideoController.getAuthorVideoStats);

// 获取作品列表
router.get('/', AuthorVideoController.getAuthorVideos);

// 获取小红书帖子详情
router.get('/xiaohongshu/note/:noteId', AuthorVideoController.getXiaohongshuNoteDetail);

// 获取作品详情
router.get('/:id', AuthorVideoController.getAuthorVideoById);

module.exports = router;
