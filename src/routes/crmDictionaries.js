/**
 * CRM字典管理路由
 *
 * 功能说明：
 * - 定义CRM字典数据同步和管理相关的API路由
 * - 包含字典数据的同步、查询、统计等操作
 * - 支持按部署类型和字段获取字典数据
 * - 所有接口都需要JWT认证，同步操作需要管理员权限
 *
 * 路由列表：
 * - GET /api/crm-dictionaries/test - 测试CRM连接
 * - GET /api/crm-dictionaries/stats - 获取字典统计信息
 * - GET /api/crm-dictionaries - 获取字典数据列表
 * - GET /api/crm-dictionaries/options/:deployId/:fieldName - 获取字典选项
 * - POST /api/crm-dictionaries/sync - 同步所有字典数据（管理员）
 * - POST /api/crm-dictionaries/sync/:deployId - 同步指定部署类型（管理员）
 * - DELETE /api/crm-dictionaries/cleanup - 清理过期数据（管理员）
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const Router = require('koa-router');
const CrmDictionaryController = require('../controllers/CrmDictionaryController');
const { authenticate, requireAdmin } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/crm-dictionaries'
});

// 应用认证中间件到所有路由
router.use(authenticate);

// ==================== 公共接口（所有用户可访问） ====================

/**
 * @swagger
 * /api/crm-dictionaries/test:
 *   get:
 *     summary: 测试CRM字典连接
 *     tags: [CRM字典管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 连接测试成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     systemStatus:
 *                       type: object
 *                     customer:
 *                       type: object
 *                     contract:
 *                       type: object
 */
router.get('/test', CrmDictionaryController.testConnection);

/**
 * @swagger
 * /api/crm-dictionaries/status:
 *   get:
 *     summary: 获取CRM字典系统状态
 *     tags: [CRM字典管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 */
router.get('/status', CrmDictionaryController.getSystemStatus);

/**
 * @swagger
 * /api/crm-dictionaries/refresh-token:
 *   post:
 *     summary: 手动刷新CRM访问令牌
 *     tags: [CRM字典管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 刷新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 */
router.post('/refresh-token', CrmDictionaryController.refreshToken);

/**
 * @swagger
 * /api/crm-dictionaries/stats:
 *   get:
 *     summary: 获取字典统计信息
 *     tags: [CRM字典管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 */
router.get('/stats', CrmDictionaryController.getDictionaryStats);

/**
 * @swagger
 * /api/crm-dictionaries:
 *   get:
 *     summary: 获取字典数据列表
 *     tags: [CRM字典管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: deployId
 *         required: true
 *         schema:
 *           type: string
 *           enum: ['customer', 'contract']
 *         description: 部署类型
 *       - in: query
 *         name: fieldCode
 *         schema:
 *           type: string
 *         description: 字段代码（可选）
 *       - in: query
 *         name: activeOnly
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'true'
 *         description: 是否只获取活跃数据
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 50
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/', CrmDictionaryController.getDictionaries);

/**
 * @swagger
 * /api/crm-dictionaries/options/{deployId}/{fieldName}:
 *   get:
 *     summary: 获取字典选项（用于表单下拉）
 *     tags: [CRM字典管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: deployId
 *         required: true
 *         schema:
 *           type: string
 *           enum: ['customer', 'contract']
 *         description: 部署类型
 *       - in: path
 *         name: fieldName
 *         required: true
 *         schema:
 *           type: string
 *         description: 字段名称
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     options:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           value:
 *                             type: string
 *                           label:
 *                             type: string
 *                           key:
 *                             type: string
 *                           order:
 *                             type: integer
 */
router.get('/options/:deployId/:fieldName', CrmDictionaryController.getDictionaryOptions);

// ==================== 管理员接口 ====================

/**
 * @swagger
 * /api/crm-dictionaries/sync:
 *   post:
 *     summary: 同步所有字典数据
 *     tags: [CRM字典管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 同步完成
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 */
router.post('/sync', requireAdmin, CrmDictionaryController.syncAllDictionaries);

/**
 * @swagger
 * /api/crm-dictionaries/sync/{deployId}:
 *   post:
 *     summary: 同步指定部署类型的字典数据
 *     tags: [CRM字典管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: deployId
 *         required: true
 *         schema:
 *           type: string
 *           enum: ['customer', 'contract']
 *         description: 部署类型
 *     responses:
 *       200:
 *         description: 同步完成
 */
router.post('/sync/:deployId', requireAdmin, CrmDictionaryController.syncDictionariesByDeployId);

/**
 * @swagger
 * /api/crm-dictionaries/cleanup:
 *   delete:
 *     summary: 清理过期字典数据
 *     tags: [CRM字典管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               daysOld:
 *                 type: integer
 *                 default: 30
 *                 description: 清理多少天前的数据
 *     responses:
 *       200:
 *         description: 清理完成
 */
router.delete('/cleanup', requireAdmin, CrmDictionaryController.cleanupOldDictionaries);

module.exports = router;
