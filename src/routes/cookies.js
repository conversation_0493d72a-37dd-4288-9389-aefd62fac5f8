/**
 * Cookie管理路由
 */

const Router = require('koa-router');
const CookieController = require('../controllers/CookieController');
const { authenticate } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/cookies'
});

// 所有Cookie接口都需要认证
router.use(authenticate);

// Cookie管理接口
router.get('/', CookieController.getCookies);                    // 获取Cookie列表
router.get('/:id', CookieController.getCookieById);              // 获取单个Cookie详情
router.post('/', CookieController.addCookie);                    // 添加Cookie
router.put('/:id', CookieController.updateCookie);               // 更新Cookie
router.delete('/:id', CookieController.deleteCookie);            // 删除Cookie

// 批量操作
router.post('/batch-import', CookieController.batchImportCookies); // 批量导入
router.get('/export', CookieController.exportCookies);            // 导出Cookie

// Cookie验证和统计
router.post('/:id/validate', CookieController.validateCookie);    // 验证Cookie
router.get('/stats', CookieController.getCookieStats);            // 获取统计信息

module.exports = router;
