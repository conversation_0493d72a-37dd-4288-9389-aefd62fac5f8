const Router = require('koa-router');
const publicInfluencerController = require('../controllers/publicInfluencerController');
const { authenticate } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件到所有路由
router.use(authenticate);

// 达人公海路由
router.get('/api/public-influencers', publicInfluencerController.getPublicInfluencers);
router.get('/api/public-influencers/export', publicInfluencerController.exportPublicInfluencers);
router.get('/api/public-influencers/:id', publicInfluencerController.getPublicInfluencerById);
router.post('/api/public-influencers/:id/import', publicInfluencerController.importToMyInfluencers);
router.post('/api/public-influencers/batch-import', publicInfluencerController.batchImportToMyInfluencers);

module.exports = router;
