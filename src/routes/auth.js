const Router = require('koa-router');
const AuthController = require('../controllers/authController');
const { authenticate } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/auth'
});

// 用户注册
router.post('/register', AuthController.register);

// 用户登录
router.post('/login', AuthController.login);

// 获取当前用户信息（需要认证）
router.get('/me', authenticate, AuthController.getCurrentUser);

// 修改密码（需要认证）
router.put('/change-password', authenticate, AuthController.changePassword);

module.exports = router;
