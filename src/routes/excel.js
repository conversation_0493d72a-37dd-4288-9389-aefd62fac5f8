/**
 * Excel文件生成路由
 * 提供Excel文件生成、下载和管理的路由配置
 */

const Router = require('koa-router');
const ExcelController = require('../controllers/ExcelController');

const router = new Router();

/**
 * @swagger
 * tags:
 *   name: Excel
 *   description: Excel文件生成和管理
 */

/**
 * @swagger
 * /api/excel/generate:
 *   post:
 *     summary: 生成Excel文件
 *     tags: [Excel]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *             properties:
 *               data:
 *                 type: array
 *                 description: 要导出的数据数组
 *                 items:
 *                   type: object
 *               fileName:
 *                 type: string
 *                 description: 文件名（可选，不包含扩展名）
 *                 example: "用户数据"
 *               formatting:
 *                 type: object
 *                 description: 格式化选项
 *                 properties:
 *                   headerStyle:
 *                     type: boolean
 *                     description: 是否应用头部样式
 *           examples:
 *             simple:
 *               summary: 简单数据导出
 *               value:
 *                 data:
 *                   - id: 1
 *                     name: "张三"
 *                     age: 25
 *                   - id: 2
 *                     name: "李四"
 *                     age: 30
 *                 fileName: "用户列表"
 *     responses:
 *       200:
 *         description: Excel文件生成成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     downloadUrl:
 *                       type: string
 *                       description: 文件下载链接
 *                     fileName:
 *                       type: string
 *                       description: 生成的文件名
 *                     fileSize:
 *                       type: number
 *                       description: 文件大小（字节）
 *                     fileSizeFormatted:
 *                       type: string
 *                       description: 格式化的文件大小
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/generate', ExcelController.generateExcel.bind(ExcelController));

/**
 * @swagger
 * /api/excel/generate-multi:
 *   post:
 *     summary: 生成多工作表Excel文件
 *     tags: [Excel]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sheets
 *             properties:
 *               sheets:
 *                 type: array
 *                 description: 工作表配置数组
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: 工作表名称
 *                     data:
 *                       type: array
 *                       description: 工作表数据
 *                     columnWidths:
 *                       type: array
 *                       description: 列宽配置
 *               fileName:
 *                 type: string
 *                 description: 文件名（可选）
 *               formatting:
 *                 type: object
 *                 description: 格式化选项
 *           example:
 *             sheets:
 *               - name: "用户信息"
 *                 data:
 *                   - id: 1
 *                     name: "张三"
 *                   - id: 2
 *                     name: "李四"
 *               - name: "订单信息"
 *                 data:
 *                   - orderId: "001"
 *                     amount: 100
 *             fileName: "综合报表"
 *     responses:
 *       200:
 *         description: 多工作表Excel文件生成成功
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/generate-multi', ExcelController.generateMultiSheetExcel.bind(ExcelController));

/**
 * @swagger
 * /api/excel/generate-sample:
 *   post:
 *     summary: 生成示例Excel文件
 *     tags: [Excel]
 *     description: 生成包含示例数据的Excel文件，用于测试功能
 *     responses:
 *       200:
 *         description: 示例Excel文件生成成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     downloadUrl:
 *                       type: string
 *                     fileName:
 *                       type: string
 *                     recordCount:
 *                       type: number
 *       500:
 *         description: 服务器内部错误
 */
router.post('/generate-sample', ExcelController.generateSample.bind(ExcelController));

/**
 * @swagger
 * /api/excel/check/{fileName}:
 *   get:
 *     summary: 检查文件是否存在
 *     tags: [Excel]
 *     parameters:
 *       - in: path
 *         name: fileName
 *         required: true
 *         schema:
 *           type: string
 *         description: 要检查的文件名
 *     responses:
 *       200:
 *         description: 文件检查结果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     fileName:
 *                       type: string
 *                     exists:
 *                       type: boolean
 *                     downloadUrl:
 *                       type: string
 *                       nullable: true
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.get('/check/:fileName', ExcelController.checkFile.bind(ExcelController));

/**
 * @swagger
 * /api/excel/status:
 *   get:
 *     summary: 获取Excel服务状态
 *     tags: [Excel]
 *     description: 获取Excel文件生成服务的运行状态和统计信息
 *     responses:
 *       200:
 *         description: 服务状态信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     service:
 *                       type: string
 *                     status:
 *                       type: string
 *                     exportDirectory:
 *                       type: string
 *                     fileCount:
 *                       type: number
 *                     totalSize:
 *                       type: number
 *                     totalSizeFormatted:
 *                       type: string
 *                     maxFileAge:
 *                       type: string
 *                     baseUrl:
 *                       type: string
 *       500:
 *         description: 服务器内部错误
 */
router.get('/status', ExcelController.getStatus.bind(ExcelController));

/**
 * @swagger
 * /api/excel/cleanup:
 *   post:
 *     summary: 手动清理过期文件
 *     tags: [Excel]
 *     description: 手动触发过期Excel文件的清理操作
 *     responses:
 *       200:
 *         description: 清理操作完成
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       500:
 *         description: 服务器内部错误
 */
router.post('/cleanup', ExcelController.cleanupFiles.bind(ExcelController));

module.exports = router;
