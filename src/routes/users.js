/**
 * 用户管理路由
 *
 * 功能说明：
 * - 提供用户账号管理的所有API路由
 * - 所有路由都需要管理员权限
 * - 支持用户的CRUD操作和密码管理
 *
 * 权限控制：
 * - 所有路由都使用authenticate中间件进行身份验证
 * - 所有路由都使用requireAdmin中间件进行管理员权限验证
 * - 确保只有管理员可以访问用户管理功能
 *
 * 路由列表：
 * - GET /api/users - 获取用户列表
 * - POST /api/users - 创建新用户
 * - GET /api/users/:id - 获取用户详情
 * - PUT /api/users/:id - 更新用户信息
 * - DELETE /api/users/:id - 删除用户
 * - PUT /api/users/:id/reset-password - 重置用户密码
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const Router = require('koa-router');
const UserController = require('../controllers/userController');
const { authenticate, requireAdmin } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/users'
});

// 所有用户管理路由都需要管理员权限
router.use(authenticate, requireAdmin);

/**
 * 用户列表相关路由
 */

// 获取用户列表（支持分页、搜索、排序）
router.get('/', UserController.getUsers);

// 创建新用户
router.post('/', UserController.createUser);

/**
 * 单个用户操作路由
 */

// 获取指定用户详情
router.get('/:id', UserController.getUser);

// 更新用户信息
router.put('/:id', UserController.updateUser);

// 删除用户
router.delete('/:id', UserController.deleteUser);

/**
 * 用户密码管理路由
 */

// 重置用户密码
router.put('/:id/reset-password', UserController.resetPassword);

/**
 * CRM用户绑定相关路由
 */

// 绑定CRM用户
router.put('/:id/bind-crm', UserController.bindCrmUser);

// 解绑CRM用户
router.put('/:id/unbind-crm', UserController.unbindCrmUser);

module.exports = router;
