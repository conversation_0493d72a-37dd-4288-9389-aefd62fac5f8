# 通用的Swagger定义文件

# 通用查询参数
components:
  parameters:
    PageParam:
      in: query
      name: page
      schema:
        type: integer
        minimum: 1
        default: 1
      description: 页码
      example: 1
    
    LimitParam:
      in: query
      name: limit
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: 每页数量
      example: 10
    
    SortByParam:
      in: query
      name: sortBy
      schema:
        type: string
        default: createdAt
      description: 排序字段
      example: createdAt
    
    SortOrderParam:
      in: query
      name: sortOrder
      schema:
        type: string
        enum: [ASC, DESC]
        default: DESC
      description: 排序方向
      example: DESC
    
    KeywordParam:
      in: query
      name: keyword
      schema:
        type: string
      description: 关键词搜索
      example: 美妆
    
    StatusParam:
      in: query
      name: status
      schema:
        type: string
        enum: [active, inactive]
      description: 状态筛选
      example: active
    
    PlatformParam:
      in: query
      name: platform
      schema:
        type: string
        enum: [xiaohongshu, juxingtu]
      description: 平台筛选
      example: xiaohongshu

  # 通用响应示例
  examples:
    SuccessResponse:
      summary: 成功响应示例
      value:
        success: true
        message: 操作成功
        data: {}
    
    ValidationErrorResponse:
      summary: 参数验证失败示例
      value:
        success: false
        message: 参数验证失败
        error: 用户名不能为空
    
    UnauthorizedResponse:
      summary: 认证失败示例
      value:
        success: false
        message: 认证失败
        error: 无效的认证令牌
    
    NotFoundResponse:
      summary: 资源不存在示例
      value:
        success: false
        message: 资源不存在
    
    ServerErrorResponse:
      summary: 服务器错误示例
      value:
        success: false
        message: 服务器内部错误

  # 通用请求头
  headers:
    Authorization:
      description: JWT认证令牌
      schema:
        type: string
        example: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
    
    Content-Type:
      description: 请求内容类型
      schema:
        type: string
        example: application/json

# 通用标签定义
tags:
  - name: 系统
    description: 系统相关接口，包括健康检查、API信息等
  - name: 认证
    description: 用户认证相关接口，包括登录、注册、密码管理等
  - name: 达人管理
    description: 达人信息管理相关接口，包括CRUD操作、搜索、导入导出等
  - name: 爬虫管理
    description: 爬虫任务管理相关接口，包括任务创建、监控、结果查看等
  - name: Cookie管理
    description: Cookie管理相关接口，包括添加、更新、验证、轮换等

# 外部文档链接
externalDocs:
  description: 项目GitHub仓库
  url: https://github.com/example/daren-server
