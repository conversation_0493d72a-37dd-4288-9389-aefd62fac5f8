const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const DateFormatter = require('../utils/dateFormatter');

const CrawlTask = sequelize.define('CrawlTask', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  taskName: {
    type: DataTypes.STRING(100),
    field: 'task_name',
    allowNull: false,
    comment: '任务名称'
  },
  platform: {
    type: DataTypes.ENUM('xiaohongshu', 'juxingtu'),
    allowNull: false,
    comment: '目标平台'
  },
  keywords: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: '搜索关键词'
  },
  config: {
    type: DataTypes.JSON,
    field: 'config',
    comment: '爬虫配置参数（页数、延迟、过滤条件等）'
  },
  status: {
    type: DataTypes.ENUM('pending', 'running', 'paused', 'completed', 'failed', 'cancelled'),
    defaultValue: 'pending',
    comment: '任务状态'
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '任务优先级（数字越大优先级越高）'
  },
  progress: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '进度百分比'
  },
  totalCount: {
    type: DataTypes.INTEGER,
    field: 'total_count',
    defaultValue: 0,
    comment: '总数量'
  },
  successCount: {
    type: DataTypes.INTEGER,
    field: 'success_count',
    defaultValue: 0,
    comment: '成功数量'
  },
  failedCount: {
    type: DataTypes.INTEGER,
    field: 'failed_count',
    defaultValue: 0,
    comment: '失败数量'
  },
  currentPage: {
    type: DataTypes.INTEGER,
    field: 'current_page',
    defaultValue: 1,
    comment: '当前爬取页数'
  },
  maxPages: {
    type: DataTypes.INTEGER,
    field: 'max_pages',
    defaultValue: 5,
    comment: '最大爬取页数'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    field: 'error_message',
    comment: '错误信息'
  },
  resultSummary: {
    type: DataTypes.JSON,
    field: 'result_summary',
    comment: '结果摘要统计'
  },
  startedAt: {
    type: DataTypes.DATE,
    field: 'started_at',
    comment: '开始时间'
  },
  completedAt: {
    type: DataTypes.DATE,
    field: 'completed_at',
    comment: '完成时间'
  },
  pausedAt: {
    type: DataTypes.DATE,
    field: 'paused_at',
    comment: '暂停时间'
  },
  resumedAt: {
    type: DataTypes.DATE,
    field: 'resumed_at',
    comment: '恢复时间'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    field: 'created_by',
    comment: '创建者ID'
  },
  retryCount: {
    type: DataTypes.INTEGER,
    field: 'retry_count',
    defaultValue: 0,
    comment: '重试次数'
  },
  maxRetries: {
    type: DataTypes.INTEGER,
    field: 'max_retries',
    defaultValue: 3,
    comment: '最大重试次数'
  },
  lastRetryAt: {
    type: DataTypes.DATE,
    field: 'last_retry_at',
    comment: '最后重试时间'
  },
  retryHistory: {
    type: DataTypes.JSON,
    field: 'retry_history',
    comment: '重试历史记录'
  }
}, {
  tableName: 'crawl_tasks',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['status']
    },
    {
      fields: ['platform']
    }
  ]
});

// 重写toJSON方法，自动格式化时间字段
CrawlTask.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return DateFormatter.formatObject(values);
};

module.exports = CrawlTask;
