const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');
const DateFormatter = require('../utils/dateFormatter');

const User = sequelize.define(
  'User',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '用户名'
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        isEmail: true
      },
      comment: '邮箱'
    },
    chineseName: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'chinese_name',
      comment: '中文名称'
    },
    crmUserId: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'crm_user_id',
      comment: 'CRM系统用户ID'
    },
    crmUserPicUrl: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'crm_user_pic_url',
      comment: 'CRM用户头像URL'
    },
    crmDepartment: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'crm_department',
      comment: 'CRM部门代码'
    },
    crmDepartmentName: {
      type: DataTypes.STRING(200),
      allowNull: true,
      field: 'crm_department_name',
      comment: 'CRM部门名称'
    },
    crmDataId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'crm_data_id',
      comment: 'CRM数据ID'
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '密码'
    },
    role: {
      type: DataTypes.ENUM('admin', 'user'),
      defaultValue: 'user',
      comment: '用户角色'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'active',
      comment: '用户状态'
    },
    lastLoginAt: {
      type: DataTypes.DATE,
      field: 'last_login_at',
      comment: '最后登录时间'
    }
  },
  {
    tableName: 'users',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        name: 'username',
        unique: true,
        fields: ['username']
      },
      {
        name: 'email',
        unique: true,
        fields: ['email']
      }
    ],
    hooks: {
      beforeCreate: async user => {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 10);
        }
      },
      beforeUpdate: async user => {
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 10);
        }
      }
    }
  }
);

// 实例方法：验证密码
User.prototype.validatePassword = async function (password) {
  return await bcrypt.compare(password, this.password);
};

// 实例方法：转换为安全的JSON对象
User.prototype.toSafeJSON = function () {
  const values = Object.assign({}, this.get());
  delete values.password;
  return DateFormatter.formatObject(values);
};

// 重写toJSON方法，自动格式化时间字段
User.prototype.toJSON = function () {
  const values = Object.assign({}, this.get());
  return DateFormatter.formatObject(values);
};

module.exports = User;
