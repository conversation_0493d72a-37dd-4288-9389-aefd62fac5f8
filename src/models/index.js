const { sequelize, testConnection } = require('../config/database');
const databaseInitService = require('../services/DatabaseInitService');
const User = require('./User');
const MyInfluencer = require('./MyInfluencer');  // 我的达人模型
const PublicInfluencer = require('./PublicInfluencer');  // 达人公海模型
const InfluencerReport = require('./InfluencerReport');  // 达人提报模型
const CrawlTask = require('./CrawlTask');
const CrawlLog = require('./CrawlLog');
const CrawlerCookie = require('./CrawlerCookie');
const AuthorVideo = require('./AuthorVideo');
const CooperationManagement = require('./CooperationManagement');
const Dictionary = require('./Dictionary');
const CrmDictionary = require('./CrmDictionary');

// 定义模型关联关系
User.hasMany(MyInfluencer, {
  foreignKey: 'createdBy',
  as: 'myInfluencers'
});

MyInfluencer.belongsTo(User, {
  foreignKey: 'createdBy',
  as: 'creator'
});

// 达人提报关联关系
User.hasMany(InfluencerReport, {
  foreignKey: 'submittedBy',
  as: 'influencerReports'
});

InfluencerReport.belongsTo(User, {
  foreignKey: 'submittedBy',
  as: 'submitter'
});

// 审核人关联关系
User.hasMany(InfluencerReport, {
  foreignKey: 'reviewedBy',
  as: 'reviewedReports'
});

InfluencerReport.belongsTo(User, {
  foreignKey: 'reviewedBy',
  as: 'reviewer'
});

// 达人提报不再直接关联具体的达人表，而是通过platformUserId字段进行逻辑关联
// 这样简化了数据结构，避免了复杂的外键约束

User.hasMany(CrawlTask, {
  foreignKey: 'createdBy',
  as: 'crawlTasks'
});

CrawlTask.belongsTo(User, {
  foreignKey: 'createdBy',
  as: 'creator'
});

// 爬虫任务与爬虫日志的关联
CrawlTask.hasMany(CrawlLog, {
  foreignKey: 'taskId',
  as: 'logs'
});

CrawlLog.belongsTo(CrawlTask, {
  foreignKey: 'taskId',
  as: 'task'
});

// PublicInfluencer（达人公海）关联关系
PublicInfluencer.belongsTo(CrawlTask, {
  foreignKey: 'taskId',
  as: 'crawlTask'
});

CrawlTask.hasMany(PublicInfluencer, {
  foreignKey: 'taskId',
  as: 'publicInfluencers'
});

// PublicInfluencer 与 MyInfluencer 的关联（导入关系）
PublicInfluencer.belongsTo(MyInfluencer, {
  foreignKey: 'importedInfluencerId',
  as: 'myInfluencer'
});

MyInfluencer.hasOne(PublicInfluencer, {
  foreignKey: 'importedInfluencerId',
  as: 'publicInfluencer'
});

// CrawlerCookie 关联关系
CrawlerCookie.belongsTo(User, {
  foreignKey: 'createdBy',
  as: 'creator'
});

// AuthorVideo 关联关系
// 我的达人与视频作品的关联（一对多）
MyInfluencer.hasMany(AuthorVideo, {
  foreignKey: 'authorId',
  as: 'videos'
});

AuthorVideo.belongsTo(MyInfluencer, {
  foreignKey: 'authorId',
  as: 'author'
});

// 爬虫任务与视频作品的关联（一对多）
CrawlTask.hasMany(AuthorVideo, {
  foreignKey: 'crawlTaskId',
  as: 'videos'
});

AuthorVideo.belongsTo(CrawlTask, {
  foreignKey: 'crawlTaskId',
  as: 'crawlTask'
});

// InfluencerReport 与 CooperationManagement 的关联关系
InfluencerReport.hasOne(CooperationManagement, {
  foreignKey: 'influencerReportId',
  as: 'cooperationRecord'
});

CooperationManagement.belongsTo(InfluencerReport, {
  foreignKey: 'influencerReportId',
  as: 'influencerReport'
});

// 智能数据库初始化和同步
const initializeAndSyncDatabase = async (force = false) => {
  try {
    console.log('🔄 开始数据库初始化和同步流程...');

    // 1. 自动检测和初始化数据库
    const initSuccess = await databaseInitService.autoDetectAndInit();

    if (!initSuccess) {
      throw new Error('数据库自动初始化失败，请检查配置和权限');
    }

    // 2. 执行Sequelize模型同步（仅在开发环境或明确指定时）
    const nodeEnv = process.env.NODE_ENV || 'development';
    const shouldSync = nodeEnv === 'development' || process.env.DB_FORCE_SYNC === 'true';

    if (shouldSync) {
      console.log('🔄 执行Sequelize模型同步...');
      // 使用alter: true来更新表结构，而不是删除重建
      await sequelize.sync({ force, alter: true });
      console.log('✅ Sequelize模型同步成功');
    } else {
      console.log('⏭️ 跳过Sequelize模型同步（生产环境）');
    }

    console.log('🎉 数据库初始化和同步流程完成！');

  } catch (error) {
    console.error('❌ 数据库初始化和同步失败:', error.message);
    throw error;
  }
};

// 保持向后兼容的同步函数
const syncDatabase = async (force = false) => {
  try {
    // 使用alter: true来更新表结构，而不是删除重建
    await sequelize.sync({ force, alter: true });
    console.log('✅ 数据库同步成功');
  } catch (error) {
    console.error('❌ 数据库同步失败:', error.message);
    throw error;
  }
};

module.exports = {
  sequelize,
  User,
  MyInfluencer,  // 我的达人模型
  PublicInfluencer,  // 达人公海模型
  InfluencerReport,  // 达人提报模型
  CrawlTask,
  CrawlLog,
  CrawlerCookie,
  AuthorVideo,
  CooperationManagement,
  Dictionary,  // 字典管理模型
  CrmDictionary,  // CRM字典管理模型
  syncDatabase,
  initializeAndSyncDatabase,
  testConnection,
  databaseInitService
};
