const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const DateFormatter = require('../utils/dateFormatter');

/**
 * 爬虫日志模型
 * 记录爬虫任务执行过程中的详细日志
 */
const CrawlLog = sequelize.define('CrawlLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  taskId: {
    type: DataTypes.INTEGER,
    field: 'task_id',
    allowNull: false,
    comment: '关联的爬虫任务ID'
  },
  level: {
    type: DataTypes.ENUM('info', 'warn', 'error', 'debug'),
    allowNull: false,
    defaultValue: 'info',
    comment: '日志级别'
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '日志消息'
  },
  details: {
    type: DataTypes.JSON,
    comment: '详细信息（JSON格式）'
  },
  step: {
    type: DataTypes.STRING(100),
    comment: '执行步骤标识'
  },
  url: {
    type: DataTypes.STRING(500),
    comment: '相关URL'
  },
  duration: {
    type: DataTypes.INTEGER,
    comment: '执行耗时（毫秒）'
  },
  retryCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '重试次数'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'crawl_logs',
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci',
  updatedAt: false, // 日志表不需要更新时间
  indexes: [
    {
      fields: ['task_id']
    },
    {
      fields: ['level']
    },
    {
      fields: ['step']
    },
    {
      fields: ['created_at']
    }
  ]
});

// 重写toJSON方法，自动格式化时间字段
CrawlLog.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return DateFormatter.formatObject(values);
};

module.exports = CrawlLog;
