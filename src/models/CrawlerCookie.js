/**
 * 爬虫Cookie管理模型
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const DateFormatter = require('../utils/dateFormatter');

const CrawlerCookie = sequelize.define('CrawlerCookie', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  accountName: {
    type: DataTypes.STRING(100),
    field: 'account_name',
    allowNull: false,
    comment: '账户名称（用于标识）'
  },
  platform: {
    type: DataTypes.ENUM('xiaohongshu', 'juxingtu'),
    allowNull: false,
    comment: '平台类型'
  },
  cookieData: {
    type: DataTypes.TEXT('long'),
    field: 'cookie_data',
    allowNull: false,
    comment: '加密后的Cookie数据'
  },
  userAgent: {
    type: DataTypes.STRING(500),
    field: 'user_agent',
    comment: '对应的User-Agent'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'expired', 'banned'),
    defaultValue: 'active',
    comment: '<PERSON>ie状态'
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '优先级（数字越大优先级越高）'
  },
  useCount: {
    type: DataTypes.INTEGER,
    field: 'use_count',
    defaultValue: 0,
    comment: '使用次数'
  },
  lastUsedAt: {
    type: DataTypes.DATE,
    field: 'last_used_at',
    comment: '最后使用时间'
  },
  lastValidatedAt: {
    type: DataTypes.DATE,
    field: 'last_validated_at',
    comment: '最后验证时间'
  },
  validationResult: {
    type: DataTypes.JSON,
    field: 'validation_result',
    comment: '验证结果详情'
  },
  cooldownUntil: {
    type: DataTypes.DATE,
    field: 'cooldown_until',
    comment: '冷却结束时间'
  },
  maxDailyUse: {
    type: DataTypes.INTEGER,
    field: 'max_daily_use',
    defaultValue: 100,
    comment: '每日最大使用次数'
  },
  dailyUseCount: {
    type: DataTypes.INTEGER,
    field: 'daily_use_count',
    defaultValue: 0,
    comment: '今日使用次数'
  },
  lastResetDate: {
    type: DataTypes.DATEONLY,
    field: 'last_reset_date',
    comment: '最后重置日期'
  },
  notes: {
    type: DataTypes.TEXT,
    comment: '备注信息'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    field: 'created_by',
    allowNull: false,
    comment: '创建者ID'
  }
}, {
  tableName: 'crawler_cookies',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['platform']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['account_name']
    },
    {
      fields: ['last_used_at']
    },
    {
      fields: ['platform', 'status', 'priority']
    }
  ]
});

// 关联关系
CrawlerCookie.associate = (models) => {
  CrawlerCookie.belongsTo(models.User, {
    foreignKey: 'created_by',
    as: 'creator'
  });
};

// 重写toJSON方法，自动格式化时间字段
CrawlerCookie.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return DateFormatter.formatObject(values);
};

module.exports = CrawlerCookie;
