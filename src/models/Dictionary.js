/**
 * 字典管理模型
 *
 * 功能说明：
 * - 管理系统中的字典数据，支持下拉选项的统一管理
 * - 支持分类管理，便于不同模块使用不同的字典项
 * - 支持排序和状态控制
 *
 * 主要字段：
 * - 字典分类：用于区分不同模块的字典项
 * - 字典键值：用于程序中的标识
 * - 字典标签：用于界面显示
 * - 排序和状态：控制显示顺序和启用状态
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Dictionary = sequelize.define('Dictionary', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '字典分类（如：cooperation_form, cooperation_brand等）'
  },
  dictKey: {
    type: DataTypes.STRING(100),
    field: 'dict_key',
    allowNull: false,
    comment: '字典键值（程序中使用的标识）'
  },
  dictLabel: {
    type: DataTypes.STRING(200),
    field: 'dict_label',
    allowNull: false,
    comment: '字典标签（界面显示的文本）'
  },
  dictValue: {
    type: DataTypes.STRING(200),
    field: 'dict_value',
    allowNull: true,
    comment: '字典值（可选的额外数据）'
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    field: 'sort_order',
    defaultValue: 0,
    comment: '排序顺序（数字越小越靠前）'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active',
    comment: '状态（启用/禁用）'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '描述信息'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    field: 'created_by',
    allowNull: false,
    comment: '创建人ID'
  },
  updatedBy: {
    type: DataTypes.INTEGER,
    field: 'updated_by',
    allowNull: false,
    comment: '更新人ID'
  }
}, {
  tableName: 'dictionaries',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['category']
    },
    {
      fields: ['dict_key']
    },
    {
      fields: ['status']
    },
    {
      // 复合索引用于分类查询
      name: 'idx_category_status_sort',
      fields: ['category', 'status', 'sort_order']
    },
    {
      // 唯一索引确保同一分类下键值唯一
      name: 'idx_category_key_unique',
      fields: ['category', 'dict_key'],
      unique: true
    }
  ]
});

module.exports = Dictionary;
