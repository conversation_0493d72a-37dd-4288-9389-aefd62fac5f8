/**
 * CRM字典数据模型
 *
 * 功能说明：
 * - 专门用于存储从CRM系统同步的字典数据
 * - 支持客户(customer)和协议(contract)两种部署类型的字典
 * - 保留CRM原始数据结构，便于数据追溯和调试
 * - 支持增量更新和数据版本管理
 *
 * 主要字段：
 * - CRM源数据：deployId、fieldName、fieldCode等
 * - 字典项数据：dataName、dataValue、dataId等
 * - 同步管理：同步时间、状态、版本等
 * - 本地映射：映射到本地字典分类和键值
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CrmDictionary = sequelize.define('CrmDictionary', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  
  // ==================== CRM源数据字段 ====================
  deployId: {
    type: DataTypes.STRING(50),
    field: 'deploy_id',
    allowNull: false,
    comment: 'CRM部署ID（customer/contract）'
  },
  fieldName: {
    type: DataTypes.STRING(200),
    field: 'field_name',
    allowNull: false,
    comment: 'CRM字段名称'
  },
  fieldCode: {
    type: DataTypes.STRING(100),
    field: 'field_code',
    allowNull: false,
    comment: 'CRM字段代码'
  },
  fieldDomType: {
    type: DataTypes.STRING(50),
    field: 'field_dom_type',
    allowNull: false,
    comment: 'CRM字段类型（SELECT等）'
  },
  
  // ==================== 字典项数据字段 ====================
  dataId: {
    type: DataTypes.STRING(100),
    field: 'data_id',
    allowNull: false,
    comment: 'CRM字典项ID'
  },
  dataName: {
    type: DataTypes.STRING(200),
    field: 'data_name',
    allowNull: false,
    comment: 'CRM字典项显示名称'
  },
  dataValue: {
    type: DataTypes.STRING(200),
    field: 'data_value',
    allowNull: true,
    comment: 'CRM字典项值'
  },
  dataOrder: {
    type: DataTypes.INTEGER,
    field: 'data_order',
    defaultValue: 0,
    comment: 'CRM字典项排序'
  },
  
  // ==================== 本地映射字段 ====================
  localCategory: {
    type: DataTypes.STRING(100),
    field: 'local_category',
    allowNull: true,
    comment: '映射到本地字典的分类'
  },
  localKey: {
    type: DataTypes.STRING(100),
    field: 'local_key',
    allowNull: true,
    comment: '映射到本地字典的键值'
  },
  localLabel: {
    type: DataTypes.STRING(200),
    field: 'local_label',
    allowNull: true,
    comment: '映射到本地字典的标签'
  },
  
  // ==================== 同步管理字段 ====================
  syncStatus: {
    type: DataTypes.ENUM('pending', 'synced', 'failed', 'disabled'),
    field: 'sync_status',
    defaultValue: 'pending',
    comment: '同步状态（待同步/已同步/失败/禁用）'
  },
  syncTime: {
    type: DataTypes.DATE,
    field: 'sync_time',
    allowNull: true,
    comment: '最后同步时间'
  },
  syncVersion: {
    type: DataTypes.STRING(50),
    field: 'sync_version',
    allowNull: true,
    comment: '同步版本号'
  },
  syncError: {
    type: DataTypes.TEXT,
    field: 'sync_error',
    allowNull: true,
    comment: '同步错误信息'
  },
  
  // ==================== 原始数据字段 ====================
  rawData: {
    type: DataTypes.JSON,
    field: 'raw_data',
    allowNull: true,
    comment: 'CRM原始数据（JSON格式）'
  },
  
  // ==================== 状态管理字段 ====================
  isActive: {
    type: DataTypes.BOOLEAN,
    field: 'is_active',
    defaultValue: true,
    comment: '是否启用'
  },
  isDeleted: {
    type: DataTypes.BOOLEAN,
    field: 'is_deleted',
    defaultValue: false,
    comment: '是否已删除（软删除）'
  },
  
  // ==================== 审计字段 ====================
  createdBy: {
    type: DataTypes.INTEGER,
    field: 'created_by',
    allowNull: true,
    comment: '创建人ID（系统同步时可为空）'
  },
  updatedBy: {
    type: DataTypes.INTEGER,
    field: 'updated_by',
    allowNull: true,
    comment: '更新人ID'
  }
}, {
  tableName: 'crm_dictionaries',
  timestamps: true,
  underscored: true,
  paranoid: false, // 使用软删除字段而不是paranoid
  indexes: [
    {
      // 部署类型索引
      name: 'idx_deploy_id',
      fields: ['deploy_id']
    },
    {
      // 字段代码索引
      name: 'idx_field_code',
      fields: ['field_code']
    },
    {
      // CRM数据ID索引
      name: 'idx_data_id',
      fields: ['data_id']
    },
    {
      // 同步状态索引
      name: 'idx_sync_status',
      fields: ['sync_status']
    },
    {
      // 本地映射索引
      name: 'idx_local_mapping',
      fields: ['local_category', 'local_key']
    },
    {
      // 复合唯一索引：确保同一字段下的数据ID唯一
      name: 'idx_unique_field_data',
      fields: ['deploy_id', 'field_code', 'data_id'],
      unique: true
    },
    {
      // 查询优化索引
      name: 'idx_query_optimization',
      fields: ['deploy_id', 'field_code', 'is_active', 'is_deleted']
    }
  ]
});

module.exports = CrmDictionary;
