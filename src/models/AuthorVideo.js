const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const DateFormatter = require('../utils/dateFormatter');

/**
 * 达人视频作品模型
 * 存储从巨量星图等平台爬取的达人视频详细信息
 */
const AuthorVideo = sequelize.define('AuthorVideo', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  authorId: {
    type: DataTypes.INTEGER,
    field: 'author_id',
    allowNull: true, // 允许为null，支持独立视频保存功能
    comment: '关联的达人ID（my_influencers表），可为null表示通过platformUserId关联'
  },
  authorNickname: {
    type: DataTypes.STRING(100),
    field: 'author_nickname',
    allowNull: true,
    comment: '达人昵称'
  },
  platform: {
    type: DataTypes.ENUM('xiaohongshu', 'juxingtu', 'douyin'),
    allowNull: false,
    comment: '平台类型'
  },
  platformUserId: {
    type: DataTypes.STRING(100),
    field: 'platform_user_id',
    allowNull: false,
    comment: '平台用户ID（星图ID等）'
  },
  videoId: {
    type: DataTypes.STRING(100),
    field: 'video_id',
    allowNull: false,
    comment: '视频ID'
  },
  title: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: '视频标题'
  },
  videoUrl: {
    type: DataTypes.STRING(1000),
    field: 'video_url',
    comment: '视频链接'
  },
  videoCover: {
    type: DataTypes.STRING(1000),
    field: 'video_cover',
    comment: '视频封面图片链接'
  },
  duration: {
    type: DataTypes.INTEGER,
    comment: '视频时长（秒）'
  },
  publishTime: {
    type: DataTypes.DATE,
    field: 'publish_time',
    comment: '发布时间'
  },
  likeCount: {
    type: DataTypes.INTEGER,
    field: 'like_count',
    defaultValue: 0,
    comment: '点赞数'
  },
  playCount: {
    type: DataTypes.INTEGER,
    field: 'play_count',
    defaultValue: 0,
    comment: '播放数'
  },
  shareCount: {
    type: DataTypes.INTEGER,
    field: 'share_count',
    defaultValue: 0,
    comment: '分享数'
  },
  commentCount: {
    type: DataTypes.INTEGER,
    field: 'comment_count',
    defaultValue: 0,
    comment: '评论数'
  },
  collectCount: {
    type: DataTypes.INTEGER,
    field: 'collect_count',
    defaultValue: 0,
    comment: '收藏数'
  },
  tags: {
    type: DataTypes.JSON,
    comment: '视频标签'
  },
  description: {
    type: DataTypes.TEXT,
    comment: '视频描述'
  },
  location: {
    type: DataTypes.STRING(100),
    comment: '拍摄地点'
  },
  musicInfo: {
    type: DataTypes.JSON,
    field: 'music_info',
    comment: '背景音乐信息'
  },
  videoStats: {
    type: DataTypes.JSON,
    field: 'video_stats',
    comment: '视频统计数据（扩展字段）'
  },
  rawData: {
    type: DataTypes.JSON,
    field: 'raw_data',
    comment: '原始爬取数据'
  },
  status: {
    type: DataTypes.ENUM('active', 'deleted', 'private'),
    defaultValue: 'active',
    comment: '视频状态'
  },
  crawlTaskId: {
    type: DataTypes.INTEGER,
    field: 'crawl_task_id',
    comment: '关联的爬虫任务ID'
  },
  lastUpdated: {
    type: DataTypes.DATE,
    field: 'last_updated',
    comment: '数据最后更新时间'
  }
}, {
  tableName: 'author_videos',
  timestamps: true,
  underscored: true,
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci',
  indexes: [
    {
      fields: ['author_id']
    },
    {
      fields: ['platform']
    },
    {
      fields: ['platform_user_id']
    },
    {
      fields: ['video_id']
    },
    {
      fields: ['publish_time']
    },
    {
      fields: ['play_count']
    },
    {
      fields: ['like_count']
    },
    {
      fields: ['crawl_task_id']
    },
    {
      // 复合索引：平台+用户ID+视频ID，确保唯一性
      unique: true,
      fields: ['platform', 'platform_user_id', 'video_id'],
      name: 'unique_platform_user_video'
    },
    {
      // 复合索引：达人ID+发布时间，优化按达人查询视频列表
      fields: ['author_id', 'publish_time'],
      name: 'idx_author_publish_time'
    }
  ]
});

// 定义查询时包含的字段（排除敏感字段）
AuthorVideo.QUERY_ATTRIBUTES = [
  'id', 'authorId', 'authorNickname', 'platform', 'platformUserId', 'videoId', 'title',
  'videoUrl', 'videoCover', 'duration', 'publishTime', 'likeCount',
  'playCount', 'shareCount', 'commentCount', 'collectCount', 'tags',
  'description', 'location', 'musicInfo', 'videoStats', 'status',
  'crawlTaskId', 'lastUpdated', 'createdAt', 'updatedAt'
];

// 重写toJSON方法，自动格式化时间字段
AuthorVideo.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return DateFormatter.formatObject(values);
};

module.exports = AuthorVideo;
