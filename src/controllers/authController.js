const { User } = require('../models');
const ResponseUtil = require('../utils/response');
const JWTUtil = require('../utils/jwt');
const ValidationUtil = require('../utils/validation');

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - username
 *         - password
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名或邮箱
 *           example: admin
 *         password:
 *           type: string
 *           format: password
 *           description: 密码
 *           example: password123
 *     RegisterRequest:
 *       type: object
 *       required:
 *         - username
 *         - email
 *         - password
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名（3-20位字母、数字、下划线）
 *           example: newuser
 *         email:
 *           type: string
 *           format: email
 *           description: 邮箱地址
 *           example: <EMAIL>
 *         password:
 *           type: string
 *           format: password
 *           description: 密码（至少8位，包含字母和数字）
 *           example: password123
 *         role:
 *           type: string
 *           enum: [admin, user]
 *           description: 用户角色
 *           default: user
 *           example: user
 *     ChangePasswordRequest:
 *       type: object
 *       required:
 *         - oldPassword
 *         - newPassword
 *       properties:
 *         oldPassword:
 *           type: string
 *           format: password
 *           description: 当前密码
 *           example: oldpassword123
 *         newPassword:
 *           type: string
 *           format: password
 *           description: 新密码（至少8位，包含字母和数字）
 *           example: newpassword123
 *     AuthResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 token:
 *                   type: string
 *                   description: JWT认证令牌
 *                   example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */

class AuthController {
  /**
   * @swagger
   * /auth/register:
   *   post:
   *     tags: [认证]
   *     summary: 用户注册
   *     description: 创建新用户账户，支持管理员和普通用户角色
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/RegisterRequest'
   *           examples:
   *             普通用户注册:
   *               value:
   *                 username: testuser
   *                 email: <EMAIL>
   *                 password: password123
   *                 role: user
   *             管理员注册:
   *               value:
   *                 username: admin
   *                 email: <EMAIL>
   *                 password: admin123456
   *                 role: admin
   *     responses:
   *       200:
   *         description: 注册成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/User'
   *             example:
   *               success: true
   *               message: 注册成功
   *               data:
   *                 id: 1
   *                 username: testuser
   *                 email: <EMAIL>
   *                 role: user
   *                 status: active
   *                 createdAt: "2024-01-01T00:00:00.000Z"
   *                 updatedAt: "2024-01-01T00:00:00.000Z"
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       409:
   *         description: 用户名或邮箱已存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               message: 用户名已存在
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async register(ctx) {
    try {
      const { username, email, password, role = 'user' } = ctx.request.body;

      // 验证必填字段
      const requiredErrors = ValidationUtil.validateRequired(
        { username, email, password },
        ['username', 'email', 'password']
      );
      if (requiredErrors) {
        return ResponseUtil.validationError(ctx, requiredErrors);
      }

      // 验证数据格式
      const errors = {};
      if (!ValidationUtil.isValidUsername(username)) {
        errors.username = '用户名格式不正确（3-20位字母、数字、下划线）';
      }
      if (!ValidationUtil.isValidEmail(email)) {
        errors.email = '邮箱格式不正确';
      }
      if (!ValidationUtil.isValidPassword(password)) {
        errors.password = '密码格式不正确（至少8位，包含字母和数字）';
      }

      if (Object.keys(errors).length > 0) {
        return ResponseUtil.validationError(ctx, errors);
      }

      // 检查用户名和邮箱是否已存在
      const existingUser = await User.findOne({
        where: {
          [require('sequelize').Op.or]: [{ username }, { email }]
        }
      });

      if (existingUser) {
        if (existingUser.username === username) {
          return ResponseUtil.error(ctx, '用户名已存在', 409);
        }
        if (existingUser.email === email) {
          return ResponseUtil.error(ctx, '邮箱已存在', 409);
        }
      }

      // 创建用户
      const user = await User.create({
        username,
        email,
        password,
        role
      });

      ResponseUtil.success(ctx, user.toSafeJSON(), '注册成功');
    } catch (error) {
      console.error('注册失败:', error);
      ResponseUtil.error(ctx, '注册失败', 500);
    }
  }

  /**
   * @swagger
   * /auth/login:
   *   post:
   *     tags: [认证]
   *     summary: 用户登录
   *     description: 用户登录认证，支持用户名或邮箱登录，返回JWT令牌
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/LoginRequest'
   *           examples:
   *             用户名登录:
   *               value:
   *                 username: admin
   *                 password: password123
   *             邮箱登录:
   *               value:
   *                 username: <EMAIL>
   *                 password: password123
   *     responses:
   *       200:
   *         description: 登录成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/AuthResponse'
   *             example:
   *               success: true
   *               message: 登录成功
   *               data:
   *                 user:
   *                   id: 1
   *                   username: admin
   *                   email: <EMAIL>
   *                   role: admin
   *                   status: active
   *                   lastLoginAt: "2024-01-01T12:00:00.000Z"
   *                   createdAt: "2024-01-01T00:00:00.000Z"
   *                   updatedAt: "2024-01-01T12:00:00.000Z"
   *                 token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInVzZXJuYW1lIjoiYWRtaW4iLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE2NDA5OTUyMDAsImV4cCI6MTY0MTA4MTYwMH0.example
   *       400:
   *         $ref: '#/components/responses/ValidationError'
   *       401:
   *         description: 密码错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               message: 密码错误
   *       403:
   *         description: 账户已被禁用
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               message: 账户已被禁用
   *       404:
   *         description: 用户不存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               message: 用户不存在
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async login(ctx) {
    try {
      const { username, password } = ctx.request.body;

      // 验证必填字段
      const requiredErrors = ValidationUtil.validateRequired(
        { username, password },
        ['username', 'password']
      );
      if (requiredErrors) {
        return ResponseUtil.validationError(ctx, requiredErrors);
      }

      // 查找用户（支持用户名或邮箱登录）
      const user = await User.findOne({
        where: {
          [require('sequelize').Op.or]: [{ username }, { email: username }]
        }
      });

      if (!user) {
        return ResponseUtil.error(ctx, '用户不存在', 404);
      }

      if (user.status !== 'active') {
        return ResponseUtil.error(ctx, '账户已被禁用', 403);
      }

      // 验证密码
      const isValidPassword = await user.validatePassword(password);
      if (!isValidPassword) {
        return ResponseUtil.error(ctx, '密码错误', 500);
      }

      // 更新最后登录时间
      await user.update({ lastLoginAt: new Date() });

      // 生成JWT令牌
      const token = JWTUtil.generateToken({
        userId: user.id,
        username: user.username,
        role: user.role
      });

      ResponseUtil.success(ctx, {
        user: user.toSafeJSON(),
        token
      }, '登录成功');
    } catch (error) {
      console.error('登录失败:', error);
      ResponseUtil.error(ctx, '登录失败', 500);
    }
  }

  /**
   * @swagger
   * /auth/me:
   *   get:
   *     tags: [认证]
   *     summary: 获取当前用户信息
   *     description: 获取当前登录用户的详细信息
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: 获取用户信息成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/User'
   *             example:
   *               success: true
   *               message: 获取用户信息成功
   *               data:
   *                 id: 1
   *                 username: admin
   *                 email: <EMAIL>
   *                 role: admin
   *                 status: active
   *                 lastLoginAt: "2024-01-01T12:00:00.000Z"
   *                 createdAt: "2024-01-01T00:00:00.000Z"
   *                 updatedAt: "2024-01-01T12:00:00.000Z"
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getCurrentUser(ctx) {
    try {
      const user = ctx.state.user;
      ResponseUtil.success(ctx, user.toSafeJSON(), '获取用户信息成功');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      ResponseUtil.error(ctx, '获取用户信息失败', 500);
    }
  }

  /**
   * @swagger
   * /auth/change-password:
   *   put:
   *     tags: [认证]
   *     summary: 修改密码
   *     description: 修改当前用户的登录密码
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/ChangePasswordRequest'
   *           example:
   *             oldPassword: oldpassword123
   *             newPassword: newpassword123
   *     responses:
   *       200:
   *         description: 密码修改成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *             example:
   *               success: true
   *               message: 密码修改成功
   *               data: null
   *       400:
   *         description: 新密码格式不正确
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               message: 新密码格式不正确（至少8位，包含字母和数字）
   *       401:
   *         description: 旧密码错误或认证失败
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             examples:
   *               旧密码错误:
   *                 value:
   *                   success: false
   *                   message: 旧密码错误
   *               认证失败:
   *                 value:
   *                   success: false
   *                   message: 认证失败
   *                   error: 无效的认证令牌
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async changePassword(ctx) {
    try {
      const { oldPassword, newPassword } = ctx.request.body;
      const user = ctx.state.user;

      // 验证必填字段
      const requiredErrors = ValidationUtil.validateRequired(
        { oldPassword, newPassword },
        ['oldPassword', 'newPassword']
      );
      if (requiredErrors) {
        return ResponseUtil.validationError(ctx, requiredErrors);
      }

      // 验证新密码格式
      if (!ValidationUtil.isValidPassword(newPassword)) {
        return ResponseUtil.error(ctx, '新密码格式不正确（至少8位，包含字母和数字）', 400);
      }

      // 验证旧密码
      const isValidOldPassword = await user.validatePassword(oldPassword);
      if (!isValidOldPassword) {
        return ResponseUtil.error(ctx, '旧密码错误', 401);
      }

      // 更新密码
      await user.update({ password: newPassword });

      ResponseUtil.success(ctx, null, '密码修改成功');
    } catch (error) {
      console.error('修改密码失败:', error);
      ResponseUtil.error(ctx, '修改密码失败', 500);
    }
  }
}

module.exports = AuthController;
