/**
 * 合作对接管理控制器
 *
 * 功能说明：
 * - 处理合作对接管理相关的HTTP请求
 * - 提供CRUD操作接口
 * - 支持笔记链接解析和数据拉取
 * - 管理定时任务相关操作
 *
 * 主要接口：
 * - POST /api/cooperation - 创建合作记录
 * - GET /api/cooperation - 获取合作记录列表
 * - GET /api/cooperation/:id - 获取合作记录详情
 * - PUT /api/cooperation/:id - 更新合作记录
 * - DELETE /api/cooperation/:id - 删除合作记录
 * - POST /api/cooperation/parse-note-link - 解析笔记链接
 * - POST /api/cooperation/:id/fetch-data - 手动拉取笔记数据
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const cooperationService = require('../services/CooperationService');
const cooperationScheduleService = require('../services/CooperationScheduleService');
const CookieManager = require('../services/CookieManager');
const PublishLinkFixService = require('../services/PublishLinkFixService');
const ResponseUtil = require('../utils/response');

class CooperationController {
  /**
   * 创建合作记录
   *
   * @swagger
   * /api/cooperation:
   *   post:
   *     tags: [合作对接管理]
   *     summary: 创建合作记录
   *     description: 创建新的合作对接记录
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - cooperationMonth
   *               - platform
   *               - bloggerName
   *               - responsiblePerson
   *             properties:
   *               cooperationMonth:
   *                 type: string
   *                 description: 合作月份，格式：YYYY-MM
   *                 example: "2024-01"
   *               platform:
   *                 type: string
   *                 enum: [xiaohongshu, douyin, weibo, bilibili]
   *                 description: 平台类型
   *               bloggerName:
   *                 type: string
   *                 description: 博主名称
   *               responsiblePerson:
   *                 type: string
   *                 description: 负责人花名
   *               influencerHomepage:
   *                 type: string
   *                 description: 达人平台主页链接
   *               cooperationNoteLink:
   *                 type: string
   *                 description: 合作笔记链接
   *               cooperationPrice:
   *                 type: number
   *                 description: 合作价格
   *               contentDirection:
   *                 type: string
   *                 description: 内容方向
   *               cooperationProduct:
   *                 type: string
   *                 description: 合作品
   *               scheduledPublishTime:
   *                 type: string
   *                 format: date-time
   *                 description: 约定发布时间
   *               workProgress:
   *                 type: string
   *                 enum: [待开始, 进行中, 已完成, 已暂停, 已取消]
   *                 description: 工作进度
   *     responses:
   *       201:
   *         description: 合作记录创建成功
   *       400:
   *         description: 请求参数错误
   */
  static async createCooperation(ctx) {
    try {
      const data = ctx.request.body;
      const userId = ctx.state.user.id;

      // 验证必填字段（新版本）
      if (!data.customerName) {
        return ResponseUtil.error(ctx, '客户名称不能为空', 400);
      }
      if (!data.title) {
        return ResponseUtil.error(ctx, '标题不能为空', 400);
      }

      // 验证CRM同步选项
      if (data.syncCreateAgreement && !data.syncCreateCustomer) {
        return ResponseUtil.error(ctx, '创建协议必须先创建客户', 400);
      }

      const cooperation = await cooperationService.createCooperation(data, userId);

      // 构建响应消息
      let message = '合作记录创建成功';
      if (cooperation.dataValues.crmSyncResult) {
        const syncResult = cooperation.dataValues.crmSyncResult;
        if (syncResult.success) {
          message += '，CRM同步成功';
        } else if (syncResult.errors.length > 0) {
          message += '，CRM同步部分失败';
        }
      }

      ResponseUtil.success(ctx, cooperation, message, 201);
    } catch (error) {
      console.error('创建合作记录失败:', error);
      ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 获取合作记录列表
   *
   * @swagger
   * /api/cooperation:
   *   get:
   *     tags: [合作对接管理]
   *     summary: 获取合作记录列表
   *     description: 获取合作对接记录列表，支持分页和筛选
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: customerName
   *         schema:
   *           type: string
   *         description: 客户名称筛选
   *       - in: query
   *         name: seedingPlatform
   *         schema:
   *           type: string
   *         description: 种草平台筛选
   *       - in: query
   *         name: cooperationBrand
   *         schema:
   *           type: string
   *         description: 合作品牌筛选
   *       - in: query
   *         name: crmLinkStatus
   *         schema:
   *           type: string
   *           enum: [unlinked, customer_linked, fully_linked]
   *         description: CRM关联状态筛选
   *       - in: query
   *         name: platform
   *         schema:
   *           type: string
   *         description: 平台筛选
   *       - in: query
   *         name: workProgress
   *         schema:
   *           type: string
   *         description: 工作进度筛选
   *       - in: query
   *         name: cooperationMonth
   *         schema:
   *           type: string
   *         description: 合作月份筛选
   *       - in: query
   *         name: responsiblePerson
   *         schema:
   *           type: string
   *         description: 负责人筛选
   *       - in: query
   *         name: bloggerName
   *         schema:
   *           type: string
   *         description: 博主名称筛选
   *       - in: query
   *         name: keyword
   *         schema:
   *           type: string
   *         description: 关键词搜索
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getCooperationList(ctx) {
    try {
      const options = ctx.query;
      const result = await cooperationService.getCooperationList(options);
      ResponseUtil.success(ctx, result.cooperations, '获取合作记录列表成功', 200, result.pagination);
    } catch (error) {
      console.error('获取合作记录列表失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 获取合作记录详情
   *
   * @swagger
   * /api/cooperation/{id}:
   *   get:
   *     tags: [合作对接管理]
   *     summary: 获取合作记录详情
   *     description: 根据ID获取合作记录详情
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 合作记录ID
   *     responses:
   *       200:
   *         description: 获取成功
   *       404:
   *         description: 记录不存在
   */
  static async getCooperationById(ctx) {
    try {
      const { id } = ctx.params;
      const cooperation = await cooperationService.getCooperationById(id);
      ResponseUtil.success(ctx, cooperation, '获取合作记录详情成功');
    } catch (error) {
      console.error('获取合作记录详情失败:', error);
      if (error.message.includes('不存在')) {
        ResponseUtil.notFound(ctx, error.message);
      } else {
        ResponseUtil.error(ctx, error.message, 500);
      }
    }
  }

  /**
   * 更新合作记录
   *
   * @swagger
   * /api/cooperation/{id}:
   *   put:
   *     tags: [合作对接管理]
   *     summary: 更新合作记录
   *     description: 根据ID更新合作记录
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 合作记录ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               cooperationMonth:
   *                 type: string
   *               platform:
   *                 type: string
   *               bloggerName:
   *                 type: string
   *               responsiblePerson:
   *                 type: string
   *               influencerHomepage:
   *                 type: string
   *               cooperationNoteLink:
   *                 type: string
   *               cooperationPrice:
   *                 type: number
   *               contentDirection:
   *                 type: string
   *               cooperationProduct:
   *                 type: string
   *               scheduledPublishTime:
   *                 type: string
   *                 format: date-time
   *               workProgress:
   *                 type: string
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 记录不存在
   */
  static async updateCooperation(ctx) {
    try {
      const { id } = ctx.params;
      const data = ctx.request.body;
      const userId = ctx.state.user.id;

      const cooperation = await cooperationService.updateCooperation(id, data, userId);
      ResponseUtil.success(ctx, cooperation, '合作记录更新成功');
    } catch (error) {
      console.error('更新合作记录失败:', error);
      if (error.message.includes('不存在')) {
        ResponseUtil.notFound(ctx, error.message);
      } else {
        ResponseUtil.error(ctx, error.message, 400);
      }
    }
  }

  /**
   * 删除合作记录
   *
   * @swagger
   * /api/cooperation/{id}:
   *   delete:
   *     tags: [合作对接管理]
   *     summary: 删除合作记录
   *     description: 根据ID删除合作记录
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 合作记录ID
   *     responses:
   *       200:
   *         description: 删除成功
   *       404:
   *         description: 记录不存在
   */
  static async deleteCooperation(ctx) {
    try {
      const { id } = ctx.params;
      await cooperationService.deleteCooperation(id);
      ResponseUtil.success(ctx, null, '合作记录删除成功');
    } catch (error) {
      console.error('删除合作记录失败:', error);
      if (error.message.includes('不存在')) {
        ResponseUtil.notFound(ctx, error.message);
      } else {
        ResponseUtil.error(ctx, error.message, 500);
      }
    }
  }

  /**
   * 解析笔记链接
   *
   * @swagger
   * /api/cooperation/parse-note-link:
   *   post:
   *     tags: [合作对接管理]
   *     summary: 解析笔记链接
   *     description: 从笔记链接中解析出笔记ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - noteLink
   *             properties:
   *               noteLink:
   *                 type: string
   *                 description: 笔记链接
   *                 example: "https://www.xiaohongshu.com/explore/686e38b60000000011001e15?xsec_token=..."
   *     responses:
   *       200:
   *         description: 解析成功
   *       400:
   *         description: 链接格式错误
   */
  static async parseNoteLink(ctx) {
    try {
      const { noteLink } = ctx.request.body;

      if (!noteLink) {
        return ResponseUtil.error(ctx, '笔记链接不能为空', 400);
      }

      const result = cooperationService.parseNoteLink(noteLink);
      ResponseUtil.success(ctx, result, '笔记链接解析成功');
    } catch (error) {
      console.error('解析笔记链接失败:', error);
      ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 手动拉取笔记数据
   *
   * @swagger
   * /api/cooperation/{id}/fetch-data:
   *   post:
   *     tags: [合作对接管理]
   *     summary: 手动拉取笔记数据
   *     description: 手动触发指定合作记录的笔记数据拉取
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 合作记录ID
   *     responses:
   *       200:
   *         description: 拉取成功
   *       404:
   *         description: 记录不存在
   */
  static async fetchNoteData(ctx) {
    try {
      const { id } = ctx.params;
      const cooperation = await cooperationService.getCooperationById(id);

      if (!cooperation.noteId) {
        return ResponseUtil.error(ctx, '该记录没有笔记ID，无法拉取数据', 400);
      }

      const result = await cooperationService.fetchNoteData(cooperation);

      if (result.success) {
        ResponseUtil.success(ctx, result.data, '笔记数据拉取成功');
      } else {
        ResponseUtil.error(ctx, `数据拉取失败: ${result.error}`, 500);
      }
    } catch (error) {
      console.error('拉取笔记数据失败:', error);
      if (error.message.includes('不存在')) {
        ResponseUtil.notFound(ctx, error.message);
      } else {
        ResponseUtil.error(ctx, error.message, 500);
      }
    }
  }

  /**
   * 获取定时任务状态
   *
   * @swagger
   * /api/cooperation/schedule/status:
   *   get:
   *     tags: [合作对接管理]
   *     summary: 获取定时任务状态
   *     description: 获取合作对接定时任务的运行状态
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getScheduleStatus(ctx) {
    try {
      const status = cooperationScheduleService.getTaskStatus();
      ResponseUtil.success(ctx, status, '获取定时任务状态成功');
    } catch (error) {
      console.error('获取定时任务状态失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 手动触发笔记数据拉取
   *
   * @swagger
   * /api/cooperation/schedule/trigger:
   *   post:
   *     tags: [合作对接管理]
   *     summary: 手动触发笔记数据拉取
   *     description: 手动触发合作对接笔记数据拉取
   *     responses:
   *       200:
   *         description: 触发成功
   */
  static async triggerDataFetch(ctx) {
    try {
      const result = await cooperationScheduleService.triggerDataFetchTask();

      if (result.success) {
        ResponseUtil.success(ctx, result, '笔记数据拉取触发成功');
      } else {
        ResponseUtil.error(ctx, `任务执行失败: ${result.error}`, 500);
      }
    } catch (error) {
      console.error('触发笔记数据拉取失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 获取笔记数据拉取列表
   *
   * @swagger
   * /api/cooperation/tasks:
   *   get:
   *     tags: [合作对接管理]
   *     summary: 获取笔记数据拉取列表
   *     description: 获取需要拉取数据的合作记录列表，支持筛选和分页
   *     parameters:
   *       - in: query
   *         name: status
   *         schema:
   *           type: string
   *           enum: [pending, fetching, success, failed]
   *         description: 拉取状态筛选
   *       - in: query
   *         name: customerName
   *         schema:
   *           type: string
   *         description: 客户名称搜索
   *       - in: query
   *         name: dateFrom
   *         schema:
   *           type: string
   *           format: date
   *         description: 数据登记日期开始
   *       - in: query
   *         name: dateTo
   *         schema:
   *           type: string
   *           format: date
   *         description: 数据登记日期结束
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 20
   *         description: 每页数量
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getDataFetchTasks(ctx) {
    try {
      const { status, customerName, dateFrom, dateTo, page = 1, limit = 20 } = ctx.query;

      const result = await cooperationService.getDataFetchTasks({
        status,
        customerName,
        dateFrom,
        dateTo,
        page: parseInt(page),
        limit: parseInt(limit)
      });

      ResponseUtil.success(ctx, result, '获取任务列表成功');
    } catch (error) {
      console.error('获取笔记数据拉取列表失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 批量拉取笔记数据
   *
   * @swagger
   * /api/cooperation/tasks/batch-fetch:
   *   post:
   *     tags: [合作对接管理]
   *     summary: 批量拉取笔记数据
   *     description: 批量触发多个合作记录的笔记数据拉取
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               ids:
   *                 type: array
   *                 items:
   *                   type: integer
   *                 description: 合作记录ID列表
   *     responses:
   *       200:
   *         description: 批量拉取成功
   */
  static async batchFetchNoteData(ctx) {
    try {
      const { ids } = ctx.request.body;

      if (!Array.isArray(ids) || ids.length === 0) {
        return ResponseUtil.error(ctx, '请提供有效的记录ID列表', 400);
      }

      const result = await cooperationService.batchFetchNoteData(ids);
      ResponseUtil.success(ctx, result, '批量拉取任务已启动');
    } catch (error) {
      console.error('批量拉取笔记数据失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 获取任务统计信息
   *
   * @swagger
   * /api/cooperation/tasks/stats:
   *   get:
   *     tags: [合作对接管理]
   *     summary: 获取任务统计信息
   *     description: 获取笔记数据拉取的统计信息
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getTaskStats(ctx) {
    try {
      const stats = await cooperationService.getDataFetchTaskStats();
      ResponseUtil.success(ctx, stats, '获取统计信息成功');
    } catch (error) {
      console.error('获取任务统计信息失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 获取定时任务状态
   *
   * @swagger
   * /api/cooperation/schedule/status:
   *   get:
   *     tags: [合作对接管理]
   *     summary: 获取定时任务状态
   *     description: 获取合作对接数据拉取定时任务的状态信息
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getScheduleStatus(ctx) {
    try {
      const status = cooperationScheduleService.getTaskStatus();
      ResponseUtil.success(ctx, status, '获取定时任务状态成功');
    } catch (error) {
      console.error('获取定时任务状态失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 检查Cookie状态
   *
   * @swagger
   * /api/cooperation/cookie/status:
   *   get:
   *     tags: [合作对接管理]
   *     summary: 检查Cookie状态
   *     description: 检查合作对接数据拉取所需的Cookie状态
   *     responses:
   *       200:
   *         description: 检查成功
   */
  static async checkCookieStatus(ctx) {
    try {
      const cookieManager = new CookieManager();
      const xiaohongshuCookie = await cookieManager.getAvailableCookie('xiaohongshu');

      const status = {
        xiaohongshu: {
          hasAvailable: !!xiaohongshuCookie,
          cookieInfo: xiaohongshuCookie
            ? {
                accountName: xiaohongshuCookie.accountName,
                priority: xiaohongshuCookie.priority,
                id: xiaohongshuCookie.id
              }
            : null
        }
      };

      ResponseUtil.success(ctx, status, 'Cookie状态检查完成');
    } catch (error) {
      console.error('检查Cookie状态失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 获取Cookie使用统计
   *
   * @swagger
   * /api/cooperation/cookie/stats:
   *   get:
   *     tags: [合作对接管理]
   *     summary: 获取Cookie使用统计
   *     description: 获取合作对接数据拉取的Cookie使用统计信息
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getCookieStats(ctx) {
    try {
      const cookieManager = new CookieManager();

      // 获取所有小红书Cookie的统计信息
      const { CrawlerCookie } = require('../models');
      const cookies = await CrawlerCookie.findAll({
        where: { platform: 'xiaohongshu' },
        attributes: ['id', 'accountName', 'status', 'useCount', 'dailyUseCount', 'lastUsedAt', 'priority'],
        order: [
          ['priority', 'DESC'],
          ['useCount', 'ASC']
        ]
      });

      const stats = {
        totalCookies: cookies.length,
        activeCookies: cookies.filter(c => c.status === 'active').length,
        expiredCookies: cookies.filter(c => c.status === 'expired').length,
        inactiveCookies: cookies.filter(c => c.status === 'inactive').length,
        bannedCookies: cookies.filter(c => c.status === 'banned').length,
        cookies: cookies.map(cookie => ({
          id: cookie.id,
          accountName: cookie.accountName,
          status: cookie.status,
          useCount: cookie.useCount,
          dailyUseCount: cookie.dailyUseCount,
          lastUsedAt: cookie.lastUsedAt,
          priority: cookie.priority
        }))
      };

      ResponseUtil.success(ctx, stats, 'Cookie统计信息获取成功');
    } catch (error) {
      console.error('获取Cookie统计失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 重新创建CRM客户
   */
  static async recreateCrmCustomer(ctx) {
    try {
      const { id } = ctx.params;
      const userId = ctx.state.user.id;

      if (!id || isNaN(parseInt(id))) {
        return ResponseUtil.error(ctx, '无效的合作记录ID', 400);
      }

      const result = await cooperationService.recreateCrmCustomer(parseInt(id), userId);

      if (result.success) {
        ResponseUtil.success(ctx, result, 'CRM客户创建成功');
      } else {
        ResponseUtil.error(ctx, result.error || 'CRM客户创建失败', 400);
      }
    } catch (error) {
      console.error('重新创建CRM客户失败:', error);
      ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 重新创建CRM协议
   */
  static async recreateCrmAgreement(ctx) {
    try {
      const { id } = ctx.params;
      const userId = ctx.state.user.id;

      if (!id || isNaN(parseInt(id))) {
        return ResponseUtil.error(ctx, '无效的合作记录ID', 400);
      }

      const result = await cooperationService.recreateCrmAgreement(parseInt(id), userId);

      if (result.success) {
        ResponseUtil.success(ctx, result, 'CRM协议创建成功');
      } else {
        ResponseUtil.error(ctx, result.error || 'CRM协议创建失败', 400);
      }
    } catch (error) {
      console.error('重新创建CRM协议失败:', error);
      ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 获取缺失publishLink的记录统计
   */
  static async getPublishLinkStats(ctx) {
    try {
      const publishLinkFixService = new PublishLinkFixService();
      const stats = await publishLinkFixService.analyzePublishLinkIssues();

      ResponseUtil.success(ctx, stats, '获取publishLink统计成功');
    } catch (error) {
      console.error('获取publishLink统计失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 获取缺失publishLink的记录列表
   */
  static async getMissingPublishLinkRecords(ctx) {
    try {
      const publishLinkFixService = new PublishLinkFixService();
      const options = {
        page: parseInt(ctx.query.page) || 1,
        limit: parseInt(ctx.query.limit) || 20,
        onlyCrmSynced: ctx.query.onlyCrmSynced === 'true',
        hasViewData: ctx.query.hasViewData === 'true'
      };

      const result = await publishLinkFixService.getMissingPublishLinkRecords(options);

      ResponseUtil.success(ctx, result.records, '获取缺失publishLink记录成功', 200, result.pagination);
    } catch (error) {
      console.error('获取缺失publishLink记录失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 更新单个记录的publishLink
   */
  static async updatePublishLink(ctx) {
    try {
      const { id } = ctx.params;
      const { publishLink } = ctx.request.body;
      const userId = ctx.state.user.id;

      if (!id || isNaN(parseInt(id))) {
        return ResponseUtil.error(ctx, '无效的记录ID', 400);
      }

      if (!publishLink) {
        return ResponseUtil.error(ctx, '发布链接不能为空', 400);
      }

      const publishLinkFixService = new PublishLinkFixService();
      const result = await publishLinkFixService.updatePublishLink(parseInt(id), publishLink, userId);

      ResponseUtil.success(ctx, result, '发布链接更新成功');
    } catch (error) {
      console.error('更新发布链接失败:', error);
      ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 批量更新publishLink
   */
  static async batchUpdatePublishLink(ctx) {
    try {
      const { updates } = ctx.request.body;
      const userId = ctx.state.user.id;

      if (!Array.isArray(updates) || updates.length === 0) {
        return ResponseUtil.error(ctx, '更新数据不能为空', 400);
      }

      const publishLinkFixService = new PublishLinkFixService();
      const result = await publishLinkFixService.batchUpdatePublishLink(updates, userId);

      ResponseUtil.success(ctx, result, '批量更新发布链接完成');
    } catch (error) {
      console.error('批量更新发布链接失败:', error);
      ResponseUtil.error(ctx, error.message, 400);
    }
  }
}

module.exports = CooperationController;
