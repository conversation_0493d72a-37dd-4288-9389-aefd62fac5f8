/**
 * 简化的MCP控制器
 * 提供基本的MCP接口，专注于核心功能
 */

const ResponseUtil = require('../utils/response');
const simpleMCPService = require('../services/SimpleMCPService');

class SimpleMCPController {
  constructor() {
    this.mcpService = simpleMCPService;
    this.init();
  }

  /**
   * 初始化MCP控制器
   */
  async init() {
    try {
      await this.mcpService.initialize();
      console.log('✅ 简化MCP控制器初始化成功');
    } catch (error) {
      console.error('❌ 简化MCP控制器初始化失败:', error);
    }
  }

  /**
   * 获取MCP工具列表
   * GET /api/simple-mcp/tools
   */
  async getTools(ctx) {
    try {
      const server = this.mcpService.getServer();
      if (!server) {
        ResponseUtil.error(ctx, 'MCP服务未初始化', 500);
        return;
      }

      // 获取已注册的工具
      const tools = [
        {
          name: 'create_crawler_task',
          title: '创建爬虫任务',
          description: '创建达人爬虫任务，支持关键词搜索指定平台的达人信息'
        },
        {
          name: 'get_task_status',
          title: '获取任务状态',
          description: '根据任务ID获取爬虫任务的执行状态和进度信息'
        },
        {
          name: 'get_task_results',
          title: '获取任务结果',
          description: '获取爬虫任务的结果数据，支持分页和筛选'
        },
        {
          name: 'export_results_to_excel',
          title: '导出结果到Excel',
          description: '将爬虫任务结果导出为Excel文件'
        }
      ];

      ResponseUtil.success(ctx, {
        tools,
        count: tools.length,
        version: '1.0.0',
        description: '简化版达人爬虫系统MCP工具集'
      }, '获取MCP工具列表成功');

    } catch (error) {
      console.error('获取MCP工具列表失败:', error);
      ResponseUtil.error(ctx, `获取MCP工具列表失败: ${error.message}`, 500);
    }
  }

  /**
   * 执行MCP工具
   * POST /api/simple-mcp/execute
   */
  async executeTool(ctx) {
    try {
      const { tool, arguments: args } = ctx.request.body;

      if (!tool) {
        ResponseUtil.error(ctx, '工具名称不能为空', 400);
        return;
      }

      if (!args) {
        ResponseUtil.error(ctx, '工具参数不能为空', 400);
        return;
      }

      console.log(`🔧 [MCP] 执行工具: ${tool}`, args);

      const server = this.mcpService.getServer();
      if (!server) {
        ResponseUtil.error(ctx, 'MCP服务未初始化', 500);
        return;
      }

      // 根据工具名称执行对应的工具
      let result;
      switch (tool) {
        case 'create_crawler_task':
          result = await this.executeCreateCrawlerTask(args);
          break;
        case 'get_task_status':
          result = await this.executeGetTaskStatus(args);
          break;
        case 'get_task_results':
          result = await this.executeGetTaskResults(args);
          break;
        case 'export_results_to_excel':
          result = await this.executeExportResultsToExcel(args);
          break;
        default:
          ResponseUtil.error(ctx, `未知的工具: ${tool}`, 400);
          return;
      }

      ResponseUtil.success(ctx, result, `工具 ${tool} 执行成功`);

    } catch (error) {
      console.error('执行MCP工具失败:', error);
      ResponseUtil.error(ctx, `执行MCP工具失败: ${error.message}`, 500);
    }
  }

  /**
   * 执行创建爬虫任务工具
   */
  async executeCreateCrawlerTask(args) {
    const { keywords, platform, taskName, maxPages = 5, config = {}, priority = 1 } = args;

    // 验证必填参数
    if (!keywords) throw new Error('搜索关键词不能为空');
    if (!platform) throw new Error('平台类型不能为空');
    if (!taskName) throw new Error('任务名称不能为空');

    // 平台参数映射和验证
    const platformMapping = {
      // 新的简化参数
      'jlxt': 'juxingtu',
      'xhs': 'xiaohongshu',
      // 保持向后兼容性
      'juxingtu': 'juxingtu',
      'xiaohongshu': 'xiaohongshu'
    };

    const mappedPlatform = platformMapping[platform];
    if (!mappedPlatform) {
      throw new Error('平台类型必须是 jlxt(巨量星图)、xhs(小红书)、xiaohongshu 或 juxingtu');
    }

    // 调用爬虫服务创建任务
    const crawlerService = require('../services/crawler');
    const task = await crawlerService.createTask({
      taskName,
      platform: mappedPlatform, // 使用映射后的平台名称
      keywords,
      maxPages,
      config: {
        pageSize: config.pageSize || 20,
        delay: config.delay || { min: 1000, max: 3000 },
        retries: 3,
        filters: {}
      },
      priority,
      createdBy: 1 // MCP系统用户
    });

    return {
      success: true,
      taskId: task.id,
      taskName: task.taskName,
      platform: task.platform, // 返回数据库中存储的标准平台名称
      originalPlatform: platform, // 保留用户传入的原始平台参数
      keywords: task.keywords,
      status: task.status,
      createdAt: task.createdAt,
      message: '爬虫任务创建成功，已添加到队列'
    };
  }

  /**
   * 执行获取任务状态工具
   */
  async executeGetTaskStatus(args) {
    const { taskId } = args;

    if (!taskId) throw new Error('任务ID不能为空');

    const crawlerService = require('../services/crawler');
    const task = await crawlerService.getTaskDetail(taskId);

    if (!task) {
      throw new Error(`任务不存在: ${taskId}`);
    }

    return {
      success: true,
      task: {
        id: task.id,
        taskName: task.taskName,
        platform: task.platform,
        keywords: task.keywords,
        status: task.status,
        progress: task.progress || 0,
        currentPage: task.currentPage || 0,
        maxPages: task.maxPages,
        createdAt: task.createdAt,
        startedAt: task.startedAt,
        completedAt: task.completedAt,
        resultStats: task.resultStats
      }
    };
  }

  /**
   * 执行获取任务结果工具
   */
  async executeGetTaskResults(args) {
    const { taskId, page = 1, limit = 20, status, keyword } = args;

    if (!taskId) throw new Error('任务ID不能为空');

    const { PublicInfluencer } = require('../models');
    const { Op } = require('sequelize');

    const where = { taskId };
    if (status) where.status = status;
    if (keyword) {
      where[Op.or] = [
        { nickname: { [Op.like]: `%${keyword}%` } },
        { platformUserId: { [Op.like]: `%${keyword}%` } },
        { uniqueId: { [Op.like]: `%${keyword}%` } }
      ];
    }

    const result = await PublicInfluencer.findAndCountAll({
      where,
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      attributes: ['id', 'nickname', 'platformUserId', 'followersCount', 'status', 'createdAt', 'platform']
    });

    return {
      success: true,
      results: result.rows,
      pagination: {
        total: result.count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(result.count / parseInt(limit))
      }
    };
  }

  /**
   * 执行导出Excel工具
   */
  async executeExportResultsToExcel(args) {
    const { taskId, filename, status } = args;

    if (!taskId) throw new Error('任务ID不能为空');

    // 检查任务是否存在
    const crawlerService = require('../services/crawler');
    const task = await crawlerService.getTaskDetail(taskId);
    if (!task) {
      throw new Error(`任务不存在: ${taskId}`);
    }

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const finalFilename = filename || `crawler_results_${taskId}_${timestamp}.xlsx`;

    // 导出Excel
    const excelService = require('../services/ExcelService');
    const exportResult = await excelService.exportCrawlResults(taskId, {
      filename: finalFilename,
      status
    });

    return {
      success: true,
      filename: exportResult.filename,
      filePath: exportResult.filePath,
      downloadUrl: exportResult.downloadUrl,
      recordCount: exportResult.recordCount,
      fileSize: exportResult.fileSize
    };
  }

  /**
   * 获取MCP服务状态
   * GET /api/simple-mcp/status
   */
  async getServiceStatus(ctx) {
    try {
      const status = this.mcpService.getStatus();

      ResponseUtil.success(ctx, {
        service: '简化版MCP达人爬虫服务',
        version: '1.0.0',
        status: 'running',
        ...status,
        timestamp: new Date().toISOString()
      }, 'MCP服务状态正常');

    } catch (error) {
      console.error('获取MCP服务状态失败:', error);
      ResponseUtil.error(ctx, `获取MCP服务状态失败: ${error.message}`, 500);
    }
  }
}

// 创建单例实例
const simpleMCPController = new SimpleMCPController();

module.exports = simpleMCPController;
