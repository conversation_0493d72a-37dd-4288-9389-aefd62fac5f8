/**
 * CRM字典管理控制器
 *
 * 功能说明：
 * - 处理CRM字典数据同步相关的HTTP请求
 * - 提供字典数据的查询、同步、管理等接口
 * - 支持按部署类型和字段获取字典数据
 * - 提供同步状态监控和错误处理
 *
 * 主要接口：
 * - POST /api/crm-dictionaries/sync - 同步所有字典数据
 * - POST /api/crm-dictionaries/sync/:deployId - 同步指定部署类型
 * - GET /api/crm-dictionaries - 获取字典数据列表
 * - GET /api/crm-dictionaries/stats - 获取同步统计信息
 * - DELETE /api/crm-dictionaries/cleanup - 清理过期数据
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const CrmDictionaryService = require('../services/CrmDictionaryService');
const ResponseUtil = require('../utils/response');

class CrmDictionaryController {
  /**
   * 同步所有字典数据
   * POST /api/crm-dictionaries/sync
   */
  static async syncAllDictionaries(ctx) {
    try {
      console.log('🚀 开始同步所有CRM字典数据...');
      
      const crmDictionaryService = new CrmDictionaryService();
      const result = await crmDictionaryService.syncAllDictionaries();
      
      const message = `同步完成 - 创建: ${result.summary.totalCreated}, 更新: ${result.summary.totalUpdated}, 失败: ${result.summary.totalFailed}`;
      
      ResponseUtil.success(ctx, {
        message: message,
        result: result,
        summary: result.summary
      }, '字典数据同步完成');
      
    } catch (error) {
      console.error('❌ 同步所有字典数据失败:', error.message);
      ResponseUtil.error(ctx, `同步字典数据失败: ${error.message}`, 500);
    }
  }

  /**
   * 同步指定部署类型的字典数据
   * POST /api/crm-dictionaries/sync/:deployId
   */
  static async syncDictionariesByDeployId(ctx) {
    try {
      const { deployId } = ctx.params;
      
      if (!deployId || !['customer', 'contract'].includes(deployId)) {
        return ResponseUtil.error(ctx, '无效的部署类型，必须是 customer 或 contract', 400);
      }
      
      console.log(`🚀 开始同步CRM字典数据 - 部署类型: ${deployId}`);
      
      const crmDictionaryService = new CrmDictionaryService();
      const result = await crmDictionaryService.syncDictionariesByDeployId(deployId);
      
      const message = `${deployId}字典同步完成 - 创建: ${result.created}, 更新: ${result.updated}, 失败: ${result.failed}`;
      
      ResponseUtil.success(ctx, {
        message: message,
        result: result
      }, `${deployId}字典数据同步完成`);
      
    } catch (error) {
      console.error(`❌ 同步${ctx.params.deployId}字典数据失败:`, error.message);
      ResponseUtil.error(ctx, `同步字典数据失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取字典数据列表
   * GET /api/crm-dictionaries
   */
  static async getDictionaries(ctx) {
    try {
      const { 
        deployId, 
        fieldCode, 
        activeOnly = 'true',
        page = 1,
        pageSize = 50
      } = ctx.query;
      
      if (!deployId) {
        return ResponseUtil.error(ctx, '部署类型不能为空', 400);
      }
      
      if (!['customer', 'contract'].includes(deployId)) {
        return ResponseUtil.error(ctx, '无效的部署类型，必须是 customer 或 contract', 400);
      }
      
      const crmDictionaryService = new CrmDictionaryService();
      const dictionaries = await crmDictionaryService.getDictionaries(
        deployId,
        fieldCode,
        activeOnly === 'true',
        'code'  // 这里仍然使用fieldCode，所以identifierType设为'code'
      );
      
      // 简单分页处理
      const startIndex = (parseInt(page) - 1) * parseInt(pageSize);
      const endIndex = startIndex + parseInt(pageSize);
      const paginatedData = dictionaries.slice(startIndex, endIndex);
      
      ResponseUtil.success(ctx, {
        data: paginatedData,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total: dictionaries.length,
          totalPages: Math.ceil(dictionaries.length / parseInt(pageSize))
        },
        filters: {
          deployId,
          fieldCode,
          activeOnly: activeOnly === 'true'
        }
      }, '获取字典数据成功');
      
    } catch (error) {
      console.error('❌ 获取字典数据失败:', error.message);
      ResponseUtil.error(ctx, `获取字典数据失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取字典统计信息
   * GET /api/crm-dictionaries/stats
   */
  static async getDictionaryStats(ctx) {
    try {
      console.log('📊 获取CRM字典统计信息...');
      
      const crmDictionaryService = new CrmDictionaryService();
      const stats = await crmDictionaryService.getDictionaryStats();
      
      // 按部署类型分组统计
      const groupedStats = {
        customer: [],
        contract: [],
        summary: {
          totalFields: stats.length,
          totalItems: 0,
          syncedItems: 0,
          failedItems: 0,
          activeItems: 0
        }
      };
      
      stats.forEach(stat => {
        const statData = stat.dataValues || stat;
        
        if (statData.deployId === 'customer') {
          groupedStats.customer.push(statData);
        } else if (statData.deployId === 'contract') {
          groupedStats.contract.push(statData);
        }
        
        // 累计总计
        groupedStats.summary.totalItems += parseInt(statData.totalItems) || 0;
        groupedStats.summary.syncedItems += parseInt(statData.syncedItems) || 0;
        groupedStats.summary.failedItems += parseInt(statData.failedItems) || 0;
        groupedStats.summary.activeItems += parseInt(statData.activeItems) || 0;
      });
      
      ResponseUtil.success(ctx, groupedStats, '获取字典统计信息成功');
      
    } catch (error) {
      console.error('❌ 获取字典统计信息失败:', error.message);
      ResponseUtil.error(ctx, `获取字典统计信息失败: ${error.message}`, 500);
    }
  }

  /**
   * 清理过期字典数据
   * DELETE /api/crm-dictionaries/cleanup
   */
  static async cleanupOldDictionaries(ctx) {
    try {
      const { daysOld = 30 } = ctx.request.body;
      
      console.log(`🧹 开始清理过期字典数据 - ${daysOld}天前的数据`);
      
      const crmDictionaryService = new CrmDictionaryService();
      const result = await crmDictionaryService.cleanupOldDictionaries(parseInt(daysOld));
      
      ResponseUtil.success(ctx, {
        cleanedRecords: result.cleanedRecords,
        cutoffDate: result.cutoffDate,
        daysOld: parseInt(daysOld)
      }, `清理完成，删除了 ${result.cleanedRecords} 条过期记录`);
      
    } catch (error) {
      console.error('❌ 清理过期字典数据失败:', error.message);
      ResponseUtil.error(ctx, `清理过期数据失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取字典数据（用于表单下拉选项）
   * GET /api/crm-dictionaries/options/:deployId/:fieldName
   * 注意：fieldName参数实际上是field_name值（如customer_select_1）
   */
  static async getDictionaryOptions(ctx) {
    try {
      const { deployId, fieldName } = ctx.params;

      if (!deployId || !fieldName) {
        return ResponseUtil.error(ctx, '部署类型和field_name不能为空', 400);
      }

      if (!['customer', 'contract'].includes(deployId)) {
        return ResponseUtil.error(ctx, '无效的部署类型，必须是 customer 或 contract', 400);
      }

      console.log(`🔍 CRM字典查询请求 - deployId: ${deployId}, field_name: ${fieldName}`);

      const crmDictionaryService = new CrmDictionaryService();

      // 重要：这里的fieldName实际上是field_name值，需要按field_name查询
      // 由于数据库中field_name存储的就是类似customer_select_1的值，
      // 我们需要使用'name'类型查询，但实际查询的是field_name字段
      const dictionaries = await crmDictionaryService.getDictionaries(deployId, fieldName, true, 'name');

      console.log(`📊 CRM字典查询结果 - field_name: ${fieldName}, 数据量: ${dictionaries.length}`);

      // 转换为下拉选项格式
      const options = dictionaries.map(dict => ({
        value: dict.dataValue || dict.dataId,
        label: dict.dataName,
        key: dict.dataId,
        order: dict.dataOrder
      }));

      ResponseUtil.success(ctx, {
        options: options,
        fieldInfo: {
          deployId: deployId,
          fieldName: fieldName,  // 这里是field_name值
          fieldCode: dictionaries.length > 0 ? dictionaries[0].fieldCode : '',
          displayName: dictionaries.length > 0 ? dictionaries[0].fieldName : '',  // 显示名称
          total: options.length
        }
      }, '获取字典选项成功');

    } catch (error) {
      console.error(`❌ 获取字典选项失败 - ${ctx.params.deployId}.${ctx.params.fieldName}:`, error.message);
      ResponseUtil.error(ctx, `获取字典选项失败: ${error.message}`, 500);
    }
  }

  /**
   * 测试CRM字典连接
   * GET /api/crm-dictionaries/test
   */
  static async testConnection(ctx) {
    try {
      console.log('🔍 测试CRM字典连接...');

      const crmDictionaryService = new CrmDictionaryService();

      // 首先检查CRM系统状态
      const systemStatus = crmDictionaryService.getCrmSystemStatus();
      console.log('📊 CRM系统状态:', systemStatus);

      // 测试token有效性
      if (!systemStatus.tokenValid) {
        console.log('🔄 Token无效，尝试刷新...');
        await crmDictionaryService.refreshToken();
      }

      // 测试获取客户字段列表
      const customerFields = await crmDictionaryService.getCrmFieldList('customer');
      const customerSelectFields = customerFields.filter(field => field.fieldDomType.includes('SELECT'));

      // 测试获取协议字段列表
      const contractFields = await crmDictionaryService.getCrmFieldList('contract');
      const contractSelectFields = contractFields.filter(field => field.fieldDomType.includes('SELECT'));

      ResponseUtil.success(ctx, {
        systemStatus: systemStatus,
        customer: {
          totalFields: customerFields.length,
          selectFields: customerSelectFields.length,
          fields: customerSelectFields.map(f => ({
            fieldName: f.fieldName,
            fieldCode: f.fieldCode,
            dictionaryCount: f.dicData?.datas?.length || 0
          }))
        },
        contract: {
          totalFields: contractFields.length,
          selectFields: contractSelectFields.length,
          fields: contractSelectFields.map(f => ({
            fieldName: f.fieldName,
            fieldCode: f.fieldCode,
            dictionaryCount: f.dicData?.datas?.length || 0
          }))
        }
      }, 'CRM字典连接测试成功');

    } catch (error) {
      console.error('❌ CRM字典连接测试失败:', error.message);
      ResponseUtil.error(ctx, `CRM字典连接测试失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取CRM系统状态
   * GET /api/crm-dictionaries/status
   */
  static async getSystemStatus(ctx) {
    try {
      console.log('📊 获取CRM字典系统状态...');

      const crmDictionaryService = new CrmDictionaryService();
      const status = crmDictionaryService.getCrmSystemStatus();

      ResponseUtil.success(ctx, status, '获取CRM字典系统状态成功');

    } catch (error) {
      console.error('❌ 获取CRM字典系统状态失败:', error.message);
      ResponseUtil.error(ctx, `获取系统状态失败: ${error.message}`, 500);
    }
  }

  /**
   * 手动刷新CRM访问令牌
   * POST /api/crm-dictionaries/refresh-token
   */
  static async refreshToken(ctx) {
    try {
      console.log('🔄 手动刷新CRM字典访问令牌...');

      const crmDictionaryService = new CrmDictionaryService();
      const token = await crmDictionaryService.refreshToken();

      ResponseUtil.success(ctx, {
        tokenValid: !!token,
        refreshTime: new Date().toISOString(),
        systemStatus: crmDictionaryService.getCrmSystemStatus()
      }, 'CRM字典访问令牌刷新成功');

    } catch (error) {
      console.error('❌ CRM字典访问令牌刷新失败:', error.message);
      ResponseUtil.error(ctx, `Token刷新失败: ${error.message}`, 500);
    }
  }
}

module.exports = CrmDictionaryController;
