/**
 * Excel文件生成控制器
 * 提供Excel文件生成、下载和管理的API接口
 */

const ExcelService = require('../services/ExcelService');
const ResponseUtil = require('../utils/response');

class ExcelController {
  constructor() {
    this.excelService = new ExcelService();
    this.init();
  }

  /**
   * 初始化服务
   */
  async init() {
    try {
      await this.excelService.initialize();
      this.excelService.startCleanupSchedule();
      console.log('✅ Excel服务初始化成功');
    } catch (error) {
      console.error('❌ Excel服务初始化失败:', error);
    }
  }

  /**
   * 生成Excel文件
   * POST /api/excel/generate
   */
  async generateExcel(ctx) {
    try {
      const { data, fileName, sheets, formatting = {} } = ctx.request.body;

      // 参数验证
      if (!data) {
        return ResponseUtil.error(ctx, '数据源不能为空', 400);
      }

      // 验证数据格式
      if (!Array.isArray(data) && typeof data !== 'object') {
        return ResponseUtil.error(ctx, '数据源必须是数组或对象格式', 400);
      }

      // 如果是数组，检查是否为空
      if (Array.isArray(data) && data.length === 0) {
        return ResponseUtil.error(ctx, '数据源不能为空数组', 400);
      }

      console.log('📊 开始生成Excel文件...');
      console.log(`📋 数据记录数: ${Array.isArray(data) ? data.length : 1}`);
      console.log(`📄 文件名: ${fileName || '自动生成'}`);

      // 生成Excel文件
      const result = await this.excelService.generateExcel({
        data,
        fileName,
        sheets,
        formatting
      });

      console.log(`✅ Excel文件生成成功: ${result.fileName}`);
      console.log(`📁 文件大小: ${(result.fileSize / 1024).toFixed(2)} KB`);

      ResponseUtil.success(
        ctx,
        {
          downloadUrl: result.downloadUrl,
          fileName: result.fileName,
          fileSize: result.fileSize,
          fileSizeFormatted: this.formatFileSize(result.fileSize)
        },
        'Excel文件生成成功'
      );
    } catch (error) {
      console.error('Excel文件生成失败:', error);
      ResponseUtil.error(ctx, `Excel文件生成失败: ${error.message}`, 500);
    }
  }

  /**
   * 生成多工作表Excel文件
   * POST /api/excel/generate-multi
   */
  async generateMultiSheetExcel(ctx) {
    try {
      const { sheets, fileName, formatting = {} } = ctx.request.body;

      // 参数验证
      if (!sheets || !Array.isArray(sheets) || sheets.length === 0) {
        return ResponseUtil.error(ctx, '工作表配置不能为空', 400);
      }

      // 验证每个工作表配置
      for (let i = 0; i < sheets.length; i++) {
        const sheet = sheets[i];
        if (!sheet.data || !Array.isArray(sheet.data)) {
          return ResponseUtil.error(ctx, `第${i + 1}个工作表的数据格式错误`, 400);
        }
        if (!sheet.name) {
          sheet.name = `工作表${i + 1}`;
        }
      }

      console.log('📊 开始生成多工作表Excel文件...');
      console.log(`📄 工作表数量: ${sheets.length}`);
      console.log(`📋 工作表名称: ${sheets.map(s => s.name).join(', ')}`);

      // 生成Excel文件
      const result = await this.excelService.generateExcel({
        data: null, // 多工作表模式不使用单一数据源
        fileName,
        sheets,
        formatting
      });

      console.log(`✅ 多工作表Excel文件生成成功: ${result.fileName}`);

      ResponseUtil.success(
        ctx,
        {
          downloadUrl: result.downloadUrl,
          fileName: result.fileName,
          fileSize: result.fileSize,
          fileSizeFormatted: this.formatFileSize(result.fileSize),
          sheetsCount: sheets.length
        },
        '多工作表Excel文件生成成功'
      );
    } catch (error) {
      console.error('多工作表Excel文件生成失败:', error);
      ResponseUtil.error(ctx, `多工作表Excel文件生成失败: ${error.message}`, 500);
    }
  }

  /**
   * 检查文件是否存在
   * GET /api/excel/check/:fileName
   */
  async checkFile(ctx) {
    try {
      const { fileName } = ctx.params;

      if (!fileName) {
        return ResponseUtil.error(ctx, '文件名不能为空', 400);
      }

      const exists = await this.excelService.fileExists(fileName);

      ResponseUtil.success(
        ctx,
        {
          fileName,
          exists,
          downloadUrl: exists ? `${this.excelService.baseUrl}/exports/${fileName}` : null
        },
        exists ? '文件存在' : '文件不存在'
      );
    } catch (error) {
      console.error('检查文件失败:', error);
      ResponseUtil.error(ctx, `检查文件失败: ${error.message}`, 500);
    }
  }

  /**
   * 手动清理过期文件
   * POST /api/excel/cleanup
   */
  async cleanupFiles(ctx) {
    try {
      console.log('🧹 开始手动清理过期Excel文件...');

      await this.excelService.cleanupExpiredFiles();

      console.log('✅ 过期文件清理完成');
      ResponseUtil.success(ctx, null, '过期文件清理完成');
    } catch (error) {
      console.error('清理文件失败:', error);
      ResponseUtil.error(ctx, `清理文件失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取服务状态
   * GET /api/excel/status
   */
  async getStatus(ctx) {
    try {
      const fs = require('fs').promises;
      const path = require('path');

      // 获取导出目录信息
      const exportDir = this.excelService.exportDir;
      let fileCount = 0;
      let totalSize = 0;

      try {
        const files = await fs.readdir(exportDir);
        const excelFiles = files.filter(file => file.endsWith('.xlsx'));
        fileCount = excelFiles.length;

        for (const file of excelFiles) {
          const filePath = path.join(exportDir, file);
          const stats = await fs.stat(filePath);
          totalSize += stats.size;
        }
      } catch (error) {
        console.warn('获取目录信息失败:', error.message);
      }

      ResponseUtil.success(
        ctx,
        {
          service: 'Excel文件生成服务',
          status: 'running',
          exportDirectory: exportDir,
          fileCount,
          totalSize,
          totalSizeFormatted: this.formatFileSize(totalSize),
          maxFileAge: `${this.excelService.maxFileAge / (1000 * 60 * 60)}小时`,
          baseUrl: this.excelService.baseUrl
        },
        '服务状态正常'
      );
    } catch (error) {
      console.error('获取服务状态失败:', error);
      ResponseUtil.error(ctx, `获取服务状态失败: ${error.message}`, 500);
    }
  }

  /**
   * 生成示例Excel文件（用于测试）
   * POST /api/excel/generate-sample
   */
  async generateSample(ctx) {
    try {
      // 格式化当前时间为统一格式
      const formatCurrentTime = () => {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
      };

      const sampleData = [
        {
          ID: 1,
          姓名: '张三',
          年龄: 25,
          城市: '北京',
          职业: '工程师',
          创建时间: formatCurrentTime()
        },
        {
          ID: 2,
          姓名: '李四',
          年龄: 30,
          城市: '上海',
          职业: '设计师',
          创建时间: formatCurrentTime()
        },
        {
          ID: 3,
          姓名: '王五',
          年龄: 28,
          城市: '广州',
          职业: '产品经理',
          创建时间: formatCurrentTime()
        }
      ];

      console.log('📊 生成示例Excel文件...');

      const result = await this.excelService.generateExcel({
        data: sampleData,
        fileName: 'sample_data',
        formatting: {
          headerStyle: true
        }
      });

      console.log(`✅ 示例Excel文件生成成功: ${result.fileName}`);

      ResponseUtil.success(
        ctx,
        {
          downloadUrl: result.downloadUrl,
          fileName: result.fileName,
          fileSize: result.fileSize,
          fileSizeFormatted: this.formatFileSize(result.fileSize),
          recordCount: sampleData.length
        },
        '示例Excel文件生成成功'
      );
    } catch (error) {
      console.error('生成示例文件失败:', error);
      ResponseUtil.error(ctx, `生成示例文件失败: ${error.message}`, 500);
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @returns {string} 格式化后的文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

module.exports = new ExcelController();
