/**
 * 字典管理控制器
 *
 * 功能说明：
 * - 处理字典管理相关的HTTP请求
 * - 提供字典数据的CRUD操作接口
 * - 支持按分类获取字典项
 * - 提供管理员权限控制
 *
 * 主要接口：
 * - GET /api/dictionaries/categories - 获取所有分类
 * - GET /api/dictionaries/category/:category - 按分类获取字典项
 * - GET /api/dictionaries - 获取字典列表（分页）
 * - POST /api/dictionaries - 创建字典项
 * - PUT /api/dictionaries/:id - 更新字典项
 * - DELETE /api/dictionaries/:id - 删除字典项
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const dictionaryService = require('../services/DictionaryService');
const ResponseUtil = require('../utils/response');
const ValidationUtil = require('../utils/validation');

class DictionaryController {
  /**
   * 获取所有字典分类
   * GET /api/dictionaries/categories
   */
  static async getCategories(ctx) {
    try {
      const categories = await dictionaryService.getAllCategories();
      
      ctx.body = {
        success: true,
        message: '获取字典分类成功',
        data: categories
      };
    } catch (error) {
      console.error('获取字典分类失败:', error);
      return ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 按分类获取字典项
   * GET /api/dictionaries/category/:category
   */
  static async getDictionariesByCategory(ctx) {
    try {
      const { category } = ctx.params;
      const { activeOnly = 'true' } = ctx.query;
      
      if (!category) {
        return ResponseUtil.error(ctx, '字典分类不能为空', 400);
      }

      const dictionaries = await dictionaryService.getDictionariesByCategory(
        category, 
        activeOnly === 'true'
      );
      
      ctx.body = {
        success: true,
        message: '获取字典项成功',
        data: dictionaries
      };
    } catch (error) {
      console.error('获取字典项失败:', error);
      return ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 获取字典列表（分页）
   * GET /api/dictionaries
   */
  static async getDictionaryList(ctx) {
    try {
      const {
        page = 1,
        pageSize = 20,
        category,
        status,
        keyword
      } = ctx.query;

      const result = await dictionaryService.getDictionaryList({
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        category,
        status,
        keyword
      });
      
      ctx.body = {
        success: true,
        message: '获取字典列表成功',
        data: result.list,
        pagination: {
          current: result.page,
          pageSize: result.pageSize,
          total: result.total,
          totalPages: result.totalPages
        }
      };
    } catch (error) {
      console.error('获取字典列表失败:', error);
      return ResponseUtil.error(ctx, error.message, 500);
    }
  }

  /**
   * 创建字典项
   * POST /api/dictionaries
   */
  static async createDictionary(ctx) {
    try {
      const {
        category,
        dictKey,
        dictLabel,
        dictValue,
        sortOrder,
        status,
        description
      } = ctx.request.body;

      // 验证必填字段
      const requiredErrors = ValidationUtil.validateRequired(
        { category, dictKey, dictLabel },
        ['category', 'dictKey', 'dictLabel']
      );
      if (requiredErrors) {
        return ResponseUtil.validationError(ctx, requiredErrors);
      }

      // 验证字段长度
      if (category.length > 50) {
        return ResponseUtil.error(ctx, '字典分类长度不能超过50个字符', 400);
      }
      if (dictKey.length > 100) {
        return ResponseUtil.error(ctx, '字典键值长度不能超过100个字符', 400);
      }
      if (dictLabel.length > 200) {
        return ResponseUtil.error(ctx, '字典标签长度不能超过200个字符', 400);
      }

      const dictionary = await dictionaryService.createDictionary({
        category,
        dictKey,
        dictLabel,
        dictValue,
        sortOrder,
        status,
        description
      }, ctx.state.user.id);
      
      ctx.body = {
        success: true,
        message: '字典项创建成功',
        data: dictionary
      };
    } catch (error) {
      console.error('创建字典项失败:', error);
      return ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 更新字典项
   * PUT /api/dictionaries/:id
   */
  static async updateDictionary(ctx) {
    try {
      const { id } = ctx.params;
      const updateData = ctx.request.body;

      if (!id || isNaN(parseInt(id))) {
        return ResponseUtil.error(ctx, '无效的字典项ID', 400);
      }

      // 验证字段长度
      if (updateData.category && updateData.category.length > 50) {
        return ResponseUtil.error(ctx, '字典分类长度不能超过50个字符', 400);
      }
      if (updateData.dictKey && updateData.dictKey.length > 100) {
        return ResponseUtil.error(ctx, '字典键值长度不能超过100个字符', 400);
      }
      if (updateData.dictLabel && updateData.dictLabel.length > 200) {
        return ResponseUtil.error(ctx, '字典标签长度不能超过200个字符', 400);
      }

      const dictionary = await dictionaryService.updateDictionary(
        parseInt(id),
        updateData,
        ctx.state.user.id
      );
      
      ctx.body = {
        success: true,
        message: '字典项更新成功',
        data: dictionary
      };
    } catch (error) {
      console.error('更新字典项失败:', error);
      return ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 删除字典项
   * DELETE /api/dictionaries/:id
   */
  static async deleteDictionary(ctx) {
    try {
      const { id } = ctx.params;

      if (!id || isNaN(parseInt(id))) {
        return ResponseUtil.error(ctx, '无效的字典项ID', 400);
      }

      await dictionaryService.deleteDictionary(parseInt(id));
      
      ctx.body = {
        success: true,
        message: '字典项删除成功'
      };
    } catch (error) {
      console.error('删除字典项失败:', error);
      return ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 批量创建字典项
   * POST /api/dictionaries/batch
   */
  static async batchCreateDictionaries(ctx) {
    try {
      const { dictionaries } = ctx.request.body;

      if (!Array.isArray(dictionaries) || dictionaries.length === 0) {
        return ResponseUtil.error(ctx, '字典项数组不能为空', 400);
      }

      const result = await dictionaryService.batchCreateDictionaries(
        dictionaries,
        ctx.state.user.id
      );

      ctx.body = {
        success: true,
        message: `批量创建完成：成功 ${result.success.length}，失败 ${result.failed.length}`,
        data: result
      };
    } catch (error) {
      console.error('批量创建字典项失败:', error);
      return ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 批量获取字典数据
   * GET /api/dictionaries/batch?categories=cooperation_form,cooperation_brand,rebate_status
   */
  static async getBatchDictionaries(ctx) {
    try {
      const { categories, activeOnly = 'true' } = ctx.query;

      if (!categories) {
        return ResponseUtil.error(ctx, '字典分类参数不能为空', 400);
      }

      const categoryList = categories.split(',').map(cat => cat.trim()).filter(cat => cat);

      if (categoryList.length === 0) {
        return ResponseUtil.error(ctx, '至少需要指定一个字典分类', 400);
      }

      console.log(`📦 批量获取字典数据 - 分类: ${categoryList.join(', ')}`);

      const result = {};
      const errors = [];

      // 并发获取所有分类的字典数据
      const promises = categoryList.map(async (category) => {
        try {
          const dictionaries = await dictionaryService.getDictionariesByCategory(
            category,
            activeOnly === 'true'
          );
          // 转换为标准格式
          const standardizedData = dictionaries.map(item => ({
            value: item.dictKey,
            label: item.dictLabel,
            key: item.dictKey,
            order: item.sortOrder || 0,
            source: 'local',
            category: item.category,
            description: item.description
          }));

          result[category] = standardizedData;
          console.log(`✅ 获取字典数据成功 - ${category}: ${standardizedData.length} 项`);

        } catch (error) {
          console.error(`❌ 获取字典数据失败 - ${category}:`, error.message);
          errors.push({
            category: category,
            error: error.message
          });
          result[category] = [];
        }
      });

      await Promise.all(promises);

      const totalItems = Object.values(result).reduce((sum, items) => sum + items.length, 0);

      ctx.body = {
        success: true,
        message: `批量获取字典数据完成 - ${categoryList.length} 个分类，共 ${totalItems} 项`,
        data: {
          dictionaries: result,
          summary: {
            requestedCategories: categoryList.length,
            successfulCategories: categoryList.length - errors.length,
            totalItems: totalItems,
            errors: errors
          }
        }
      };

    } catch (error) {
      console.error('❌ 批量获取字典数据失败:', error);
      return ResponseUtil.error(ctx, `批量获取字典数据失败: ${error.message}`, 500);
    }
  }
}

module.exports = DictionaryController;
