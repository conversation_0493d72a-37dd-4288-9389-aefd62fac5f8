/**
 * Dashboard控制器
 * 
 * 功能说明：
 * - 提供仪表板统计数据API
 * - 包括总体统计、今日概览、最近活动、平台分布等
 * - 支持实时数据统计和趋势分析
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const { Op, fn, col, literal } = require('sequelize');
const { MyInfluencer, PublicInfluencer, CrawlTask, CrawlLog } = require('../models');
const ResponseUtil = require('../utils/response');

class DashboardController {
  /**
   * 获取仪表板总体统计数据
   * 
   * @swagger
   * /api/dashboard/overview:
   *   get:
   *     tags: [仪表板]
   *     summary: 获取总体统计数据
   *     description: 获取达人数量、任务状态、内容统计等总体数据
   *     responses:
   *       200:
   *         description: 统计数据获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 message:
   *                   type: string
   *                 data:
   *                   type: object
   *                   properties:
   *                     totalInfluencers:
   *                       type: integer
   *                       description: 总达人数
   *                     influencerGrowth:
   *                       type: integer
   *                       description: 较昨日新增达人数
   *                     activeTasks:
   *                       type: integer
   *                       description: 活跃任务数
   *                     runningTasks:
   *                       type: integer
   *                       description: 运行中任务数
   *                     totalContent:
   *                       type: integer
   *                       description: 总采集内容数
   *                     todayContent:
   *                       type: integer
   *                       description: 今日新增内容数
   *                     successRate:
   *                       type: number
   *                       description: 任务成功率
   */
  static async getOverview(ctx) {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

      // 并行查询各项统计数据
      const [
        publicInfluencersTotal,
        myInfluencersTotal,
        newInfluencersToday,
        newInfluencersYesterday,
        crawlResultsTotal,
        crawlResultsToday,
        taskStats
      ] = await Promise.all([
        // 达人公海数量（总的可用达人数）
        PublicInfluencer.count(),

        // 我的达人数量（当前用户收藏/关注的达人数）
        MyInfluencer.count(),

        // 今日新增达人数量
        PublicInfluencer.count({
          where: {
            createdAt: {
              [Op.gte]: today
            }
          }
        }),

        // 昨日新增达人数量
        PublicInfluencer.count({
          where: {
            createdAt: {
              [Op.gte]: yesterday,
              [Op.lt]: today
            }
          }
        }),

        // 采集作品数量（已爬取的作品总数）
        PublicInfluencer.count(),

        // 今日新增采集作品数量
        PublicInfluencer.count({
          where: {
            createdAt: {
              [Op.gte]: today
            }
          }
        }),

        // 任务成功率统计
        CrawlTask.findAll({
          attributes: [
            [fn('COUNT', col('id')), 'total'],
            [fn('SUM', literal('CASE WHEN status = "completed" THEN 1 ELSE 0 END')), 'completed'],
            [fn('SUM', literal('CASE WHEN status = "failed" THEN 1 ELSE 0 END')), 'failed']
          ],
          raw: true
        })
      ]);

      // 计算成功率
      const taskTotal = parseInt(taskStats[0]?.total || 0);
      const taskCompleted = parseInt(taskStats[0]?.completed || 0);
      const successRate = taskTotal > 0 ? Math.round((taskCompleted / taskTotal) * 100) : 0;

      // 计算新增达人增长
      const newInfluencersGrowth = newInfluencersToday - newInfluencersYesterday;

      const data = {
        publicInfluencersTotal,
        myInfluencersTotal,
        newInfluencersToday,
        newInfluencersGrowth,
        crawlResultsTotal,
        crawlResultsToday,
        successRate
      };

      ResponseUtil.success(ctx, data, '统计数据获取成功');
    } catch (error) {
      console.error('获取总体统计数据失败:', error);
      ResponseUtil.error(ctx, '获取统计数据失败', 500);
    }
  }

  /**
   * 获取平台分布统计
   * 
   * @swagger
   * /api/dashboard/platform-stats:
   *   get:
   *     tags: [仪表板]
   *     summary: 获取平台分布统计
   *     description: 获取各平台的达人数量分布
   *     responses:
   *       200:
   *         description: 平台统计数据获取成功
   */
  static async getPlatformStats(ctx) {
    try {
      // 查询我的达人平台分布
      const myInfluencerStats = await MyInfluencer.findAll({
        attributes: [
          'platform',
          [fn('COUNT', col('id')), 'count']
        ],
        group: ['platform'],
        raw: true
      });

      // 查询达人公海平台分布
      const publicInfluencerStats = await PublicInfluencer.findAll({
        attributes: [
          'platform',
          [fn('COUNT', col('id')), 'count']
        ],
        group: ['platform'],
        raw: true
      });

      // 平台名称映射
      const platformNames = {
        'xiaohongshu': '小红书',
        'juxingtu': '巨量星图'
      };

      // 平台颜色映射
      const platformColors = {
        'xiaohongshu': '#ff6b6b',
        'juxingtu': '#4ecdc4'
      };

      // 处理我的达人统计
      const myStats = myInfluencerStats.map(stat => ({
        platform: stat.platform,
        name: platformNames[stat.platform] || stat.platform,
        count: parseInt(stat.count),
        color: platformColors[stat.platform],
        type: 'my'
      }));

      // 处理达人公海统计
      const publicStats = publicInfluencerStats.map(stat => ({
        platform: stat.platform,
        name: platformNames[stat.platform] || stat.platform,
        count: parseInt(stat.count),
        color: platformColors[stat.platform],
        type: 'public'
      }));

      // 计算总数和百分比
      const myTotal = myStats.reduce((sum, stat) => sum + stat.count, 0);
      const publicTotal = publicStats.reduce((sum, stat) => sum + stat.count, 0);

      myStats.forEach(stat => {
        stat.percent = myTotal > 0 ? Math.round((stat.count / myTotal) * 100) : 0;
      });

      publicStats.forEach(stat => {
        stat.percent = publicTotal > 0 ? Math.round((stat.count / publicTotal) * 100) : 0;
      });

      const data = {
        myInfluencers: myStats,
        publicInfluencers: publicStats,
        totals: {
          my: myTotal,
          public: publicTotal
        }
      };

      ResponseUtil.success(ctx, data, '平台统计数据获取成功');
    } catch (error) {
      console.error('获取平台统计数据失败:', error);
      ResponseUtil.error(ctx, '获取平台统计数据失败', 500);
    }
  }

  /**
   * 获取最近活动记录
   *
   * @swagger
   * /api/dashboard/recent-activities:
   *   get:
   *     tags: [仪表板]
   *     summary: 获取最近活动记录
   *     description: 获取系统最近的操作活动记录
   *     parameters:
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 返回记录数量限制
   *     responses:
   *       200:
   *         description: 活动记录获取成功
   */
  static async getRecentActivities(ctx) {
    try {
      const limit = parseInt(ctx.query.limit) || 10;
      const activities = [];

      // 获取最近的爬虫任务活动
      const recentTasks = await CrawlTask.findAll({
        attributes: ['id', 'taskName', 'status', 'platform', 'createdAt', 'completedAt', 'successCount'],
        order: [['updatedAt', 'DESC']],
        limit: limit * 2, // 获取更多数据以便筛选
        raw: true
      });

      // 获取最近的达人创建活动
      const recentInfluencers = await MyInfluencer.findAll({
        attributes: ['id', 'nickname', 'platform', 'createdAt'],
        order: [['createdAt', 'DESC']],
        limit: limit,
        raw: true
      });

      // 处理爬虫任务活动
      recentTasks.forEach(task => {
        const platformName = task.platform === 'xiaohongshu' ? '小红书' : '巨量星图';

        if (task.status === 'completed' && task.completedAt) {
          activities.push({
            type: 'complete',
            title: '任务完成',
            description: `${platformName}任务"${task.taskName}"执行完成，共采集 ${task.successCount || 0} 条内容`,
            time: task.completedAt,
            platform: task.platform
          });
        } else if (task.status === 'running') {
          activities.push({
            type: 'start',
            title: '启动任务',
            description: `开始执行${platformName}任务"${task.taskName}"`,
            time: task.createdAt,
            platform: task.platform
          });
        } else if (task.status === 'failed') {
          activities.push({
            type: 'error',
            title: '任务失败',
            description: `${platformName}任务"${task.taskName}"执行失败`,
            time: task.updatedAt || task.createdAt,
            platform: task.platform
          });
        }
      });

      // 处理达人创建活动
      recentInfluencers.forEach(influencer => {
        const platformName = influencer.platform === 'xiaohongshu' ? '小红书' : '巨量星图';
        activities.push({
          type: 'create',
          title: '新增达人',
          description: `添加了${platformName}达人"${influencer.nickname}"`,
          time: influencer.createdAt,
          platform: influencer.platform
        });
      });

      // 按时间排序并限制数量
      activities.sort((a, b) => new Date(b.time) - new Date(a.time));
      const limitedActivities = activities.slice(0, limit);

      // 格式化时间显示
      limitedActivities.forEach(activity => {
        const now = new Date();
        const activityTime = new Date(activity.time);
        const diffMs = now - activityTime;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMins < 1) {
          activity.timeDisplay = '刚刚';
        } else if (diffMins < 60) {
          activity.timeDisplay = `${diffMins}分钟前`;
        } else if (diffHours < 24) {
          activity.timeDisplay = `${diffHours}小时前`;
        } else if (diffDays < 7) {
          activity.timeDisplay = `${diffDays}天前`;
        } else {
          activity.timeDisplay = activityTime.toLocaleDateString();
        }
      });

      ResponseUtil.success(ctx, limitedActivities, '活动记录获取成功');
    } catch (error) {
      console.error('获取活动记录失败:', error);
      ResponseUtil.error(ctx, '获取活动记录失败', 500);
    }
  }

  /**
   * 获取达人采集数量趋势数据
   *
   * @swagger
   * /api/dashboard/influencer-trends:
   *   get:
   *     tags: [仪表板]
   *     summary: 获取达人采集数量趋势
   *     description: 获取最近7天的达人采集数量趋势数据
   *     parameters:
   *       - in: query
   *         name: days
   *         schema:
   *           type: integer
   *           default: 7
   *         description: 统计天数
   *     responses:
   *       200:
   *         description: 趋势数据获取成功
   */
  static async getInfluencerTrends(ctx) {
    try {
      const days = parseInt(ctx.query.days) || 7;
      const trends = [];

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

        // 查询当天的达人采集统计
        const [
          publicInfluencersCount,
          myInfluencersCount,
          crawlResultsCount,
          completedTasksCount
        ] = await Promise.all([
          // 达人公海新增数量
          PublicInfluencer.count({
            where: {
              createdAt: {
                [Op.gte]: startOfDay,
                [Op.lt]: endOfDay
              }
            }
          }),
          // 我的达人新增数量
          MyInfluencer.count({
            where: {
              createdAt: {
                [Op.gte]: startOfDay,
                [Op.lt]: endOfDay
              }
            }
          }),
          // 采集作品数量（爬虫结果）
          PublicInfluencer.count({
            where: {
              createdAt: {
                [Op.gte]: startOfDay,
                [Op.lt]: endOfDay
              }
            }
          }),
          // 完成的任务数量
          CrawlTask.count({
            where: {
              status: 'completed',
              completedAt: {
                [Op.gte]: startOfDay,
                [Op.lt]: endOfDay
              }
            }
          })
        ]);

        trends.push({
          date: startOfDay.toISOString().split('T')[0],
          dateDisplay: `${date.getMonth() + 1}/${date.getDate()}`,
          publicInfluencers: publicInfluencersCount,
          myInfluencers: myInfluencersCount,
          crawlResults: crawlResultsCount,
          completedTasks: completedTasksCount
        });
      }

      ResponseUtil.success(ctx, trends, '达人采集趋势数据获取成功');
    } catch (error) {
      console.error('获取达人采集趋势数据失败:', error);
      ResponseUtil.error(ctx, '获取达人采集趋势数据失败', 500);
    }
  }

  /**
   * 获取任务执行趋势数据
   *
   * @swagger
   * /api/dashboard/task-trends:
   *   get:
   *     tags: [仪表板]
   *     summary: 获取任务执行趋势
   *     description: 获取最近7天的任务执行趋势数据
   *     parameters:
   *       - in: query
   *         name: days
   *         schema:
   *           type: integer
   *           default: 7
   *         description: 统计天数
   *     responses:
   *       200:
   *         description: 趋势数据获取成功
   */
  static async getTaskTrends(ctx) {
    try {
      const days = parseInt(ctx.query.days) || 7;
      const trends = [];

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

        // 查询当天的任务统计
        const [totalTasks, completedTasks, failedTasks, runningTasks] = await Promise.all([
          CrawlTask.count({
            where: {
              createdAt: {
                [Op.gte]: startOfDay,
                [Op.lt]: endOfDay
              }
            }
          }),
          CrawlTask.count({
            where: {
              status: 'completed',
              completedAt: {
                [Op.gte]: startOfDay,
                [Op.lt]: endOfDay
              }
            }
          }),
          CrawlTask.count({
            where: {
              status: 'failed',
              updatedAt: {
                [Op.gte]: startOfDay,
                [Op.lt]: endOfDay
              }
            }
          }),
          CrawlTask.count({
            where: {
              status: 'running',
              startedAt: {
                [Op.gte]: startOfDay,
                [Op.lt]: endOfDay
              }
            }
          })
        ]);

        trends.push({
          date: startOfDay.toISOString().split('T')[0],
          dateDisplay: `${date.getMonth() + 1}/${date.getDate()}`,
          total: totalTasks,
          completed: completedTasks,
          failed: failedTasks,
          running: runningTasks,
          successRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
        });
      }

      ResponseUtil.success(ctx, trends, '趋势数据获取成功');
    } catch (error) {
      console.error('获取趋势数据失败:', error);
      ResponseUtil.error(ctx, '获取趋势数据失败', 500);
    }
  }

  /**
   * 获取综合仪表板数据
   *
   * @swagger
   * /api/dashboard:
   *   get:
   *     tags: [仪表板]
   *     summary: 获取综合仪表板数据
   *     description: 一次性获取仪表板所需的所有数据
   *     responses:
   *       200:
   *         description: 仪表板数据获取成功
   */
  static async getDashboard(ctx) {
    try {
      // 并行获取各模块数据
      const [overviewData, platformData, activitiesData, influencerTrendsData] = await Promise.all([
        // 获取总体统计数据
        DashboardController.getOverviewData(),
        // 获取平台分布数据
        DashboardController.getPlatformStatsData(),
        // 获取最近活动数据
        DashboardController.getRecentActivitiesData(5),
        // 获取达人采集趋势数据
        DashboardController.getInfluencerTrendsData(7)
      ]);

      const data = {
        overview: overviewData,
        platformStats: platformData,
        recentActivities: activitiesData,
        influencerTrends: influencerTrendsData
      };

      ResponseUtil.success(ctx, data, '仪表板数据获取成功');
    } catch (error) {
      console.error('获取仪表板数据失败:', error);
      ResponseUtil.error(ctx, '获取仪表板数据失败', 500);
    }
  }

  // 内部辅助方法，用于获取总体统计数据
  static async getOverviewData() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

    const [
      publicInfluencersTotal,
      myInfluencersTotal,
      newInfluencersToday,
      newInfluencersYesterday,
      crawlResultsTotal,
      crawlResultsToday,
      taskStats
    ] = await Promise.all([
      // 达人公海数量（总的可用达人数）
      PublicInfluencer.count(),
      // 我的达人数量（当前用户收藏/关注的达人数）
      MyInfluencer.count(),
      // 今日新增达人数量
      PublicInfluencer.count({ where: { createdAt: { [Op.gte]: today } } }),
      // 昨日新增达人数量
      PublicInfluencer.count({ where: { createdAt: { [Op.gte]: yesterday, [Op.lt]: today } } }),
      // 采集作品数量（已爬取的作品总数）
      PublicInfluencer.count(),
      // 今日新增采集作品数量
      PublicInfluencer.count({ where: { createdAt: { [Op.gte]: today } } }),
      CrawlTask.findAll({
        attributes: [
          [fn('COUNT', col('id')), 'total'],
          [fn('SUM', literal('CASE WHEN status = "completed" THEN 1 ELSE 0 END')), 'completed']
        ],
        raw: true
      })
    ]);

    const taskTotal = parseInt(taskStats[0]?.total || 0);
    const taskCompleted = parseInt(taskStats[0]?.completed || 0);
    const successRate = taskTotal > 0 ? Math.round((taskCompleted / taskTotal) * 100) : 0;
    const newInfluencersGrowth = newInfluencersToday - newInfluencersYesterday;

    return {
      publicInfluencersTotal,
      myInfluencersTotal,
      newInfluencersToday,
      newInfluencersGrowth,
      crawlResultsTotal,
      crawlResultsToday,
      successRate
    };
  }

  // 内部辅助方法，用于获取平台分布数据
  static async getPlatformStatsData() {
    const myInfluencerStats = await MyInfluencer.findAll({
      attributes: ['platform', [fn('COUNT', col('id')), 'count']],
      group: ['platform'],
      raw: true
    });

    const platformNames = { 'xiaohongshu': '小红书', 'juxingtu': '巨量星图' };
    const platformColors = { 'xiaohongshu': '#ff6b6b', 'juxingtu': '#4ecdc4' };

    const stats = myInfluencerStats.map(stat => ({
      platform: stat.platform,
      name: platformNames[stat.platform] || stat.platform,
      count: parseInt(stat.count),
      color: platformColors[stat.platform]
    }));

    const total = stats.reduce((sum, stat) => sum + stat.count, 0);
    stats.forEach(stat => {
      stat.percent = total > 0 ? Math.round((stat.count / total) * 100) : 0;
    });

    return stats;
  }

  // 内部辅助方法，用于获取最近活动数据
  static async getRecentActivitiesData(limit = 5) {
    const activities = [];

    const recentTasks = await CrawlTask.findAll({
      attributes: ['taskName', 'status', 'platform', 'createdAt', 'completedAt', 'successCount'],
      order: [['updatedAt', 'DESC']],
      limit: limit * 2,
      raw: true
    });

    const recentInfluencers = await MyInfluencer.findAll({
      attributes: ['nickname', 'platform', 'createdAt'],
      order: [['createdAt', 'DESC']],
      limit: limit,
      raw: true
    });

    // 处理任务活动
    recentTasks.forEach(task => {
      const platformName = task.platform === 'xiaohongshu' ? '小红书' : '巨量星图';
      if (task.status === 'completed' && task.completedAt) {
        activities.push({
          type: 'complete',
          title: '任务完成',
          description: `${platformName}任务"${task.taskName}"执行完成，共采集 ${task.successCount || 0} 条内容`,
          time: task.completedAt
        });
      }
    });

    // 处理达人活动
    recentInfluencers.forEach(influencer => {
      const platformName = influencer.platform === 'xiaohongshu' ? '小红书' : '巨量星图';
      activities.push({
        type: 'create',
        title: '新增达人',
        description: `添加了${platformName}达人"${influencer.nickname}"`,
        time: influencer.createdAt
      });
    });

    // 排序并格式化时间
    activities.sort((a, b) => new Date(b.time) - new Date(a.time));
    return activities.slice(0, limit).map(activity => {
      const now = new Date();
      const activityTime = new Date(activity.time);
      const diffMins = Math.floor((now - activityTime) / (1000 * 60));

      if (diffMins < 1) activity.timeDisplay = '刚刚';
      else if (diffMins < 60) activity.timeDisplay = `${diffMins}分钟前`;
      else if (diffMins < 1440) activity.timeDisplay = `${Math.floor(diffMins / 60)}小时前`;
      else activity.timeDisplay = `${Math.floor(diffMins / 1440)}天前`;

      return activity;
    });
  }

  // 内部辅助方法，用于获取达人采集趋势数据
  static async getInfluencerTrendsData(days = 7) {
    const trends = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const [
        publicInfluencersCount,
        myInfluencersCount,
        crawlResultsCount,
        completedTasksCount
      ] = await Promise.all([
        PublicInfluencer.count({ where: { createdAt: { [Op.gte]: startOfDay, [Op.lt]: endOfDay } } }),
        MyInfluencer.count({ where: { createdAt: { [Op.gte]: startOfDay, [Op.lt]: endOfDay } } }),
        PublicInfluencer.count({ where: { createdAt: { [Op.gte]: startOfDay, [Op.lt]: endOfDay } } }),
        CrawlTask.count({ where: { status: 'completed', completedAt: { [Op.gte]: startOfDay, [Op.lt]: endOfDay } } })
      ]);

      trends.push({
        date: startOfDay.toISOString().split('T')[0],
        dateDisplay: `${date.getMonth() + 1}/${date.getDate()}`,
        publicInfluencers: publicInfluencersCount,
        myInfluencers: myInfluencersCount,
        crawlResults: crawlResultsCount,
        completedTasks: completedTasksCount
      });
    }

    return trends;
  }

  // 内部辅助方法，用于获取趋势数据
  static async getTaskTrendsData(days = 7) {
    const trends = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const [totalTasks, completedTasks] = await Promise.all([
        CrawlTask.count({ where: { createdAt: { [Op.gte]: startOfDay, [Op.lt]: endOfDay } } }),
        CrawlTask.count({ where: { status: 'completed', completedAt: { [Op.gte]: startOfDay, [Op.lt]: endOfDay } } })
      ]);

      trends.push({
        date: startOfDay.toISOString().split('T')[0],
        dateDisplay: `${date.getMonth() + 1}/${date.getDate()}`,
        total: totalTasks,
        completed: completedTasks,
        successRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
      });
    }

    return trends;
  }
}

module.exports = DashboardController;
