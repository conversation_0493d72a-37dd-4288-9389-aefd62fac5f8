const { PublicInfluencer, CrawlTask } = require('../models');
const { Op } = require('sequelize');
const ResponseUtil = require('../utils/response');

/**
 * @swagger
 * components:
 *   schemas:
 *     PublicInfluencer:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 达人公海ID
 *         taskId:
 *           type: integer
 *           description: 关联的爬虫任务ID
 *         platform:
 *           type: string
 *           enum: [xiaohongshu, juxingtu]
 *           description: 平台类型
 *         platformUserId:
 *           type: string
 *           description: 平台用户ID
 *         nickname:
 *           type: string
 *           description: 达人昵称
 *         avatarUrl:
 *           type: string
 *           description: 头像链接
 *         followersCount:
 *           type: integer
 *           description: 粉丝数量
 *         city:
 *           type: string
 *           description: 城市
 *         uniqueId:
 *           type: string
 *           description: 唯一标识
 *         contactInfo:
 *           type: object
 *           description: 联系方式
 *         videoStats:
 *           type: object
 *           description: 视频统计数据
 *         authorExtInfo:
 *           type: object
 *           description: 达人扩展信息
 *         status:
 *           type: string
 *           enum: [pending, processed, imported, failed]
 *           description: 处理状态
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 */

/**
 * @swagger
 * /api/public-influencers:
 *   get:
 *     summary: 获取达人公海列表
 *     tags: [达人公海]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: platform
 *         schema:
 *           type: string
 *           enum: [xiaohongshu, juxingtu]
 *         description: 平台筛选
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processed, imported, failed]
 *         description: 状态筛选
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词（昵称、城市）
 *       - in: query
 *         name: crawlTaskId
 *         schema:
 *           type: integer
 *         description: 爬虫任务ID筛选
 *     responses:
 *       200:
 *         description: 成功获取达人公海列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/PublicInfluencer'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 */
async function getPublicInfluencers(ctx) {
  try {
    const {
      page = 1,
      limit = 20,
      platform,
      status,
      search,
      keyword,
      platformUserId,
      minFollowers,
      maxFollowers,
      crawlTaskId
    } = ctx.query;

    const offset = (page - 1) * limit;
    const where = {};

    // 平台筛选
    if (platform) {
      where.platform = platform;
    }

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 星图ID精确查询
    if (platformUserId) {
      where.platformUserId = platformUserId.trim();
    }

    // 爬虫任务ID精确查询
    if (crawlTaskId) {
      where.taskId = parseInt(crawlTaskId);
    }

    // 搜索条件 - 支持 search 和 keyword 参数
    const searchTerm = search || keyword;
    if (searchTerm) {
      const likePattern = `%${searchTerm}%`;
      where[Op.or] = [
        { nickname: { [Op.like]: likePattern } },
        { city: { [Op.like]: likePattern } },
        { uniqueId: { [Op.like]: likePattern } }
      ];
    }

    // 粉丝数范围筛选
    if (minFollowers || maxFollowers) {
      where.followersCount = {};
      if (minFollowers) {
        where.followersCount[Op.gte] = parseInt(minFollowers);
      }
      if (maxFollowers) {
        where.followersCount[Op.lte] = parseInt(maxFollowers);
      }
    }

    const { count, rows } = await PublicInfluencer.findAndCountAll({
      where,
      attributes: {
        exclude: ['rawData', 'errorMessage'] // 排除敏感字段
      },
      include: [
        {
          model: CrawlTask,
          as: 'crawlTask',
          attributes: ['id', 'taskName', 'keywords', 'platform']
        }
      ],
      order: [['updatedAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    ResponseUtil.sequelizePagination(ctx, { count, rows }, { page, limit }, '获取达人公海列表成功');
  } catch (error) {
    console.error('获取达人公海列表失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '获取达人公海列表失败',
      error: error.message
    };
  }
}

/**
 * @swagger
 * /api/public-influencers/{id}:
 *   get:
 *     summary: 获取达人公海详情
 *     tags: [达人公海]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 达人公海ID
 *     responses:
 *       200:
 *         description: 成功获取达人公海详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/PublicInfluencer'
 *       404:
 *         description: 达人公海不存在
 */
async function getPublicInfluencerById(ctx) {
  try {
    const { id } = ctx.params;

    const publicInfluencer = await PublicInfluencer.findByPk(id, {
      include: [
        {
          model: CrawlTask,
          as: 'crawlTask',
          attributes: ['id', 'taskName', 'keywords', 'platform', 'createdAt']
        }
      ]
    });

    if (!publicInfluencer) {
      ctx.status = 404;
      ctx.body = {
        success: false,
        message: '达人公海记录不存在'
      };
      return;
    }

    ctx.body = {
      success: true,
      data: publicInfluencer
    };
  } catch (error) {
    console.error('获取达人公海详情失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '获取达人公海详情失败',
      error: error.message
    };
  }
}

/**
 * @swagger
 * /api/public-influencers/{id}/import:
 *   post:
 *     summary: 将达人公海数据收藏到我的达人
 *     tags: [达人公海]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 达人公海ID
 *     responses:
 *       200:
 *         description: 成功收藏到我的达人
 *       404:
 *         description: 达人公海不存在
 *       409:
 *         description: 达人已存在于我的达人中
 */
async function importToMyInfluencers(ctx) {
  try {
    const { id } = ctx.params;
    const userId = ctx.state.user.id;

    console.log(`🔄 开始收藏达人公海数据: ${id}, 用户: ${userId}`);

    // 验证ID
    if (!id || isNaN(parseInt(id))) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        message: '无效的达人公海ID'
      };
      return;
    }

    // 查找达人公海记录
    const publicInfluencer = await PublicInfluencer.findByPk(parseInt(id));
    if (!publicInfluencer) {
      ctx.status = 404;
      ctx.body = {
        success: false,
        message: '达人公海记录不存在'
      };
      return;
    }

    // 检查是否已经收藏过
    if (publicInfluencer.status === 'imported') {
      ctx.status = 409;
      ctx.body = {
        success: false,
        message: '该达人已经收藏过了',
        data: {
          importedInfluencerId: publicInfluencer.importedInfluencerId
        }
      };
      return;
    }

    const { MyInfluencer } = require('../models');

    // 检查是否已存在相同的达人
    const existingInfluencer = await MyInfluencer.findOne({
      where: {
        platform: publicInfluencer.platform,
        platformId: publicInfluencer.platformUserId
      }
    });

    if (existingInfluencer) {
      // 更新公海记录状态
      await publicInfluencer.update({
        status: 'imported',
        importedInfluencerId: existingInfluencer.id
      });

      ctx.status = 409;
      ctx.body = {
        success: false,
        message: '该达人已存在于我的达人中',
        data: {
          existingInfluencerId: existingInfluencer.id,
          nickname: existingInfluencer.nickname
        }
      };
      return;
    }

    // 创建新的达人记录
    const newInfluencer = await MyInfluencer.create({
      nickname: publicInfluencer.nickname,
      platform: publicInfluencer.platform,
      platformId: publicInfluencer.platformUserId,
      avatarUrl: publicInfluencer.avatarUrl,
      followersCount: publicInfluencer.followersCount,
      contactInfo: publicInfluencer.contactInfo,
      videoStats: publicInfluencer.videoStats,
      authorExtInfo: publicInfluencer.authorExtInfo,
      contentTheme: publicInfluencer.contentTheme,
      influencerTags: publicInfluencer.influencerTags,
      playMid: publicInfluencer.playMid,
      status: 'active',
      createdBy: userId
    });

    // 更新公海记录状态
    await publicInfluencer.update({
      status: 'imported',
      importedInfluencerId: newInfluencer.id
    });

    console.log(`✅ 达人收藏成功: ${publicInfluencer.nickname} -> ${newInfluencer.id}`);

    ctx.body = {
      success: true,
      message: '达人收藏成功',
      data: {
        influencerId: newInfluencer.id,
        nickname: newInfluencer.nickname,
        platform: newInfluencer.platform,
        followersCount: newInfluencer.followersCount
      }
    };
  } catch (error) {
    console.error('收藏达人失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '收藏达人失败',
      error: error.message
    };
  }
}

/**
 * @swagger
 * /api/public-influencers/export:
 *   get:
 *     summary: 导出达人公海数据到Excel
 *     tags: [达人公海]
 *     parameters:
 *       - in: query
 *         name: platform
 *         schema:
 *           type: string
 *           enum: [xiaohongshu, juxingtu]
 *         description: 平台筛选
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processed, imported, failed]
 *         description: 状态筛选
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 关键词搜索（达人昵称）
 *       - in: query
 *         name: platformUserId
 *         schema:
 *           type: string
 *         description: 星图ID筛选
 *       - in: query
 *         name: minFollowers
 *         schema:
 *           type: integer
 *         description: 最小粉丝数
 *       - in: query
 *         name: maxFollowers
 *         schema:
 *           type: integer
 *         description: 最大粉丝数
 *       - in: query
 *         name: crawlTaskId
 *         schema:
 *           type: integer
 *         description: 爬虫任务ID筛选
 *     responses:
 *       200:
 *         description: Excel文件下载链接
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     downloadUrl:
 *                       type: string
 *                     fileName:
 *                       type: string
 *                     recordCount:
 *                       type: integer
 */
async function exportPublicInfluencers(ctx) {
  try {
    const {
      platform,
      status,
      keyword,
      platformUserId,
      minFollowers,
      maxFollowers,
      crawlTaskId
    } = ctx.query;

    console.log('🔄 开始导出达人公海数据，筛选条件:', ctx.query);

    const where = {};

    // 构建查询条件（与getPublicInfluencers相同的逻辑）
    if (platform) {
      where.platform = platform;
    }
    if (status) {
      where.status = status;
    }
    if (platformUserId) {
      where.platformUserId = platformUserId.trim();
    }
    if (crawlTaskId) {
      where.taskId = parseInt(crawlTaskId);
    }
    if (keyword) {
      where[Op.or] = [
        { nickname: { [Op.like]: `%${keyword}%` } },
        { city: { [Op.like]: `%${keyword}%` } },
        { uniqueId: { [Op.like]: `%${keyword}%` } }
      ];
    }
    if (minFollowers || maxFollowers) {
      where.followersCount = {};
      if (minFollowers) {
        where.followersCount[Op.gte] = parseInt(minFollowers);
      }
      if (maxFollowers) {
        where.followersCount[Op.lte] = parseInt(maxFollowers);
      }
    }

    // 获取所有符合条件的达人公海数据
    const publicInfluencers = await PublicInfluencer.findAll({
      where,
      attributes: PublicInfluencer.SAFE_ATTRIBUTES, // 排除敏感字段
      include: [
        {
          model: CrawlTask,
          as: 'crawlTask',
          attributes: ['id', 'taskName', 'keywords', 'platform']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    if (publicInfluencers.length === 0) {
      ctx.status = 404;
      ctx.body = {
        success: false,
        message: '没有找到符合条件的达人数据'
      };
      return;
    }

    console.log(`📊 找到 ${publicInfluencers.length} 条达人数据，开始生成Excel文件`);

    // 使用ExcelService生成Excel文件
    const ExcelService = require('../services/ExcelService');
    const excelService = new ExcelService();
    await excelService.initialize();

    // 准备Excel数据
    const excelData = publicInfluencers.map(influencer => {
      const authorExtInfo = influencer.authorExtInfo || {};
      const contactInfo = influencer.contactInfo || {};

      return {
        'ID': influencer.id,
        '达人昵称': influencer.nickname || '',
        '平台': influencer.platform === 'xiaohongshu' ? '小红书' : '巨量星图',
        '星图/小红书ID': influencer.platformUserId || '',
        '粉丝数': influencer.followersCount || 0,
        '城市': influencer.city || '',
        '联系方式': formatContactInfo(contactInfo),
        '达人标签': influencer.influencerTags || authorExtInfo.tags_relation,
        '内容主题': influencer.contentTheme || authorExtInfo.content_theme_labels_180d || '',
        '20～60s报价': formatPrice(authorExtInfo.price_20_60),
        '状态': getStatusText(influencer.status),
        '创建时间': influencer.createdAt ? new Date(influencer.createdAt).toLocaleString('zh-CN') : '',
        '任务名称': influencer.crawlTask?.taskName || ''
      };
    });

    // 生成Excel文件
    const result = await excelService.generateExcel({
      data: excelData,
      fileName: '达人公海数据',
      formatting: {
        headerStyle: true
      }
    });

    console.log('✅ Excel文件生成成功:', result.fileName);

    // 返回下载链接
    ctx.body = {
      success: true,
      message: '导出Excel成功',
      data: {
        downloadUrl: result.downloadUrl,
        fileName: result.fileName,
        recordCount: publicInfluencers.length
      }
    };

  } catch (error) {
    console.error('导出达人公海Excel失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '导出Excel失败',
      error: error.message
    };
  }
}

/**
 * @swagger
 * /api/public-influencers/batch-import:
 *   post:
 *     summary: 批量收藏达人公海数据到我的达人
 *     tags: [达人公海]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 达人公海ID数组
 *     responses:
 *       200:
 *         description: 批量收藏结果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       description: 总数量
 *                     successCount:
 *                       type: integer
 *                       description: 成功数量
 *                     failedCount:
 *                       type: integer
 *                       description: 失败数量
 *                     skippedCount:
 *                       type: integer
 *                       description: 跳过数量
 *                     errors:
 *                       type: array
 *                       description: 错误列表
 */
async function batchImportToMyInfluencers(ctx) {
  try {
    const { ids } = ctx.request.body;
    const userId = ctx.state.user.id;

    console.log(`🔄 开始批量收藏达人公海数据: ${ids?.length || 0} 个, 用户: ${userId}`);

    // 验证参数
    if (!Array.isArray(ids) || ids.length === 0) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        message: '请选择要导入的达人'
      };
      return;
    }

    // 验证ID格式
    const numericIds = ids.map(id => parseInt(id)).filter(id => !isNaN(id));
    if (numericIds.length === 0) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        message: '无效的达人公海ID'
      };
      return;
    }

    // 查找所有匹配的记录
    const publicInfluencers = await PublicInfluencer.findAll({
      where: {
        id: { [Op.in]: numericIds }
      }
    });

    if (publicInfluencers.length === 0) {
      ctx.status = 404;
      ctx.body = {
        success: false,
        message: '未找到匹配的达人公海记录'
      };
      return;
    }

    const { MyInfluencer } = require('../models');
    const results = {
      total: publicInfluencers.length,
      successCount: 0,
      failedCount: 0,
      skippedCount: 0,
      errors: [],
      imported: []
    };

    // 逐个处理导入
    for (const publicInfluencer of publicInfluencers) {
      try {
        // 检查是否已经收藏过
        if (publicInfluencer.status === 'imported') {
          results.skippedCount++;
          results.errors.push({
            id: publicInfluencer.id,
            nickname: publicInfluencer.nickname,
            error: '该达人已经收藏过了'
          });
          continue;
        }

        // 检查是否已存在相同的达人
        const existingInfluencer = await MyInfluencer.findOne({
          where: {
            platform: publicInfluencer.platform,
            platformId: publicInfluencer.platformUserId
          }
        });

        if (existingInfluencer) {
          // 更新公海记录状态
          await publicInfluencer.update({
            status: 'imported',
            importedInfluencerId: existingInfluencer.id
          });

          results.skippedCount++;
          results.errors.push({
            id: publicInfluencer.id,
            nickname: publicInfluencer.nickname,
            error: `该达人已存在于我的达人中 (ID: ${existingInfluencer.id})`
          });
          continue;
        }

        // 创建新的达人记录
        const newInfluencer = await MyInfluencer.create({
          nickname: publicInfluencer.nickname,
          platform: publicInfluencer.platform,
          platformId: publicInfluencer.platformUserId,
          avatarUrl: publicInfluencer.avatarUrl,
          followersCount: publicInfluencer.followersCount,
          contactInfo: publicInfluencer.contactInfo,
          videoStats: publicInfluencer.videoStats,
          authorExtInfo: publicInfluencer.authorExtInfo,
          contentTheme: publicInfluencer.contentTheme,
          influencerTags: publicInfluencer.influencerTags,
          playMid: publicInfluencer.playMid,
          status: 'active',
          createdBy: userId
        });

        // 更新公海记录状态
        await publicInfluencer.update({
          status: 'imported',
          importedInfluencerId: newInfluencer.id
        });

        results.successCount++;
        results.imported.push({
          publicInfluencerId: publicInfluencer.id,
          influencerId: newInfluencer.id,
          nickname: newInfluencer.nickname,
          platform: newInfluencer.platform
        });

        console.log(`✅ 达人收藏成功: ${publicInfluencer.nickname} -> ${newInfluencer.id}`);

      } catch (error) {
        results.failedCount++;
        results.errors.push({
          id: publicInfluencer.id,
          nickname: publicInfluencer.nickname,
          error: error.message
        });
        console.error(`❌ 收藏达人失败: ${publicInfluencer.nickname}`, error);
      }
    }

    console.log(`🎉 批量收藏完成: 成功 ${results.successCount}, 失败 ${results.failedCount}, 跳过 ${results.skippedCount}`);

    ctx.body = {
      success: true,
      message: `批量收藏完成: 成功 ${results.successCount} 个，失败 ${results.failedCount} 个，跳过 ${results.skippedCount} 个`,
      data: results
    };
  } catch (error) {
    console.error('批量收藏达人失败:', error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: '批量收藏达人失败',
      error: error.message
    };
  }
}

// 辅助函数：格式化联系方式
function formatContactInfo(contactInfo) {
  if (!contactInfo || typeof contactInfo !== 'object') {
    return '';
  }

  const parts = [];
  if (contactInfo.phone) parts.push(`电话: ${contactInfo.phone}`);
  if (contactInfo.email) parts.push(`邮箱: ${contactInfo.email}`);
  if (contactInfo.wechat) parts.push(`微信: ${contactInfo.wechat}`);
  if (contactInfo.qq) parts.push(`QQ: ${contactInfo.qq}`);

  return parts.join('; ');
}

// 辅助函数：格式化价格
function formatPrice(price) {
  if (!price) return '';
  if (typeof price === 'number') {
    return `¥${price.toLocaleString()}`;
  }
  if (typeof price === 'string') {
    return price;
  }
  return '';
}

// 辅助函数：获取状态文本
function getStatusText(status) {
  const statusMap = {
    'pending': '待处理',
    'processed': '已处理',
    'imported': '已导入',
    'failed': '失败'
  };
  return statusMap[status] || status;
}

module.exports = {
  getPublicInfluencers,
  getPublicInfluencerById,
  importToMyInfluencers,
  batchImportToMyInfluencers,
  exportPublicInfluencers
};
