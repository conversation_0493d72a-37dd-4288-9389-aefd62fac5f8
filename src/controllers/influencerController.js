const { MyInfluencer, User, AuthorVideo } = require('../models');
const { Op } = require('sequelize');
const ResponseUtil = require('../utils/response');
const ValidationUtil = require('../utils/validation');
const ExcelUtil = require('../utils/excel');
const AuthorVideoService = require('../services/AuthorVideoService');

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateInfluencerRequest:
 *       type: object
 *       required:
 *         - nickname
 *         - platform
 *       properties:
 *         nickname:
 *           type: string
 *           description: 达人昵称
 *           example: 美妆达人小红
 *         platform:
 *           type: string
 *           enum: [xiaohongshu, juxingtu]
 *           description: 平台类型
 *           example: xiaohongshu
 *         platformId:
 *           type: string
 *           description: 平台ID
 *           example: user123456
 *         avatarUrl:
 *           type: string
 *           format: uri
 *           description: 头像链接
 *           example: https://example.com/avatar.jpg
 *         followersCount:
 *           type: integer
 *           minimum: 0
 *           description: 粉丝数量
 *           example: 50000
 *         category:
 *           type: string
 *           description: 分类
 *           example: 美妆
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: 标签
 *           example: ["美妆", "护肤", "彩妆"]
 *         contactInfo:
 *           type: object
 *           description: 联系方式
 *           example:
 *             wechat: xiaohong123
 *             email: <EMAIL>
 *             phone: "13800138000"
 *         priceInfo:
 *           type: object
 *           description: 报价信息
 *           example:
 *             note: "5000-8000"
 *             video: "8000-12000"
 *             live: "10000-15000"
 *         cooperationHistory:
 *           type: object
 *           description: 合作历史
 *           example:
 *             brands: ["品牌A", "品牌B"]
 *             projects: 5
 *             lastCooperation: "2023-12-01"
 *         notes:
 *           type: string
 *           description: 备注
 *           example: 合作态度好，效果不错
 *     UpdateInfluencerRequest:
 *       type: object
 *       properties:
 *         nickname:
 *           type: string
 *           description: 达人昵称
 *         platform:
 *           type: string
 *           enum: [xiaohongshu, juxingtu]
 *           description: 平台类型
 *         platformId:
 *           type: string
 *           description: 平台ID
 *         avatarUrl:
 *           type: string
 *           format: uri
 *           description: 头像链接
 *         followersCount:
 *           type: integer
 *           minimum: 0
 *           description: 粉丝数量
 *         category:
 *           type: string
 *           description: 分类
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: 标签
 *         contactInfo:
 *           type: object
 *           description: 联系方式
 *         priceInfo:
 *           type: object
 *           description: 报价信息
 *         cooperationHistory:
 *           type: object
 *           description: 合作历史
 *         notes:
 *           type: string
 *           description: 备注
 *         status:
 *           type: string
 *           enum: [active, inactive]
 *           description: 状态
 */

class InfluencerController {
  /**
   * @swagger
   * /influencers:
   *   get:
   *     tags: [达人管理]
   *     summary: 获取达人列表
   *     description: 分页获取达人列表，支持多种筛选条件和排序
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: platform
   *         schema:
   *           type: string
   *           enum: [xiaohongshu, juxingtu]
   *         description: 平台筛选
   *       - in: query
   *         name: category
   *         schema:
   *           type: string
   *         description: 分类筛选
   *       - in: query
   *         name: keyword
   *         schema:
   *           type: string
   *         description: 昵称关键词搜索
   *       - in: query
   *         name: minFollowers
   *         schema:
   *           type: integer
   *           minimum: 0
   *         description: 最小粉丝数
   *       - in: query
   *         name: maxFollowers
   *         schema:
   *           type: integer
   *           minimum: 0
   *         description: 最大粉丝数
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           enum: [createdAt, updatedAt, followersCount, nickname]
   *           default: createdAt
   *         description: 排序字段
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [ASC, DESC]
   *           default: DESC
   *         description: 排序方向
   *     responses:
   *       200:
   *         description: 获取达人列表成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/PaginatedResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: array
   *                       items:
   *                         allOf:
   *                           - $ref: '#/components/schemas/Influencer'
   *                           - type: object
   *                             properties:
   *                               creator:
   *                                 type: object
   *                                 properties:
   *                                   id:
   *                                     type: integer
   *                                   username:
   *                                     type: string
   *             example:
   *               success: true
   *               message: 获取达人列表成功
   *               data:
   *                 - id: 1
   *                   nickname: 美妆达人小红
   *                   platform: xiaohongshu
   *                   platformId: user123456
   *                   avatarUrl: https://example.com/avatar.jpg
   *                   followersCount: 50000
   *                   category: 美妆
   *                   tags: ["美妆", "护肤"]
   *                   status: active
   *                   creator:
   *                     id: 1
   *                     username: admin
   *                   createdAt: "2024-01-01T00:00:00.000Z"
   *                   updatedAt: "2024-01-01T00:00:00.000Z"
   *               pagination:
   *                 page: 1
   *                 limit: 10
   *                 total: 100
   *                 pages: 10
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getInfluencers(ctx) {
    try {
      const {
        page = 1,
        limit = 10,
        platform,
        category,
        keyword,
        platformId,
        minFollowers,
        maxFollowers,
        sortBy = 'updatedAt',
        sortOrder = 'DESC'
      } = ctx.query;

      const offset = (page - 1) * limit;
      const where = {};

      // 构建查询条件
      if (platform) {
        where.platform = platform;
      }
      if (category) {
        where.category = category;
      }
      if (keyword) {
        where.nickname = {
          [Op.like]: `%${keyword}%`
        };
      }
      if (platformId) {
        where.platformId = platformId.trim();
      }
      if (minFollowers || maxFollowers) {
        where.followersCount = {};
        if (minFollowers) {
          where.followersCount[Op.gte] = parseInt(minFollowers);
        }
        if (maxFollowers) {
          where.followersCount[Op.lte] = parseInt(maxFollowers);
        }
      }

      const { count, rows } = await MyInfluencer.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username']
          }
        ],
        order: [[sortBy, sortOrder.toUpperCase()]],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const pagination = {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      };

      ResponseUtil.successWithPagination(ctx, rows, pagination, '获取达人列表成功');
    } catch (error) {
      console.error('获取达人列表失败:', error);
      ResponseUtil.error(ctx, '获取达人列表失败', 500);
    }
  }

  /**
   * @swagger
   * /influencers/{id}:
   *   get:
   *     tags: [达人管理]
   *     summary: 获取达人详情
   *     description: 根据ID获取达人的详细信息
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 达人ID
   *         example: 1
   *     responses:
   *       200:
   *         description: 获取达人详情成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       allOf:
   *                         - $ref: '#/components/schemas/Influencer'
   *                         - type: object
   *                           properties:
   *                             creator:
   *                               type: object
   *                               properties:
   *                                 id:
   *                                   type: integer
   *                                 username:
   *                                   type: string
   *             example:
   *               success: true
   *               message: 获取达人详情成功
   *               data:
   *                 id: 1
   *                 nickname: 美妆达人小红
   *                 platform: xiaohongshu
   *                 platformId: user123456
   *                 avatarUrl: https://example.com/avatar.jpg
   *                 followersCount: 50000
   *                 category: 美妆
   *                 tags: ["美妆", "护肤", "彩妆"]
   *                 contactInfo:
   *                   wechat: xiaohong123
   *                   email: <EMAIL>
   *                 priceInfo:
   *                   note: "5000-8000"
   *                   video: "8000-12000"
   *                 cooperationHistory:
   *                   brands: ["品牌A", "品牌B"]
   *                   projects: 5
   *                 notes: 合作态度好，效果不错
   *                 status: active
   *                 creator:
   *                   id: 1
   *                   username: admin
   *                 createdAt: "2024-01-01T00:00:00.000Z"
   *                 updatedAt: "2024-01-01T00:00:00.000Z"
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getInfluencer(ctx) {
    try {
      const { id } = ctx.params;

      const influencer = await MyInfluencer.findByPk(id, {
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username']
          }
        ]
      });

      if (!influencer) {
        return ResponseUtil.notFound(ctx, '达人不存在');
      }

      ResponseUtil.success(ctx, influencer, '获取达人详情成功');
    } catch (error) {
      console.error('获取达人详情失败:', error);
      ResponseUtil.error(ctx, '获取达人详情失败', 500);
    }
  }

  /**
   * @swagger
   * /influencers/{id}/detail:
   *   get:
   *     tags: [达人管理]
   *     summary: 获取达人详细信息面板
   *     description: 获取达人的完整详细信息，包括基础资料、扩展信息解析、视频数据统计等
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 达人ID
   *     responses:
   *       200:
   *         description: 获取达人详细信息成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         basicInfo:
   *                           $ref: '#/components/schemas/Influencer'
   *                         extendedInfo:
   *                           type: object
   *                           description: 解析后的扩展信息
   *                         videoStats:
   *                           type: object
   *                           properties:
   *                             totalVideos:
   *                               type: integer
   *                               description: 总视频数
   *                             totalPlays:
   *                               type: integer
   *                               description: 总播放量
   *                             totalLikes:
   *                               type: integer
   *                               description: 总点赞数
   *                             totalComments:
   *                               type: integer
   *                               description: 总评论数
   *                             totalShares:
   *                               type: integer
   *                               description: 总分享数
   *                             avgPlays:
   *                               type: integer
   *                               description: 平均播放量
   *                             maxPlays:
   *                               type: integer
   *                               description: 最高播放量
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async getInfluencerDetail(ctx) {
    try {
      const { id } = ctx.params;

      // 获取达人基础信息
      const influencer = await MyInfluencer.findByPk(id, {
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username']
          }
        ]
      });

      if (!influencer) {
        return ResponseUtil.notFound(ctx, '达人不存在');
      }

      // 解析扩展信息
      let extendedInfo = {};
      if (influencer.authorExtInfo) {
        try {
          extendedInfo =
            typeof influencer.authorExtInfo === 'string'
              ? JSON.parse(influencer.authorExtInfo)
              : influencer.authorExtInfo;
        } catch (error) {
          console.warn('解析达人扩展信息失败:', error.message);
          extendedInfo = { error: '扩展信息格式异常' };
        }
      }

      // 获取视频数据统计
      let videoStats = {
        totalVideos: 0,
        totalPlays: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0,
        avgPlays: 0,
        maxPlays: 0
      };

      try {
        videoStats = await AuthorVideoService.getVideoStats(id);
      } catch (error) {
        console.warn('获取视频统计数据失败:', error.message);
      }

      // 构建响应数据
      const detailData = {
        basicInfo: influencer,
        extendedInfo,
        videoStats
      };

      ResponseUtil.success(ctx, detailData, '获取达人详细信息成功');
    } catch (error) {
      console.error('获取达人详细信息失败:', error);
      ResponseUtil.error(ctx, '获取达人详细信息失败', 500);
    }
  }

  /**
   * @swagger
   * /influencers:
   *   post:
   *     tags: [达人管理]
   *     summary: 创建达人
   *     description: 创建新的达人信息记录
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateInfluencerRequest'
   *           examples:
   *             基础信息:
   *               value:
   *                 nickname: 美妆达人小红
   *                 platform: xiaohongshu
   *                 platformId: user123456
   *                 followersCount: 50000
   *                 category: 美妆
   *             完整信息:
   *               value:
   *                 nickname: 美妆达人小红
   *                 platform: xiaohongshu
   *                 platformId: user123456
   *                 avatarUrl: https://example.com/avatar.jpg
   *                 followersCount: 50000
   *                 category: 美妆
   *                 tags: ["美妆", "护肤", "彩妆"]
   *                 contactInfo:
   *                   wechat: xiaohong123
   *                   email: <EMAIL>
   *                   phone: "13800138000"
   *                 priceInfo:
   *                   note: "5000-8000"
   *                   video: "8000-12000"
   *                   live: "10000-15000"
   *                 cooperationHistory:
   *                   brands: ["品牌A", "品牌B"]
   *                   projects: 5
   *                   lastCooperation: "2023-12-01"
   *                 notes: 合作态度好，效果不错
   *     responses:
   *       200:
   *         description: 创建达人成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Influencer'
   *             example:
   *               success: true
   *               message: 创建达人成功
   *               data:
   *                 id: 1
   *                 nickname: 美妆达人小红
   *                 platform: xiaohongshu
   *                 platformId: user123456
   *                 avatarUrl: https://example.com/avatar.jpg
   *                 followersCount: 50000
   *                 category: 美妆
   *                 tags: ["美妆", "护肤", "彩妆"]
   *                 status: active
   *                 createdBy: 1
   *                 createdAt: "2024-01-01T00:00:00.000Z"
   *                 updatedAt: "2024-01-01T00:00:00.000Z"
   *       400:
   *         description: 参数验证失败或平台类型无效
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             examples:
   *               参数验证失败:
   *                 value:
   *                   success: false
   *                   message: 参数验证失败
   *                   error: 昵称不能为空
   *               平台类型无效:
   *                 value:
   *                   success: false
   *                   message: 平台类型无效
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       409:
   *         description: 该平台上的达人已存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               message: 该平台上的达人已存在
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
  static async createInfluencer(ctx) {
    try {
      const {
        nickname,
        platform,
        platformId,
        avatarUrl,
        followersCount = 0,
        category,
        tags,
        contactInfo,
        priceInfo,
        cooperationHistory,
        playMid,
        notes
      } = ctx.request.body;

      // 验证必填字段
      const requiredErrors = ValidationUtil.validateRequired({ nickname, platform }, ['nickname', 'platform']);
      if (requiredErrors) {
        return ResponseUtil.validationError(ctx, requiredErrors);
      }

      // 验证平台类型
      const { PLATFORM_CONSTANTS } = require('../config/constants');
      if (!PLATFORM_CONSTANTS.ALL_PLATFORMS.includes(platform)) {
        return ResponseUtil.error(ctx, '平台类型无效', 400);
      }

      // 检查是否已存在相同平台的达人
      if (platformId) {
        const existingInfluencer = await MyInfluencer.findOne({
          where: {
            platform,
            platformId
          }
        });

        if (existingInfluencer) {
          return ResponseUtil.error(ctx, '该平台上的达人已存在', 409);
        }
      }

      const influencer = await MyInfluencer.create({
        nickname,
        platform,
        platformId,
        avatarUrl,
        followersCount: parseInt(followersCount) || 0,
        category,
        tags,
        contactInfo,
        priceInfo,
        cooperationHistory,
        playMid,
        notes,
        createdBy: ctx.state.user.id
      });

      ResponseUtil.success(ctx, influencer, '创建达人成功');
    } catch (error) {
      console.error('创建达人失败:', error);
      ResponseUtil.error(ctx, '创建达人失败', 500);
    }
  }

  // 更新达人
  static async updateInfluencer(ctx) {
    try {
      const { id } = ctx.params;
      const updateData = ctx.request.body;

      const influencer = await MyInfluencer.findByPk(id);
      if (!influencer) {
        return ResponseUtil.notFound(ctx, '达人不存在');
      }

      // 验证平台类型
      if (updateData.platform && !['xiaohongshu', 'juxingtu'].includes(updateData.platform)) {
        return ResponseUtil.error(ctx, '平台类型无效', 400);
      }

      // 检查平台ID冲突
      if (updateData.platformId && updateData.platform) {
        const existingInfluencer = await MyInfluencer.findOne({
          where: {
            platform: updateData.platform,
            platformId: updateData.platformId,
            id: { [Op.ne]: id }
          }
        });

        if (existingInfluencer) {
          return ResponseUtil.error(ctx, '该平台上的达人已存在', 409);
        }
      }

      await influencer.update(updateData);

      ResponseUtil.success(ctx, influencer, '更新达人成功');
    } catch (error) {
      console.error('更新达人失败:', error);
      ResponseUtil.error(ctx, '更新达人失败', 500);
    }
  }

  // 删除达人
  static async deleteInfluencer(ctx) {
    try {
      const { id } = ctx.params;
      const { PublicInfluencer } = require('../models');

      const influencer = await MyInfluencer.findByPk(id);
      if (!influencer) {
        return ResponseUtil.notFound(ctx, '达人不存在');
      }

      // 检查是否有关联的爬虫结果
      const relatedResults = await PublicInfluencer.findAll({
        where: {
          importedInfluencerId: id
        }
      });

      // 如果有关联的爬虫结果，先清除关联关系
      if (relatedResults.length > 0) {
        await PublicInfluencer.update(
          { importedInfluencerId: null, status: 'processed' },
          {
            where: {
              importedInfluencerId: id
            }
          }
        );
        console.log(`清除了 ${relatedResults.length} 个爬虫结果的关联关系`);
      }

      // 删除达人记录
      await influencer.destroy();

      ResponseUtil.success(ctx, null, '删除达人成功');
    } catch (error) {
      console.error('删除达人失败:', error);
      ResponseUtil.error(ctx, '删除达人失败', 500);
    }
  }

  // 批量删除达人
  static async batchDeleteInfluencers(ctx) {
    try {
      const { ids } = ctx.request.body;
      const { PublicInfluencer } = require('../models');

      if (!Array.isArray(ids) || ids.length === 0) {
        return ResponseUtil.error(ctx, '请提供要删除的达人ID列表', 400);
      }

      // 检查是否有关联的爬虫结果
      const relatedResults = await PublicInfluencer.findAll({
        where: {
          importedInfluencerId: {
            [Op.in]: ids
          }
        },
        attributes: ['id', 'importedInfluencerId'] // 只查询必要字段，排除敏感字段
      });

      // 如果有关联的爬虫结果，先清除关联关系
      if (relatedResults.length > 0) {
        await PublicInfluencer.update(
          { importedInfluencerId: null, status: 'processed' },
          {
            where: {
              importedInfluencerId: {
                [Op.in]: ids
              }
            }
          }
        );
        console.log(`清除了 ${relatedResults.length} 个爬虫结果的关联关系`);
      }

      // 批量删除达人记录
      const deletedCount = await MyInfluencer.destroy({
        where: {
          id: {
            [Op.in]: ids
          }
        }
      });

      ResponseUtil.success(ctx, { deletedCount }, `成功删除 ${deletedCount} 个达人`);
    } catch (error) {
      console.error('批量删除达人失败:', error);
      ResponseUtil.error(ctx, '批量删除达人失败', 500);
    }
  }

  // 获取达人统计信息
  static async getInfluencerStats(ctx) {
    try {
      const totalCount = await MyInfluencer.count();

      const platformStats = await MyInfluencer.findAll({
        attributes: ['platform', [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count']],
        group: ['platform']
      });

      const categoryStats = await MyInfluencer.findAll({
        attributes: ['category', [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count']],
        where: {
          category: {
            [Op.ne]: null
          }
        },
        group: ['category'],
        order: [[require('sequelize').fn('COUNT', require('sequelize').col('id')), 'DESC']],
        limit: 10
      });

      ResponseUtil.success(
        ctx,
        {
          totalCount,
          platformStats,
          categoryStats
        },
        '获取统计信息成功'
      );
    } catch (error) {
      console.error('获取统计信息失败:', error);
      ResponseUtil.error(ctx, '获取统计信息失败', 500);
    }
  }

  // 导出达人信息到Excel
  static async exportInfluencers(ctx) {
    try {
      const { platform, category, keyword, platformId, minFollowers, maxFollowers } = ctx.query;

      const where = {};

      // 构建查询条件（与getInfluencers相同的逻辑）
      if (platform) {
        where.platform = platform;
      }
      if (category) {
        where.category = category;
      }
      if (keyword) {
        where.nickname = {
          [Op.like]: `%${keyword}%`
        };
      }
      if (platformId) {
        where.platformId = platformId.trim();
      }
      if (minFollowers || maxFollowers) {
        where.followersCount = {};
        if (minFollowers) {
          where.followersCount[Op.gte] = parseInt(minFollowers);
        }
        if (maxFollowers) {
          where.followersCount[Op.lte] = parseInt(maxFollowers);
        }
      }

      // 获取所有符合条件的达人数据
      const influencers = await MyInfluencer.findAll({
        where,
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username']
          }
        ],
        order: [['createdAt', 'DESC']]
      });

      if (influencers.length === 0) {
        return ResponseUtil.error(ctx, '没有找到符合条件的达人数据', 404);
      }

      // 生成Excel文件
      const excelBuffer = ExcelUtil.exportInfluencers(influencers);
      const fileName = ExcelUtil.generateFileName('达人信息');

      // 设置响应头
      ctx.set({
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${encodeURIComponent(fileName)}"`,
        'Content-Length': excelBuffer.length
      });

      ctx.body = excelBuffer;
    } catch (error) {
      console.error('导出Excel失败:', error);
      ResponseUtil.error(ctx, '导出Excel失败', 500);
    }
  }
}

module.exports = InfluencerController;
