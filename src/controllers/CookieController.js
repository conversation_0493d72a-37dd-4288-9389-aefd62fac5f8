/**
 * Cookie管理控制器
 *
 * 功能说明：
 * - 提供Cookie的增删改查操作
 * - 支持Cookie的验证和状态管理
 * - 实现Cookie的轮换和使用统计
 * - 提供Cookie的导入导出功能
 *
 * 主要接口：
 * - GET /api/cookies - 获取Cookie列表（支持分页和筛选）
 * - GET /api/cookies/:id - 获取单个Cookie详情
 * - POST /api/cookies - 添加新Cookie
 * - PUT /api/cookies/:id - 更新Cookie信息
 * - DELETE /api/cookies/:id - 删除Cookie
 * - POST /api/cookies/:id/validate - 验证Cookie有效性
 * - GET /api/cookies/stats - 获取Cookie统计信息
 * - GET /api/cookies/export - 导出Cookie列表
 *
 * 安全说明：
 * - 当前运行在测试模式，Cookie数据以明文存储
 * - 生产环境需要启用加密功能保证数据安全
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const CookieManager = require('../services/CookieManager');
const CrawlerCookie = require('../models/CrawlerCookie');
const User = require('../models/User');
const { Op } = require('sequelize');
const ResponseUtil = require('../utils/response');
const DateFormatter = require('../utils/dateFormatter');

// 创建Cookie管理器实例
const cookieManager = new CookieManager();

class CookieController {
  /**
   * 获取Cookie列表
   *
   * 功能说明：
   * - 支持分页查询Cookie列表
   * - 支持按平台、状态、账户名筛选
   * - 返回脱敏后的Cookie信息（测试模式显示前缀）
   * - 包含创建者信息和分页数据
   *
   * 查询参数：
   * @param {number} page - 页码，默认1
   * @param {number} limit - 每页数量，默认10
   * @param {string} platform - 平台筛选（可选）
   * @param {string} status - 状态筛选（可选）
   * @param {string} accountName - 账户名模糊搜索（可选）
   *
   * 响应格式：
   * @returns {Object} 响应对象
   * @returns {boolean} success - 操作是否成功
   * @returns {string} message - 响应消息
   * @returns {Array} data - Cookie列表数据
   * @returns {Object} pagination - 分页信息
   *
   * 使用示例：
   * GET /api/cookies?page=1&limit=10&platform=xiaohongshu&status=active
   */
  static async getCookies(ctx) {
    try {
      const {
        page = 1,
        limit = 10,
        platform,
        status,
        accountName
      } = ctx.query;

      const offset = (page - 1) * limit;
      const where = {};

      if (platform) where.platform = platform;
      if (status) where.status = status;
      if (accountName) {
        where.accountName = {
          [Op.like]: `%${accountName}%`
        };
      }

      const { count, rows } = await CrawlerCookie.findAndCountAll({
        where,
        include: [{
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }],
        order: [['priority', 'DESC'], ['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset),
        // attributes: {
        //   exclude: ['cookieData'] // 不返回敏感的Cookie数据
        // }
      });

      // ⚠️ 测试阶段：显示更多Cookie信息用于调试
      // 🔒 生产环境需要恢复完全脱敏处理
      const sanitizedRows = rows.map(cookie => {
        const data = cookie.toJSON();

        // 测试阶段：显示Cookie的前20个字符用于识别
        if (data.cookieData) {
          const cookieStr = data.cookieData.toString();
          data.cookiePreview = `${cookieStr.substring(0, 20)}... [测试模式-明文前缀]`;
        } else {
          data.cookiePreview = '未设置';
        }

        delete data.cookieData; // 仍然不返回完整Cookie数据
        return data;
      });

      const formattedData = DateFormatter.formatArray(sanitizedRows);

      ResponseUtil.successWithPagination(
        ctx,
        formattedData,
        {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        },
        '获取Cookie列表成功'
      );
    } catch (error) {
      console.error('获取Cookie列表失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '获取Cookie列表失败',
        error: error.message
      };
    }
  }

  /**
   * 获取单个Cookie详情
   *
   * 功能说明：
   * - 根据ID获取单个Cookie的详细信息
   * - 包含创建者信息
   * - 测试模式下显示Cookie数据前缀
   *
   * 路径参数：
   * @param {number} id - Cookie的唯一标识符
   *
   * 响应格式：
   * @returns {Object} 响应对象
   * @returns {boolean} success - 操作是否成功
   * @returns {string} message - 响应消息
   * @returns {Object} data - Cookie详细信息
   *
   * 错误情况：
   * - 404: Cookie不存在
   * - 500: 服务器内部错误
   *
   * 使用示例：
   * GET /api/cookies/123
   */
  static async getCookieById(ctx) {
    try {
      const { id } = ctx.params;

      const cookie = await CrawlerCookie.findByPk(id, {
        include: [{
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }]
      });

      if (!cookie) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          message: 'Cookie不存在'
        };
        return;
      }

      const data = cookie.toJSON();

      // 明文模式：返回完整的Cookie数据用于编辑
      if (data.cookieData) {
        data.cookieValue = data.cookieData; // 前端编辑需要的字段
        data.cookiePreview = `${data.cookieData.substring(0, 20)}... [明文模式]`;
      } else {
        data.cookieValue = '';
        data.cookiePreview = '未设置';
      }

      // 确保返回所有编辑需要的字段
      const responseData = {
        id: data.id,
        accountName: data.accountName,
        platform: data.platform,
        cookieData: data.cookieData,
        cookieValue: data.cookieValue,
        cookiePreview: data.cookiePreview,
        userAgent: data.userAgent,
        priority: data.priority,
        maxDailyUse: data.maxDailyUse,
        notes: data.notes,
        status: data.status,
        useCount: data.useCount,
        dailyUseCount: data.dailyUseCount,
        lastUsedAt: data.lastUsedAt,
        expiresAt: data.expiresAt,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        creator: data.creator
      };

      ctx.body = {
        success: true,
        message: '获取Cookie详情成功',
        data: responseData
      };
    } catch (error) {
      console.error('获取Cookie详情失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '获取Cookie详情失败',
        error: error.message
      };
    }
  }

  /**
   * 添加Cookie
   *
   * 功能说明：
   * - 添加新的Cookie到系统中
   * - 验证必填字段和数据格式
   * - 检查账户名和平台的唯一性
   * - 自动设置创建者信息
   *
   * 请求体参数：
   * @param {string} accountName - 账户名称（必填）
   * @param {string} platform - 平台类型（必填，如：xiaohongshu, juxingtu）
   * @param {string} cookieString - Cookie字符串（必填）
   * @param {string} userAgent - 用户代理字符串（可选）
   * @param {number} priority - 优先级，默认1
   * @param {number} maxDailyUse - 每日最大使用次数，默认100
   * @param {string} notes - 备注信息（可选）
   *
   * 响应格式：
   * @returns {Object} 响应对象
   * @returns {boolean} success - 操作是否成功
   * @returns {string} message - 响应消息
   * @returns {Object} data - 创建的Cookie基本信息
   *
   * 错误情况：
   * - 400: 必填字段缺失或账户已存在
   * - 500: 服务器内部错误
   *
   * 使用示例：
   * POST /api/cookies
   * {
   *   "accountName": "test_account",
   *   "platform": "xiaohongshu",
   *   "cookieString": "session_id=abc123...",
   *   "userAgent": "Mozilla/5.0...",
   *   "priority": 2,
   *   "maxDailyUse": 50,
   *   "notes": "测试账户"
   * }
   */
  static async addCookie(ctx) {
    try {
      const {
        accountName,
        platform,
        cookieString,
        userAgent,
        priority = 1,
        maxDailyUse = 100,
        notes
      } = ctx.request.body;

      // 验证必填字段
      if (!accountName || !platform || !cookieString) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '账户名、平台和Cookie数据为必填项'
        };
        return;
      }

      const cookie = await cookieManager.addCookie({
        accountName,
        platform,
        cookieString,
        userAgent,
        priority,
        maxDailyUse,
        notes,
        createdBy: ctx.state.user.id
      });

      ctx.body = {
        success: true,
        message: 'Cookie添加成功',
        data: {
          id: cookie.id,
          accountName: cookie.accountName,
          platform: cookie.platform,
          status: cookie.status,
          priority: cookie.priority
        }
      };
    } catch (error) {
      console.error('添加Cookie失败:', error);
      ctx.status = 400;
      ctx.body = {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 更新Cookie
   */
  static async updateCookie(ctx) {
    try {
      const { id } = ctx.params;
      const updateData = ctx.request.body;

      const cookie = await CrawlerCookie.findByPk(id);
      if (!cookie) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          message: 'Cookie不存在'
        };
        return;
      }

      // 明文模式：直接存储Cookie数据
      if (updateData.cookieString) {
        updateData.cookieData = cookieManager.encryptCookie(updateData.cookieString);
        delete updateData.cookieString;
        console.log(`🔧 [明文模式] Cookie数据已更新为明文存储`);
      }

      await cookie.update(updateData);

      ctx.body = {
        success: true,
        message: 'Cookie更新成功',
        data: {
          id: cookie.id,
          accountName: cookie.accountName,
          platform: cookie.platform,
          status: cookie.status
        }
      };
    } catch (error) {
      console.error('更新Cookie失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '更新Cookie失败',
        error: error.message
      };
    }
  }

  /**
   * 删除Cookie
   */
  static async deleteCookie(ctx) {
    try {
      const { id } = ctx.params;

      const cookie = await CrawlerCookie.findByPk(id);
      if (!cookie) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          message: 'Cookie不存在'
        };
        return;
      }

      await cookie.destroy();

      ctx.body = {
        success: true,
        message: 'Cookie删除成功'
      };
    } catch (error) {
      console.error('删除Cookie失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '删除Cookie失败',
        error: error.message
      };
    }
  }

  /**
   * 批量导入Cookie
   */
  static async batchImportCookies(ctx) {
    try {
      const { cookies } = ctx.request.body;

      if (!Array.isArray(cookies) || cookies.length === 0) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: 'Cookie数据格式错误或为空'
        };
        return;
      }

      const results = await cookieManager.batchImportCookies(cookies, ctx.state.user.id);

      ctx.body = {
        success: true,
        message: '批量导入完成',
        data: results
      };
    } catch (error) {
      console.error('批量导入Cookie失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '批量导入失败',
        error: error.message
      };
    }
  }

  /**
   * 验证Cookie
   */
  static async validateCookie(ctx) {
    try {
      const { id } = ctx.params;

      const cookie = await CrawlerCookie.findByPk(id);
      if (!cookie) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          message: 'Cookie不存在'
        };
        return;
      }

      // 简单的验证函数（实际使用时应该调用对应平台的验证接口）
      const validationFunction = async (cookieString, userAgent) => {
        // 这里应该实现具体的验证逻辑
        // 例如：发送一个测试请求到平台API
        return {
          isValid: true,
          message: '验证成功'
        };
      };

      const isValid = await cookieManager.validateCookie(id, validationFunction);

      ctx.body = {
        success: true,
        message: 'Cookie验证完成',
        data: {
          isValid,
          cookieId: id,
          accountName: cookie.accountName
        }
      };
    } catch (error) {
      console.error('验证Cookie失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '验证Cookie失败',
        error: error.message
      };
    }
  }

  /**
   * 获取Cookie统计信息
   */
  static async getCookieStats(ctx) {
    try {
      const { platform } = ctx.query;

      const stats = await cookieManager.getCookieStats(platform);

      ctx.body = {
        success: true,
        message: '获取统计信息成功',
        data: stats
      };
    } catch (error) {
      console.error('获取Cookie统计失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '获取统计信息失败',
        error: error.message
      };
    }
  }

  /**
   * 导出Cookie（脱敏）
   */
  static async exportCookies(ctx) {
    try {
      const { platform } = ctx.query;
      const where = platform ? { platform } : {};

      const cookies = await CrawlerCookie.findAll({
        where,
        attributes: [
          'id', 'accountName', 'platform', 'status', 
          'priority', 'useCount', 'maxDailyUse', 
          'lastUsedAt', 'notes', 'createdAt'
        ],
        include: [{
          model: User,
          as: 'creator',
          attributes: ['username']
        }],
        order: [['priority', 'DESC'], ['created_at', 'DESC']]
      });

      ctx.body = {
        success: true,
        message: '导出Cookie列表成功',
        data: cookies
      };
    } catch (error) {
      console.error('导出Cookie失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '导出Cookie失败',
        error: error.message
      };
    }
  }
}

module.exports = CookieController;
