/**
 * 模块总入口文件
 * 
 * 功能说明：
 * - 统一导出所有功能模块
 * - 提供模块化的应用架构
 * - 便于模块间的依赖管理和调用
 * 
 * 模块列表：
 * - auth: 用户认证模块
 * - crawler: 爬虫管理模块
 * - cookie: <PERSON>ie管理模块
 * - influencer: 达人管理模块
 * 
 * 使用示例：
 * const { auth, crawler } = require('./modules');
 * app.use(auth.routes.routes());
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const auth = require('./auth');
const crawler = require('./crawler');
const cookie = require('./cookie');
const influencer = require('./influencer');

module.exports = {
  auth,
  crawler,
  cookie,
  influencer
};
