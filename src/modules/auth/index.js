/**
 * 认证模块入口文件
 * 
 * 功能说明：
 * - 统一导出认证模块的所有组件
 * - 包含控制器、服务、中间件、路由等
 * - 提供模块化的认证功能封装
 * 
 * 模块结构：
 * - controller: 认证控制器
 * - service: 认证服务
 * - middleware: 认证中间件
 * - routes: 认证路由
 * - validators: 认证验证器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const authController = require('../../controllers/authController');
const authMiddleware = require('../../middleware/auth');
const authRoutes = require('../../routes/auth');

module.exports = {
  controller: authController,
  middleware: authMiddleware,
  routes: authRoutes
};
