/**
 * 达人管理模块入口文件
 * 
 * 功能说明：
 * - 统一导出达人管理模块的所有组件
 * - 包含控制器、服务、模型、路由等
 * - 提供完整的达人管理功能封装
 * 
 * 模块结构：
 * - controller: 达人控制器
 * - model: 达人模型
 * - routes: 达人路由
 * - services: 达人相关服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const influencerController = require('../../controllers/influencerController');
const influencerRoutes = require('../../routes/influencers');
const MyInfluencer = require('../../models/MyInfluencer');

module.exports = {
  controller: influencerController,
  routes: influencerRoutes,
  model: MyInfluencer
};
