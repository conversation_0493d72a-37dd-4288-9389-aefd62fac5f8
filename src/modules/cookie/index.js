/**
 * Cookie管理模块入口文件
 * 
 * 功能说明：
 * - 统一导出Cookie管理模块的所有组件
 * - 包含控制器、服务、模型、路由等
 * - 提供完整的Cookie管理功能封装
 * 
 * 模块结构：
 * - controller: Cookie控制器
 * - service: Cookie管理服务
 * - model: Cookie模型
 * - routes: Cookie路由
 * - manager: Cook<PERSON>管理器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const cookieController = require('../../controllers/CookieController');
const cookieManager = require('../../services/CookieManager');
const cookieRoutes = require('../../routes/cookies');
const CrawlerCookie = require('../../models/CrawlerCookie');

module.exports = {
  controller: cookieController,
  manager: cookieManager,
  routes: cookieRoutes,
  model: CrawlerCookie
};
