/**
 * 爬虫模块入口文件
 * 
 * 功能说明：
 * - 统一导出爬虫模块的所有组件
 * - 包含控制器、服务、模型、路由等
 * - 提供完整的爬虫功能封装
 * 
 * 模块结构：
 * - controller: 爬虫控制器
 * - service: 爬虫服务
 * - models: 爬虫相关模型
 * - routes: 爬虫路由
 * - crawlers: 具体爬虫实现
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const crawlerController = require('../../controllers/CrawlerController');
const crawlerService = require('../../services/crawler');
const crawlerRoutes = require('../../routes/crawler');

// 爬虫相关模型
const CrawlTask = require('../../models/CrawlTask');
const PublicInfluencer = require('../../models/PublicInfluencer');
const CrawlLog = require('../../models/CrawlLog');

module.exports = {
  controller: crawlerController,
  service: crawlerService,
  routes: crawlerRoutes,
  models: {
    CrawlTask,
    PublicInfluencer,
    CrawlLog
  }
};
