/**
 * 环境变量配置管理
 *
 * 功能说明：
 * - 统一管理所有环境变量的读取和默认值
 * - 提供类型转换和验证功能
 * - 支持开发、测试、生产环境的配置切换
 * - 确保必要的环境变量存在
 *
 * 使用方式：
 * const config = require('./config/env');
 * console.log(config.database.host);
 *
 * <AUTHOR>
 * @version 1.0.0
 */

// 根据环境加载对应的配置文件
const path = require('path');
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';
require('dotenv').config({ path: envFile, override: true });

/**
 * 获取环境变量值，支持类型转换和默认值
 * @param {string} key - 环境变量名
 * @param {any} defaultValue - 默认值
 * @param {string} type - 数据类型 (string|number|boolean)
 * @returns {any} 环境变量值
 */
function getEnvValue(key, defaultValue, type = 'string') {
  const value = process.env[key];

  if (value === undefined || value === '') {
    return defaultValue;
  }

  switch (type) {
    case 'number':
      const num = Number(value);
      return isNaN(num) ? defaultValue : num;
    case 'boolean':
      return value.toLowerCase() === 'true';
    case 'array':
      return value.split(',').map(item => item.trim());
    default:
      return value;
  }
}

/**
 * 验证必需的环境变量
 * @param {Array} requiredVars - 必需的环境变量列表
 */
function validateRequiredEnvVars(requiredVars) {
  const missing = requiredVars.filter(varName => !process.env[varName]);

  if (missing.length > 0) {
    console.error('❌ 缺少必需的环境变量:', missing.join(', '));
    console.error('请检查 .env 文件或环境变量配置');
    process.exit(1);
  }
}

// 环境配置
const env = {
  // 基础环境信息
  NODE_ENV: getEnvValue('NODE_ENV', 'development'),
  PORT: getEnvValue('PORT', 3001, 'number'),
  HOST: getEnvValue('HOST', 'localhost'),

  // 数据库配置
  database: {
    host: getEnvValue('DB_HOST', 'localhost'),
    port: getEnvValue('DB_PORT', 3306, 'number'),
    name: getEnvValue('DB_NAME', 'daren_db'),
    username: getEnvValue('DB_USER', 'root'),
    password: getEnvValue('DB_PASSWORD', ''),
    dialect: getEnvValue('DB_DIALECT', 'mysql'),
    timezone: getEnvValue('DB_TIMEZONE', '+08:00'),
    logging: getEnvValue('DB_LOGGING', 'false', 'boolean'),

    // 连接池配置
    pool: {
      max: getEnvValue('DB_POOL_MAX', 10, 'number'),
      min: getEnvValue('DB_POOL_MIN', 0, 'number'),
      acquire: getEnvValue('DB_POOL_ACQUIRE', 30000, 'number'),
      idle: getEnvValue('DB_POOL_IDLE', 10000, 'number')
    }
  },

  // JWT配置
  jwt: {
    secret: getEnvValue('JWT_SECRET', 'your-super-secret-jwt-key'),
    expiresIn: getEnvValue('JWT_EXPIRES_IN', '7d'),
    issuer: getEnvValue('JWT_ISSUER', 'daren-system'),
    audience: getEnvValue('JWT_AUDIENCE', 'daren-users')
  },

  // Cookie管理配置
  cookie: {
    encryptionKey: getEnvValue('COOKIE_ENCRYPTION_KEY', 'your-secret-key-32-chars-long!!'),
    testMode: getEnvValue('COOKIE_TEST_MODE', 'true', 'boolean'),
    maxDailyUse: getEnvValue('COOKIE_MAX_DAILY_USE', 100, 'number'),
    cooldownMinutes: getEnvValue('COOKIE_COOLDOWN_MINUTES', 5, 'number'),
    validationInterval: getEnvValue('COOKIE_VALIDATION_INTERVAL', 60, 'number')
  },

  // 爬虫配置
  crawler: {
    maxConcurrent: getEnvValue('CRAWLER_MAX_CONCURRENT', 3, 'number'),
    defaultTimeout: getEnvValue('CRAWLER_DEFAULT_TIMEOUT', 30000, 'number'),
    retryTimes: getEnvValue('CRAWLER_RETRY_TIMES', 3, 'number'),
    retryDelay: getEnvValue('CRAWLER_RETRY_DELAY', 5000, 'number'),
    userAgent: getEnvValue('CRAWLER_USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
  },

  // Redis配置（如果使用）
  redis: {
    host: getEnvValue('REDIS_HOST', 'localhost'),
    port: getEnvValue('REDIS_PORT', 6379, 'number'),
    password: getEnvValue('REDIS_PASSWORD', ''),
    db: getEnvValue('REDIS_DB', 0, 'number'),
    keyPrefix: getEnvValue('REDIS_KEY_PREFIX', 'daren:')
  },

  // 日志配置
  logging: {
    level: getEnvValue('LOG_LEVEL', 'info'),
    file: getEnvValue('LOG_FILE', 'logs/app.log'),
    maxSize: getEnvValue('LOG_MAX_SIZE', '10m'),
    maxFiles: getEnvValue('LOG_MAX_FILES', 5, 'number'),
    datePattern: getEnvValue('LOG_DATE_PATTERN', 'YYYY-MM-DD')
  },

  // 文件上传配置
  upload: {
    path: getEnvValue('UPLOAD_PATH', 'uploads/'),
    maxSize: getEnvValue('UPLOAD_MAX_SIZE', 10485760, 'number'), // 10MB
    allowedTypes: getEnvValue('UPLOAD_ALLOWED_TYPES', 'image/jpeg,image/png,image/gif', 'array')
  },

  // API限制配置
  rateLimit: {
    windowMs: getEnvValue('RATE_LIMIT_WINDOW_MS', 3600000, 'number'), // 1小时
    max: getEnvValue('RATE_LIMIT_MAX', 1000, 'number'),
    message: getEnvValue('RATE_LIMIT_MESSAGE', 'Too many requests'),
    standardHeaders: getEnvValue('RATE_LIMIT_STANDARD_HEADERS', 'true', 'boolean'),
    legacyHeaders: getEnvValue('RATE_LIMIT_LEGACY_HEADERS', 'false', 'boolean')
  },

  // CORS配置
  cors: {
    origin: getEnvValue('CORS_ORIGIN', '*'),
    credentials: getEnvValue('CORS_CREDENTIALS', 'true', 'boolean'),
    methods: getEnvValue('CORS_METHODS', 'GET,HEAD,PUT,PATCH,POST,DELETE', 'array'),
    allowedHeaders: getEnvValue('CORS_ALLOWED_HEADERS', 'Content-Type,Authorization', 'array')
  },

  // 邮件配置（如果需要）
  email: {
    host: getEnvValue('EMAIL_HOST', 'smtp.gmail.com'),
    port: getEnvValue('EMAIL_PORT', 587, 'number'),
    secure: getEnvValue('EMAIL_SECURE', 'false', 'boolean'),
    user: getEnvValue('EMAIL_USER', ''),
    password: getEnvValue('EMAIL_PASSWORD', ''),
    from: getEnvValue('EMAIL_FROM', '<EMAIL>')
  },

  // 外部API配置
  externalApi: {
    xiaohongshu: {
      baseUrl: getEnvValue('XHS_API_BASE_URL', 'https://www.xiaohongshu.com'),
      timeout: getEnvValue('XHS_API_TIMEOUT', 10000, 'number')
    },
    juxingtu: {
      baseUrl: getEnvValue('JXT_API_BASE_URL', 'https://www.juxingtu.com'),
      timeout: getEnvValue('JXT_API_TIMEOUT', 10000, 'number')
    }
  },

  // CRM集成配置
  crm: {
    // 代理服务器配置
    // proxyUrl: getEnvValue('CRM_PROXY_URL', 'http://*************:8788'),
    proxyUrl: getEnvValue('CRM_PROXY_URL', 'http://crmapi.superboss.cc'),

    // 钉钉CRM认证配置
    corpId: getEnvValue('CRM_CORP_ID', 'ding96479bbd378e45bf35c2f4657eb6378f'),
    appId: getEnvValue('CRM_APP_ID', '3a3acf7ff6c9486d8d39342f24b0b887'),
    appSecret: getEnvValue('CRM_APP_SECRET', '13DBA19386A53548B7573B754E639958'),

    // API配置
    timeout: getEnvValue('CRM_API_TIMEOUT', 30000, 'number'),
    retryAttempts: getEnvValue('CRM_RETRY_ATTEMPTS', 3, 'number'),
    retryDelay: getEnvValue('CRM_RETRY_DELAY', 2000, 'number'),

    // 频率限制配置
    rateLimit: {
      maxRequests: getEnvValue('CRM_RATE_LIMIT_MAX', 60, 'number'), // 最大60次/20秒
      windowMs: getEnvValue('CRM_RATE_LIMIT_WINDOW', 20000, 'number'), // 20秒窗口
      requestDelay: getEnvValue('CRM_REQUEST_DELAY', 500, 'number') // 请求间隔500ms
    },

    // Token缓存配置
    tokenCache: {
      ttl: getEnvValue('CRM_TOKEN_TTL', 7200, 'number'), // token有效期7200秒
      refreshBuffer: getEnvValue('CRM_TOKEN_REFRESH_BUFFER', 300, 'number') // 提前5分钟刷新
    }
  }
};

// 开发环境特殊配置
if (env.NODE_ENV === 'development') {
  // 开发环境可以放宽一些验证
  console.log('🔧 运行在开发环境');
}

// 生产环境验证必需的环境变量
if (env.NODE_ENV === 'production') {
  console.log('🚀 运行在生产环境');

  const requiredVars = ['JWT_SECRET', 'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];

  validateRequiredEnvVars(requiredVars);

  // 生产环境安全检查
  if (env.jwt.secret === 'your-super-secret-jwt-key') {
    console.error('❌ 生产环境必须设置安全的JWT_SECRET');
    process.exit(1);
  }

  if (env.cookie.encryptionKey === 'your-secret-key-32-chars-long!!') {
    console.error('❌ 生产环境必须设置安全的COOKIE_ENCRYPTION_KEY');
    process.exit(1);
  }
}

// 导出配置
module.exports = env;
