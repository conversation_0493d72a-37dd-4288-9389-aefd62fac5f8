/**
 * 数据库配置和连接管理
 *
 * 功能说明：
 * - 使用统一的环境配置管理数据库连接
 * - 提供数据库连接测试功能
 * - 配置Sequelize实例和连接池
 * - 支持多环境数据库配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const { Sequelize } = require('sequelize');
const envConfig = require('./env');

// 数据库配置
const config = {
  host: envConfig.database.host,
  port: envConfig.database.port,
  database: envConfig.database.name,
  username: envConfig.database.username,
  password: envConfig.database.password,
  dialect: envConfig.database.dialect,
  timezone: envConfig.database.timezone,
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  },
  pool: envConfig.database.pool,
  logging: envConfig.database.logging ? console.log : false
};

// 创建Sequelize实例
const sequelize = new Sequelize(config.database, config.username, config.password, config);

/**
 * 测试数据库连接
 *
 * 功能说明：
 * - 验证数据库连接是否正常
 * - 输出连接状态信息
 * - 用于应用启动时的健康检查
 *
 * @returns {Promise<void>} 无返回值，通过控制台输出结果
 */
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    console.log(`📊 数据库: ${config.database}@${config.host}:${config.port}`);
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    throw error;
  }
};

module.exports = {
  sequelize,
  testConnection
};
