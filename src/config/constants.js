/**
 * 系统常量配置文件
 *
 * 功能说明：
 * - 定义系统中使用的所有常量
 * - 避免硬编码，提高代码可维护性
 * - 统一管理配置项，便于修改和扩展
 *
 * 配置分类：
 * - 用户相关：角色、状态等
 * - 平台相关：支持的平台类型
 * - 任务相关：任务状态、优先级等
 * - Cookie相关：状态、轮换配置等
 * - 系统相关：分页、限制等
 *
 * <AUTHOR>
 * @version 1.0.0
 */

// 用户相关常量
const USER_CONSTANTS = {
  // 用户角色
  ROLES: {
    ADMIN: 'admin',
    USER: 'user'
  },

  // 用户状态
  STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    BANNED: 'banned'
  },

  // 密码相关
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    SALT_ROUNDS: 10
  }
};

// 平台相关常量
const PLATFORM_CONSTANTS = {
  // 支持的平台类型
  TYPES: {
    XIAOHONGSHU: 'xiaohongshu',
    JUXINGTU: 'juxingtu',
    TAOBAO: '淘宝',
    KUAISHOU: '快手',
    BILIBILI: 'B站',
    WECHAT_VIDEO: '微信&视频号',
    WEIBO: '微博',
    ZHIHU: '知乎',
    OTHER: '其他',
    EXCHANGE: '置换'
  },

  // 平台中文名称映射
  NAMES: {
    xiaohongshu: '小红书',
    juxingtu: '巨量星图',
    淘宝: '淘宝',
    快手: '快手',
    B站: 'B站',
    '微信&视频号': '微信&视频号',
    微博: '微博',
    知乎: '知乎',
    其他: '其他',
    置换: '置换'
  },

  // 支持爬虫功能的平台（仅小红书和巨量星图）
  CRAWLER_SUPPORTED: ['xiaohongshu', 'juxingtu'],

  // 所有支持的平台列表
  ALL_PLATFORMS: ['xiaohongshu', 'juxingtu', '淘宝', '快手', 'B站', '微信&视频号', '微博', '知乎', '其他', '置换']
};

// 爬虫任务相关常量
const CRAWLER_CONSTANTS = {
  // 任务状态
  TASK_STATUS: {
    PENDING: 'pending',
    RUNNING: 'running',
    PAUSED: 'paused',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled'
  },

  // 任务优先级
  PRIORITY: {
    LOW: 0,
    NORMAL: 1,
    HIGH: 2,
    URGENT: 3
  },

  // 默认配置
  DEFAULTS: {
    MAX_PAGES: 5,
    PAGE_SIZE: 20,
    RETRY_TIMES: 3,
    TIMEOUT: 30000 // 30秒
  }
};

// Cookie管理相关常量
const COOKIE_CONSTANTS = {
  // Cookie状态
  STATUS: {
    ACTIVE: 'active',
    EXPIRED: 'expired',
    BANNED: 'banned',
    DISABLED: 'disabled'
  },

  // 轮换配置
  ROTATION: {
    MAX_DAILY_USE: 100,
    COOLDOWN_MINUTES: 5,
    VALIDATION_INTERVAL: 60,
    MAX_CONSECUTIVE_FAILURES: 3
  },

  // 加密配置
  ENCRYPTION: {
    ALGORITHM: 'aes-256-cbc',
    KEY_LENGTH: 32,
    IV_LENGTH: 16
  }
};

// 达人管理相关常量
const INFLUENCER_CONSTANTS = {
  // 达人状态
  STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    BLACKLISTED: 'blacklisted'
  },

  // 分类
  CATEGORIES: {
    BEAUTY: 'beauty',
    FASHION: 'fashion',
    FOOD: 'food',
    TRAVEL: 'travel',
    LIFESTYLE: 'lifestyle',
    TECH: 'tech',
    FITNESS: 'fitness',
    PARENTING: 'parenting'
  },

  // 分类中文名称
  CATEGORY_NAMES: {
    beauty: '美妆',
    fashion: '时尚',
    food: '美食',
    travel: '旅行',
    lifestyle: '生活方式',
    tech: '科技',
    fitness: '健身',
    parenting: '育儿'
  }
};

// 系统相关常量
const SYSTEM_CONSTANTS = {
  // 分页配置
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
    MAX_LIMIT: 100
  },

  // 文件上传
  UPLOAD: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
    UPLOAD_PATH: 'uploads/'
  },

  // 缓存配置
  CACHE: {
    DEFAULT_TTL: 3600, // 1小时
    SHORT_TTL: 300, // 5分钟
    LONG_TTL: 86400 // 24小时
  },

  // API限制
  API_LIMITS: {
    RATE_LIMIT: 1000, // 每小时请求次数
    BURST_LIMIT: 100, // 突发请求次数
    WINDOW_SIZE: 3600000 // 1小时窗口
  }
};

// HTTP状态码常量
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500
};

// 错误消息常量
const ERROR_MESSAGES = {
  // 通用错误
  INTERNAL_ERROR: '服务器内部错误',
  INVALID_PARAMS: '参数错误',
  RESOURCE_NOT_FOUND: '资源不存在',

  // 认证相关
  UNAUTHORIZED: '未授权访问',
  INVALID_TOKEN: '无效的令牌',
  TOKEN_EXPIRED: '令牌已过期',
  INSUFFICIENT_PERMISSIONS: '权限不足',

  // 用户相关
  USER_NOT_FOUND: '用户不存在',
  USER_ALREADY_EXISTS: '用户已存在',
  INVALID_CREDENTIALS: '用户名或密码错误',
  ACCOUNT_DISABLED: '账户已被禁用',

  // Cookie相关
  COOKIE_NOT_FOUND: 'Cookie不存在',
  COOKIE_EXPIRED: 'Cookie已过期',
  NO_AVAILABLE_COOKIE: '没有可用的Cookie',

  // 任务相关
  TASK_NOT_FOUND: '任务不存在',
  TASK_ALREADY_RUNNING: '任务已在运行中',
  INVALID_TASK_STATUS: '无效的任务状态'
};

// 成功消息常量
const SUCCESS_MESSAGES = {
  // 通用成功
  OPERATION_SUCCESS: '操作成功',
  CREATE_SUCCESS: '创建成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',

  // 认证相关
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出成功',
  REGISTER_SUCCESS: '注册成功',

  // 任务相关
  TASK_CREATED: '任务创建成功',
  TASK_STARTED: '任务启动成功',
  TASK_PAUSED: '任务暂停成功',
  TASK_COMPLETED: '任务完成',

  // Cookie相关
  COOKIE_ADDED: 'Cookie添加成功',
  COOKIE_VALIDATED: 'Cookie验证成功',
  COOKIE_UPDATED: 'Cookie更新成功'
};

module.exports = {
  USER_CONSTANTS,
  PLATFORM_CONSTANTS,
  CRAWLER_CONSTANTS,
  COOKIE_CONSTANTS,
  INFLUENCER_CONSTANTS,
  SYSTEM_CONSTANTS,
  HTTP_STATUS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
};
