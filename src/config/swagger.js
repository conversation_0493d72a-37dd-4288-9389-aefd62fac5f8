const swaggerJSDoc = require('swagger-jsdoc');
const path = require('path');

// Swagger配置
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '达人信息管理系统 API',
      version: '1.0.0',
      description: `
        基于 Koa2 + MySQL 构建的现代化达人信息管理系统API文档
        
        ## 功能特性
        - 🎯 **达人管理**: 完整的达人信息CRUD操作，支持批量导入导出
        - 🤖 **爬虫管理**: 智能爬虫任务调度，实时进度监控，结果分析
        - 🍪 **Cookie管理**: 多账户Cookie轮换，自动失效检测，使用统计
        - 👤 **用户认证**: JWT认证，角色权限控制，会话管理
        
        ## 认证说明
        大部分API接口需要JWT认证，请先调用登录接口获取token，然后在请求头中添加：
        \`Authorization: Bearer <your-token>\`
      `,
      contact: {
        name: 'API支持',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3001/api',
        description: '开发环境'
      },
      {
        url: 'https://api.example.com/api',
        description: '生产环境'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT认证，格式：Bearer <token>'
        }
      },
      schemas: {
        // 通用响应格式
        ApiResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              description: '请求是否成功'
            },
            message: {
              type: 'string',
              description: '响应消息'
            },
            data: {
              description: '响应数据'
            }
          }
        },
        // 分页响应格式
        PaginatedResponse: {
          allOf: [
            { $ref: '#/components/schemas/ApiResponse' },
            {
              type: 'object',
              properties: {
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'integer', description: '当前页码' },
                    limit: { type: 'integer', description: '每页数量' },
                    total: { type: 'integer', description: '总记录数' },
                    pages: { type: 'integer', description: '总页数' }
                  }
                }
              }
            }
          ]
        },
        // 错误响应格式
        ErrorResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              description: '错误消息'
            },
            error: {
              type: 'string',
              description: '详细错误信息'
            }
          }
        },
        // 用户模型
        User: {
          type: 'object',
          properties: {
            id: { type: 'integer', description: '用户ID' },
            username: { type: 'string', description: '用户名' },
            email: { type: 'string', format: 'email', description: '邮箱' },
            role: { type: 'string', enum: ['admin', 'user'], description: '用户角色' },
            status: { type: 'string', enum: ['active', 'inactive'], description: '用户状态' },
            lastLoginAt: { type: 'string', format: 'date-time', description: '最后登录时间' },
            createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
            updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
          }
        },
        // 达人模型
        Influencer: {
          type: 'object',
          properties: {
            id: { type: 'integer', description: '达人ID' },
            nickname: { type: 'string', description: '达人昵称' },
            platform: { type: 'string', enum: ['xiaohongshu', 'juxingtu'], description: '平台类型' },
            platformId: { type: 'string', description: '平台ID' },
            avatarUrl: { type: 'string', format: 'uri', description: '头像链接' },
            followersCount: { type: 'integer', description: '粉丝数量' },
            category: { type: 'string', description: '分类' },
            tags: { type: 'array', items: { type: 'string' }, description: '标签' },
            contactInfo: { type: 'object', description: '联系方式' },
            priceInfo: { type: 'object', description: '报价信息' },
            cooperationHistory: { type: 'object', description: '合作历史' },
            notes: { type: 'string', description: '备注' },
            status: { type: 'string', enum: ['active', 'inactive'], description: '状态' },
            createdBy: { type: 'integer', description: '创建者ID' },
            createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
            updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
          }
        },
        // 爬虫任务模型
        CrawlTask: {
          type: 'object',
          properties: {
            id: { type: 'integer', description: '任务ID' },
            taskName: { type: 'string', description: '任务名称' },
            platform: { type: 'string', enum: ['xiaohongshu', 'juxingtu'], description: '目标平台' },
            keywords: { type: 'string', description: '搜索关键词' },
            status: { 
              type: 'string', 
              enum: ['pending', 'running', 'paused', 'completed', 'failed', 'cancelled'], 
              description: '任务状态' 
            },
            progress: { type: 'integer', minimum: 0, maximum: 100, description: '进度百分比' },
            totalCount: { type: 'integer', description: '总数量' },
            successCount: { type: 'integer', description: '成功数量' },
            failedCount: { type: 'integer', description: '失败数量' },
            currentPage: { type: 'integer', description: '当前爬取页数' },
            maxPages: { type: 'integer', description: '最大爬取页数' },
            config: { type: 'object', description: '爬虫配置参数' },
            priority: { type: 'integer', description: '任务优先级' },
            errorMessage: { type: 'string', description: '错误信息' },
            resultSummary: { type: 'object', description: '结果摘要统计' },
            retryCount: { type: 'integer', description: '重试次数' },
            maxRetries: { type: 'integer', description: '最大重试次数' },
            lastRetryAt: { type: 'string', format: 'date-time', description: '最后重试时间' },
            retryHistory: { type: 'array', items: { type: 'object' }, description: '重试历史记录' },
            startedAt: { type: 'string', format: 'date-time', description: '开始时间' },
            completedAt: { type: 'string', format: 'date-time', description: '完成时间' },
            createdBy: { type: 'integer', description: '创建者ID' },
            createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
            updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
          }
        },
        // Cookie模型
        CrawlerCookie: {
          type: 'object',
          properties: {
            id: { type: 'integer', description: 'Cookie ID' },
            accountName: { type: 'string', description: '账户名称' },
            platform: { type: 'string', enum: ['xiaohongshu', 'juxingtu'], description: '平台类型' },
            status: { 
              type: 'string', 
              enum: ['active', 'inactive', 'expired', 'banned'], 
              description: 'Cookie状态' 
            },
            priority: { type: 'integer', description: '优先级' },
            useCount: { type: 'integer', description: '使用次数' },
            maxDailyUse: { type: 'integer', description: '每日最大使用次数' },
            dailyUseCount: { type: 'integer', description: '今日使用次数' },
            lastUsedAt: { type: 'string', format: 'date-time', description: '最后使用时间' },
            lastValidatedAt: { type: 'string', format: 'date-time', description: '最后验证时间' },
            cooldownUntil: { type: 'string', format: 'date-time', description: '冷却结束时间' },
            notes: { type: 'string', description: '备注信息' },
            createdBy: { type: 'integer', description: '创建者ID' },
            createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
            updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: '认证失败',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                success: false,
                message: '认证失败',
                error: '无效的认证令牌'
              }
            }
          }
        },
        ValidationError: {
          description: '参数验证失败',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                success: false,
                message: '参数验证失败',
                error: '用户名不能为空'
              }
            }
          }
        },
        NotFoundError: {
          description: '资源不存在',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                success: false,
                message: '资源不存在'
              }
            }
          }
        },
        ServerError: {
          description: '服务器内部错误',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                success: false,
                message: '服务器内部错误'
              }
            }
          }
        }
      }
    },
    tags: [
      {
        name: '系统',
        description: '系统相关接口'
      },
      {
        name: '认证',
        description: '用户认证相关接口'
      },
      {
        name: '用户管理',
        description: '用户账号管理相关接口（仅管理员）'
      },
      {
        name: '达人管理',
        description: '达人信息管理相关接口'
      },
      {
        name: '爬虫管理',
        description: '爬虫任务管理相关接口'
      },
      {
        name: 'Cookie管理',
        description: 'Cookie管理相关接口'
      },
      {
        name: '合作对接管理',
        description: '合作对接管理相关接口'
      }
    ]
  },
  apis: [
    path.join(__dirname, '../routes/*.js'),
    path.join(__dirname, '../controllers/*.js'),
    path.join(__dirname, '../docs/swagger/*.yaml')
  ]
};

// 生成Swagger规范
const swaggerSpec = swaggerJSDoc(swaggerOptions);

module.exports = {
  swaggerSpec,
  swaggerOptions
};
