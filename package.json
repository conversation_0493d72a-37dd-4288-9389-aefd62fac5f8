{"name": "daren-server", "version": "1.0.0", "description": "达人信息管理系统后端API服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["koa", "mysql", "api", "daren", "influencer"], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.15.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "dotenv": "^17.2.0", "echarts": "^5.6.0", "eventsource": "^4.0.0", "jsonwebtoken": "^9.0.2", "koa": "^3.0.0", "koa-bodyparser": "^4.4.1", "koa-cors": "^0.0.16", "koa-jwt": "^4.0.4", "koa-router": "^13.1.1", "koa-static": "^5.0.0", "koa2-swagger-ui": "^5.11.0", "mysql2": "^3.14.2", "node-cron": "^4.2.1", "sequelize": "^6.37.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-dist": "^5.26.2", "uuid": "^11.1.0", "vue-echarts": "^7.0.3", "xlsx": "^0.18.5", "zod": "^3.25.76"}, "devDependencies": {"nodemon": "^3.1.10"}}